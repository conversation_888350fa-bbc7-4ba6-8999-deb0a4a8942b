package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ExtratoFaturamento;
import SasBeans.FatTVGuias;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FatTVGuiasDao {

    /**
     * Troca a Série da Guia em FatTVGuias
     *
     * @param persistencia - conexão ao banco de dados
     * @param fattvguias - dados da guia Obrigatório - Guia, Série, Codfil
     * @param nova_serie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, FatTVGuias fattvguias, String nova_serie) throws Exception {
        try {
            String sql;
            sql = "update FatTVGuias set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie =  ?"
                    + " and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nova_serie);
            consulta.setString(fattvguias.getGuia().toPlainString());
            consulta.setString(fattvguias.getSerie());
            consulta.setString(fattvguias.getCodFil().toPlainString());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("FatTVGuiasDao.TrocaSerieGuia - " + e.getMessage() + "\r\n"
                    + "update FatTVGuias set Serie = " + nova_serie
                    + " where Guia = " + fattvguias.getGuia()
                    + " and Serie =  " + fattvguias.getSerie()
                    + " and CodFil = " + fattvguias.getCodFil().toPlainString());
        }
    }

    public List<ExtratoFaturamento> extratoFaturamentoListaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<ExtratoFaturamento> retorno = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        try {

            /*--------------------------------------------
            |  Consulta de Extrato de Pagamento - Parte 1 |
            ----------------------------------------------*/
            sql.append("SELECT FatTVPlan.*, FatTVFechaNF.NF, OS_Vig.CCusto, OS_Vig.Descricao, OS_Vig.Cliente, OS_Vig.NRed, OS_Vig.NRedDst, CONVERT(BigInt, FatTVPlan.Guia) GuiaFormatada \n");
            sql.append(" INTO #tblDados");
            sql.append(" from FatTVPlan \n");
            sql.append(" left Join OS_Vig on  OS_Vig.OS = FatTVPlan.OS \n");
            sql.append("                  and OS_Vig.CodFil = FatTVPlan.CodFil \n");
            sql.append(" Left Join FatTvFecha on FatTvFecha.Numero = FatTvPlan.Numero\n");
            sql.append("                     and FatTvFecha.CodFil = FatTvPlan.CodFil\n");
            sql.append(" Left Join FatTVFechaNF on  FatTVFechaNF.Numero = FatTVPlan.Numero \n");
            sql.append("                        and FatTVFechaNF.SeqNF  = FatTVPlan.SeqNF \n");
            sql.append("                        and FatTvFechaNF.CodFil = FatTvPlan.CodFIl\n");
            sql.append(" Left Join PessoaCliAut on PessoaCliAut.CodCli = OS_Vig.Cliente\n");
            sql.append("                       and PessoaCliAut.CodFil = OS_Vig.CodFil");
            sql.append(" WHERE PessoaCliAut.CodCli is not null \n");

            /*--------------------------------------------
            |               Aplicar Filtros               |
            ----------------------------------------------*/
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql.append(" AND ").append(entrada.getKey());
                }
            }

            /*--------------------------------------------
            |  Consulta de Extrato de Pagamento - Parte 2 |
            ----------------------------------------------*/
            sql.append(" ORDER BY OS_Vig.NRed, FatTVPlan.Data \n");
            sql.append(" SELECT * FROM (SELECT ROW_NUMBER() OVER (ORDER BY Numero) AS RowNum, * FROM #tblDados) AS X \n");
            sql.append(" WHERE RowNum >= ? ");
            sql.append(" AND RowNum < ? ");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            Consulta consultaTotais = new Consulta(sql.toString(), persistencia);

            /*--------------------------------------------
            |         Atribuir valores dos filtros        |
            ----------------------------------------------*/
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consultaTotais.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consultaTotais.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consultaTotais.setInt(primeiro + 1 + linhas);
            consulta.select();
            consultaTotais.select();

            ExtratoFaturamento extrato;

            /*--------------------------------------------
            |                Calcular Totais              |
            ----------------------------------------------*/
            List<ExtratoFaturamento> extratoListaTotais = new ArrayList<>();
            ExtratoFaturamento extratoCalculo;
            double ICMS = 0.0, ISS = 0.0, Outros = 0.0, Total = 0.0, AdValorem = 0.0, Montante = 0.0, TempoEspera = 0.0, TempoEsperaQtde = 0.0, Custodia = 0.0, CustodiaQtde = 0.0, Milheiros = 0.0, MilheirosQtde = 0.0, Embarques = 0.0, EmbarquesQtde = 0.0, Envelopes = 0.0, EnvelopesQtde = 0.0;
            String Nred = "";

            while (consultaTotais.Proximo()) {
                if (!consultaTotais.getString("NRed").equals(Nred) && !Nred.equals("")) {
                    extratoCalculo = new ExtratoFaturamento();

                    extratoCalculo.setNred(Nred);
                    extratoCalculo.setICMS(String.valueOf(ICMS));
                    extratoCalculo.setISS(String.valueOf(ISS));
                    extratoCalculo.setOutros(String.valueOf(Outros));
                    extratoCalculo.setTotal(String.valueOf(Total));
                    extratoCalculo.setAdValorem(String.valueOf(AdValorem));
                    extratoCalculo.setMontante(String.valueOf(Montante));
                    extratoCalculo.setTempoEspera(String.valueOf(TempoEspera));
                    extratoCalculo.setTempoEsperaQtde(String.valueOf(TempoEsperaQtde));
                    extratoCalculo.setCustodia(String.valueOf(Custodia));
                    extratoCalculo.setCustodiaQtde(String.valueOf(CustodiaQtde));
                    extratoCalculo.setMilheiros(String.valueOf(Milheiros));
                    extratoCalculo.setMilheirosQtde(String.valueOf(MilheirosQtde));
                    extratoCalculo.setEmbarques(String.valueOf(Embarques));
                    extratoCalculo.setEmbarquesQtde(String.valueOf(EmbarquesQtde));
                    extratoCalculo.setEnvelopesMalotes(String.valueOf(Envelopes));
                    extratoCalculo.setEnvelopesMalotesQtde(String.valueOf(EnvelopesQtde));

                    extratoListaTotais.add(extratoCalculo);

                    ICMS = 0.0;
                    ISS = 0.0;
                    Outros = 0.0;
                    Total = 0.0;
                    AdValorem = 0.0;
                    Montante = 0.0;
                    TempoEspera = 0.0;
                    TempoEsperaQtde = 0.0;
                    Custodia = 0.0;
                    CustodiaQtde = 0.0;
                    Milheiros = 0.0;
                    MilheirosQtde = 0.0;
                    Embarques = 0.0;
                    EmbarquesQtde = 0.0;
                    Envelopes = 0.0;
                    EnvelopesQtde = 0.0;
                }

                Nred = consultaTotais.getString("NRed");

                if (null != consultaTotais.getString("ICMS")
                        && !consultaTotais.getString("ICMS").equals("")) {
                    ICMS += new Double(consultaTotais.getString("ICMS"));
                }

                if (null != consultaTotais.getString("ISS")
                        && !consultaTotais.getString("ISS").equals("")) {
                    ISS += new Double(consultaTotais.getString("ISS"));
                }

                if (null != consultaTotais.getString("Repasse")
                        && !consultaTotais.getString("Repasse").equals("")) {
                    Outros += new Double(consultaTotais.getString("Repasse"));
                }

                if (null != consultaTotais.getString("Total")
                        && !consultaTotais.getString("Total").equals("")) {
                    Total += new Double(consultaTotais.getString("Total"));
                }

                if (null != consultaTotais.getString("AdValorem")
                        && !consultaTotais.getString("AdValorem").equals("")) {
                    AdValorem += new Double(consultaTotais.getString("AdValorem"));
                }

                if (null != consultaTotais.getString("vTempoEspera")
                        && !consultaTotais.getString("vTempoEspera").equals("")) {
                    TempoEspera += new Double(consultaTotais.getString("vTempoEspera"));
                }

                if (null != consultaTotais.getString("TempoEspera")
                        && !consultaTotais.getString("TempoEspera").equals("")) {
                    TempoEsperaQtde += new Double(consultaTotais.getString("TempoEspera"));
                }

                if (null != consultaTotais.getString("vCustodia")
                        && !consultaTotais.getString("vCustodia").equals("")) {
                    Custodia += new Double(consultaTotais.getString("vCustodia"));
                }

                if (null != consultaTotais.getString("MontanteCst")
                        && !consultaTotais.getString("MontanteCst").equals("")) {
                    CustodiaQtde += new Double(consultaTotais.getString("MontanteCst"));
                }

                if (null != consultaTotais.getString("vMilheiros")
                        && !consultaTotais.getString("vMilheiros").equals("")) {
                    Milheiros += new Double(consultaTotais.getString("vMilheiros"));
                }

                if (null != consultaTotais.getString("Milheiros")
                        && !consultaTotais.getString("Milheiros").equals("")) {
                    MilheirosQtde += new Double(consultaTotais.getString("Milheiros"));
                }

                if (null != consultaTotais.getString("vEmb")
                        && !consultaTotais.getString("vEmb").equals("")) {
                    Embarques += new Double(consultaTotais.getString("vEmb"));
                }

                if (null != consultaTotais.getString("nEmb")
                        && !consultaTotais.getString("nEmb").equals("")) {
                    EmbarquesQtde += new Double(consultaTotais.getString("nEmb"));
                }

                if (null != consultaTotais.getString("vMalotes")
                        && !consultaTotais.getString("vMalotes").equals("")) {
                    Envelopes += new Double(consultaTotais.getString("vMalotes"));
                }

                if (null != consultaTotais.getString("Malotes")
                        && !consultaTotais.getString("Malotes").equals("")) {
                    EnvelopesQtde += new Double(consultaTotais.getString("Malotes"));
                }

                if (null != consultaTotais.getString("Montante")
                        && !consultaTotais.getString("Montante").equals("")) {
                    Montante += new Double(consultaTotais.getString("Montante"));
                }
            }

            if (!Nred.equals("")) {
                extratoCalculo = new ExtratoFaturamento();

                extratoCalculo.setNred(Nred);
                extratoCalculo.setICMS(String.valueOf(ICMS));
                extratoCalculo.setISS(String.valueOf(ISS));
                extratoCalculo.setOutros(String.valueOf(Outros));
                extratoCalculo.setTotal(String.valueOf(Total));
                extratoCalculo.setAdValorem(String.valueOf(AdValorem));
                extratoCalculo.setMontante(String.valueOf(Montante));
                extratoCalculo.setTempoEspera(String.valueOf(TempoEspera));
                extratoCalculo.setTempoEsperaQtde(String.valueOf(TempoEsperaQtde));
                extratoCalculo.setCustodia(String.valueOf(Custodia));
                extratoCalculo.setCustodiaQtde(String.valueOf(CustodiaQtde));
                extratoCalculo.setMilheiros(String.valueOf(Milheiros));
                extratoCalculo.setMilheirosQtde(String.valueOf(MilheirosQtde));
                extratoCalculo.setEmbarques(String.valueOf(Embarques));
                extratoCalculo.setEmbarquesQtde(String.valueOf(EmbarquesQtde));
                extratoCalculo.setEnvelopesMalotes(String.valueOf(Envelopes));
                extratoCalculo.setEnvelopesMalotesQtde(String.valueOf(EnvelopesQtde));

                extratoListaTotais.add(extratoCalculo);
            }

            /*--------------------------------------------
            |           Guardar Dados no Modelo           |
            ----------------------------------------------*/
            while (consulta.Proximo()) {
                extrato = new ExtratoFaturamento();

                extrato.setNred(consulta.getString("Nred"));
                extrato.setData(consulta.getString("Data"));
                extrato.setGuia(consulta.getString("GuiaFormatada"));
                extrato.setHorarioChegada(consulta.getString("HrCheg"));
                extrato.setHorarioSaida(consulta.getString("HrSaida"));

                extrato.setICMS(consulta.getString("ICMS"));
                extrato.setISS(consulta.getString("ISS"));
                extrato.setOutros(consulta.getString("Repasse"));
                extrato.setTotal(consulta.getString("Total"));
                extrato.setAdValorem(consulta.getString("AdValorem"));
                extrato.setMontante(consulta.getString("Montante"));
                extrato.setTempoEspera(consulta.getString("vTempoEspera"));
                extrato.setTempoEsperaQtde(consulta.getString("TempoEspera"));
                extrato.setCustodia(consulta.getString("vCustodia"));
                extrato.setCustodiaQtde(consulta.getString("MontanteCst"));
                extrato.setMilheiros(consulta.getString("vMilheiros"));
                extrato.setMilheirosQtde(consulta.getString("Milheiros"));
                extrato.setEmbarques(consulta.getString("vEmb"));
                extrato.setEmbarquesQtde(consulta.getString("nEmb"));
                extrato.setEnvelopesMalotes(consulta.getString("vMalotes"));
                extrato.setEnvelopesMalotesQtde(consulta.getString("Malotes"));

                for (ExtratoFaturamento extratoListaTotais1 : extratoListaTotais) {
                    if (extratoListaTotais1.getNred().equals(consulta.getString("Nred"))) {
                        extrato.setTotalICMS(extratoListaTotais1.getICMS());
                        extrato.setTotalISS(extratoListaTotais1.getISS());
                        extrato.setTotalOutros(extratoListaTotais1.getOutros());
                        extrato.setTotalTotal(extratoListaTotais1.getTotal());
                        extrato.setTotalAdValorem(extratoListaTotais1.getAdValorem());
                        extrato.setTotalMontante(extratoListaTotais1.getMontante());
                        extrato.setTotalTempoEspera(extratoListaTotais1.getTempoEspera());
                        extrato.setTotalTempoEsperaQtde(extratoListaTotais1.getTempoEsperaQtde());
                        extrato.setTotalCustodia(extratoListaTotais1.getCustodia());
                        extrato.setTotalCustodiaQtde(extratoListaTotais1.getCustodiaQtde());
                        extrato.setTotalMilheiros(extratoListaTotais1.getMilheiros());
                        extrato.setTotalMilheirosQtde(extratoListaTotais1.getMontanteQtde());
                        extrato.setTotalEmbarques(extratoListaTotais1.getEmbarques());
                        extrato.setTotalEmbarquesQtde(extratoListaTotais1.getEmbarquesQtde());
                        extrato.setTotalEnvelopesMalotes(extratoListaTotais1.getEnvelopesMalotes());
                        extrato.setTotalEnvelopesMalotesQtde(extratoListaTotais1.getEnvelopesMalotesQtde());
                        break;
                    }
                }

                retorno.add(extrato);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FatTVGuiasDao.extratoFaturamentoListaPaginada - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public Integer totalExtratoFaturamento(Map filtros, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {

            /*--------------------------------------------
            |  Consulta de Extrato de Pagamento - Parte 1 |
            ----------------------------------------------*/
            sql.append("SELECT COUNT(*) total \n");
            sql.append(" from FatTVPlan \n");
            sql.append(" left Join OS_Vig on  OS_Vig.OS = FatTVPlan.OS \n");
            sql.append("                  and OS_Vig.CodFil = FatTVPlan.CodFil \n");
            sql.append(" Left Join FatTvFecha on FatTvFecha.Numero = FatTvPlan.Numero\n");
            sql.append("                     and FatTvFecha.CodFil = FatTvPlan.CodFil\n");
            sql.append(" Left Join FatTVFechaNF on  FatTVFechaNF.Numero = FatTVPlan.Numero \n");
            sql.append("                        and FatTVFechaNF.SeqNF  = FatTVPlan.SeqNF \n");
            sql.append("                        and FatTvFechaNF.CodFil = FatTvPlan.CodFIl\n");
            sql.append(" Left Join PessoaCliAut on PessoaCliAut.CodCli = OS_Vig.Cliente\n");
            sql.append("                       and PessoaCliAut.CodFil = OS_Vig.CodFil");
            sql.append(" WHERE PessoaCliAut.CodCli is not null \n");

            /*--------------------------------------------
            |               Aplicar Filtros               |
            ----------------------------------------------*/
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql.append(" AND ").append(entrada.getKey());
                }
            }

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            /*--------------------------------------------
            |         Atribuir valores dos filtros        |
            ----------------------------------------------*/
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            if (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("FatTVGuiasDao.totalExtratoFaturamento - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }



    /**
     * Lista extrato de faturamento com nova consulta para clientes
     *
     * @param codPessoa - código da pessoa logada
     * @param codfil - código da filial
     * @param dataInicio - data início do período
     * @param dataFim - data fim do período
     * @param numeroNF - número da NF (opcional)
     * @param pracaNF - praça da NF (opcional)
     * @param persistencia - conexão ao banco de dados local
     * @param central - conexão ao banco central
     * @return Lista de extrato de faturamento
     * @throws Exception
     */
    public List<ExtratoFaturamento> listarExtratoCliente(String codPessoa, String codfil, String dataInicio, String dataFim,
                                                         String numeroNF, String pracaNF, Persistencia persistencia, Persistencia central) throws Exception {
        try {
            List<ExtratoFaturamento> retorno = new ArrayList<>();

            // Primeiro, buscar os códigos de cliente no banco central
            List<String> codigosCliente = new ArrayList<>();
            try {
                String sqlCentral = "Select CodCli from GTVeAcesso where GTVeAcesso.CodPessoa = ? AND GTVeAcesso.CodFil = ?";
                Consulta consultaCentral = new Consulta(sqlCentral, central);
                consultaCentral.setString(codPessoa);
                consultaCentral.setString(codfil);
                consultaCentral.select();

                while (consultaCentral.Proximo()) {
                    codigosCliente.add(consultaCentral.getString("CodCli"));
                }
                consultaCentral.Close();
            } catch (Exception e) {
                System.out.println("Erro ao consultar GTVeAcesso no banco central para extrato: " + e.getMessage());
                return retorno;
            }

            if (codigosCliente.isEmpty()) {
                System.out.println("Nenhum cliente encontrado para extrato - codPessoa: " + codPessoa + ", codfil: " + codfil);
                return retorno;
            }

            // Construir a consulta com os códigos encontrados
            StringBuilder inClause = new StringBuilder();
            for (int i = 0; i < codigosCliente.size(); i++) {
                if (i > 0) inClause.append(",");
                inClause.append("?");
            }

            StringBuilder sql = new StringBuilder();

            sql.append("Select FatTVFechaNF.NF, FatTVFechaNF.Praca, FatTVPlan.Data, ");
            sql.append("Case When FatTVPlan.TipoSrv = 'R' then 'Recolhimento' when FatTVPlan.TipoSrv = 'E' then 'Entrega' else FatTVPlan.TipoSrv End TipoSrv, ");
            sql.append("FatTVPlan.Guia, FatTVPlan.Serie, FatTVPlan.Montante, FatTVPlan.vEmb, FatTVPlan.AdValorem, ");
            sql.append("FatTVPlan.TempoEspera, FatTVPlan.vCustodia, FatTVPlan.vMilheiros, FatTVPlan.vEnvelopes, ");
            sql.append("FatTVPlan.vMalotes, FatTVPlan.Repasse ISS_ICMS_Repasse, FatTVPlan.Total ");
            sql.append("from fattvplan ");
            sql.append("left join FatTVFechaNF on FatTVFechaNF.Numero = FatTVPlan.Numero ");
            sql.append("                       and FatTVFechaNF.SeqNF = FatTVPlan.SeqNF ");
            sql.append("where FatTVFechaNF.CliFat in (" + inClause.toString() + ") ");
            sql.append("  and FatTVFechaNF.CodFil = ? ");

            List<String> parametros = new ArrayList<>();
            // Adicionar os códigos de cliente
            parametros.addAll(codigosCliente);
            parametros.add(codfil);

            // Se filtrar por NF específica, desconsidera o período
            if (numeroNF != null && !numeroNF.trim().isEmpty() && pracaNF != null && !pracaNF.trim().isEmpty()) {
                sql.append("  and FatTVFechaNF.NF = ? ");
                sql.append("  and FatTVFechaNF.Praca = ? ");
                parametros.add(numeroNF);
                parametros.add(pracaNF);
            } else {
                // Filtro por período
                sql.append("  and FatTVPlan.Data >= ? ");
                sql.append("  and FattvPlan.Data <= ? ");
                parametros.add(dataInicio);
                parametros.add(dataFim);
            }

            sql.append("order by FatTVPlan.Data desc, FatTVFechaNF.NF desc");

            Consulta consulta = new Consulta(sql.toString(), persistencia);
            for (int i = 0; i < parametros.size(); i++) {
                consulta.setString(parametros.get(i));
            }
            consulta.select();

            while (consulta.Proximo()) {
                ExtratoFaturamento extrato = new ExtratoFaturamento();

                // Converter NF para inteiro (remover .0)
                String nf = consulta.getString("NF");
                if (nf != null && nf.contains(".")) {
                    nf = nf.substring(0, nf.indexOf("."));
                }
                extrato.setNF(nf);

                // Converter Praça para inteiro (remover .0)
                String praca = consulta.getString("Praca");
                if (praca != null && praca.contains(".")) {
                    praca = praca.substring(0, praca.indexOf("."));
                }
                extrato.setPraca(praca);

                extrato.setData(consulta.getString("Data"));
                extrato.setTipoSrv(consulta.getString("TipoSrv"));

                // Converter Guia para inteiro (remover .0 e notação científica)
                String guia = consulta.getString("Guia");
                if (guia != null) {
                    try {
                        // Se está em notação científica, converter para BigDecimal primeiro
                        if (guia.contains("E") || guia.contains("e")) {
                            java.math.BigDecimal bd = new java.math.BigDecimal(guia);
                            guia = bd.toPlainString();
                        }
                        // Remover .0 se existir
                        if (guia.contains(".")) {
                            guia = guia.substring(0, guia.indexOf("."));
                        }
                    } catch (Exception e) {
                        // Se der erro, manter o valor original
                        System.out.println("Erro ao converter guia: " + guia + " - " + e.getMessage());
                    }
                }
                extrato.setGuia(guia);

                // Converter Série para inteiro (remover .0)
                String serie = consulta.getString("Serie");
                if (serie != null && serie.contains(".")) {
                    serie = serie.substring(0, serie.indexOf("."));
                }
                extrato.setSerie(serie);

                extrato.setMontante(consulta.getString("Montante"));
                extrato.setEmbarques(consulta.getString("vEmb"));
                extrato.setAdValorem(consulta.getString("AdValorem"));
                extrato.setTempoEspera(consulta.getString("TempoEspera"));
                extrato.setCustodia(consulta.getString("vCustodia"));
                extrato.setMilheiros(consulta.getString("vMilheiros"));
                extrato.setEnvelopesMalotes(consulta.getString("vEnvelopes"));
                // Adicionar vMalotes se necessário
                extrato.setISS(consulta.getString("ISS_ICMS_Repasse"));
                extrato.setTotal(consulta.getString("Total"));
                retorno.add(extrato);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FatTVGuiasDao.listarExtratoCliente - " + e.getMessage());
        }
    }
}
