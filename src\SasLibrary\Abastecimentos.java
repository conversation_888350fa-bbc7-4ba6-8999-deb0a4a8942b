/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Veiculos;
import SasBeans.VeiculosAbast;
import SasBeansCompostas.ProdutosTbVal;
import SasDaos.ProdutosTbValDao;
import SasDaos.VeiculosAbastDao;
import SasDaos.VeiculosDao;
import Xml.Xmls;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Abastecimentos {
    
    /**
     * Busca os veículos e os combustíveis
     * @param sCodPessoa
     * @param dataAtual
     * @param codfil
     * @param persistencia
     * @return 
     */
    public static String CarregaAbastecimento(String sCodPessoa, String dataAtual, String codfil, Persistencia persistencia) {

        String ret = "<?xml version=\"1.0\"?>";
        List<Veiculos> lVeiculos;
        Integer iVeiculo;
        try {
            //carrega lista com custo e combustível do veículo
            VeiculosDao oVeiculos = new VeiculosDao();
            try {
                iVeiculo = oVeiculos.buscaVeiculo(sCodPessoa, dataAtual, persistencia);
                if (iVeiculo == 0) {
                    lVeiculos = oVeiculos.getVeiculos(codfil, persistencia);
                } else {
                    lVeiculos = oVeiculos.buscaDadosVeiculo(iVeiculo, persistencia);
                }
            } catch (Exception e) {
                throw new Exception("Falha ao carregar dados de Abastecimento  - " + e.getMessage());
            }
            if (lVeiculos.isEmpty()) {
                ret += "<resp>BuscaAbastecimento_2</resp>";  //sem dados de abastecimento do veículo 
                return ret;
            }

            StringBuilder resp = new StringBuilder("<?xml version=\"1.0\"?><resp>1</resp>"), auxVeic;

            ProdutosTbValDao prod = new ProdutosTbValDao();
            List<ProdutosTbVal> lprod = prod.buscaCombustiveis(persistencia);
            VeiculosAbastDao abastecimento = new VeiculosAbastDao();

            VeiculosAbast lveiculoab;
            resp.append(Xmls.tag("nVeic", lVeiculos.size()));
            for (Veiculos lVeic : lVeiculos) {

                auxVeic = new StringBuilder();

                auxVeic.append(Xmls.tag("ccusto", lVeic.getCCusto()))
                        .append(Xmls.tag("veiculo", lVeic.getNumero()))
                        .append(Xmls.tag("combust", lVeic.getCombust()))
                        .append(Xmls.tag("descricao", lVeic.getPlaca()+ " " + lVeic.getObs()))
                        .append(Xmls.tag("codfil", lVeic.getCodFil().toBigInteger()));

                lveiculoab = abastecimento.ultimoKM(lVeic.getNumero(), dataAtual, persistencia);
                if (lveiculoab == null) {
                    auxVeic.append(Xmls.tag("ultimokm", 0));
                } else {
                    auxVeic.append(Xmls.tag("ultimokm", lveiculoab.getKM()));
                }
                resp.append(Xmls.tag("veiculos", auxVeic));
            }

            int nComb = 0;
            for (ProdutosTbVal lprod1 : lprod) {
                if ("G".equals(lprod1.getProdutos().getTipoCombust())
                        || "A".equals(lprod1.getProdutos().getTipoCombust())
                        || "N".equals(lprod1.getProdutos().getTipoCombust())
                        || "D".equals(lprod1.getProdutos().getTipoCombust())) {
                    auxVeic = new StringBuilder();
                    auxVeic.append(Xmls.tag("descricao", lprod1.getProdutos().getDescricao()))
                            .append(Xmls.tag("un", lprod1.getProdutos().getUn()))
                            .append(Xmls.tag("tipo", lprod1.getProdutos().getTipoCombust()))
                            .append(Xmls.tag("codigo", lprod1.getProdutos().getCodigo()))
                            .append(Xmls.tag("precocusto", lprod1.getProdutos().getPrecoCusto()));
                    resp.append(Xmls.tag("combustivel", auxVeic));
                    nComb++;
                }
            }

            resp.append(Xmls.tag("nComb", nComb));

            return resp.toString();

        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String CarregaAbastecimento(String sCodPessoa, String dataAtual, Persistencia persistencia) {

        String ret = "<?xml version=\"1.0\"?>";
        List<Veiculos> lVeiculos;
        Integer iVeiculo;
        try {
            //carrega lista com custo e combustível do veículo
            VeiculosDao oVeiculos = new VeiculosDao();
            try {
                iVeiculo = oVeiculos.buscaVeiculo(sCodPessoa, dataAtual, persistencia);
                lVeiculos = oVeiculos.buscaDadosVeiculo(iVeiculo, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar dados de Abastecimento  - " + e.getMessage());
            }
            if (lVeiculos.isEmpty()) {
                ret += "<resp>BuscaAbastecimento_2</resp>";  //sem dados de abastecimento do veículo
                return ret;
            }
            String xml = null;
            String sNreg = null;

            for (Veiculos lVeic : lVeiculos) {

                List<ProdutosTbVal> lprod = new ArrayList();
                ProdutosTbValDao prod = new ProdutosTbValDao();
                VeiculosAbastDao abastecimento = new VeiculosAbastDao();

                xml = "<ccusto>" + lVeic.getCCusto() + "</ccusto>";
                xml += "<veiculo>" + iVeiculo + "</veiculo>";
                xml += "<codfil>" + lVeic.getCodFil().toString() + "</codfil>";

                lprod = prod.buscaCombustiveis(persistencia);
                VeiculosAbast lveiculoab = abastecimento.ultimoKM(iVeiculo, dataAtual, persistencia);
                if (lveiculoab == null) {
                    xml += "<ultimokm>0</ultimokm>";
                } else {
                    xml += "<ultimokm>" + lveiculoab.getKM() + "</ultimokm>";
                }
                int nreg = 0;
                for (ProdutosTbVal lprod1 : lprod) {

                    switch (lVeic.getCombust()) {
                        case ("G"):
                            if ("G".equals(lprod1.getProdutos().getTipoCombust())) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("A"):
                            if ("A".equals(lprod1.getProdutos().getTipoCombust())) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("F"):
                            if (("G".equals(lprod1.getProdutos().getTipoCombust())) || ("A".equals(lprod1.getProdutos().getTipoCombust()))) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("T"):
                            if (("G".equals(lprod1.getProdutos().getTipoCombust())) || ("A".equals(lprod1.getProdutos().getTipoCombust())) || ("N".equals(lprod1.getProdutos().getTipoCombust()))) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("N"):
                            if ("N".equals(lprod1.getProdutos().getTipoCombust())) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("D"):
                            if ("D".equals(lprod1.getProdutos().getTipoCombust())) {
                                xml += "<combustivel>";
                                xml += "<descricao>" + lprod1.getProdutos().getDescricao() + "</descricao>";
                                xml += "<un>" + lprod1.getProdutos().getUn() + "</un>";
                                xml += "<codigo>" + lprod1.getProdutos().getCodigo() + "</codigo>";
                                xml += "<precocusto>" + lprod1.getProdutos().getPrecoCusto() + "</precocusto>";
                                xml += "</combustivel>";
                                nreg++;
                            }
                            break;
                        case ("O"):
                            break;
                    }
                }
                sNreg = "<?xml version=\"1.0\"?> <nreg>" + nreg + "</nreg>";

            }
            String xml_ret = sNreg;
            xml_ret += xml;
            return xml_ret;

        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
