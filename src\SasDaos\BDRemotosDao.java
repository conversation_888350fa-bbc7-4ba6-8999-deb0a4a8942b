package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.BDRemotos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BDRemotosDao {

    public static List<BDRemotos> BuscaConexao(String Param, Persistencia persistencia) throws Exception {
        String sql = "select BancoDados, Empresa, HostName, BDNome, Usuario, Senha from BDRemotos where BancoDados=?";
        List<BDRemotos> listbd = new ArrayList();
        BDRemotos bdremotos;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Param);
            consult.select();
            while (consult.Proximo()) {
                bdremotos = new BDRemotos();
                bdremotos.setBancoDados(consult.getString("BancoDados"));
                bdremotos.setEmpresa(consult.getString("Empresa"));
                bdremotos.setHostName(consult.getString("HostName"));
                bdremotos.setBDNome(consult.getString("BDNome"));
                bdremotos.setUsuario(consult.getString("Usuario"));
                bdremotos.setSenha(consult.getString("Senha"));
                listbd.add(bdremotos);
            }
            consult.Close();
            return listbd;
        } catch (Exception e) {
            throw new Exception("BDRemotosDao.BuscaConexao - " + e.getMessage() + "\r\n"
                    + "select BancoDados, Empresa, HostName, BDNome, Usuario, Senha from BDRemotos where BancoDados=" + Param);
        }
    }
}
