/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Psthstqst;
import SasDaos.PsthstqstDao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class PstHstQsts {

    public static Boolean InserePstHstPst(String sMatr, String sSeq, Psthstqst oPsthstqst, String Senha, Persistencia persistencia) throws Exception {
        Boolean seq = false;

        PsthstqstDao oPsthstqstDao = new PsthstqstDao();

        String[] sQuestao = oPsthstqst.getCodQuestao().split(",");
        String[] sResposta = oPsthstqst.getResposta().split(",");

        //montando o não se aplica
        //String[] MontaObsNa = null;//sQuestao[sQuestao.length-1]+": "+sResposta[sResposta.length-1];
        String sDetalhe = oPsthstqst.getDetalhes();
        BigDecimal CodPessoa = oPsthstqst.getCodPessoa();
        String mtr = oPsthstqst.getMatr().toString();

        for (int i = 0; i < sQuestao.length; i++) {

            String rsp = FuncoesString.RecortaString(sResposta[i], 0, 2);

            if (!"-1".equals(rsp)) {
                String MontaObsNa = "null";
                oPsthstqstDao.gravaPsthstqst(MontaObsNa, sSeq, mtr,
                        Integer.parseInt(sQuestao[i]), Integer.parseInt(sResposta[i]), sDetalhe, CodPessoa, persistencia);
            } else {
                //String[] MontaObsNa =  sResposta[i].split("1");
                String MontaObsNa = FuncoesString.RecortaString(sResposta[i], 2, 40);
                oPsthstqstDao.gravaPsthstqst(MontaObsNa, sSeq, mtr,
                        Integer.parseInt(sQuestao[i]), Integer.parseInt(rsp), sDetalhe, CodPessoa, persistencia);
            }
        }

        seq = true;
//            }
//        }
        return seq;
    }

    public static Boolean InserePstHstPst(String sMatr, String sSeq, Psthstqst oPsthstqst, String Senha, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        Boolean seq = false;

        PsthstqstDao oPsthstqstDao = new PsthstqstDao();

        String[] sQuestao = oPsthstqst.getCodQuestao().split(",");
        String[] sResposta = oPsthstqst.getResposta().split(",");

        //montando o não se aplica
        //String[] MontaObsNa = null;//sQuestao[sQuestao.length-1]+": "+sResposta[sResposta.length-1];
        String sDetalhe = oPsthstqst.getDetalhes();
        BigDecimal CodPessoa = oPsthstqst.getCodPessoa();
        String mtr = oPsthstqst.getMatr().toString();

        for (int i = 0; i < sQuestao.length; i++) {

            String rsp = FuncoesString.RecortaString(sResposta[i], 0, 2);

            if (!"-1".equals(rsp)) {
                String MontaObsNa = "null";
                oPsthstqstDao.gravaPsthstqst(MontaObsNa, sSeq, mtr,
                        Integer.parseInt(sQuestao[i]), Integer.parseInt(sResposta[i]), sDetalhe, CodPessoa, dataAtual, horaAtual, persistencia);
            } else {
                //String[] MontaObsNa =  sResposta[i].split("1");
                String MontaObsNa = FuncoesString.RecortaString(sResposta[i], 2, 40);
                oPsthstqstDao.gravaPsthstqst(MontaObsNa, sSeq, mtr,
                        Integer.parseInt(sQuestao[i]), Integer.parseInt(rsp), sDetalhe, CodPessoa, dataAtual, horaAtual, persistencia);
            }
        }

        seq = true;
//            }
//        }
        return seq;
    }

    public static Boolean atualizaPstHstPst(String sCodPessoa, String sSeq, Psthstqst oPsthstqst, String Senha, Persistencia persistencia) throws Exception {
        Boolean seq = false;

        PsthstqstDao oPsthstqstDao = new PsthstqstDao();
        try {
            String[] sQuestao = oPsthstqst.getCodQuestao().split(",");
            String[] sResposta = oPsthstqst.getResposta().split(",");

            //montando o não se aplica
            //String MontaObsNa = null; //sQuestao[sQuestao.length - 1] + ": " + sResposta[sResposta.length - 1];
            String sDetalhe = oPsthstqst.getDetalhes();
            BigDecimal CodPessoa = oPsthstqst.getCodPessoa();
            String mtr = oPsthstqst.getMatr().toString();

            for (int i = 0; i < sQuestao.length; i++) {

                String rsp = FuncoesString.RecortaString(sResposta[i], 0, 2);

                if (!"-1".equals(rsp)) {
                    String MontaObsNa = "null";
                    oPsthstqstDao.updatePsthstqst(MontaObsNa, sSeq, mtr,
                            Integer.parseInt(sQuestao[i]), Integer.parseInt(sResposta[i]), sDetalhe, CodPessoa, persistencia);
                } else {
                    String[] MontaObsNa = sResposta[i].split("1");
                    oPsthstqstDao.updatePsthstqst(MontaObsNa[1], sSeq, mtr,
                            Integer.parseInt(sQuestao[i]), Integer.parseInt(rsp), sDetalhe, CodPessoa, persistencia);
                }
            }
            seq = true;
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar questionário - " + e.getMessage());
        }
        return seq;
    }

    public static Boolean atualizaPstHstPst(String sCodPessoa, String sSeq, Psthstqst oPsthstqst, String Senha, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        Boolean seq = false;

        PsthstqstDao oPsthstqstDao = new PsthstqstDao();
        try {
            String[] sQuestao = oPsthstqst.getCodQuestao().split(",");
            String[] sResposta = oPsthstqst.getResposta().split(",");

            //montando o não se aplica
            //String MontaObsNa = null; //sQuestao[sQuestao.length - 1] + ": " + sResposta[sResposta.length - 1];
            String sDetalhe = oPsthstqst.getDetalhes();
            BigDecimal CodPessoa = oPsthstqst.getCodPessoa();
            String mtr = oPsthstqst.getMatr().toString();

            for (int i = 0; i < sQuestao.length; i++) {

                String rsp = FuncoesString.RecortaString(sResposta[i], 0, 2);

                if (!"-1".equals(rsp)) {
                    String MontaObsNa = "null";
                    oPsthstqstDao.updatePsthstqst(MontaObsNa, sSeq, mtr,
                            Integer.parseInt(sQuestao[i]), Integer.parseInt(sResposta[i]), sDetalhe, CodPessoa, dataAtual, horaAtual, persistencia);
                } else {
                    String[] MontaObsNa = sResposta[i].split("1");
                    oPsthstqstDao.updatePsthstqst(MontaObsNa[1], sSeq, mtr,
                            Integer.parseInt(sQuestao[i]), Integer.parseInt(rsp), sDetalhe, CodPessoa, dataAtual, horaAtual, persistencia);
                }
            }
            seq = true;
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar questionário - " + e.getMessage());
        }
        return seq;
    }
}
