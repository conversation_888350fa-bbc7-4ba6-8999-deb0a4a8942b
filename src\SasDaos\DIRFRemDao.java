package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class DIRFRemDao {

    /**
     * Retorna a data de envio da DIRF
     *
     * @param AnoCompet - Ano da Competência da DIRF
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public LocalDate DataEnvio(String AnoCompet, Persistencia persistencia) throws Exception {
        LocalDate retorno = LocalDate.now();
        try {
            String sql = "select top 1 dtenvio from dirfrem where anocompet=? order by dt_alter,hr_alter desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(AnoCompet);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getLocalDate("dtenvio");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DIRFRemDao.DataEnvio - " + e.getMessage() + "\r\n"
                    + "select top 1 dtenvio from dirfrem where anocompet=" + AnoCompet + " order by dt_alter,hr_alter desc");
        }
    }
}
