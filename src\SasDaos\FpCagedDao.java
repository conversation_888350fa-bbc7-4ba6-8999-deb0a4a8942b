/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FpCaged;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FpCagedDao {

    public List<FpCaged> getCompetencia(BigDecimal bMatr, Persistencia persiste) throws Exception {
        List<FpCaged> lFpCaged;
        String sql = "select top 6 substring(compet,1,2)+'/'+substring(compet,3,4) Compet from fpcaged "
                + " where codfil= ? "
                + " group by codfil,compet "
                + " order by substring(compet,3,4) desc ,substring(compet,1,2) desc";

        try {
            Consulta consult = new Consulta(sql, persiste);
            consult.setBigDecimal(bMatr);
            consult.select();
            lFpCaged = new ArrayList<>();
            while (consult.Proximo()) {
                FpCaged oFpCaged = new FpCaged();

                oFpCaged.setCompet(consult.getString("Compet"));

                lFpCaged.add(oFpCaged);
            }
            consult.Close();
        } catch (Exception e) {
            lFpCaged = null;
            throw new Exception("Falha ao buscar filiais- " + e.getMessage());
        }
        return lFpCaged;
    }
}
