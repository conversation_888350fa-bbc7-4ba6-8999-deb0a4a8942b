/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasDaos.ClientesDao;

/**
 *
 * <AUTHOR>
 */
public class Clientes {

    public static void atualizaLtLg(String sCodPessoa, String Senha, String latitude, String longitude, String sequencia, String parada, Persistencia persistencia) throws Exception {

        ClientesDao oClientesDao = new ClientesDao();

        //valida login e senha do usuário
//        List<LoginRota> list_lrota;
//        try {
//            list_lrota = LoginDao.LoginRota(sCodPessoa, Senha, persistencia);
//
//        } catch (Exception e) {
//            throw new Exception("Falha no login - " + e.getMessage());
//        }
//        for (LoginRota list_lrota1 : list_lrota) {
//            if (!list_lrota1.getPessoa().getPWWeb().equals(<PERSON>ha)) {
//                String sSenha = "Senha inválida.";
//            } else {
        oClientesDao.updateLglt(latitude, longitude, sequencia, parada, persistencia);
//            }
//        }

    }
}
