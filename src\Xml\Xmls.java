package Xml;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import org.w3c.dom.Document;

/**
 *
 * <AUTHOR>
 */
public class Xmls {

    public static String getConteudo(Document doc, String campo) {
        try {
            return doc.getElementsByTagName(campo).item(0).getTextContent();
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * Metodo Muito usado no retorno de webservices Transforma um objeto Source
     * em String no formato XML
     *
     * @param result - source de entrada
     * @return - XML no formato de string
     * @throws Exception - devolve exception
     */
    public static String sourceToXMLString(Source result) throws Exception {
        String xmlResult = null;
        try {
            TransformerFactory factory = TransformerFactory.newInstance();
            Transformer transformer = factory.newTransformer();
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
            transformer.setOutputProperty(OutputKeys.METHOD, "xml");
            OutputStream out = new ByteArrayOutputStream();
            StreamResult streamResult = new StreamResult();
            streamResult.setOutputStream(out);
            transformer.transform(result, streamResult);
            xmlResult = streamResult.getOutputStream().toString();
        } catch (TransformerException e) {
            throw new Exception("Falha ao transformar em XML- " + e.getMessage());
        }
        return xmlResult;
    }

    /**
     * Montador de tags xmls
     *
     * @param tag - titutlo da tag
     * @param campo - conteúdo da tag
     * @return
     */
    public static String tag(String tag, String campo) {
        String retorno;
        try {
            if (("null").equals(campo)) {
                throw new Exception("");
            }
            retorno = "<" + tag + ">" + campo + "</" + tag + ">";
        } catch (Exception e) {
            retorno = "<" + tag + "></" + tag + ">";
        }
        return retorno;
    }

    /**
     * Montador de tags xmls
     *
     * @param tag - titutlo da tag
     * @param campo - conteúdo da tag
     * @return
     */
    public static String tag(String tag, Object campo) {
        String retorno;
        try {
            if (campo == null) {
                throw new Exception("");
            }
            retorno = "<" + tag + ">" + String.valueOf(campo) + "</" + tag + ">";
        } catch (Exception e) {
            retorno = "<" + tag + "></" + tag + ">";
        }
        return retorno;
    }
}
