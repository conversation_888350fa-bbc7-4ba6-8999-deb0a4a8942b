package SasLibrary;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeans.Funcion;
import SasBeansCompostas.FPMensalFPPeriodosFuncion;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FolhaPagamento {

    /**
     * Traz os dados de rendimento do funcionário
     *
     * @param Matr - Matricula do funcionário
     * @param Dtinicial - Data do início do período
     * @param DtFinal - Data do final do período
     * @param TipoFP - Tipo de folha
     * @param persistencia - conexão ao banco
     * @return
     * @throws Exception
     */
    public List<FPMensalFPPeriodosFuncion> getRendimentos(String Matr, String Dtinicial, String DtFinal, String TipoFP,
            Persistencia persistencia) throws Exception {
        List<FPMensalFPPeriodosFuncion> retorno = new ArrayList();
        try {
            String sql = "Select FPMensal.Matr, Funcion.Nome, Funcion.CPF, Funcion.CodFil, Sum(BaseRT) 'Rendtrib', Sum(INSS) 'ContribOfi', "
                    + " Sum(FPMensal.IRRF) IRRF"
                    + " from FPMensal"
                    + " left join FPPeriodos on FPPeriodos.CodMovFP = FPMensal.CodMovFP"
                    + " left join Funcion on Funcion.Matr = FPMensal.Matr"
                    + " where FPPeriodos.DtInicio >= ?"
                    + " and FPPeriodos.DtFinal  <= ?"
                    + " and FPMensal.TipoFP = ?"
                    + " and FPMensal.Matr = ?"
                    + " group by FPMensal.Matr, Funcion.Nome, Funcion.CPF, Funcion.CodFil";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Dtinicial);
            consult.setString(DtFinal);
            consult.setString(TipoFP);
            consult.setString(Matr);
            consult.select();
            while (consult.Proximo()) {
                FPMensal fpmensal = new FPMensal();
                FPPeriodos fpperiodos = new FPPeriodos();
                Funcion funcion = new Funcion();
                FPMensalFPPeriodosFuncion ret = new FPMensalFPPeriodosFuncion();
                fpmensal.setMatr(consult.getString("Matr"));
                fpmensal.setBaseRT(consult.getString("RendTrib"));
                fpmensal.setINSS(consult.getString("ContribOfi"));
                fpmensal.setIRRF(consult.getString("IRRF"));
                funcion.setNome(consult.getString("Nome"));
                funcion.setMatr(consult.getString("Matr"));
                funcion.setCPF(consult.getString("CPF"));
                funcion.setCodFil(consult.getString("CodFil"));
                ret.setFpmensal(fpmensal);
                ret.setFuncion(funcion);
                ret.setFpperiodos(fpperiodos);
                retorno.add(ret);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar rendimentos - " + e.getMessage());
        }
    }

    public String ResponsavelInformacaoDIRF(String Codfil, Persistencia persistencia) throws Exception {
        try {
            String retorno = "";
            String sql = "select nome from dirf "
                    + " left join pessoa on pessoa.codigo = dirf.codpessoarin"
                    + " where codfil=?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Codfil);
            consult.select();
            while (consult.Proximo()) {
                retorno = consult.getString("nome");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao Buscar dados do responsável pela informação - " + e.getMessage());
        }
    }
}
