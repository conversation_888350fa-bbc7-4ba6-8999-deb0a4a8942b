/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ContratosDoctos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContratosDoctosDao {

    public void inserirDocumento(ContratosDoctos contratosDoctos, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO ContratosDoctos (Contrato, CodFil, DtArquivo, Ordem, Descricao, \n"
                    + " Dt_Inicio, Dt_Termino, Comissao, Operador, Dt_alter, Hr_Alter) \n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratosDoctos.getContrato());
            consulta.setString(contratosDoctos.getCodFil());
            consulta.setString(contratosDoctos.getDtArquivo());
            consulta.setInt(contratosDoctos.getOrdem());
            consulta.setString(contratosDoctos.getDescricao());
            consulta.setString(contratosDoctos.getDt_Inicio());
            consulta.setString(contratosDoctos.getDt_Termino());
            consulta.setString(contratosDoctos.getComissao());
            consulta.setString(contratosDoctos.getOperador());
            consulta.setString(contratosDoctos.getDt_alter());
            consulta.setString(contratosDoctos.getHr_Alter());
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDoctosDao.inserirDocumento - " + e.getMessage() + "\r\n"
                    + " INSERT INTO ContratosDoctos (Contrato, CodFil, DtArquivo, Ordem, Descricao, \n"
                    + " Dt_Inicio, Dt_Termino, Comissao, Operador, Dt_alter, Hr_Alter) \n"
                    + " VALUES (" + contratosDoctos.getContrato() + ", " + contratosDoctos.getCodFil() + ", " + contratosDoctos.getDtArquivo() + ", "
                    + contratosDoctos.getOrdem() + ", " + contratosDoctos.getDescricao() + ", " + contratosDoctos.getDt_Inicio() + ", "
                    + contratosDoctos.getDt_Termino() + ", " + contratosDoctos.getComissao() + ", " + contratosDoctos.getOperador() + ", "
                    + contratosDoctos.getDt_alter() + ", " + contratosDoctos.getHr_Alter() + ")");
        }
    }

    public int maxOrdem(String contrato, String codFil, String dtArquivo, Persistencia persistencia) throws Exception {
        try {
            int retorno = 1;
            String sql = " SELECT ISNULL(MAX(Ordem), 0) + 1 Ordem FROM ContratosDoctos "
                    + " WHERE Contrato = ? AND CodFil = ? AND DtArquivo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato);
            consulta.setString(codFil);
            consulta.setString(dtArquivo);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getInt("Ordem");
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContratosDoctosDao.maxOrdem - " + e.getMessage() + "\r\n"
                    + " SELECT ISNULL(MAX(Ordem), 0) Ordem + 1 FROM ContratosDoctos "
                    + " WHERE Contrato = " + contrato + " AND CodFil = " + codFil + " AND DtArquivo = " + dtArquivo);
        }
    }

    /**
     * Lista os documento de um contrato
     *
     * @param contrato
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ContratosDoctos> listarDocumentos(String contrato, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<ContratosDoctos> retorno = new ArrayList<>();
            String sql = " SELECT *, CONVERT(VarChar, DtArquivo, 112) DtArquivoF \n"
                    + " FROM ContratosDoctos WHERE Contrato = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato);
            consulta.setString(codFil);
            consulta.select();
            ContratosDoctos contratosDoctos;
            while (consulta.Proximo()) {
                contratosDoctos = new ContratosDoctos();
                contratosDoctos.setContrato(consulta.getString("Contrato"));
                contratosDoctos.setCodFil(consulta.getString("CodFil"));
                contratosDoctos.setDtArquivo(consulta.getString("DtArquivoF"));
                contratosDoctos.setOrdem(consulta.getInt("Ordem"));
                contratosDoctos.setDescricao(consulta.getString("Descricao"));
                contratosDoctos.setDt_Inicio(consulta.getString("Dt_Inicio"));
                contratosDoctos.setDt_Termino(consulta.getString("Dt_Termino"));
                contratosDoctos.setComissao(consulta.getString("Comissao"));
                contratosDoctos.setOperador(consulta.getString("Operador"));
                contratosDoctos.setDt_alter(consulta.getString("Dt_alter"));
                contratosDoctos.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(contratosDoctos);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContratosDoctosDao.listarDocumentos - " + e.getMessage() + "\r\n"
                    + "SELECT *, CONVERT(VarChar, DtArquivo, 112) DtArquivoF \n"
                    + " FROM ContratosDoctos WHERE Contrato = " + contrato + " AND CodFil = " + codFil);
        }
    }

    public void excluirDocumento(ContratosDoctos contratosDoctos, Persistencia persistencia) throws Exception {
        try {
            String sql = " DELETE "
                    + " FROM ContratosDoctos "
                    + " WHERE Contrato = ? AND CodFil = ? AND DtArquivo = ? AND Ordem = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratosDoctos.getContrato());
            consulta.setString(contratosDoctos.getCodFil());
            consulta.setString(contratosDoctos.getDtArquivo());
            consulta.setInt(contratosDoctos.getOrdem());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDoctosDao.excluirDocumento - " + e.getMessage() + "\r\n"
                    + " DELETE "
                    + " FROM ContratosDoctos "
                    + " WHERE Contrato = " + contratosDoctos.getContrato() + " AND CodFil = " + contratosDoctos.getCodFil()
                    + " AND DtArquivo = " + contratosDoctos.getDtArquivo() + " AND Ordem = " + contratosDoctos.getOrdem());
        }
    }
}
