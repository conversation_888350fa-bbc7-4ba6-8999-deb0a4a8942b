/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.TbVal;
import SasDaos.TbValDao;
import java.util.List;

/**
 *
 * <AUTHOR> França 25/09/2015
 */
public class Questionarios {

    public static String carregaQuestionario(Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<TbVal> list_TbVal;
            TbValDao oTbValDao = new TbValDao();
            try {
                list_TbVal = oTbValDao.getTbVal312(persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar Questionário - " + e.getMessage());
            }
            if (list_TbVal.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>Questionario_2</resp>";//sem questionario
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_TbVal.size() + "</nreg>";

            for (int i = 0; i < list_TbVal.size(); i++) {
                xml += "<questionario>";
                xml += "<codigo>" + list_TbVal.get(i).getCodigo() + "</codigo>";
                xml += "<descricao>" + list_TbVal.get(i).getDescricao() + "</descricao>";
                xml += "</questionario>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd 
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public static String carregaMotivos(Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<TbVal> list_TbVal;
            TbValDao oTbValDao = new TbValDao();
            try {
                list_TbVal = oTbValDao.getTbVal311(persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar Motivos - " + e.getMessage());
            }
            if (list_TbVal.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>Motivos_2</resp>";//sem motivos
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_TbVal.size() + "</nreg>";

            for (int i = 0; i < list_TbVal.size(); i++) {
                xml += "<motivos>";
                xml += "<codigo>" + list_TbVal.get(i).getCodigo() + "</codigo>";
                xml += "<descricao>" + list_TbVal.get(i).getDescricao() + "</descricao>";
                xml += "</motivos>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd 
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
