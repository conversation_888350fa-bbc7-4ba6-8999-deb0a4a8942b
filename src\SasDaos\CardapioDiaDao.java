/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cardapio;
import SasBeans.CardapioDia;
import SasBeansCompostas.CardapioDiaDietaCardapio;
import SasBeansCompostas.Dieta;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CardapioDiaDao {

    private final String paginacaoCardapioDia = "SELECT * FROM (\n"
            + "SELECT ROW_NUMBER() OVER ( ORDER BY Data, Sequencia ) AS RowNum,\n"
            + "Sequencia,\n"
            + "CodCardapio,\n"
            + "Data,\n"
            + "CodDieta,\n"
            + "Especificacao,\n"
            + "Operador,\n"
            + "Dt_alter,\n"
            + "Hr_Alter\n"
            + "FROM CardapioDia\n";

    private final String paginacaoCardapioDiaComDietaECardapio = "SELECT * FROM (\n"
            + "SELECT ROW_NUMBER() OVER ( ORDER BY Data, CardapioDia.Sequencia ) AS RowNum,\n"
            + "    CardapioDia.Sequencia,\n"
            + "    CardapioDia.CodCardapio,\n"
            + "    CardapioDia.Data,\n"
            + "    CardapioDia.CodDieta,\n"
            + "    CardapioDia.Especificacao,\n"
            + "    CardapioDia.Operador,\n"
            + "    CardapioDia.Dt_alter,\n"
            + "    CardapioDia.Hr_Alter,\n"
            + "    CardapioDia.CodCli,\n"
            + "    Clientes.Nred,\n"
            + "    CardapioDia.Periodo,\n"
            + "    Cardapio.Codigo AS CardapioCodigo,\n"
            + "    Cardapio.Descricao AS CardapioDescricao,\n"
            + "    Cardapio.Especificacao AS CardapioEspecificacao,\n"
            + "    Cardapio.Operador AS CardapioOperador,\n"
            + "    Cardapio.Dt_alter AS CardapioDt_alter,\n"
            + "    Cardapio.Hr_Alter AS CardapioHr_Alter,\n"
            + "    Dieta.Sequencia AS DietaSequencia,\n"
            + "    Dieta.Descricao AS DietaDescricao,\n"
            + "    Dieta.Especificacao AS DietaEspecificacao,\n"
            + "    Dieta.Operador AS DietaOperador,\n"
            + "    Dieta.Dt_alter AS DietaDt_alter,\n"
            + "    Dieta.Hr_Alter AS DietaHr_Alter\n"
            + "FROM\n"
            + "    CardapioDia\n"
            + "    LEFT JOIN Cardapio ON Cardapio.Codigo = CardapioDia.CodCardapio\n"
            + "    LEFT JOIN Dieta ON Dieta.Sequencia = CardapioDia.CodDieta\n"
            + "    LEFT JOIN Clientes ON CardapioDia.CodCli = Clientes.codigo\n";

    private final String paginacaoFinal = ") AS RowConstrainedResult\n"
            + "WHERE RowNum >= ? "
            + "AND RowNum < ?\n"
            + "ORDER BY RowNum\n";

    private String whereClauseListarPaginada(Map<String, String> filters) {
        String sql = "";
        boolean first = true;

        for (Map.Entry<String, String> entrada : filters.entrySet()) {
            if (!entrada.getValue().equals("")) {
                String keyword = first ? "WHERE " : "AND ";
                sql += keyword + entrada.getKey() + " \n";
                first = false;
            }
        }

        return sql;
    }

    private List<CardapioDia> selectCardapioDia(Consulta consulta) throws Exception {
        List<CardapioDia> lista = new ArrayList<>();
        try {
            while (consulta.Proximo()) {
                CardapioDia cardapioDia = new CardapioDia();

                cardapioDia.setSequencia(consulta.getString("Sequencia"));
                cardapioDia.setCodCardapio(consulta.getString("CodCardapio"));
                cardapioDia.setData(consulta.getString("Data"));
                cardapioDia.setCodDieta(consulta.getString("CodDieta"));
                cardapioDia.setEspecificacao(consulta.getString("Especificacao"));
                cardapioDia.setOperador(consulta.getString("Operador"));
                cardapioDia.setDt_alter(consulta.getString("Dt_alter"));
                cardapioDia.setHr_Alter(consulta.getString("Hr_Alter"));

                lista.add(cardapioDia);
            }
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public List<Cardapio> listaCardapiosCadastrados(Persistencia persistencia) throws Exception {
        String sql = "";
        try {

            sql = "SELECT * FROM Cardapio";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            List<Cardapio> retorno = new ArrayList<>();
            Cardapio cardapio = new Cardapio();

            while (consulta.Proximo()) {
                cardapio = new Cardapio();

                cardapio.setCodigo(consulta.getString("Codigo"));
                cardapio.setDescricao(consulta.getString("Descricao"));
                cardapio.setDt_alter(consulta.getString("Dt_alter"));
                cardapio.setEspecificacao(consulta.getString("Especificacao"));
                cardapio.setHr_Alter(consulta.getString("Hr_Alter"));
                cardapio.setOperador(consulta.getString("Operador"));

                retorno.add(cardapio);
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.listaCardapiosCadastrados - " + e.getMessage() + '\n' + sql);
        }
    }

    public List<Cardapio> listaCardapiosCadastradosPaginado(int primeiro, int ultimo, Persistencia persistencia, String where) throws Exception {
        String sql = "";
        try {

            sql = "SELECT * FROM(SELECT ROW_NUMBER() OVER ( ORDER BY Descricao) AS RowNum, * FROM Cardapio";
            if (!where.equals("")) {
                sql += where;
            }
            sql += " ) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + ultimo);
            consulta.select();

            List<Cardapio> retorno = new ArrayList<>();
            Cardapio cardapio = new Cardapio();

            while (consulta.Proximo()) {
                cardapio = new Cardapio();

                cardapio.setCodigo(consulta.getString("Codigo"));
                cardapio.setDescricao(consulta.getString("Descricao"));
                cardapio.setDt_alter(consulta.getString("Dt_alter"));
                cardapio.setEspecificacao(consulta.getString("Especificacao"));
                cardapio.setHr_Alter(consulta.getString("Hr_Alter"));
                cardapio.setOperador(consulta.getString("Operador"));

                retorno.add(cardapio);
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.listaCardapiosCadastrados - " + e.getMessage() + '\n' + sql);
        }
    }

    public void salvarCadastroCardapio(Cardapio cardapio, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            if (cardapio.getCodigo().equals("0")) {
                // INSERT
                sql = "INSERT INTO Cardapio (Codigo, Descricao, Especificacao, Operador, Dt_Alter, Hr_Alter) VALUES("
                        + " (SELECT ISNULL((MAX(Codigo) + 1),1) FROM Cardapio),"
                        + " ?, ?, ?, ?, ?);";
            } else {
                // UPDATE
                sql = "UPDATE Cardapio "
                        + " SET Descricao = ?,"
                        + " Especificacao = ?,"
                        + " Operador      = ?,"
                        + " Dt_Alter      = ?,"
                        + " Hr_Alter      = ?"
                        + " WHERE Codigo  = ?";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cardapio.getDescricao());
            consulta.setString(cardapio.getEspecificacao());
            consulta.setString(cardapio.getOperador());
            consulta.setString(cardapio.getDt_alter());
            consulta.setString(cardapio.getHr_Alter());

            if (!cardapio.getCodigo().equals("0")) {
                consulta.setBigDecimal(cardapio.getCodigo());
                consulta.update();
            } else {
                consulta.insert();
            }

            consulta.close();
        } catch (Exception e) {
            throw new Exception("CardapiosDao.salvarCadastroCardapio - " + e.getMessage() + "\n" + sql);
        }
    }

    private List<CardapioDiaDietaCardapio> selectCardapioDiaDietaCardapio(Consulta consulta) throws Exception {
        List<CardapioDiaDietaCardapio> lista = new ArrayList<>();
        try {
            while (consulta.Proximo()) {
                CardapioDiaDietaCardapio cardapioDia = new CardapioDiaDietaCardapio();
                Dieta dieta = new Dieta();
                Cardapio cardapio = new Cardapio();

                cardapioDia.setSequencia(consulta.getString("Sequencia"));
                cardapioDia.setCodCardapio(consulta.getString("CodCardapio"));
                cardapioDia.setData(consulta.getString("Data"));
                cardapioDia.setCodDieta(consulta.getString("CodDieta"));
                cardapioDia.setEspecificacao(consulta.getString("Especificacao"));
                cardapioDia.setOperador(consulta.getString("Operador"));
                cardapioDia.setDt_alter(consulta.getString("Dt_alter"));
                cardapioDia.setHr_Alter(consulta.getString("Hr_Alter"));
                cardapioDia.getClientes().setCodCli(consulta.getString("CodCli"));
                cardapioDia.getClientes().setNRed(consulta.getString("Nred"));
                cardapioDia.setPeriodo(consulta.getString("Periodo"));

                dieta.setDescricao(consulta.getString("DietaDescricao"));
                dieta.setDt_alter(consulta.getString("DietaDt_alter"));
                dieta.setEspecificacao(consulta.getString("DietaEspecificacao"));
                dieta.setHr_Alter(consulta.getString("DietaHr_Alter"));
                dieta.setOperador(consulta.getString("DietaOperador"));
                dieta.setSequencia(consulta.getString("DietaSequencia"));

                cardapio.setCodigo(consulta.getString("CardapioCodigo"));
                cardapio.setDescricao(consulta.getString("CardapioDescricao"));
                cardapio.setEspecificacao(consulta.getString("CardapioEspecificacao"));
                cardapio.setOperador(consulta.getString("CardapioOperador"));
                cardapio.setDt_alter(consulta.getString("CardapioDt_alter"));
                cardapio.setHr_Alter(consulta.getString("CardapioHr_Alter"));

                cardapioDia.setCardapio(cardapio);
                cardapioDia.setDieta(dieta);

                lista.add(cardapioDia);
            }
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    private int getTotal(Consulta consulta) throws Exception {
        try {
            while (consulta.Proximo()) {
                return consulta.getInt("total");
            }
            return 0;
        } catch (Exception e) {
            throw e;
        }
    }

    public List<CardapioDia> listarCardapiosDiaPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = paginacaoCardapioDia
                    + whereClauseListarPaginada(filters)
                    + paginacaoFinal;

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);
            consulta.select();
            List<CardapioDia> cardapiosDia = selectCardapioDia(consulta);
            consulta.Close();
            return cardapiosDia;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.listarCardapiosDiaPaginada - " + e.getMessage());
        }
    }

    public boolean cadastrarCardapioDia(CardapioDia cardapioDia, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO CardapioDia\n"
                    + "(Sequencia, CodCardapio, Data, CodDieta, Especificacao, Operador, Dt_alter, Hr_Alter, CodCli, Periodo)\n"
                    + "SELECT COALESCE (MAX(Sequencia) + 1.0, 1.0), ?, ?, ?, ?, ?, ?, ?, ?, ?\n"
                    + "FROM CardapioDia;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cardapioDia.getCodCardapio());
            consulta.setString(cardapioDia.getData());
            consulta.setString(cardapioDia.getCodDieta());
            consulta.setString(cardapioDia.getEspecificacao());
            consulta.setString(cardapioDia.getOperador());
            consulta.setString(cardapioDia.getDt_alter());
            consulta.setString(cardapioDia.getHr_Alter());
            consulta.setString(cardapioDia.getCodCli());
            consulta.setString(cardapioDia.getPeriodo());
            consulta.insert();
            consulta.Close();

            return true;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.cadastrarCardapioDia - " + e.getMessage());
        }
    }

    public void excluirCardapio(String Codigo, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DELETE FROM Cardapio\n"
                    + " WHERE Codigo = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Codigo);
            consulta.delete();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("CardapiosDao.excluirCardapio - " + e.getMessage());
        }
    }
    
    public void excluirCardapioDia(String Sequencia, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DELETE FROM CardapioDia\n"
                    + " WHERE Sequencia = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Sequencia);
            consulta.delete();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("CardapiosDao.excluirCardapioDia - " + e.getMessage());
        }
    }

    public boolean editarCardapioDia(CardapioDia cardapioDia, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE CardapioDia SET\n"
                    + "CodCardapio = ?,\n"
                    + "Data = ?,\n"
                    + "CodDieta = ?,\n"
                    + "Especificacao = ?,\n"
                    + "Operador = ?,\n"
                    + "Dt_alter = ?,\n"
                    + "Hr_Alter = ?\n,"
                    + "CodCli = ?\n,"
                    + "Periodo = ?\n"
                    + "WHERE Sequencia = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cardapioDia.getCodCardapio());
            consulta.setString(cardapioDia.getData());
            consulta.setString(cardapioDia.getCodDieta());
            consulta.setString(cardapioDia.getEspecificacao());
            consulta.setString(cardapioDia.getOperador());
            consulta.setString(cardapioDia.getDt_alter());
            consulta.setString(cardapioDia.getHr_Alter());
            consulta.setString(cardapioDia.getCodCli());
            consulta.setString(cardapioDia.getPeriodo());
            consulta.setString(cardapioDia.getSequencia());
            consulta.update();
            consulta.Close();

            return true;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.editarCardapioDia - " + e.getMessage());
        }
    }

    public int contarCardapiosDiaTotal(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) AS total \n"
                    + "FROM CardapioDia \n"
                    + whereClauseListarPaginada(filters);

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int total = getTotal(consulta);
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.contarCardapiosDiaTotal - " + e.getMessage());
        }
    }

    public List<CardapioDiaDietaCardapio> listarCardapiosDiaComDietaECardapioPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = paginacaoCardapioDiaComDietaECardapio
                    + whereClauseListarPaginada(filters)
                    + paginacaoFinal;

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);
            consulta.select();
            List<CardapioDiaDietaCardapio> cardapiosDia = selectCardapioDiaDietaCardapio(consulta);
            consulta.Close();
            return cardapiosDia;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.listarCardapiosDiaComDietaECardapioPaginada - " + e.getMessage());
        }
    }

    public int contarCardapiosDiaComDietaECardapioTotal(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) AS total \n"
                    + "FROM CardapioDia \n"
                    + "LEFT JOIN Cardapio ON Cardapio.Codigo = CardapioDia.CodCardapio \n"
                    + "LEFT JOIN Dieta ON Dieta.Sequencia = CardapioDia.CodDieta \n"
                    + whereClauseListarPaginada(filters);

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int total = getTotal(consulta);
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.contarCardapiosDiaComDietaECardapioTotal - " + e.getMessage());
        }
    }
}
