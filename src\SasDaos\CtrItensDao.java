/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CtrItens;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CtrItensDao {

    private final String sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

    /**
     * Obtem o codigo máximo do posto
     *
     * @param codFil codigo da filial
     * @param contrato contrato da que será buscado
     * @param persistencia Conexão com o banco de dados
     * @param periodo o perído do posto se é A => diurno e B => noturno
     * @return tipo do posto
     * @throws Exception
     */
    public String obterTipoPosto(String codFil, String contrato, String periodo, Persistencia persistencia) throws Exception {
        String tipoPosto = "";
        try {
            String sql = "SELECT max(TipoPosto) maximo FROM ctritens WHERE CodFil = ?"
                    + " AND Contrato LIKE ? AND TipoPosto LIKE ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(contrato + "%");
            consulta.setString(periodo + "%");
            consulta.select();

            while (consulta.Proximo()) {
                tipoPosto = consulta.getString("maximo");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.obterTipoPosto  - " + e.getMessage() + "\r\n"
                    + "SELECT max(TipoPosto) maximo FROM ctritens WHERE CodFil = " + codFil
                    + " AND Contrato LIKE " + contrato + " AND TipoPosto LIKE " + periodo);
        }
        return tipoPosto;
    }

    /**
     * Busca postos e descricao de postos referentes a um contrato de uma filial
     *
     * @param CodFil - codigo da filial
     * @param contrato - numero do contrato
     * @param persistencia - conexão com o banco
     * @return lista de postos e tipos
     * @throws Exception
     */
    public List<CtrItens> BuscarContratos(BigDecimal CodFil, String contrato, String query,
            Persistencia persistencia) throws Exception {
        try {
            List<CtrItens> retorno = new ArrayList<>();
            String sql = "select top 20 tipoposto, descricao, contrato "
                    + " from ctritens "
                    + " where codfil = ? "
                    + " and contrato = ? and "
                    + " (tipoposto like ? or descricao like ? or contrato like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.setString(contrato);
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            CtrItens ctritens;
            while (consult.Proximo()) {
                ctritens = new CtrItens();
                ctritens.setDescricao(consult.getString("descricao"));
                ctritens.setTipoPosto(consult.getString("tipoposto"));
                ctritens.setContrato(consult.getString("contrato"));
                retorno.add(ctritens);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrItensDao.BuscarContratos  - " + e.getMessage() + "\r\n"
                    + "select top 20 tipoposto, descricao, contrato "
                    + " from ctritens "
                    + " where codfil = " + CodFil
                    + " and contrato = " + contrato + " and "
                    + " (tipoposto like " + query + " or descricao like " + query + " or contrato like " + query + ")");
        }
    }

    /**
     * Busca todos os contratos, postos e descricao de postos
     *
     * @param CodFil - codigo da filial
     * @param query
     * @param persistencia - conexão com o banco
     * @return lista de postos e tipos
     * @throws Exception
     */
    public List<CtrItens> BuscarTodosContratos(BigDecimal CodFil, String query,
            Persistencia persistencia) throws Exception {
        try {
            List<CtrItens> retorno = new ArrayList<>();
            String sql = "select tipoposto, descricao, contrato "
                    + " from ctritens "
                    + " where codfil = ? and "
                    + " (tipoposto like ? or descricao like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            CtrItens ctritens;
            while (consult.Proximo()) {
                ctritens = new CtrItens();
                ctritens.setDescricao(consult.getString("descricao"));
                ctritens.setTipoPosto(consult.getString("tipoposto"));
                ctritens.setContrato(consult.getString("contrato"));
                retorno.add(ctritens);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrItensDao.BuscarTodosContratos  - " + e.getMessage() + "\r\n"
                    + "select tipoposto, descricao, contrato "
                    + " from ctritens "
                    + " where codfil = " + CodFil + " and "
                    + " (tipoposto like " + query + " or descricao like " + query + ")");
        }
    }

    /**
     * Verifica a existencia do contrato
     *
     * @param contrato Numero do contrato
     * @param codFil Código do filial
     * @param tipoPosto tipo do posto se é A ou B
     * @param persistencia COnexão com o banco de dados
     * @return e existe ou nao o contrato
     * @throws Exception
     */
    public boolean existeContratoItens(String contrato, String codFil, String tipoPosto, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM ctritens WHERE contrato = ? "
                    + "AND codfil = ? AND tipoposto = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato);
            consulta.setString(codFil);
            consulta.setString(tipoPosto);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.existeContratoItens - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM ctritens WHERE contrato = " + contrato
                    + "AND codfil = " + codFil + " AND tipoposto = " + tipoPosto);
        }
        return existe;
    }

    /**
     * Salva registros do itens do contrato
     *
     * @param contratoItens objeto contendo item do contrato
     * @param persistencia conexao com o banco de dados
     * @throws Exception
     */
    public void salvarCtrItens(CtrItens contratoItens, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ctritens (contrato, codfil, tipoposto, "
                    + "descricao, operador, hr_alter, tipocalc, CodTipo, dt_alter) "
                    + "VALUES(?,?,?,?,?,?,?,?,CONVERT(DATE, getDate()))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoItens.getContrato());
            consulta.setBigDecimal(contratoItens.getCodFil());
            consulta.setString(contratoItens.getTipoPosto());
            consulta.setString(contratoItens.getDescricao());
            consulta.setString(contratoItens.getOperador());
            consulta.setString(contratoItens.getHr_Alter());
            consulta.setString(contratoItens.getTipoCalc());
            consulta.setBigDecimal(contratoItens.getCodTipo());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.salvarCtrItens  - " + e.getMessage() + "\r\n"
                    + "INSERT INTO ctritens (contrato, codfil, tipoposto, "
                    + "descricao, operador, hr_alter, tipocalc, CodTipo, dt_alter) "
                    + "VALUES(" + contratoItens.getContrato() + "," + contratoItens.getCodFil() + "," + contratoItens.getTipoPosto() + ","
                    + contratoItens.getDescricao() + "," + contratoItens.getOperador() + "," + contratoItens.getHr_Alter() + ","
                    + contratoItens.getTipoCalc() + "," + contratoItens.getCodTipo() + ",CONVERT(DATE, getDate()))");

        }
    }

    public void salvarCtrItens(CtrItens contratoItens, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ctritens (contrato, codfil, tipoposto, "
                    + "descricao, operador, hr_alter, tipocalc, CodTipo, dt_alter) "
                    + "VALUES(?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoItens.getContrato());
            consulta.setBigDecimal(contratoItens.getCodFil());
            consulta.setString(contratoItens.getTipoPosto());
            consulta.setString(contratoItens.getDescricao());
            consulta.setString(contratoItens.getOperador());
            consulta.setString(contratoItens.getHr_Alter());
            consulta.setString(contratoItens.getTipoCalc());
            consulta.setBigDecimal(contratoItens.getCodTipo());
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.salvarCtrItens - " + e.getMessage() + "\r\n"
                    + "INSERT INTO ctritens (contrato, codfil, tipoposto, "
                    + "descricao, operador, hr_alter, tipocalc, CodTipo, dt_alter) "
                    + "VALUES(" + contratoItens.getContrato() + "," + contratoItens.getCodFil() + "," + contratoItens.getTipoPosto() + ","
                    + contratoItens.getDescricao() + "," + contratoItens.getOperador() + "," + contratoItens.getHr_Alter() + ","
                    + contratoItens.getTipoCalc() + "," + contratoItens.getCodTipo() + "," + dataAtual + ")");
        }
    }

    /**
     * Lista os itens de faturamento de um contrato
     *
     * @param contrato
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrItens> listarItensFaturamento(String contrato, String codFil, Persistencia persistencia) throws Exception {
        String sql = " SELECT * FROM CtrItens \n"
                + " WHERE Contrato = ? AND CodFil = ? ";

        try {
            List<CtrItens> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato);
            consulta.setString(codFil);
            consulta.select();
            CtrItens ctrItens;

            while (consulta.Proximo()) {
                ctrItens = new CtrItens();

                ctrItens.setContrato(consulta.getString("Contrato"));
                ctrItens.setCodFil(consulta.getString("CodFil"));
                ctrItens.setTipoPosto(consulta.getString("TipoPosto"));
                ctrItens.setDescricao(consulta.getString("Descricao"));
                ctrItens.setHoras(consulta.getString("Horas"));
                ctrItens.setValorPosto(consulta.getString("ValorPosto"));
                ctrItens.setHEDiurna(consulta.getString("HEDiurna"));
                ctrItens.setHENoturna(consulta.getString("HENoturna"));
                ctrItens.setHEDiurna2(consulta.getString("HEDiurna2"));
                ctrItens.setHENoturna2(consulta.getString("HENoturna2"));
                ctrItens.setReforco(consulta.getString("Reforco"));
                ctrItens.setCodTipo(consulta.getString("CodTipo"));
                ctrItens.setOperador(consulta.getString("Operador"));
                ctrItens.setDt_Alter(consulta.getString("Dt_Alter"));
                ctrItens.setHr_Alter(consulta.getString("Hr_Alter"));
                ctrItens.setCHSeman(consulta.getString("CHSeman"));
                ctrItens.setCHMensal(consulta.getString("CHMensal"));
                ctrItens.setCHFuncion(consulta.getString("CHFuncion"));
                ctrItens.setQtdeFunc(consulta.getString("QtdeFunc"));
                ctrItens.setSalario(consulta.getString("Salario"));
                ctrItens.setHsNormais(consulta.getString("HsNormais"));
                ctrItens.setHsExtras(consulta.getString("HsExtras"));
                ctrItens.setIndiceHE(consulta.getString("IndiceHE"));
                ctrItens.setTipoCalc(consulta.getString("TipoCalc"));
                ctrItens.setValorRot(consulta.getString("ValorRot"));
                ctrItens.setFranquiaRot(consulta.getString("FranquiaRot"));
                ctrItens.setValorEve(consulta.getString("ValorEve"));
                ctrItens.setFranquiaEve(consulta.getString("FranquiaEve"));
                ctrItens.setValorEsp(consulta.getString("ValorEsp"));
                ctrItens.setFranquiaEsp(consulta.getString("FranquiaEsp"));
                ctrItens.setValorAst(consulta.getString("ValorAst"));
                ctrItens.setFranquiaAst(consulta.getString("FranquiaAst"));
                ctrItens.setCargo(consulta.getString("Cargo"));
                ctrItens.setCodCargo(consulta.getString("CodCargo"));
                ctrItens.setQtdePreench(consulta.getString("QtdePreench"));
                ctrItens.setInibirReajuste(consulta.getInt("InibirReajuste"));
                retorno.add(ctrItens);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrItensDao.listarItensFaturamento - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    /**
     * Lista os itens de faturamento de um contrato
     *
     * @param contrato
     * @param OS
     * @param codFil
     * @param TipoPosto
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrItens> listarItensFaturamentoOS(String contrato, String OS, String codFil, String TipoPosto, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append("SELECT CtrItens.* \n");
            sql.append(" FROM CtrItens\n");
            sql.append(" LEFT JOIN OS_Vitens\n");
            sql.append("   ON CtrItens.TipoPosto = OS_Vitens.TipoPosto\n");
            sql.append("  AND CtrItens.CodFil    = OS_Vitens.CodFil\n");
            sql.append("  AND OS_Vitens.OS       = ?\n");
            sql.append(" WHERE CtrItens.Contrato = ?\n");
            sql.append(" AND   CtrItens.CodFil   = ?\n");
            if (TipoPosto.equals("0")) {
                sql.append(" AND   OS_Vitens.TipoPosto IS NULL");
            } else {
                sql.append(" AND   (OS_Vitens.TipoPosto IS NULL OR CtrItens.TipoPosto = ?)");
            }

            List<CtrItens> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(OS);
            consulta.setString(contrato);
            consulta.setString(codFil);
            if (!TipoPosto.equals("0")) {
                consulta.setString(TipoPosto);
            }
            consulta.select();
            CtrItens ctrItens;

            while (consulta.Proximo()) {
                ctrItens = new CtrItens();

                ctrItens.setContrato(consulta.getString("Contrato"));
                ctrItens.setCodFil(consulta.getString("CodFil"));
                ctrItens.setTipoPosto(consulta.getString("TipoPosto"));
                ctrItens.setDescricao(consulta.getString("Descricao"));
                ctrItens.setHoras(consulta.getString("Horas"));
                ctrItens.setValorPosto(consulta.getString("ValorPosto"));
                ctrItens.setHEDiurna(consulta.getString("HEDiurna"));
                ctrItens.setHENoturna(consulta.getString("HENoturna"));
                ctrItens.setHEDiurna2(consulta.getString("HEDiurna2"));
                ctrItens.setHENoturna2(consulta.getString("HENoturna2"));
                ctrItens.setReforco(consulta.getString("Reforco"));
                ctrItens.setCodTipo(consulta.getString("CodTipo"));
                ctrItens.setOperador(consulta.getString("Operador"));
                ctrItens.setDt_Alter(consulta.getString("Dt_Alter"));
                ctrItens.setHr_Alter(consulta.getString("Hr_Alter"));
                ctrItens.setCHSeman(consulta.getString("CHSeman"));
                ctrItens.setCHMensal(consulta.getString("CHMensal"));
                ctrItens.setCHFuncion(consulta.getString("CHFuncion"));
                ctrItens.setQtdeFunc(consulta.getString("QtdeFunc"));
                ctrItens.setSalario(consulta.getString("Salario"));
                ctrItens.setHsNormais(consulta.getString("HsNormais"));
                ctrItens.setHsExtras(consulta.getString("HsExtras"));
                ctrItens.setIndiceHE(consulta.getString("IndiceHE"));
                ctrItens.setTipoCalc(consulta.getString("TipoCalc"));
                ctrItens.setValorRot(consulta.getString("ValorRot"));
                ctrItens.setFranquiaRot(consulta.getString("FranquiaRot"));
                ctrItens.setValorEve(consulta.getString("ValorEve"));
                ctrItens.setFranquiaEve(consulta.getString("FranquiaEve"));
                ctrItens.setValorEsp(consulta.getString("ValorEsp"));
                ctrItens.setFranquiaEsp(consulta.getString("FranquiaEsp"));
                ctrItens.setValorAst(consulta.getString("ValorAst"));
                ctrItens.setFranquiaAst(consulta.getString("FranquiaAst"));
                ctrItens.setCargo(consulta.getString("Cargo"));
                ctrItens.setCodCargo(consulta.getString("CodCargo"));
                ctrItens.setQtdePreench(consulta.getString("QtdePreench"));
                ctrItens.setInibirReajuste(consulta.getInt("InibirReajuste"));
                retorno.add(ctrItens);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrItensDao.listarItensFaturamento - " + e.getMessage() + "\r\n"
                    + sql.toString());
        }
    }

    /**
     * Lista os itens de faturamento de um contrato
     *
     * @param contrato
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrItens> listarItensFaturamentoOS(String contrato, String OS, String codFil, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();

        try {
            sql.append("SELECT CtrItens.* \n");
            sql.append(" FROM CtrItens\n");
            sql.append(" LEFT JOIN OS_Vitens\n");
            sql.append("   ON CtrItens.TipoPosto = OS_Vitens.TipoPosto\n");
            sql.append("  AND CtrItens.CodFil    = OS_Vitens.CodFil\n");
            sql.append("  AND OS_Vitens.OS = ?\n");
            sql.append(" WHERE CtrItens.Contrato = ?\n");
            sql.append(" AND   CtrItens.CodFil   = ?\n");
            sql.append(" AND   OS_Vitens.TipoPosto IS NULL");

            List<CtrItens> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql.toString(), persistencia);
            consulta.setString(OS);
            consulta.setString(contrato);
            consulta.setString(codFil);
            consulta.select();
            CtrItens ctrItens;

            while (consulta.Proximo()) {
                ctrItens = new CtrItens();

                ctrItens.setContrato(consulta.getString("Contrato"));
                ctrItens.setCodFil(consulta.getString("CodFil"));
                ctrItens.setTipoPosto(consulta.getString("TipoPosto"));
                ctrItens.setDescricao(consulta.getString("Descricao"));
                ctrItens.setHoras(consulta.getString("Horas"));
                ctrItens.setValorPosto(consulta.getString("ValorPosto"));
                ctrItens.setHEDiurna(consulta.getString("HEDiurna"));
                ctrItens.setHENoturna(consulta.getString("HENoturna"));
                ctrItens.setHEDiurna2(consulta.getString("HEDiurna2"));
                ctrItens.setHENoturna2(consulta.getString("HENoturna2"));
                ctrItens.setReforco(consulta.getString("Reforco"));
                ctrItens.setCodTipo(consulta.getString("CodTipo"));
                ctrItens.setOperador(consulta.getString("Operador"));
                ctrItens.setDt_Alter(consulta.getString("Dt_Alter"));
                ctrItens.setHr_Alter(consulta.getString("Hr_Alter"));
                ctrItens.setCHSeman(consulta.getString("CHSeman"));
                ctrItens.setCHMensal(consulta.getString("CHMensal"));
                ctrItens.setCHFuncion(consulta.getString("CHFuncion"));
                ctrItens.setQtdeFunc(consulta.getString("QtdeFunc"));
                ctrItens.setSalario(consulta.getString("Salario"));
                ctrItens.setHsNormais(consulta.getString("HsNormais"));
                ctrItens.setHsExtras(consulta.getString("HsExtras"));
                ctrItens.setIndiceHE(consulta.getString("IndiceHE"));
                ctrItens.setTipoCalc(consulta.getString("TipoCalc"));
                ctrItens.setValorRot(consulta.getString("ValorRot"));
                ctrItens.setFranquiaRot(consulta.getString("FranquiaRot"));
                ctrItens.setValorEve(consulta.getString("ValorEve"));
                ctrItens.setFranquiaEve(consulta.getString("FranquiaEve"));
                ctrItens.setValorEsp(consulta.getString("ValorEsp"));
                ctrItens.setFranquiaEsp(consulta.getString("FranquiaEsp"));
                ctrItens.setValorAst(consulta.getString("ValorAst"));
                ctrItens.setFranquiaAst(consulta.getString("FranquiaAst"));
                ctrItens.setCargo(consulta.getString("Cargo"));
                ctrItens.setCodCargo(consulta.getString("CodCargo"));
                ctrItens.setQtdePreench(consulta.getString("QtdePreench"));
                ctrItens.setInibirReajuste(consulta.getInt("InibirReajuste"));
                retorno.add(ctrItens);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrItensDao.listarItensFaturamento - " + e.getMessage() + "\r\n"
                    + sql.toString());
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param primeiro elemento inicial de paginação
     * @param linhas quantidade de elementos de paginação
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<CtrItens> listarItensPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            List<CtrItens> itens = new ArrayList<>();

            String sql = "SELECT ctritens.* \n"
                    + "FROM contrvig \n"
                    + "LEFT JOIN ctritens \n"
                    + "ON contrvig.Contrato = ctritens.Contrato \n"
                    + "AND contrvig.CodFil = ctritens.CodFil \n"
                    + "WHERE contrvig.Contrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            sql += "ORDER BY ctritens.Contrato \n" + sqlPaginacao;
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);

            consulta.select();
            CtrItens item;
            while (consulta.Proximo()) {
                item = new CtrItens();
                item.setContrato(consulta.getString("Contrato"));
                item.setCodFil(consulta.getString("CodFil"));
                item.setTipoPosto(consulta.getString("TipoPosto"));
                item.setDescricao(consulta.getString("Descricao"));
                item.setHoras(consulta.getString("Horas"));
                item.setValorPosto(consulta.getString("ValorPosto"));
                item.setHEDiurna(consulta.getString("HEDiurna"));
                item.setHENoturna(consulta.getString("HENoturna"));
                item.setHEDiurna2(consulta.getString("HEDiurna2"));
                item.setHENoturna2(consulta.getString("HENoturna2"));
                item.setReforco(consulta.getString("Reforco"));
                item.setCodTipo(consulta.getString("CodTipo"));
                item.setOperador(consulta.getString("Operador"));
                item.setDt_Alter(consulta.getString("Dt_Alter"));
                item.setHr_Alter(consulta.getString("Hr_Alter"));
                item.setCHSeman(consulta.getString("CHSeman"));
                item.setCHMensal(consulta.getString("CHMensal"));
                item.setCHFuncion(consulta.getString("CHFuncion"));
                item.setQtdeFunc(consulta.getString("QtdeFunc"));
                item.setSalario(consulta.getString("Salario"));
                item.setHsNormais(consulta.getString("HsNormais"));
                item.setHsExtras(consulta.getString("HsExtras"));
                item.setIndiceHE(consulta.getString("IndiceHE"));
                item.setTipoCalc(consulta.getString("TipoCalc"));
                item.setValorRot(consulta.getString("ValorRot"));
                item.setFranquiaRot(consulta.getString("FranquiaRot"));
                item.setValorEve(consulta.getString("ValorEve"));
                item.setFranquiaEve(consulta.getString("FranquiaEve"));
                item.setValorEsp(consulta.getString("ValorEsp"));
                item.setFranquiaEsp(consulta.getString("FranquiaEsp"));
                item.setValorAst(consulta.getString("ValorAst"));
                item.setFranquiaAst(consulta.getString("FranquiaAst"));
                item.setCargo(consulta.getString("Cargo"));
                item.setCodCargo(consulta.getString("CodCargo"));
                item.setQtdePreench(consulta.getString("QtdePreench"));
                item.setInibirReajuste(consulta.getInt("InibirReajuste"));

                itens.add(item);
            }
            consulta.Close();
            return itens;
        } catch (Exception e) {
            throw new Exception("CtrlItensDao.listarItensPaginada - " + e.getMessage());
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public int contagemItens(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            int total = 0;

            String sql = "SELECT COUNT(*) AS total \n"
                    + "FROM contrvig \n"
                    + "LEFT JOIN ctritens \n"
                    + "ON contrvig.Contrato = ctritens.Contrato \n"
                    + "AND contrvig.CodFil = ctritens.CodFil \n"
                    + "WHERE contrvig.Contrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            while (consulta.Proximo()) {
                total = consulta.getInt("Total");
            }
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("CtrlItensDao.listarItensPaginada - " + e.getMessage());
        }
    }

    public void editarCtrItens(CtrItens contratoItens, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update ctritens set \n"
                    + "descricao = ?, \n"
                    + "operador = ?, \n"
                    + "hr_alter = ?, \n"
                    + "tipocalc = ?, \n"
                    + "CodTipo = ?, \n"
                    + "dt_alter = ?, \n"
                    + "Horas = ?, \n"
                    + "ValorPosto = ?, \n"
                    + "HEDiurna = ?, \n"
                    + "HENoturna = ?, \n"
                    + "HEDiurna2 = ?, \n"
                    + "HENoturna2 = ?, \n"
                    + "Reforco = ?, \n"
                    + "CHSeman = ?, \n"
                    + "CHMensal = ?, \n"
                    + "CHFuncion = ?, \n"
                    + "QtdeFunc = ?, \n"
                    + "Salario = ?, \n"
                    + "HsNormais = ?, \n"
                    + "HsExtras = ?, \n"
                    + "IndiceHE = ?, \n"
                    + "ValorRot = ?, \n"
                    + "FranquiaRot = ?, \n"
                    + "ValorEve = ?, \n"
                    + "FranquiaEve = ?, \n"
                    + "ValorEsp = ?, \n"
                    + "FranquiaEsp = ?, \n"
                    + "ValorAst = ?, \n"
                    + "FranquiaAst = ?, \n"
                    + "Cargo = ?, \n"
                    + "CodCargo = ?, \n"
                    + "QtdePreench = ?, \n"
                    + "InibirReajuste = ? \n"
                    + "where contrato = ? \n"
                    + "  and codfil = ? \n"
                    + "  and tipoposto = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoItens.getDescricao());
            consulta.setString(contratoItens.getOperador());
            consulta.setString(contratoItens.getHr_Alter());
            consulta.setString(contratoItens.getTipoCalc());
            consulta.setBigDecimal(contratoItens.getCodTipo());
            consulta.setString(contratoItens.getDt_Alter());
            consulta.setString(contratoItens.getHoras());
            consulta.setString(contratoItens.getValorPosto());
            consulta.setString(contratoItens.getHEDiurna());
            consulta.setString(contratoItens.getHENoturna());
            consulta.setString(contratoItens.getHEDiurna2());
            consulta.setString(contratoItens.getHENoturna2());
            consulta.setString(contratoItens.getReforco());
            consulta.setString(contratoItens.getCHSeman());
            consulta.setString(contratoItens.getCHMensal());
            consulta.setString(contratoItens.getCHFuncion());
            consulta.setString(contratoItens.getQtdeFunc());
            consulta.setString(contratoItens.getSalario());
            consulta.setString(contratoItens.getHsNormais());
            consulta.setString(contratoItens.getHsExtras());
            consulta.setString(contratoItens.getIndiceHE());
            consulta.setString(contratoItens.getValorRot());
            consulta.setString(contratoItens.getFranquiaRot());
            consulta.setString(contratoItens.getValorEve());
            consulta.setString(contratoItens.getFranquiaEve());
            consulta.setString(contratoItens.getValorEsp());
            consulta.setString(contratoItens.getFranquiaEsp());
            consulta.setString(contratoItens.getValorAst());
            consulta.setString(contratoItens.getFranquiaAst());
            consulta.setString(contratoItens.getCargo());
            consulta.setString(contratoItens.getCodCargo());
            consulta.setString(contratoItens.getQtdePreench());
            consulta.setInt(contratoItens.getInibirReajuste());

            consulta.setString(contratoItens.getContrato());
            consulta.setString(contratoItens.getCodFil().replace(".0", ""));
            consulta.setString(contratoItens.getTipoPosto());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.editarCtrItens - " + e.getMessage());
        }
    }

    public void gravarCtrItens(CtrItens contratoItens, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into ctritens( \n"
                    + "descricao,operador,hr_alter,tipocalc,CodTipo,dt_alter,Horas,ValorPosto,HEDiurna, \n"
                    + "HENoturna,HEDiurna2,HENoturna2,Reforco,CHSeman,CHMensal,CHFuncion,QtdeFunc,Salario, \n"
                    + "HsNormais,HsExtras,IndiceHE,ValorRot,FranquiaRot,ValorEve,FranquiaEve,ValorEsp,FranquiaEsp, \n"
                    + "ValorAst,FranquiaAst,Cargo,CodCargo,QtdePreench,InibirReajuste,Contrato,Codfil,TipoPosto) \n"
                    + "values(?,?,?,?,?,?,?,?,?, \n"
                    + "?,?,?,?,?,?,?,?,?, \n"
                    + "?,?,?,?,?,?,?,?,?, \n"
                    + "?,?,?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoItens.getDescricao());
            consulta.setString(contratoItens.getOperador());
            consulta.setString(contratoItens.getHr_Alter());
            consulta.setString(contratoItens.getTipoCalc());
            consulta.setBigDecimal(contratoItens.getCodTipo());
            consulta.setString(contratoItens.getDt_Alter());
            consulta.setString(contratoItens.getHoras());
            consulta.setString(contratoItens.getValorPosto());
            consulta.setString(contratoItens.getHEDiurna());
            consulta.setString(contratoItens.getHENoturna());
            consulta.setString(contratoItens.getHEDiurna2());
            consulta.setString(contratoItens.getHENoturna2());
            consulta.setString(contratoItens.getReforco());
            consulta.setString(contratoItens.getCHSeman());
            consulta.setString(contratoItens.getCHMensal());
            consulta.setString(contratoItens.getCHFuncion());
            consulta.setString(contratoItens.getQtdeFunc());
            consulta.setString(contratoItens.getSalario());
            consulta.setString(contratoItens.getHsNormais());
            consulta.setString(contratoItens.getHsExtras());
            consulta.setString(contratoItens.getIndiceHE());
            consulta.setString(contratoItens.getValorRot());
            consulta.setString(contratoItens.getFranquiaRot());
            consulta.setString(contratoItens.getValorEve());
            consulta.setString(contratoItens.getFranquiaEve());
            consulta.setString(contratoItens.getValorEsp());
            consulta.setString(contratoItens.getFranquiaEsp());
            consulta.setString(contratoItens.getValorAst());
            consulta.setString(contratoItens.getFranquiaAst());
            consulta.setString(contratoItens.getCargo());
            consulta.setString(contratoItens.getCodCargo());
            consulta.setString(contratoItens.getQtdePreench());
            consulta.setInt(contratoItens.getInibirReajuste());
            consulta.setString(contratoItens.getContrato());
            consulta.setString(contratoItens.getCodFil().replace(".0", ""));
            consulta.setString(contratoItens.getTipoPosto());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrItensDao.gravarCtrItens - " + e.getMessage());
        }
    }
}
