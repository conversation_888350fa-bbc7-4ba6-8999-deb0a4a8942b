package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CodClienteAut;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CodClienteAuthDao {

    /**
     * Obtem informações do nome da assinatura
     *
     * @param sequencia sequencia da rota
     * @param codPessoa codpessoa que assinou
     * @param persistencia conexão com o banco de dados
     * @return nome dequem assinou
     * @throws Exception
     */
    public String obtemNomeAssinado(String sequencia, String codPessoa, Persistencia persistencia) throws Exception {
        String nome = "";
        try {
            String sql = "Select  pessoa.Nome nome from Rotas "
                    + "Left join RT_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                    + "INNER join PessoaCliAut  on PessoaCliAut.CodCli = Rt_Perc.CodCli1 "
                    + "    and PessoaCliAut.CodFil = Rotas.CodFil "
                    + "left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
                    + "left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
                    + "where Rotas.Sequencia = ? AND pessoa.Codigo = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codPessoa);
            consulta.select();

            while (consulta.Proximo()) {
                nome = consulta.getString("nome");
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CodClienteAuthDAO.obtemNomeAssinado - " + e.getMessage() + "\r\n"
                    + "Select  pessoa.Nome nome from Rotas "
                    + "Left join RT_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                    + "INNER join PessoaCliAut  on PessoaCliAut.CodCli = Rt_Perc.CodCli1 "
                    + "    and PessoaCliAut.CodFil = Rotas.CodFil "
                    + "left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
                    + "left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
                    + "where Rotas.Sequencia = " + sequencia + " AND pessoa.Codigo = " + codPessoa);
        }
        return nome;
    }

    /**
     * Recupera informações
     *
     * @param codigo Código do cliente
     * @param codFil Codigo da filial
     * @param persistencia Conexão com o banco e dados
     * @return lista de codigo do cliente
     * @throws Exception
     */
    public List<CodClienteAut> recuperarInformacoes(String codigo, String codFil, Persistencia persistencia) throws Exception {
        List<CodClienteAut> autoziacoes = new ArrayList<>();
        try {
            String sql = "Select  pessoa.PWWeb, pessoa.Codigo from PessoaCliAut "
                    + "left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
                    + "left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
                    + "where Clientes.Codigo = ? AND Clientes.CodFil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(codFil);
            consulta.select();

            CodClienteAut autoziacao = null;
            while (consulta.Proximo()) {
                autoziacao = new CodClienteAut();
                autoziacao.setPwWeb(consulta.getString("PWWeb"));
                autoziacao.setCodigo(consulta.getString("codigo"));
                autoziacoes.add(autoziacao);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CodClienteAuthDAO.recuperarInformacoes - " + e.getMessage() + "\r\n"
                    + "Select  pessoa.PWWeb, pessoa.Codigo from PessoaCliAut "
                    + "left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
                    + "left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
                    + "where Clientes.Codigo = " + codigo + " AND Clientes.CodFil = " + codFil);
        }
        return autoziacoes;
    }

    /**
     * Recupera informacoes da rota
     *
     * @param sequencia sequencia da rota
     * @param persistencia Conexao com o banco de dados
     * @return lista contendo registros da parada
     * @throws Exception
     */
    public List<CodClienteAut> recuperaInformacoesPorRota(String sequencia, Persistencia persistencia) throws Exception {
        List<CodClienteAut> senhas = new ArrayList<>();
        try {
            String sql = "Select  pessoa.Nome, pessoa.PWWeb, pessoa.Codigo, Rt_Perc.Parada from Rotas "
                    + "Left join RT_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                    + "Left join PessoaCliAut  on PessoaCliAut.CodCli = Rt_Perc.CodCli1 "
                    + "    and PessoaCliAut.CodFil = Rotas.CodFil "
                    + "Left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
/*
                    + "Left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
*/
                    + "where Rotas.Sequencia = ?"
                    + "  and Pessoa.Codigo is not null";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();

            CodClienteAut senha = null;
            while (consulta.Proximo()) {
                senha = new CodClienteAut();
                senha.setNome(consulta.getString("Nome"));
                senha.setCodigo(consulta.getString("Codigo"));
                senha.setParada(consulta.getInt("Parada"));
                senha.setPwWeb(consulta.getString("PWWeb"));
                senhas.add(senha);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CodClienteAuthDAO.recuperaInformacoesPorRota - " + e.getMessage() + "\r\n"
                    + "Select  pessoa.Nome, pessoa.PWWeb, pessoa.Codigo, Rt_Perc.Parada from Rotas "
                    + "inner join RT_Perc  on Rt_Perc.Sequencia = Rotas.Sequencia "
                    + "INNER join PessoaCliAut  on PessoaCliAut.CodCli = Rt_Perc.CodCli1 "
                    + "    and PessoaCliAut.CodFil = Rotas.CodFil "
                    + "inner join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo "
                    + "inner join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil "
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli "
                    + "where Rotas.Sequencia = " + sequencia);
        }
        return senhas;
    }

}
