package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FPPeriodos;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FPPeriodosDao {

    String sql;

    /**
     * 13 terceiro Retorna dados FpPeriodos
     *
     * @param persistencia - conexão com o banco
     * @return - retorna lista com 06 registros
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<FPPeriodos> getFpPeriodos(Persistencia persistencia) throws Exception {

        List<FPPeriodos> lFpPeriodos = new ArrayList();

        sql = "select top 6 DtInicio, CodMovFP"
                + " from fpperiodos "
                + " where (dtpublicweb<?) OR (codmovfp = 1612 AND tipofp = '132')"
                + " order by dtinicio desc";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setString(getDataAtual("SQL"));
            cFpPeriodos.select();

            while (cFpPeriodos.Proximo()) {
                FPPeriodos oFpPeriodos = new FPPeriodos();

                oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").replace(".0", ""));
                lFpPeriodos.add(oFpPeriodos);

            }
            cFpPeriodos.Close();
            return lFpPeriodos;
        } catch (Exception e) {
            throw new Exception("FPPeriodosDao.getFpPeriodos  - " + e.getMessage() + "\r\n"
                    + "select top 6 DtInicio, CodMovFP"
                    + " from fpperiodos "
                    + " where (dtpublicweb<" + getDataAtual("SQL") + ") OR (codmovfp = 1612 AND tipofp = '132')"
                    + " order by dtinicio desc");
        }

    }

    /**
     * Retorna dados FpPeriodos
     *
     * @param persistencia - conexão com o banco
     * @return - retorna lista com 06 registros
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<FPPeriodos> getFPs(Persistencia persistencia) throws Exception {

        List<FPPeriodos> lFpPeriodos = new ArrayList();

        sql = "select top 6 DtInicio, CodMovFP"
                + " from fpperiodos "
                + " where (dtpublicweb<?)"
                + " order by dtinicio desc";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setString(getDataAtual("SQL"));
            cFpPeriodos.select();

            while (cFpPeriodos.Proximo()) {
                FPPeriodos oFpPeriodos = new FPPeriodos();

                oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").replace(".0", ""));
                lFpPeriodos.add(oFpPeriodos);
            }
            cFpPeriodos.Close();
            return lFpPeriodos;
        } catch (Exception e) {
            throw new Exception("FPPeriodosDao.getFPs  - " + e.getMessage() + "\r\n"
                    + "select top 6 DtInicio, CodMovFP"
                    + " from fpperiodos "
                    + " where (dtpublicweb<" + getDataAtual("SQL") + ")"
                    + " order by dtinicio desc");
        }

    }

    /**
     * Retorna dados FpPeriodos
     *
     * @param persistencia - conexão com o banco
     * @return - retorna lista com 06 registros
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<FPPeriodos> getFP(String codMovFP, Persistencia persistencia) throws Exception {

        List<FPPeriodos> lFpPeriodos = new ArrayList();

        sql = "select top 6 DtInicio, CodMovFP"
                + " from fpperiodos "
                + " where CodMovFP = ? AND (dtpublicweb<?)"
                + " order by dtinicio desc";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setString(codMovFP);
            cFpPeriodos.setString(getDataAtual("SQL"));
            cFpPeriodos.select();

            while (cFpPeriodos.Proximo()) {
                FPPeriodos oFpPeriodos = new FPPeriodos();

                oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").replace(".0", ""));
                lFpPeriodos.add(oFpPeriodos);
            }
            cFpPeriodos.Close();
            return lFpPeriodos;
        } catch (Exception e) {
            throw new Exception("FPPeriodosDao.getFP  - " + e.getMessage() + "\r\n"
                    + "select top 6 DtInicio, CodMovFP"
                    + " from fpperiodos "
                    + " where CodMovFP = " + codMovFP + " AND (dtpublicweb<" + getDataAtual("SQL") + ")"
                    + " order by dtinicio desc");
        }

    }
}
