package Xml;

import com.thoughtworks.xstream.XStream;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Converte_XML {

    private XStream xstream;
    private List objeto = null;
    private int tamanho = 0;

    public void Converte_XML() {
        xstream = new XStream();
    }

    /**
     * Transforma XML em uma lista
     *
     * @param XML - XML em string
     */
    public void xml2obj(String XML) {
        objeto = (List) xstream.fromXML(XML);
        tamanho = objeto.size();

    }

    /**
     * Converte um objeto em XML
     *
     * @param obj - Obejto que sera transformado
     * @return - String contendo o XML
     */
    public String obj2xml(Object obj) {
        String XML = xstream.toXML(obj);
        return XML;
    }

    /**
     * Devolve o tamanho da lista obtida do xml
     *
     * @return inteiro
     */
    public int getTamanholista() {
        return tamanho;
    }

    /**
     * Devolte o obejto da lista que foi montado pelo XML
     *
     * @param index - Posicao da lista desejada
     * @return - Obejeto desejado
     */
    public Object getObjeto(int index) {
        return objeto.get(index);
    }

}
