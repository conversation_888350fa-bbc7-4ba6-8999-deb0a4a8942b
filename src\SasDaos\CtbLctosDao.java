package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ContasCTB;
import SasBeans.CtbLctos;
import SasBeans.Filiais;
import SasBeansCompostas.RegistroSaidaLivro;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CtbLctosDao {

    public List<RegistroSaidaLivro> listaLivroDiario(String dtInicio, String dtFim, String codFilInicio, String codFilFim, Persistencia persistencia) throws Exception {

        try {
            List<RegistroSaidaLivro> retornoLivro = new ArrayList<>();
            String sql = "Select CtbLctos.Sequencia, CtbLctos.CodFil, CtbLctos.Data,\n"
                    + " CtbLctos.ContaDEB, ContaDeb.Descricao ContaDEB_Desc,CtbLctos.Detalhes, CtbLctos.ContaCred, \n"
                    + "ContaCRED.Descricao ContaCred_Cred, CtbLctos.Valor, CtbLctos.Historico, "
                    + "Filiais.CNPJ, Filiais.RazaoSocial, Filiais.InscEst, Filiais.Descricao\n"
                    + "from CtbLctos\n"
                    + "Left join ContasCTB ContaDEB  on ContaDEB.Codigo = CtbLctos.ContaDEB\n"
                    + "Left join ContasCTB ContaCRED on ContaCRED.Codigo = CtbLctos.ContaCred\n"
                    + "Left join Filiais on Filiais.CodFil = CtbLctos.CodFil\n"
                    + "Where CtbLctos.Data Between ? and ? \n"
                    + "  and CtbLctos.CodFil >= ? \n"
                    + "  and CtbLctos.CodFil <= ? \n"
                    + "Order by CtbLctos.Sequencia";

            Consulta consultaLivro = new Consulta(sql, persistencia);
            consultaLivro.setString(dtInicio);
            consultaLivro.setString(dtFim);
            consultaLivro.setString(codFilInicio);
            consultaLivro.setString(codFilFim);
            consultaLivro.select();

            RegistroSaidaLivro regLivro;
            ContasCTB contasCtb;
            CtbLctos ctbLctos;
            Filiais filiais;

            while (consultaLivro.Proximo()) {
                regLivro = new RegistroSaidaLivro();
                contasCtb = new ContasCTB();
                ctbLctos = new CtbLctos();
                filiais = new Filiais();

                contasCtb.setDescricao(consultaLivro.getString("ContaDEB_Desc"));
                contasCtb.setDescricao(consultaLivro.getString("ContaCred_Cred"));

                ctbLctos.setSequencia(consultaLivro.getString("Sequencia"));
                ctbLctos.setCodFil(consultaLivro.getString("CodFil"));
                ctbLctos.setData(consultaLivro.getLocalDate("Data"));
                ctbLctos.setContaDeb(consultaLivro.getString("ContaDEB"));
                ctbLctos.setContaCred(consultaLivro.getString("ContaCred"));
                ctbLctos.setValor(consultaLivro.getString("Valor"));
                ctbLctos.setHistorico(consultaLivro.getString("Historico"));
                ctbLctos.setDetalhes(consultaLivro.getString("Detalhes"));

                filiais.setCNPJ(consultaLivro.getString("CNPJ"));
                filiais.setRazaoSocial(consultaLivro.getString("RazaoSocial"));
                filiais.setInscEst(consultaLivro.getString("InscEst"));
                filiais.setDescricao(consultaLivro.getString("Descricao"));

                regLivro.setContasCtb(contasCtb);
                regLivro.setCtbLctos(ctbLctos);
                regLivro.setFiliais(filiais);

                retornoLivro.add(regLivro);

            }

            consultaLivro.Close();
            return retornoLivro;
        } catch (Exception ex) {
            throw new Exception("LivroDao.ListaLivroDiario - " + ex.getMessage() + ""
                    + "Select CtbLctos.Sequencia, CtbLctos.CodFil, CtbLctos.Data,\n"
                    + " CtbLctos.ContaDEB, ContaDeb.Descricao ContaDEB_Desc, CtbLctos.ContaCred, \n"
                    + "ContaCRED.Descricao ContaCred_Cred, CtbLctos.Valor, CtbLctos.Historico, Filiais.CNPJ, Filiais.RazaoSocial, Filiais.InscEst, Filiais.Descricao\n"
                    + "from CtbLctos\n"
                    + "Left join ContasCTB ContaDEB  on ContaDEB.Codigo = CtbLctos.ContaDEB\n"
                    + "Left join ContasCTB ContaCRED on ContaCRED.Codigo = CtbLctos.ContaCred\n"
                    + "Where CtbLctos.Data Between " + dtInicio + " and " + dtFim + "\n"
                    + "  and CtbLctos.CodFil >= " + codFilInicio + "\n"
                    + "  and CtbLctos.CodFil <= " + codFilFim + "\n"
                    + "Order by CtbLctos.Sequencia");
        }
    }
}
