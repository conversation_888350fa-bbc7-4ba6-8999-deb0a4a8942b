/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Fat_Grp;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Fat_GrpDao {

    public Fat_Grp buscarFat_Grp(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {

            String sql = " SELECT Fat_Grp.Codigo, Fat_Grp.CodFil, Fat_Grp.Descricao, Fat_Grp.CodHistFix, Fat_Grp.CodHistExt, Fat_Grp.CodHist, \n"
                    + " Fat_Grp.AgrupInterf, Fat_Grp.TCobran, Fat_Grp.AliqIRRF, Fat_Grp.AliqPIS, Fat_Grp.PISRet, Fat_Grp.AliqCSL, Fat_Grp.CSLRet, \n"
                    + " Fat_Grp.AliqCOFINS, Fat_Grp.COFINSRet, Fat_Grp.AliqIRPJ, Fat_Grp.IRPJRet, Fat_Grp.BaseINSSPerc, Fat_Grp.AliqINSS, \n"
                    + " Fat_Grp.INSSRet, Fat_Grp.AgrupISS, Fat_Grp.Repasse, Fat_Grp.IndRepasseCst, Fat_Grp.CCusto, Fat_Grp.SitFiscal, Fat_Grp.ObsFiscal, \n"
                    + " Fat_Grp.OperFiscal, Fat_Grp.Dt_Fiscal, Fat_Grp.Hr_Fiscal, Fat_Grp.Operador, Fat_Grp.Dt_Alter, Fat_Grp.Hr_Alter, Fat_Grp.Flag_Excl,\n"
                    + " Fat_Grp.OperExcl, Fat_Grp.Dt_Excl, Fat_Grp.Hr_Excl, \n"
                    + " FatISSGrp.Tipo, FatISSGrp.Descricao ISSGrpDesc, FatISSGrp.CodMunic, FatISSGrp.ISSRet, FatISSGrp.AliqICMS, FatISSGrp.ICMSRet, \n"
                    + " Municipios.Nome Municipio, Municipios.UF, \n"
                    + " Ht_NF.CFOP, FatCFOP.Descricao CFOPDesc, MunicCFOP.AliqISS \n"
                    + " FROM Fat_Grp \n"
                    + " left join FatISSGrp  on  FatISSGrp.Codigo   = Fat_Grp.AgrupISS \n"
                    + "                 left join Municipios on  Municipios.Codigo  = FatISSGrp.CodMunic \n"
                    + "                 left join HT_NF      on  HT_NF.Codigo       = Fat_Grp.CodHist \n"
                    + "                                      and HT_NF.CodFil       = Fat_Grp.CodFil  \n"
                    + "                 left join FatCFOP    on  FatCFOP.Codigo     = HT_NF.CFOP \n"
                    + "                 left Join MunicCFOP  on  MunicCFOP.CodMunic = FatISSGrp.CodMunic \n"
                    + "                                      and MunicCFOP.CFOP     = HT_NF.CFOP \n"
                    + " WHERE Fat_Grp.Codigo = ? AND Fat_Grp.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(codFil);
            consulta.select();

            Fat_Grp fat_Grp = null;
            if (consulta.Proximo()) {
                fat_Grp = new Fat_Grp();
                fat_Grp.setCodigo(consulta.getString("Codigo").replace(".0", ""));
                fat_Grp.setCodFil(consulta.getString("CodFil").replace(".0", ""));
                fat_Grp.setDescricao(consulta.getString("Descricao"));
                fat_Grp.setCodHistFix(consulta.getString("CodHistFix").replace(".0", ""));
                fat_Grp.setCodHistExt(consulta.getString("CodHistExt").replace(".0", ""));
                fat_Grp.setCodHist(consulta.getString("CodHist").replace(".0", ""));
                fat_Grp.setAgrupInterf(consulta.getString("AgrupInterf"));
                fat_Grp.setTCobran(consulta.getString("TCobran").replace(".0", ""));
                fat_Grp.setAliqIRRF(consulta.getString("AliqIRRF"));
                fat_Grp.setAliqPIS(consulta.getString("AliqPIS"));
                fat_Grp.setPISRet(consulta.getString("PISRet"));
                fat_Grp.setAliqCSL(consulta.getString("AliqCSL"));
                fat_Grp.setCSLRet(consulta.getString("CSLRet"));
                fat_Grp.setAliqCOFINS(consulta.getString("AliqCOFINS"));
                fat_Grp.setCOFINSRet(consulta.getString("COFINSRet"));
                fat_Grp.setAliqIRPJ(consulta.getString("AliqIRPJ"));
                fat_Grp.setIRPJRet(consulta.getString("IRPJRet"));
                fat_Grp.setBaseINSSPerc(consulta.getString("BaseINSSPerc"));
                fat_Grp.setAliqINSS(consulta.getString("AliqINSS"));
                fat_Grp.setINSSRet(consulta.getString("INSSRet"));
                fat_Grp.setAgrupISS(consulta.getString("AgrupISS").replace(".0", ""));
                fat_Grp.setRepasse(consulta.getString("Repasse"));
                fat_Grp.setIndRepasseCst(consulta.getString("IndRepasseCst"));
                fat_Grp.setCCusto(consulta.getString("CCusto"));
                fat_Grp.setSitFiscal(consulta.getString("SitFiscal"));
                fat_Grp.setObsFiscal(consulta.getString("ObsFiscal"));
                fat_Grp.setOperFiscal(consulta.getString("OperFiscal"));
                fat_Grp.setDt_Fiscal(consulta.getString("Dt_Fiscal"));
                fat_Grp.setHr_Fiscal(consulta.getString("Hr_Fiscal"));
                fat_Grp.setOperador(consulta.getString("Operador"));
                fat_Grp.setDt_Alter(consulta.getString("Dt_Alter"));
                fat_Grp.setHr_Alter(consulta.getString("Hr_Alter"));
                fat_Grp.setFlag_Excl(consulta.getString("Flag_Excl"));
                fat_Grp.setOperExcl(consulta.getString("OperExcl"));
                fat_Grp.setDt_Excl(consulta.getString("Dt_Excl"));
                fat_Grp.setHr_Excl(consulta.getString("Hr_Excl"));

                fat_Grp.setTipo(consulta.getString("Tipo"));
                fat_Grp.setISSGrpDesc(consulta.getString("ISSGrpDesc"));
                fat_Grp.setMunicipio(consulta.getString("Municipio"));
                fat_Grp.setUF(consulta.getString("UF"));
                fat_Grp.setCodMunic(consulta.getString("CodMunic"));
                fat_Grp.setISSRet(consulta.getString("ISSRet"));
                fat_Grp.setAliqICMS(consulta.getString("AliqICMS"));
                fat_Grp.setICMSRet(consulta.getString("ICMSRet"));
                fat_Grp.setCFOP(consulta.getString("CFOP"));
                fat_Grp.setCFOPDesc(consulta.getString("CFOPDesc"));
                fat_Grp.setAliqISS(consulta.getString("AliqISS"));
            }
            consulta.close();
            return fat_Grp;
        } catch (Exception e) {
            throw new Exception("Fat_GrpDao.buscarFat_Grp - " + e.getMessage() + "\r\n"
                    + " SELECT Fat_Grp.Codigo, Fat_Grp.CodFil, Fat_Grp.Descricao, Fat_Grp.CodHistFix, Fat_Grp.CodHistExt, Fat_Grp.CodHist, \n"
                    + " Fat_Grp.AgrupInterf, Fat_Grp.TCobran, Fat_Grp.AliqIRRF, Fat_Grp.AliqPIS, Fat_Grp.PISRet, Fat_Grp.AliqCSL, Fat_Grp.CSLRet, \n"
                    + " Fat_Grp.AliqCOFINS, Fat_Grp.COFINSRet, Fat_Grp.AliqIRPJ, Fat_Grp.IRPJRet, Fat_Grp.BaseINSSPerc, Fat_Grp.AliqINSS, \n"
                    + " Fat_Grp.INSSRet, Fat_Grp.AgrupISS, Fat_Grp.Repasse, Fat_Grp.IndRepasseCst, Fat_Grp.CCusto, Fat_Grp.SitFiscal, Fat_Grp.ObsFiscal, \n"
                    + " Fat_Grp.OperFiscal, Fat_Grp.Dt_Fiscal, Fat_Grp.Hr_Fiscal, Fat_Grp.Operador, Fat_Grp.Dt_Alter, Fat_Grp.Hr_Alter, Fat_Grp.Flag_Excl,\n"
                    + " Fat_Grp.OperExcl, Fat_Grp.Dt_Excl, Fat_Grp.Hr_Excl, \n"
                    + " FatISSGrp.Tipo, FatISSGrp.Descricao ISSGrpDesc, FatISSGrp.CodMunic, FatISSGrp.ISSRet, FatISSGrp.AliqICMS, FatISSGrp.ICMSRet, \n"
                    + " Municipios.Nome Municipio, Municipios.UF, \n"
                    + " Ht_NF.CFOP, FatCFOP.Descricao CFOPDesc, MunicCFOP.AliqISS \n"
                    + " FROM Fat_Grp \n"
                    + " left join FatISSGrp  on  FatISSGrp.Codigo   = Fat_Grp.AgrupISS \n"
                    + "                 left join Municipios on  Municipios.Codigo  = FatISSGrp.CodMunic \n"
                    + "                 left join HT_NF      on  HT_NF.Codigo       = Fat_Grp.CodHist \n"
                    + "                                      and HT_NF.CodFil       = Fat_Grp.CodFil  \n"
                    + "                 left join FatCFOP    on  FatCFOP.Codigo     = HT_NF.CFOP \n"
                    + "                 left Join MunicCFOP  on  MunicCFOP.CodMunic = FatISSGrp.CodMunic \n"
                    + "                                      and MunicCFOP.CFOP     = HT_NF.CFOP \n"
                    + " WHERE Fat_Grp.Codigo = " + codigo + " AND Fat_Grp.CodFil = " + codFil);
        }
    }

    public List<Fat_Grp> buscarFat_GrpList(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Fat_Grp> retorno = new ArrayList<>();
            String sql = " SELECT Fat_Grp.Codigo, Fat_Grp.CodFil, Fat_Grp.Descricao, FatISSGrp.Tipo, FatISSGrp.Descricao ISSGrpDesc, \n"
                    + " Municipios.Nome Municipio, Municipios.UF, FatISSGrp.CodMunic, FatISSGrp.ISSRet, FatISSGrp.AliqICMS, FatISSGrp.ICMSRet, \n"
                    + " Ht_NF.CFOP, FatCFOP.Descricao CFOPDesc, MunicCFOP.AliqISS \n"
                    + " FROM Fat_Grp \n"
                    + " left join FatISSGrp  on  FatISSGrp.Codigo   = Fat_Grp.AgrupISS \n"
                    + "                 left join Municipios on  Municipios.Codigo  = FatISSGrp.CodMunic \n"
                    + "                 left join HT_NF      on  HT_NF.Codigo       = Fat_Grp.CodHist \n"
                    + "                                      and HT_NF.CodFil       = Fat_Grp.CodFil  \n"
                    + "                 left join FatCFOP    on  FatCFOP.Codigo     = HT_NF.CFOP \n"
                    + "                 left Join MunicCFOP  on  MunicCFOP.CodMunic = FatISSGrp.CodMunic \n"
                    + "                                      and MunicCFOP.CFOP     = HT_NF.CFOP \n"
                    + " WHERE (Convert(VarChar, Fat_Grp.Codigo) like ? OR Fat_Grp.Descricao like ?) AND Fat_Grp.CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.setString(codFil);
            consulta.select();

            Fat_Grp fat_Grp;
            while (consulta.Proximo()) {
                fat_Grp = new Fat_Grp();
                fat_Grp.setCodigo(consulta.getString("Codigo").replace(".0", ""));
                fat_Grp.setCodFil(consulta.getString("CodFil").replace(".0", ""));
                fat_Grp.setDescricao(consulta.getString("Descricao"));

                fat_Grp.setTipo(consulta.getString("Tipo"));
                fat_Grp.setISSGrpDesc(consulta.getString("ISSGrpDesc"));
                fat_Grp.setMunicipio(consulta.getString("Municipio"));
                fat_Grp.setUF(consulta.getString("UF"));
                fat_Grp.setCodMunic(consulta.getString("CodMunic"));
                fat_Grp.setISSRet(consulta.getString("ISSRet"));
                fat_Grp.setAliqICMS(consulta.getString("AliqICMS"));
                fat_Grp.setICMSRet(consulta.getString("ICMSRet"));
                fat_Grp.setCFOP(consulta.getString("CFOP"));
                fat_Grp.setCFOPDesc(consulta.getString("CFOPDesc"));
                fat_Grp.setAliqISS(consulta.getString("AliqISS"));
                retorno.add(fat_Grp);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Fat_GrpDao.buscarFat_GrpList - " + e.getMessage() + "\r\n"
                    + " SELECT Fat_Grp.Codigo, Fat_Grp.CodFil, Fat_Grp.Descricao, FatISSGrp.Tipo, FatISSGrp.Descricao ISSGrpDesc, \n"
                    + " Municipios.Nome Municipio, Municipios.UF, FatISSGrp.CodMunic, FatISSGrp.ISSRet, FatISSGrp.AliqICMS, FatISSGrp.ICMSRet, \n"
                    + " Ht_NF.CFOP, FatCFOP.Descricao CFOPDesc, MunicCFOP.AliqISS \n"
                    + " FROM Fat_Grp \n"
                    + " left join FatISSGrp  on  FatISSGrp.Codigo   = Fat_Grp.AgrupISS \n"
                    + "                 left join Municipios on  Municipios.Codigo  = FatISSGrp.CodMunic \n"
                    + "                 left join HT_NF      on  HT_NF.Codigo       = Fat_Grp.CodHist \n"
                    + "                                      and HT_NF.CodFil       = Fat_Grp.CodFil  \n"
                    + "                 left join FatCFOP    on  FatCFOP.Codigo     = HT_NF.CFOP \n"
                    + "                 left Join MunicCFOP  on  MunicCFOP.CodMunic = FatISSGrp.CodMunic \n"
                    + "                                      and MunicCFOP.CFOP     = HT_NF.CFOP \n"
                    + " WHERE (Fat_Grp.Codigo like %" + query + "% OR Fat_Grp.Descricao like %" + query + "%) AND Fat_Grp.CodFil = " + codFil);
        }
    }
}
