/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Rh_Horas;
import SasDaos.Rh_HorasDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_horas {

    public static String CarregaHoras(String sCodFil, String sMatr, Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<Rh_Horas> list_Rh_hora;
            Rh_HorasDao oRh_Hora = new Rh_HorasDao();
            try {
                list_Rh_hora = oRh_Hora.getRh_Horas(sMatr, sCodFil, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar dados horas - " + e.getMessage());
            }
            if (list_Rh_hora.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>Rh_horas_2</resp>";//sem registros
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_Rh_hora.size() + "</nreg>";

            for (int i = 0; i < list_Rh_hora.size(); i++) {
                xml += "<rhhoras>";
                xml += "<matr>" + list_Rh_hora.get(i).getMatr() + "</matr>";
                xml += "<codfil>" + list_Rh_hora.get(i).getCodFil() + "</codfil>";
                xml += "<semana>" + list_Rh_hora.get(i).getSemana() + "</semana>";
                xml += "<data>" + list_Rh_hora.get(i).getData() + "</data>";
                xml += "<diasem>" + list_Rh_hora.get(i).getDiaSem() + "</diasem>";
                xml += "<hora1>" + list_Rh_hora.get(i).getHora1() + "</hora1>";
                xml += "<hora2>" + list_Rh_hora.get(i).getHora2() + "</hora2>";
                xml += "<hora3>" + list_Rh_hora.get(i).getHora3() + "</hora3>";
                xml += "<hora4>" + list_Rh_hora.get(i).getHora4() + "</hora4>";
                xml += "<hora5>" + list_Rh_hora.get(i).getHora5() + "</hora5>";
                xml += "<hora6>" + list_Rh_hora.get(i).getHora6() + "</hora6>";
                xml += "<hsdiurnas>" + list_Rh_hora.get(i).getHsDiurnas() + "</hsdiurnas>";
                xml += "<hsnoturnas>" + list_Rh_hora.get(i).getHsNoturnas() + "</hsnoturnas>";
                xml += "<he50>" + list_Rh_hora.get(i).getHE50() + "</he50>";
                xml += "<he100>" + list_Rh_hora.get(i).getHE100() + "</he100>";
                xml += "<he50i>" + list_Rh_hora.get(i).getHE50i() + "</he50i>";
                xml += "<he100i>" + list_Rh_hora.get(i).getHE100i() + "</he100i>";
                xml += "<hsdiamin>" + list_Rh_hora.get(i).getHsDiaMin() + "</hsdiamin>";
                xml += "<hedia>" + list_Rh_hora.get(i).getHEDia() + "</hedia>";
                xml += "<hsabono>" + list_Rh_hora.get(i).getHsAbono() + "</hsabono>";
                xml += "<hsprojecao>" + list_Rh_hora.get(i).getHsProjecao() + "</hsprojecao>";
                xml += "<hsexceden>" + list_Rh_hora.get(i).getHsExceden() + "</hsexceden>";
                xml += "<hsescala>" + list_Rh_hora.get(i).getHsEscala() + "</hsescala>";
                xml += "<hsintraj>" + list_Rh_hora.get(i).getHsIntraJ() + "</hsintraj>";
                xml += "<situacao>" + list_Rh_hora.get(i).getSituacao() + "</situacao>";
                xml += "<secao>" + list_Rh_hora.get(i).getSecao() + "</secao>";
                xml += "<operador>" + list_Rh_hora.get(i).getOperador() + "</operador>";
                xml += "<dtalter>" + list_Rh_hora.get(i).getDt_Alter() + "</dtalter>";
                xml += "<hralter>" + list_Rh_hora.get(i).getHr_Alter() + "</hralter>";
                xml += "</rhhoras>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
