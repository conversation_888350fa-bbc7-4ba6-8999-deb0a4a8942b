package SasLibrary;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Psthstqst;
import SasBeans.TbVal;
import SasBeans.TmktDetPst;
import SasBeansCompostas.PstHstQstTbValFuncion;
import SasBeansCompostas.TmktDetPstPstServClientes;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.GPS;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Supervisao {

    public void updateFiltroWeb(String sequencia, boolean filtroWeb, Persistencia persistencia) throws Exception {
        try {
            String sql = " update tmktdetpst set filtroWeb = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(filtroWeb ? "1" : "0");
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("Supervisao.updateFiltroWeb - " + e.getMessage() + "\r\n"
                    + "update tmktdetpst set filtroWeb = " + (filtroWeb ? "1" : "0")
                    + " where sequencia = " + sequencia);
        }
    }

    /**
     * Busca uma supervisao pelo seu número de sequencia
     *
     * @param sequencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes obterSupervisao(String sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = " select pessoa.nome, tmktdetpst.codfil, tmktdetpst.TipoCont, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + " convert(varchar, tmktdetpst.data, 112) data, tmktdetpst.hora, clientes.nred, pstserv.local, "
                    + " tmktdetpst.historico, convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, "
                    + " tmktdetpst.situacao, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude latcli, clientes.longitude longcli, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas,"
                    + " clientes.codigo, filtroWeb "
                    + " from tmktdetpst"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa "
                    + " where tmktdetpst.sequencia = ? "
                    + " group by pessoa.nome, tmktdetpst.codfil, tmktdetpst.TipoCont, tmktdetpst.data, tmktdetpst.hora, clientes.nred,"
                    + " pstserv.local, tmktdetpst.historico, convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude, clientes.longitude, tmktdetpst.sequencia, tmktdetpst.secao, clientes.codigo, filtroWeb "
                    + " order by tmktdetpst.sequencia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.select();
            TmktDetPstPstServClientes retorno = new TmktDetPstPstServClientes();
            if (consulta.Proximo()) {
                String fSeq, nome, caminho;
                TmktDetPst tm = new TmktDetPst();
                tm.setCodFil(consulta.getString("codfil"));
                tm.setSequencia(consulta.getString("sequencia"));
                tm.setData(consulta.getString("data"));
                tm.setHora(consulta.getString("hora"));
                tm.setHistorico(consulta.getString("historico"));
                tm.setDetalhes(consulta.getString("detalhes"));
                tm.setQtdeFotos(consulta.getInt("qtdefotos"));
                tm.setSituacao(consulta.getString("situacao"));
                tm.setLatitude(consulta.getString("latitude"));
                tm.setLongitude(consulta.getString("longitude"));
                tm.setPrecisao(consulta.getString("precisao"));
                tm.setOperador(consulta.getString("nome"));
                tm.setSecao(consulta.getString("secao"));
                tm.setFiltroWeb(consulta.getString("filtroWeb").equals("1"));

                PstServ pst = new PstServ();
                pst.setLocal(consulta.getString("local"));
                pst.setCodFil(consulta.getString("codfil"));
                pst.setSecao(consulta.getString("secao"));
                pst.setCodCli(consulta.getString("codigo"));

                Clientes cli = new Clientes();
                cli.setCodFil(consulta.getString("codfil"));
                cli.setCodigo(consulta.getString("codigo"));
                cli.setNRed(consulta.getString("nred"));
                cli.setLatitude(consulta.getString("latcli"));
                cli.setLongitude(consulta.getString("longcli"));
                try {
                    retorno.setDistsup(GPS.distanciaCoordenadas(consulta.getString("latcli"), consulta.getString("longcli"),
                            consulta.getString("latitude"), consulta.getString("longitude")).doubleValue());
                } catch (Exception e) {
                    retorno.setDistsup(0.0);
                }
                retorno.setTmktdetpst(tm);
                retorno.setPstserv(pst);
                retorno.setClientes(cli);
                retorno.setQtdEntrevistas(consulta.getInt("qtdentrevistas"));
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    retorno.getFotos().add(nome);
                }
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Supervisao.obterSupervisao - " + e.getMessage() + "\r\n "
                    + " select tmktdetpst.codfil, tmktdetpst.TipoCont, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + " convert(varchar, tmktdetpst.data, 112) data, tmktdetpst.hora, clientes.nred, pstserv.local, "
                    + " tmktdetpst.historico, convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, "
                    + " tmktdetpst.situacao, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude latcli, clientes.longitude longcli, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas "
                    + " from tmktdetpst"
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " where tmktdetpst.sequencia = " + sequencia
                    + " group by tmktdetpst.codfil, tmktdetpst.TipoCont, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, "
                    + " tmktdetpst.historico, convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude, clientes.longitude, tmktdetpst.sequencia, tmktdetpst.secao "
                    + " order by tmktdetpst.sequencia desc ");
        }
    }

    /**
     * Busca todos os relatórios de uma filial em um período. Se codPessoa for
     * uma string vazia, busca para todos os codPessoa.
     *
     * @param dataInicio
     * @param dataFim
     * @param codfil
     * @param codPessoa
     * @param secao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> obterTodosRelatorios(String dataInicio, String dataFim,
            String codfil, String codPessoa, String secao, Persistencia persistencia) throws Exception {
        try {
            List<TmktDetPstPstServClientes> retorno = new ArrayList<>();
            String sql = " select pessoa.nome, tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, pstserv.local, "
                    + "  pstserv.secao, tmktdetpst.historico,  convert(varchar(max),tmktdetpst.detalhes) detalhes, "
                    + "    tmktdetpst.qtdefotos, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.sequencia, "
                    + " tmktdetpst.fotos, tmktdetpst.tipocont "
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa "
                    + " where tmktdetpst.data between ? and ? "
                    + "       and tmktdetpst.tipocont like 'R%' ";
            if (!codfil.equals("")) {
                sql += " and pstserv.codfil = ? ";
            }
            if (!codPessoa.equals("")) {
                sql += " and tmktdetpst.codpessoa = ? ";
            }
            if (!secao.equals("")) {
                sql += " and pstserv.secao = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            if (!codfil.equals("")) {
                consulta.setString(codfil);
            }
            if (!codPessoa.equals("")) {
                consulta.setString(codPessoa);
            }
            if (!secao.equals("")) {
                consulta.setString(secao);
            }
            consulta.select();
            TmktDetPstPstServClientes tmktDetPstPstServClientes;
            TmktDetPst tmktDetPst;
            PstServ pstServ;
            String fSeq, nome, caminho;
            while (consulta.Proximo()) {
                tmktDetPstPstServClientes = new TmktDetPstPstServClientes();
                tmktDetPst = new TmktDetPst();
                pstServ = new PstServ();
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                tmktDetPst.setCodFil(consulta.getString("codfil"));
                tmktDetPst.setSequencia(consulta.getString("sequencia"));
                tmktDetPst.setData(consulta.getString("data"));
                tmktDetPst.setHora(consulta.getString("hora"));
                tmktDetPst.setHistorico(consulta.getString("historico"));
                tmktDetPst.setDetalhes(consulta.getString("detalhes"));
                tmktDetPst.setQtdeFotos(consulta.getInt("qtdefotos"));
                tmktDetPst.setFotos(consulta.getString("fotos"));
                tmktDetPst.setLatitude(consulta.getString("latitude"));
                tmktDetPst.setLongitude(consulta.getString("longitude"));
                tmktDetPst.setTipoCont(consulta.getString("tipocont"));
                tmktDetPstPstServClientes.setFuncionario(consulta.getString("nome"));
                tmktDetPstPstServClientes.setTmktdetpst(tmktDetPst);
                tmktDetPstPstServClientes.setPstserv(pstServ);
                tmktDetPstPstServClientes.setFotos(new ArrayList<>());
                for (int i = 1; i <= tmktDetPst.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tmktDetPst.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tmktDetPst.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    tmktDetPstPstServClientes.getFotos().add(nome);
                }
                retorno.add(tmktDetPstPstServClientes);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Supervisao.obterTodosRelatorios - " + e.getMessage() + "\r\n "
                    + " select tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, pstserv.local, "
                    + "  pstserv.secao, tmktdetpst.historico,  convert(varchar(max),tmktdetpst.detalhes) detalhes, "
                    + "    tmktdetpst.qtdefotos, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.sequencia "
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " where tmktdetpst.data between " + dataInicio + " and " + dataFim
                    + "       and pstserv.codfil = " + codfil
                    + "       and tmktdetpst.tipocont = 'R' "
                    + " order by sequencia desc ");
        }
    }

    /**
     * Carrega as informações de um relatório
     *
     * @param sequencia
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes buscaRelatorio(String sequencia, String codfil, Persistencia persistencia) throws Exception {
        try {
            String sql = " select pessoa.nome, tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, pstserv.local, "
                    + " pstserv.secao, tmktdetpst.historico, convert(varchar(max),tmktdetpst.detalhes) detalhes, "
                    + " tmktdetpst.qtdefotos, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.sequencia "
                    + " from tmktdetpst "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa "
                    + " where tmktdetpst.sequencia = ? "
                    + " and pstserv.codfil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(codfil);
            consulta.select();
            TmktDetPstPstServClientes tmktDetPstPstServClientes = new TmktDetPstPstServClientes();
            TmktDetPst tmktDetPst;
            PstServ pstServ;
            String fSeq, nome, caminho;
            while (consulta.Proximo()) {
                tmktDetPst = new TmktDetPst();
                pstServ = new PstServ();
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                tmktDetPst.setCodFil(consulta.getString("codfil"));
                tmktDetPst.setSequencia(consulta.getString("sequencia"));
                tmktDetPst.setData(consulta.getString("data"));
                tmktDetPst.setHora(consulta.getString("hora"));
                tmktDetPst.setHistorico(consulta.getString("historico"));
                tmktDetPst.setDetalhes(consulta.getString("detalhes"));
                tmktDetPst.setQtdeFotos(consulta.getInt("qtdefotos"));
                tmktDetPst.setLatitude(consulta.getString("latitude"));
                tmktDetPst.setLongitude(consulta.getString("longitude"));
                tmktDetPstPstServClientes.setTmktdetpst(tmktDetPst);
                tmktDetPstPstServClientes.setPstserv(pstServ);
                tmktDetPstPstServClientes.setFotos(new ArrayList<>());
                for (int i = 1; i <= tmktDetPst.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tmktDetPst.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tmktDetPst.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    tmktDetPstPstServClientes.getFotos().add(nome);
                }
                tmktDetPstPstServClientes.setFuncionario(consulta.getString("nome"));
            }
            consulta.Close();
            return tmktDetPstPstServClientes;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    /**
     * Lista os relatórios
     *
     * @param codPessoa
     * @param secao
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> obterRelatorios(String codPessoa, String secao, String codfil, Persistencia persistencia) throws Exception {
        try {
            List<TmktDetPstPstServClientes> retorno = new ArrayList<>();
            String sql = " select top 10 tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, pstserv.local, "
                    + "  pstserv.secao, tmktdetpst.historico,  convert(varchar(max),tmktdetpst.detalhes) detalhes, "
                    + "    tmktdetpst.qtdefotos, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.sequencia "
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                 and pstserv.codfil = tmktdetpst.codfil "
                    + " where pstserv.secao = ? "
                    + " and tmktdetpst.codpessoa = ? "
                    + " and pstserv.codfil = ? "
                    + " order by sequencia desc ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codPessoa);
            consulta.setString(codfil);
            consulta.select();
            TmktDetPstPstServClientes tmktDetPstPstServClientes;
            TmktDetPst tmktDetPst;
            PstServ pstServ;
            String fSeq, nome, caminho;
            while (consulta.Proximo()) {
                tmktDetPstPstServClientes = new TmktDetPstPstServClientes();
                tmktDetPst = new TmktDetPst();
                pstServ = new PstServ();
                pstServ.setSecao(consulta.getString("secao"));
                pstServ.setLocal(consulta.getString("local"));
                tmktDetPst.setCodFil(consulta.getString("codfil"));
                tmktDetPst.setSequencia(consulta.getString("sequencia"));
                tmktDetPst.setData(consulta.getString("data"));
                tmktDetPst.setHora(consulta.getString("hora"));
                tmktDetPst.setHistorico(consulta.getString("historico"));
                tmktDetPst.setDetalhes(consulta.getString("detalhes"));
                tmktDetPst.setQtdeFotos(consulta.getInt("qtdefotos"));
                tmktDetPst.setLatitude(consulta.getString("latitude"));
                tmktDetPst.setLongitude(consulta.getString("longitude"));
                tmktDetPstPstServClientes.setTmktdetpst(tmktDetPst);
                tmktDetPstPstServClientes.setPstserv(pstServ);
                tmktDetPstPstServClientes.setFotos(new ArrayList<>());
                for (int i = 1; i <= tmktDetPst.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tmktDetPst.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tmktDetPst.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    tmktDetPstPstServClientes.getFotos().add(nome);
                }
                retorno.add(tmktDetPstPstServClientes);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    /**
     * Listagem de supervisão
     *
     * @param dtinicio - data incial
     * @param dtfinal - data final
     * @param codfil - codigo da filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> BuscaSupervisaoPeriodo(LocalDate dtinicio, LocalDate dtfinal,
            BigDecimal codfil, Persistencia persistencia)
            throws Exception {
        try {
            GPS gps = new GPS();
            List<TmktDetPstPstServClientes> retorno = new ArrayList();
            String sql = "select tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.operador, "
                    + " pstserv.secao, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, tmktdetpst.historico, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas, "
                    + " convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, "
                    + " clientes.latitude latcli, clientes.longitude longcli, clientes.nred, "
                    + " clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + " tmktdetpst.sequencia"
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                  and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.data between ? and ? "
                    + " and tmktdetpst.codfil = ?"
                    + " group by tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + "    tmktdetpst.operador, "
                    + "    tmktdetpst.sequencia, tmktdetpst.secao, convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, "
                    + "    tmktdetpst.situacao, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "    clientes.latitude, clientes.longitude, pstserv.Secao, pstserv.TipoPosto, ctritens.Descricao, clientes.nred, "
                    + "    clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + "    cast(tmktdetpst.Detalhes as varchar(max))"
                    + " order by data desc, hora desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(DataAtual.LC2Date(dtinicio));
            consult.setDate(DataAtual.LC2Date(dtfinal));
            consult.setBigDecimal(codfil);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                pst.setSecao(consult.getString("secao"));
                pst.setLocal(consult.getString("local"));
                tm.setOperador(consult.getString("operador"));
                tm.setCodFil(consult.getString("codfil"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem de supervisão de um posto por período
     *
     * @param posto
     * @param dataSupervisao1
     * @param dataSupervisao2
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> BuscaSupervisaoPeriodoPosto(PstServ posto, String dataSupervisao1, String dataSupervisao2,
            Persistencia persistencia)
            throws Exception {
        try {
            GPS gps = new GPS();
            List<TmktDetPstPstServClientes> retorno = new ArrayList();
            String sql = " select convert(varchar, tmktdetpst.data, 112) data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, "
                    + " pstserv.local, tmktdetpst.historico, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + " convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude latcli, clientes.longitude longcli, "
                    + " tmktdetpst.sequencia, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas "
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                  and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " where tmktdetpst.codfil = ? and tmktdetpst.data between ? and ? "
                    + " and tmktdetpst.secao = ? and tmktdetpst.tipocont = 'V' "
                    + " group by tmktdetpst.data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + " tmktdetpst.sequencia, tmktdetpst.secao, "
                    + " convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao,"
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude, clientes.longitude "
                    + " order by data desc, hora desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(posto.getCodFil());
            consult.setString(dataSupervisao1);
            consult.setString(dataSupervisao2);
            consult.setString(posto.getSecao());
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                tm.setSequencia(consult.getString("sequencia"));
                tm.setCodFil(consult.getString("codfil"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                tm.setOperador(consult.getString("operador"));
                tm.setSecao(consult.getString("secao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    /**
     * Lista todos os relatórios por data de um posto.
     *
     * @param codfil
     * @param secao
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> listaRelatoriosPosto(String codfil, String secao, String data, Persistencia persistencia) throws Exception {
        try {
            GPS gps = new GPS();
            List<TmktDetPstPstServClientes> retorno = new ArrayList();
            String sql = " select tmktdetpst.data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, "
                    + "     clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + "     pstserv.local, tmktdetpst.historico, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + "     convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + "     tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "     clientes.latitude latcli, clientes.longitude longcli, tmktdetpst.sequencia, "
                    + "     pstserv.tipoposto, substring(ctritens.descricao,1,25) as tipopostodesc"
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + "    left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                       and ctritens.contrato = pstserv.contrato "
                    + "                       and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.codfil = ? "
                    + "    and tmktdetpst.secao = ?"
                    + "     and tmktdetpst.data = ? "
                    + " order by data desc, hora desc ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(secao);
            consult.setString(data);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                tm.setSequencia(consult.getString("sequencia"));
                tm.setCodFil(consult.getString("codfil"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                pst.setLocal(consult.getString("local"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes").replace("\\N", "\n"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                tm.setOperador(consult.getString("operador"));
                tm.setSecao(consult.getString("secao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Supervisao.listaRelatoriosPosto - " + e.getMessage() + "\r\n"
                    + " select tmktdetpst.data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, "
                    + "     pstserv.local, tmktdetpst.historico, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + "     convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + "     tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "     clientes.latitude latcli, clientes.longitude longcli, tmktdetpst.sequencia"
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.codfil = " + codfil
                    + "    and tmktdetpst.secao = " + secao
                    + "     and tmktdetpst.data = " + data
                    + " order by data desc, hora desc ");
        }
    }

    /**
     * Lista todos os relatórios por data de um posto.
     *
     * @param codfil
     * @param secao
     * @param data1
     * @param data2
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> listaRelatoriosPosto(String codfil, String secao, String data1,
            String data2, Persistencia persistencia) throws Exception {
        try {
            GPS gps = new GPS();
            List<TmktDetPstPstServClientes> retorno = new ArrayList();
            String sql = " select pessoa.nome funcionario, tmktdetpst.data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, "
                    + "     clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + "     pstserv.local, tmktdetpst.historico, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + "     convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + "     tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "     clientes.latitude latcli, clientes.longitude longcli, tmktdetpst.sequencia, "
                    + "     pstserv.tipoposto, substring(ctritens.descricao,1,25) as tipopostodesc"
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + "    left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                       and ctritens.contrato = pstserv.contrato "
                    + "                       and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " left join pessoa on pessoa.codigo = tmktdetpst.codpessoa "
                    + " where tmktdetpst.codfil = ? "
                    + "    and tmktdetpst.secao = ? "
                    + "     and tmktdetpst.data between ? and ? "
                    + "     and (tmktdetpst.tipocont = 'R' OR tmktdetpst.tipocont = 'RS') "
                    + " order by data desc, hora desc ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(secao);
            consult.setString(data1);
            consult.setString(data2);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                tm.setSequencia(consult.getString("sequencia"));
                tm.setCodFil(consult.getString("codfil"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                pst.setLocal(consult.getString("local"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes").replace("\\N", "\n"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                tm.setOperador(consult.getString("operador"));
                tm.setSecao(consult.getString("secao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                sup.setFuncionario(consult.getString("funcionario"));
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Supervisao.listaRelatoriosPosto - " + e.getMessage() + "\r\n"
                    + " select tmktdetpst.data, tmktdetpst.hora, tmktdetpst.codfil, clientes.nred, "
                    + "     pstserv.local, tmktdetpst.historico, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + "     convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + "     tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "     clientes.latitude latcli, clientes.longitude longcli, tmktdetpst.sequencia"
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                   and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.codfil = " + codfil
                    + "    and tmktdetpst.secao = " + secao
                    + "     and tmktdetpst.data between " + data1 + " and " + data2
                    + "     and tmktdetpst.tipocont = 'R' "
                    + " order by data desc, hora desc ");
        }
    }

    /**
     * Busca de uma supervisão
     *
     * @param supervisao - parametros que devem ser preenchidos: sequencia -
     * numero da supervisão, codfil - codigo da filial, e secao - codigo do
     * posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes BuscaSupervisao(TmktDetPstPstServClientes supervisao, Persistencia persistencia)
            throws Exception {
        try {
            GPS gps = new GPS();
            String sql = "select tmktdetpst.codfil, tmktdetpst.sequencia, tmktdetpst.secao, convert(varchar, tmktdetpst.data, 112) "
                    + " data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + " convert(varchar,tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude latcli, clientes.longitude longcli, isnull(count(distinct psthstqst.matr),0) "
                    + " as qtdentrevistas "
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + " and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + " and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " where tmktdetpst.codfil = ? "
                    + " and tmktdetpst.secao = ? "
                    + " and tmktdetpst.sequencia = ? "
                    + " group by tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local,"
                    + " tmktdetpst.historico, convert(varchar,tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude, clientes.longitude, tmktdetpst.sequencia, tmktdetpst.secao"
                    + " order by tmktdetpst.sequencia desc ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(supervisao.getTmktdetpst().getCodFil());
            consult.setString(supervisao.getTmktdetpst().getSecao());
            consult.setBigDecimal(supervisao.getTmktdetpst().getSequencia());
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                tm.setCodFil(consult.getString("codfil"));
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                tm.setOperador(consult.getString("operador"));
                tm.setSecao(consult.getString("secao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                return sup;
            }
            return null;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    public List<String> BuscarSequenciasSupervisao(TmktDetPstPstServClientes supervisao, Persistencia persistencia) throws Exception {
        try {
            List<String> retorno = new ArrayList<>();
            String sql = " select tmktdetpst.sequencia "
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + " and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + " and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " where tmktdetpst.codfil = ? "
                    + " and tmktdetpst.secao = ? "
                    + " group by tmktdetpst.sequencia"
                    + " order by tmktdetpst.sequencia";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(supervisao.getTmktdetpst().getCodFil());
            consult.setString(supervisao.getTmktdetpst().getSecao());
            consult.select();
            String seq;
            while (consult.Proximo()) {
                seq = consult.getString("sequencia");
                retorno.add(seq);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca da supervisão anterior a que está sendo exibida na tela
     *
     * @param sequencia - numero da supervisão
     * @param codfil - codigo da filial
     * @param secao - codigo do posto
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public TmktDetPstPstServClientes BuscaSupervisaoSeguintePosto(BigDecimal codfil, String secao, BigDecimal sequencia, Persistencia persistencia)
            throws Exception {
        try {
            GPS gps = new GPS();
            String sql = "select top 1 isnull(tmktdetpst.sequencia,0) sequencia, tmktdetpst.secao, convert(varchar, tmktdetpst.data, 112) "
                    + " data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + " convert(varchar,tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude latcli, clientes.longitude longcli, isnull(count(distinct psthstqst.matr),0) "
                    + " as qtdentrevistas "
                    + " from tmktdetpst "
                    + " left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + " and pstserv.codfil = tmktdetpst.codfil "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + " and clientes.codfil = pstserv.codfil "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " where tmktdetpst.codfil = ? "
                    + " and tmktdetpst.secao = ? "
                    + " and tmktdetpst.sequencia > ? "
                    + " group by tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + " convert(varchar,tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + " clientes.latitude, clientes.longitude, tmktdetpst.sequencia, tmktdetpst.secao"
                    + " order by tmktdetpst.sequencia";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codfil);
            consult.setString(secao);
            consult.setBigDecimal(sequencia);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                tm.setOperador(consult.getString("operador"));
                tm.setSecao(consult.getString("secao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                return sup;
            }
            return null;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    /**
     * Busca questionários de uma supervisão
     *
     * @param Sequencia - sequencia de supervisao
     * @param Data - Data da supervisão
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<PstHstQstTbValFuncion> ListaQuestionarioSupervisao(BigDecimal Sequencia, String Data, Persistencia persistencia) throws Exception {
        try {
            List<PstHstQstTbValFuncion> retorno = new ArrayList();
            String sql = "select sup.sequencia, sup.matr, "
                    + " funcion.nome,"
                    + " tbval.descricao, sup.resposta, sup.detalhes,"
                    + " tbval.tabela, tbval.codigo, sup.qtdefotos  "
                    + " from psthstqst as sup"
                    + " left join tbval on tbval.tabela = 312"
                    + "                and tbval.codigo = sup.codquestao"
                    + " left join funcion on funcion.matr = sup.matr"
                    + " where sup.sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(Sequencia);
            consult.select();
            PstHstQstTbValFuncion supqst;
            Psthstqst qst;
            TbVal tb;
            Funcion funcion;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                supqst = new PstHstQstTbValFuncion();
                qst = new Psthstqst();
                tb = new TbVal();
                funcion = new Funcion();
                qst.setSequencia(consult.getString("sequencia"));
                qst.setMatr(consult.getString("matr"));
                funcion.setMatr(consult.getString("matr"));
                funcion.setNome(consult.getString("nome"));
                tb.setTabela(consult.getInt("tabela"));
                tb.setCodigo(consult.getInt("codigo"));
                tb.setDescricao(consult.getString("descricao"));
                qst.setResposta(consult.getString("resposta"));
                qst.setDetalhes(consult.getString("detalhes"));
                qst.setQtdeFotos(consult.getInt("qtdefotos"));
                supqst.setPsthstqst(qst);
                supqst.setTbval(tb);
                supqst.setFuncion(funcion);
                supqst.setQtdefotos(qst.getQtdeFotos());
                for (int i = 1; i <= qst.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += Data + "/";
                    fSeq = FuncoesString.preencheCom(qst.getSequencia().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + funcion.getMatr().toPlainString().replace(".0", "") + "_" + i + ".jpg";
                    supqst.getEndfotos().add(nome);
                }
                retorno.add(supqst);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar questionario - \r\n" + e.getMessage());
        }
    }

    public void AtualizarSupervisao(TmktDetPstPstServClientes supervisao, Persistencia persistencia) throws Exception {
        try {
            String sql = "update tmktdetpst set situacao = ?"
                    + " where codfil = ? "
                    + " and sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(supervisao.getTmktdetpst().getSituacao());
            consulta.setBigDecimal(supervisao.getTmktdetpst().getCodFil());
            consulta.setString(supervisao.getTmktdetpst().getSequencia().toPlainString());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao atualizar supervisão - \r\n" + e.getMessage());
        }
    }

    /**
     * Lista as supervisões vinculadas ao cliente
     *
     * @param codfil Código da filial
     * @param codcli Código do cliente
     * @param data Data da supervisão
     * @param persistencia Conexão com o banco
     * @return Lista de Supervisões
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> BuscaSupervisaoCliente(BigDecimal codfil, String codcli, String data, Persistencia persistencia) throws Exception {
        List<TmktDetPstPstServClientes> retorno = new ArrayList<>();

        try {
            GPS gps = new GPS();

            String sql = "select tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.operador, "
                    + " pstserv.secao, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, tmktdetpst.historico, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas, "
                    + " convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, "
                    + " clientes.latitude latcli, clientes.longitude longcli, clientes.nred, "
                    + " clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + " tmktdetpst.sequencia"
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                  and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.data = ? "
                    + " and tmktdetpst.codfil = ? "
                    + " and pstserv.codcli = ? and tmktdetpst.tipocont = 'V' "
                    + " group by tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + "    tmktdetpst.operador, "
                    + "    tmktdetpst.sequencia, tmktdetpst.secao, convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, "
                    + "    tmktdetpst.situacao, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "    clientes.latitude, clientes.longitude, pstserv.Secao, pstserv.TipoPosto, ctritens.Descricao, clientes.nred, "
                    + "    clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + "    cast(tmktdetpst.Detalhes as varchar(max))"
                    + " order by data desc, hora desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(data);
            consult.setBigDecimal(codfil);
            consult.setString(codcli);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                pst.setSecao(consult.getString("secao"));
                pst.setLocal(consult.getString("local"));
                tm.setOperador(consult.getString("operador"));
                tm.setCodFil(consult.getString("codfil"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }

    }

    public List<TmktDetPstPstServClientes> PesquisarSupervisoes(LocalDate dtinicio, LocalDate dtfinal,
            TmktDetPstPstServClientes supervisao, Persistencia persistencia)
            throws Exception {
        try {
            GPS gps = new GPS();
            List<TmktDetPstPstServClientes> retorno = new ArrayList();
            String sql = "select tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.operador, "
                    + " pstserv.secao, pstserv.tipoposto, substring(ctritens.descricao,1,25) "
                    + " as tipopostodesc, tmktdetpst.historico, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas, "
                    + " convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + " tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, "
                    + " clientes.latitude latcli, clientes.longitude longcli, clientes.nred, "
                    + " clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + " tmktdetpst.sequencia"
                    + " from tmktdetpst "
                    + " left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + " inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                  and pstserv.codfil = tmktdetpst.codfil "
                    + " left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                    and ctritens.contrato = pstserv.contrato "
                    + "                    and ctritens.tipoposto = pstserv.tipoposto "
                    + " left join clientes on clientes.codigo = pstserv.codcli "
                    + "                  and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.codfil is not null ";
            if (null != dtinicio && !dtinicio.equals("") && null != dtfinal && !dtfinal.equals("")) {
                sql = sql + " and tmktdetpst.data between ? and ?";
            }
            if (null != supervisao.getTmktdetpst().getCodFil() && !supervisao.getTmktdetpst().getCodFil().equals(new BigDecimal("-1"))) {
                sql = sql + " and tmktdetpst.codfil = ?";
            }
            sql = sql + " group by tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local, tmktdetpst.historico, "
                    + "    tmktdetpst.operador, "
                    + "    tmktdetpst.sequencia, tmktdetpst.secao, convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, "
                    + "    tmktdetpst.situacao, tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador, "
                    + "    clientes.latitude, clientes.longitude, pstserv.Secao, pstserv.TipoPosto, ctritens.Descricao, clientes.nred, "
                    + "    clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, clientes.cep, "
                    + "    cast(tmktdetpst.Detalhes as varchar(max))"
                    + " order by tmktdetpst.data desc";
            Consulta consult = new Consulta(sql, persistencia);
            if (null != dtinicio && !dtinicio.equals("") && null != dtfinal && !dtfinal.equals("")) {
                consult.setDate(DataAtual.LC2Date(dtinicio));
                consult.setDate(DataAtual.LC2Date(dtfinal));
            }
            if (null != supervisao.getTmktdetpst().getCodFil() && !supervisao.getTmktdetpst().getCodFil().equals(new BigDecimal("-1"))) {
                consult.setBigDecimal(supervisao.getTmktdetpst().getCodFil());
            }
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                pst.setSecao(consult.getString("secao"));
                pst.setLocal(consult.getString("local"));
                tm.setOperador(consult.getString("operador"));
                tm.setCodFil(consult.getString("codfil"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar supervisoes - \r\n" + e.getMessage());
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de supervisoes no banco
     *
     * @param filtros filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalSupervisoesMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT count(*) total"
                    + "    from tmktdetpst"
                    + "    left join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                     and pstserv.codfil = tmktdetpst.codfil "
                    + "    left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                       and ctritens.contrato = pstserv.contrato "
                    + "                       and ctritens.tipoposto = pstserv.tipoposto "
                    + "    left join clientes on clientes.codigo = pstserv.codcli "
                    + "                     and clientes.codfil = pstserv.codfil "
                    + " where tmktdetpst.codfil in (select filiais.codfil"
                    + "                               from saspw "
                    + "                               inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                               inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                               inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                               where saspw.codpessoa = ? and paramet.path = ?) AND";
            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " tmktdetpst.sequencia IS NOT null ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar supervisoes - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de supervisoes para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<TmktDetPstPstServClientes> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<TmktDetPstPstServClientes> retorno = new ArrayList();
        GPS gps = new GPS();
        try {
            String sql = "SELECT  *  FROM"
                    + "    ( SELECT    ROW_NUMBER() OVER ( ORDER BY  tmktdetpst.data desc,  tmktdetpst.hora desc  ) AS RowNum,"
                    + "        tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, pstserv.local,"
                    + "        tmktdetpst.operador,  pstserv.secao, pstserv.tipoposto, substring(ctritens.descricao,1,25) as tipopostodesc,"
                    + "        tmktdetpst.historico, isnull(count(distinct psthstqst.matr),0) as qtdentrevistas,"
                    + "        convert(varchar(max),tmktdetpst.detalhes) detalhes, tmktdetpst.qtdefotos, tmktdetpst.situacao,"
                    + "        tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao,  clientes.latitude latcli,"
                    + "        clientes.longitude longcli, clientes.nred,  clientes.nome, clientes.ende, clientes.bairro,"
                    + "        clientes.cidade, clientes.estado, clientes.cep,  tmktdetpst.sequencia"
                    + "    from tmktdetpst"
                    + "    left join psthstqst on psthstqst.sequencia = tmktdetpst.sequencia "
                    + "    inner join pstserv on pstserv.secao = tmktdetpst.secao "
                    + "                     and pstserv.codfil = tmktdetpst.codfil "
                    + "    left join ctritens on  ctritens.codfil = pstserv.codfil "
                    + "                       and ctritens.contrato = pstserv.contrato "
                    + "                       and ctritens.tipoposto = pstserv.tipoposto "
                    + "    left join clientes on clientes.codigo = pstserv.codcli "
                    + "                     and clientes.codfil = pstserv.codfil "
                    + "     where tmktdetpst.tipocont = 'V' and "
                    + "     tmktdetpst.codfil in (select filiais.codfil"
                    + "                               from saspw "
                    + "                               inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                               inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                               inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                               where saspw.codpessoa = ? and paramet.path = ?) AND ";
            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " tmktdetpst.sequencia IS NOT null "
                    + " group by tmktdetpst.codfil, tmktdetpst.data, tmktdetpst.hora, clientes.nred, pstserv.local,"
                    + "      tmktdetpst.historico, tmktdetpst.operador, tmktdetpst.sequencia, tmktdetpst.secao, "
                    + "      convert(varchar(max),tmktdetpst.detalhes), tmktdetpst.qtdefotos, tmktdetpst.situacao, "
                    + "      tmktdetpst.latitude, tmktdetpst.longitude, tmktdetpst.precisao, tmktdetpst.operador,     "
                    + "      clientes.latitude, clientes.longitude, pstserv.Secao, pstserv.TipoPosto, ctritens.Descricao, "
                    + "      clientes.nred, clientes.nome, clientes.ende, clientes.bairro, clientes.cidade, clientes.estado, "
                    + "      clientes.cep, cast(tmktdetpst.Detalhes as varchar(max))) AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            TmktDetPstPstServClientes sup;
            TmktDetPst tm;
            PstServ pst;
            Clientes cli;
            Double clilat, clilong, suplat, suplong;
            String fSeq;
            String nome;
            String caminho;
            while (consult.Proximo()) {
                sup = new TmktDetPstPstServClientes();
                tm = new TmktDetPst();
                pst = new PstServ();
                cli = new Clientes();
                pst.setSecao(consult.getString("secao"));
                pst.setLocal(consult.getString("local"));
                tm.setOperador(consult.getString("operador"));
                tm.setCodFil(consult.getString("codfil"));
                pst.setTipoPosto(consult.getString("tipoposto"));
                pst.setTipoPostoDesc(consult.getString("tipopostodesc"));
                tm.setSequencia(consult.getString("sequencia"));
                tm.setData(consult.getString("data"));
                tm.setHora(consult.getString("hora"));
                cli.setNRed(consult.getString("nred"));
                pst.setLocal(consult.getString("local"));
                tm.setHistorico(consult.getString("historico"));
                tm.setDetalhes(consult.getString("detalhes"));
                tm.setQtdeFotos(consult.getInt("qtdefotos"));
                tm.setSituacao(consult.getString("situacao"));
                tm.setLatitude(consult.getString("latitude"));
                tm.setLongitude(consult.getString("longitude"));
                tm.setPrecisao(consult.getString("precisao"));
                cli.setLatitude(consult.getString("latcli"));
                cli.setLongitude(consult.getString("longcli"));
                cli.setNRed(consult.getString("nred"));
                cli.setNome(consult.getString("nome"));
                cli.setEnde(consult.getString("ende"));
                cli.setBairro(consult.getString("bairro"));
                cli.setCidade(consult.getString("cidade"));
                cli.setEstado(consult.getString("estado"));
                cli.setCEP(consult.getString("cep"));
                try {
                    clilat = Double.valueOf(consult.getString("latcli"));
                    clilong = Double.valueOf(consult.getString("longcli"));
                    suplat = Double.valueOf(consult.getString("latitude"));
                    suplong = Double.valueOf(consult.getString("longitude"));
                    sup.setDistsup(gps.distancia2pontos(clilat, 0, 0, suplat, 0, 0, clilong, 0, 0, suplong, 0, 0));
                } catch (Exception e) {
                    sup.setDistsup(0.0);
                }
                sup.setTmktdetpst(tm);
                sup.setPstserv(pst);
                sup.setClientes(cli);
                for (int i = 1; i <= tm.getQtdeFotos(); i++) {
                    caminho = "https://mobile.sasw.com.br:9091/satellite/fotos/" + persistencia.getEmpresa() + "/";
                    caminho += FuncoesString.RecortaString(tm.getData(), 0, 10).replaceAll("-", "") + "/";
                    fSeq = FuncoesString.preencheCom(tm.getSequencia().toPlainString().replace(".0", ""), "0", 8, 1);
                    nome = caminho + fSeq + "_" + i + ".jpg";
                    sup.getFotos().add(nome);
                }
                sup.setQtdEntrevistas(consult.getInt("qtdentrevistas"));
                retorno.add(sup);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de supervisoes - \r\n" + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
}
