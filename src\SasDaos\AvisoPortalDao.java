package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.AvisoPortal;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class AvisoPortalDao {

    String sql;

    /*
     * Busca avisos da tabela AvisoPortal com Flag
     * @param sCodfil  - Código filial
     * @param persistencia  - Conexão ao Banco
     * @return - Lista de registros
     * @throws Exception 
     */
    public AvisoPortal getAvisoPortalFag(String sCodfil, Persistencia persistencia) throws Exception {
        sql = "Select top 1 Assunto, Mensagem From AvisoPortal "
                + "where CodFil = ? and Flag <> *"
                + " Order by Sequencia desc";
        AvisoPortal oAvisoPortal = null;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodfil);
            consult.select();
            while (consult.Proximo()) {
                oAvisoPortal = new AvisoPortal();
                oAvisoPortal.setAssunto(consult.getString("Assunto"));
                oAvisoPortal.setMensagem(consult.getString("Mensagem"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("AvisoPortalDao.getAvisoPortalFag - " + e.getMessage() + "\r\n"
                    + "Select top 1 Assunto, Mensagem From AvisoPortal "
                    + "where CodFil = " + sCodfil + " and Flag <> *"
                    + " Order by Sequencia desc");
        }
        return oAvisoPortal;
    }

    /*
     * Busca avisos da tabela AvisoPortal
     * @param sCodfil  - Código filial
     * @param persistencia  - Conexão ao Banco
     * @return - Lista de registros
     * @throws Exception 
     */
//    public List<AvisoPortal> getAvisoPortal(String sCodfil, Persistencia persistencia) throws Exception {
//        sql = "Select top 1 Assunto, Mensagem From AvisoPortal where CodFil = ? Order by Sequencia desc";
//        AvisoPortal oAvisoPortal = null;
//        List<AvisoPortal> lAvisoPortal = new ArrayList<>();
//        try {
//            Consulta consult = new Consulta(sql, persistencia);
//            consult.setString(sCodfil);
//            consult.Executa();
//            while (consult.Proximo()) {
//                oAvisoPortal = new AvisoPortal();
//                oAvisoPortal.setAssunto(consult.getString("Assunto"));
//                oAvisoPortal.setMensagem(consult.getString("Mensagem"));
//                lAvisoPortal.add(oAvisoPortal);
//            }
//            consult.Close();
//        } catch (Exception e) {
//            throw new Exception("AvisoPortalDao. - " + e.getMessage());
//        }
//        return lAvisoPortal;
//    }
    public AvisoPortal getAvisoPortal(String sCodfil, Persistencia persistencia) throws Exception {
        sql = "Select top 1 Assunto, Mensagem From AvisoPortal where CodFil = ? Order by Sequencia desc";
        AvisoPortal oAvisoPortal = null;
//        List<AvisoPortal> lAvisoPortal = new ArrayList<>();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodfil);
            consult.select();
            while (consult.Proximo()) {
                oAvisoPortal = new AvisoPortal();
                oAvisoPortal.setAssunto(consult.getString("Assunto"));
                oAvisoPortal.setMensagem(consult.getString("Mensagem"));
//                lAvisoPortal.add(oAvisoPortal);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("AvisoPortalDao.getAvisoPortal - " + e.getMessage() + "\r\n"
                    + "Select top 1 Assunto, Mensagem From AvisoPortal where CodFil = " + sCodfil + " Order by Sequencia desc");
        }
        return oAvisoPortal;
    }

    /*
     * Busca avisos sobrecarca
     * @param sCodfil  - Código filial
     * @param persistencia  - Conexão ao Banco
     * @return - Lista de registros
     * @throws Exception 
     */
    public BigDecimal getAvisoPortal(Persistencia persistencia) throws Exception {
        sql = "Select isnull(Max(Sequencia),0) Sequencia from AvisoPortal";
        BigDecimal oAvisoPortal = BigDecimal.ZERO;
        try {
            Consulta smt = new Consulta(sql, persistencia);
            smt.select();
            while (smt.Proximo()) {
                oAvisoPortal = new BigDecimal(smt.getString("Sequencia"));
            }
            smt.Close();
        } catch (Exception e) {
            throw new Exception("AvisoPortalDao.getAvisoPortal - " + e.getMessage() + "\r\n"
                    + "Select isnull(Max(Sequencia),0) Sequencia from AvisoPortal");
        }
        return oAvisoPortal.add(BigDecimal.ONE);
    }

    /*
     * Insere Avisos
     * @param sSequencia  - Número sequêencia
     * @param sCodFil  - Código Filial
     * @param sAssunto  - Assunto
     * @param sMensagem  - Mensagem
     * @param sOperador  - 
     * @param sDt_Alter  - 
     * @param sHr_Alter  - 
     * @param persistencia  - Conexão ao Banco
     * @throws Exception 
     */
    public void insereAviso(String sSequencia, String sCodFil, String sAssunto, String sMensagem,
            String sOperador, String sDt_Alter, String sHr_Alter, Persistencia persistencia) throws Exception {
        try {
            sql = "Insert into AvisoPortal (SEQUENCIA,CODFIL, ASSUNTO, MENSAGEM, OPERADOR,DT_ALTER,HR_ALTER) "
                    + " VALUES(?,?,?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sSequencia);
            consulta.setString(sCodFil);
            consulta.setString(sAssunto);
            consulta.setString(sMensagem);
            consulta.setString(sOperador);
            consulta.setString(sDt_Alter);
            consulta.setString(sHr_Alter);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AvisoPortalDao.insereAviso - " + e.getMessage() + "\r\n"
                    + "Insert into AvisoPortal (SEQUENCIA,CODFIL, ASSUNTO, MENSAGEM, OPERADOR,DT_ALTER,HR_ALTER) "
                    + " VALUES(" + sSequencia + "," + sCodFil + "," + sAssunto + "," + sMensagem + "," + sOperador + "," + sDt_Alter + "," + sHr_Alter + ")");
        }
    }
}
