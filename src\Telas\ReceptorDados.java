/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package Telas;

/**
 *
 * <AUTHOR>
 */
public class ReceptorDados {

    private float Codfil;
    private float Matr;
    private float CodMovFP;
    private int Praca;

    public void setCodfil(String codfil) {
        Codfil = Float.valueOf(codfil);
    }

    public void setMatr(String matr) {
        Matr = Float.valueOf(matr);
    }

    public void setCodMovFP(String codmovfp) {
        CodMovFP = Float.valueOf(codmovfp);
    }

    public void setPraca(String praca) {
        Praca = Integer.valueOf(praca);
    }

    /**
     * Devolve o ultimo CodFil Pesquisado
     *
     * @return
     */
    public float getCodFil() {
        return Codfil;
    }

    /**
     * Devolve a ultima Matricula Pesquisada
     *
     * @return
     */
    public float getMatr() {
        return Matr;
    }

    /**
     * Devolve o ultimo Codigo de Movimentacao de Folha pesquisado
     *
     * @return
     */
    public float getCodMovFP() {
        return CodMovFP;
    }

    /**
     * Devolve a ultima praca pesquisada
     *
     * @return
     */
    public int getPraca() {
        return Praca;
    }
}
