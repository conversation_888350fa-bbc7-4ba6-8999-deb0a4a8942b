/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.PstServ;
import SasBeans.TmktDetPst;
import SasDaos.PstServDao;
import SasDaos.TmktDetPstDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PostoServ {

    public static String CarregaPstServ(String sTurno, String sCodcli, String sCodFil, String dataAtual, Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<PstServ> list_PstServ;
            PstServDao oPstServ = new PstServDao();

            try {
                list_PstServ = oPstServ.getPostos(persistencia, sCodFil, sCodcli, sTurno);

            } catch (Exception e) {
                throw new Exception("Falha ao carregar Postos de serviços - " + e.getMessage());
            }
            if (list_PstServ.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>PstServ_2</resp>";//sem postos de serviços
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_PstServ.size() + "</nreg>";

            for (int i = 0; i < list_PstServ.size(); i++) {
                xml += "<pstserv>";
                xml += "<secao>" + list_PstServ.get(i).getSecao() + "</secao>";
                xml += "<codfil>" + list_PstServ.get(i).getCodFil() + "</codfil>";
                xml += "<regional>" + list_PstServ.get(i).getRegional() + "</regional>";
                xml += "<local>" + list_PstServ.get(i).getLocal() + "</local>";
                xml += "<contrato>" + list_PstServ.get(i).getContrato() + "</contrato>";
                xml += "<tipoposto>" + list_PstServ.get(i).getTipoPosto() + "</tipoposto>";
                xml += "<codcli>" + list_PstServ.get(i).getCodCli() + "</codcli>";
                xml += "<posto>" + list_PstServ.get(i).getPosto() + "</posto>";
                xml += "<os>" + list_PstServ.get(i).getOS() + "</os>";

                //Verificar se tem secao
                PstServDao oSecao = new PstServDao();
                Boolean secao = oSecao.getSecao(list_PstServ.get(i).getSecao(), dataAtual, persistencia);
                if (secao) {
                    //se tem secao verificar se funcinario ja foram supervisionado
                    Boolean funcP = oPstServ.getFuncSuperv(sTurno, list_PstServ.get(i).getSecao(), list_PstServ.get(i).getCodFil().toString(), dataAtual, persistencia);
                    if (funcP) {
                        xml += "<pendencia>0</pendencia>";
                    } else {
                        xml += "<pendencia>1</pendencia>";
                    }
                    TmktDetPstDao oTmktDetPstsDao = new TmktDetPstDao();
                    List<TmktDetPst> lTmkDetPsts = oTmktDetPstsDao.getMotivosSuperv(sCodFil, list_PstServ.get(i).getSecao(), dataAtual, persistencia);
                    for (int ii = 0; ii < lTmkDetPsts.size(); ii++) {
                        xml += "<sequenciamotivos>" + lTmkDetPsts.get(ii).getSequencia() + "</sequenciamotivos>";
                        xml += "<historico>" + lTmkDetPsts.get(ii).getHistorico() + "</historico>";
                        xml += "<detalhes>" + lTmkDetPsts.get(ii).getDetalhes() + "</detalhes>";
                        xml += "<data>" + lTmkDetPsts.get(ii).getData() + "</data>";
                        xml += "<hora>" + lTmkDetPsts.get(ii).getHora() + "</hora>";
                        xml += "<longitude>" + lTmkDetPsts.get(ii).getLongitude() + "</longitude>";
                        xml += "<latitude>" + lTmkDetPsts.get(ii).getLatitude() + "</latitude>";
                        xml += "<precisao>" + lTmkDetPsts.get(ii).getPrecisao() + "</precisao>";
                    }
                }

                xml += "</pstserv>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
