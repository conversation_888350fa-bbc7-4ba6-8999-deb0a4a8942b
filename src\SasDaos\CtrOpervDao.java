package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CtrOperV;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CtrOpervDao {

    private String sql;

    /**
     * Busca registros da tabela CtrOperV
     *
     * @param Numr
     * @param ctrOperv - estrutura de dados da classe CtrOperV
     * @param persistencia - Conexão ao Banco
     * @throws java.lang.Exception
     */
//    public void adicionaCtrOperV(BigDecimal Numr, CtrOperV ctrOperv, Persistencia persistencia) throws Exception {
//        sql = " insert into CtrOperV (Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
//                + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
//                + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
//                + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
//                + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo) "
//                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
//        try {
//            PreparedStatement Mstat = persistencia.getState(sql);
//            Mstat.setBigDecimal(1, Numr);
//            Mstat.setBigDecimal(2, ctrOperv.getCodFil());
//            Mstat.setString(3, ctrOperv.getData());
//            Mstat.setString(4, ctrOperv.getPeriodo());
//            Mstat.setString(5, ctrOperv.getPosto());
//            Mstat.setString(6, ctrOperv.getMesario());
//            Mstat.setBigDecimal(7, ctrOperv.getFuncAus());
//            Mstat.setString(8, ctrOperv.getMotivo_Aus());
//            Mstat.setBigDecimal(9, ctrOperv.getFuncSubs());
//            Mstat.setString(10, ctrOperv.getMotivo_Subs());
//            Mstat.setString(11, ctrOperv.getHora_Extra());
//            Mstat.setBigDecimal(12, ctrOperv.getVR());
//            Mstat.setBigDecimal(13, ctrOperv.getVT());
//            Mstat.setBigDecimal(14, ctrOperv.getHospedagem());
//            Mstat.setString(15, ctrOperv.getHora1());
//            Mstat.setString(16, ctrOperv.getHora2());
//            Mstat.setString(17, ctrOperv.getHora3());
//            Mstat.setString(18, ctrOperv.getHora4());
//            Mstat.setBigDecimal(19, ctrOperv.getHEDiurna());
//            Mstat.setBigDecimal(20, ctrOperv.getHENoturna());
//            Mstat.setBigDecimal(21, ctrOperv.getNro_HE());
//            Mstat.setBigDecimal(22, ctrOperv.getHsItinere());
//            Mstat.setString(23, ctrOperv.getRefTipo2());
//            Mstat.setString(24, ctrOperv.getCodSrv());
//            Mstat.setString(25, ctrOperv.getNotas());
//            Mstat.setString(26, ctrOperv.getObs());
//            Mstat.setString(27, ctrOperv.getPedido());
//            Mstat.setInt(28, ctrOperv.getIntraj());
//            Mstat.setString(29, ctrOperv.getOperador());
//            Mstat.setString(30, ctrOperv.getDt_Alter());
//            Mstat.setString(31, ctrOperv.getHr_Alter());
//            Mstat.setString(32, ctrOperv.getOper_Excl());
//            Mstat.setString(33, ctrOperv.getDt_Excl());
//            Mstat.setString(34, ctrOperv.getHr_Excl());
//            Mstat.setString(35, ctrOperv.getFlag_Caixa());
//            Mstat.setString(36, ctrOperv.getFlag_NF());
//            Mstat.setString(37, ctrOperv.getFlag_Excl());
//            Mstat.setString(38, ctrOperv.getDt_Faturado());
//            Mstat.setBigDecimal(39, ctrOperv.getNumCalculo());
//            Mstat.execute();
//            Mstat.close();
//        } catch (Exception e) {
//            throw new Exception("CtrOperVDao.adicionaCtrOperV  - " + e.getMessage() + "\r\n"
//                    + "insert into CtrOperV (Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
//                    + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
//                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
//                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
//                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo) "
//                    + " values(" + Numr + "," + ctrOperv.getCodFil() + "," + ctrOperv.getData() + "," + ctrOperv.getPeriodo() + "," + ctrOperv.getPosto() + ","
//                    + ctrOperv.getMesario() + "," + ctrOperv.getFuncAus() + "," + ctrOperv.getMotivo_Aus() + "," + ctrOperv.getFuncSubs() + ","
//                    + ctrOperv.getMotivo_Subs() + "," + ctrOperv.getHora_Extra() + "," + ctrOperv.getVR() + "," + ctrOperv.getVT() + "," + ctrOperv.getHospedagem() + ","
//                    + ctrOperv.getHora1() + "," + ctrOperv.getHora2() + "," + ctrOperv.getHora3() + "," + ctrOperv.getHora4() + "," + ctrOperv.getHEDiurna() + ","
//                    + ctrOperv.getHENoturna() + "," + ctrOperv.getNro_HE() + "," + ctrOperv.getHsItinere() + "," + ctrOperv.getRefTipo2() + "," + ctrOperv.getCodSrv() + ","
//                    + ctrOperv.getNotas() + "," + ctrOperv.getObs() + "," + ctrOperv.getPedido() + "," + ctrOperv.getIntraj() + "," + ctrOperv.getOperador() + ","
//                    + ctrOperv.getDt_Alter() + "," + ctrOperv.getHr_Alter() + "," + ctrOperv.getOper_Excl() + "," + ctrOperv.getDt_Excl() + ","
//                    + ctrOperv.getHr_Excl() + "," + ctrOperv.getFlag_Caixa() + "," + ctrOperv.getFlag_NF() + "," + ctrOperv.getFlag_Excl() + ","
//                    + ctrOperv.getDt_Faturado() + "," + ctrOperv.getNumCalculo() + ")");
//        }
//
//    }
//       public void adicionaCtrOperv(BigDecimal Numr, CtrOperV ctrOperv, Persistencia persistencia) throws Exception {
//        String sql = " insert into CtrOperV (Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
//                + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
//                + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
//                + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
//                + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo) "
//                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
//        try {
//            Consulta consulta = new Consulta(sql, persistencia);
//           consulta.setBigDecimal(1, Numr);
//            consulta.setBigDecimal(2, ctrOperv.getCodFil());
//            consulta.setString(3, ctrOperv.getData());
//            consulta.setString(4, ctrOperv.getPeriodo());
//            consulta.setString(5, ctrOperv.getPosto());
//            consulta.setString(6, ctrOperv.getMesario());
//            consulta.setBigDecimal(7, ctrOperv.getFuncAus());
//            consulta.setString(8, ctrOperv.getMotivo_Aus());
//            consulta.setBigDecimal(9, ctrOperv.getFuncSubs());
//            consulta.setString(10, ctrOperv.getMotivo_Subs());
//            consulta.setString(11, ctrOperv.getHora_Extra());
//            consulta.setBigDecimal(12, ctrOperv.getVR());
//            consulta.setBigDecimal(13, ctrOperv.getVT());
//           consulta.setBigDecimal(14, ctrOperv.getHospedagem());
//            consulta.setString(15, ctrOperv.getHora1());
//            consulta.setString(16, ctrOperv.getHora2());
//            consulta.setString(17, ctrOperv.getHora3());
//            consulta.setString(18, ctrOperv.getHora4());
//            consulta.setBigDecimal(19, ctrOperv.getHEDiurna());
//            consulta.setBigDecimal(20, ctrOperv.getHENoturna());
//            consulta.setBigDecimal(21, ctrOperv.getNro_HE());
//            consulta.setBigDecimal(22, ctrOperv.getHsItinere());
//            consulta.setString(23, ctrOperv.getRefTipo2());
//            consulta.setString(24, ctrOperv.getCodSrv());
//            consulta.setString(25, ctrOperv.getNotas());
//            consulta.setString(26, ctrOperv.getObs());
//            consulta.setString(27, ctrOperv.getPedido());
//            consulta.setInt(28, ctrOperv.getIntraj());
//            consulta.setString(29, ctrOperv.getOperador());
//            consulta.setString(30, ctrOperv.getDt_Alter());
//            consulta.setString(31, ctrOperv.getHr_Alter());
//            consulta.setString(32, ctrOperv.getOper_Excl());
//            consulta.setString(33, ctrOperv.getDt_Excl());
//            consulta.setString(34, ctrOperv.getHr_Excl());
//            consulta.setString(35, ctrOperv.getFlag_Caixa());
//            consulta.setString(36, ctrOperv.getFlag_NF());
//           consulta.setString(37, ctrOperv.getFlag_Excl());
//            consulta.setString(38, ctrOperv.getDt_Faturado());
//            consulta.setBigDecimal(39, ctrOperv.getNumCalculo());
//            consulta.execute();
//            consulta.close();
    /**
     * Busca registros da tabela CtrOperV
     *
     * @param persistencia - Conexão ao Banco
     * @return - Lista de registros
     * @throws Exception
     */
    public CtrOperV selecionaCtrOperV(Persistencia persistencia) throws Exception {
        sql = "SELECT TOP 1 Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                + "Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV";
        CtrOperV ctrOperV = null;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                ctrOperV = new CtrOperV();
                ctrOperV.setNumero(consult.getString("Numero"));
                ctrOperV.setCodFil(consult.getBigDecimal("CodFil"));
                ctrOperV.setData(consult.getString("Data"));
                ctrOperV.setPeriodo(consult.getString("Periodo"));
                ctrOperV.setPosto(consult.getString("Posto"));
                ctrOperV.setMesario(consult.getString("Mesario"));
                ctrOperV.setFuncAus(consult.getString("FuncAus"));
                ctrOperV.setMotivo_Aus(consult.getString("Motivo_Aus"));
                ctrOperV.setFuncSubs(consult.getString("FuncSubs"));
                ctrOperV.setMotivo_Subs(consult.getString("Motivo_Subs"));
                ctrOperV.setHora_Extra(consult.getString("Hora_Extra"));
                ctrOperV.setVR(consult.getString("VR"));
                ctrOperV.setVT(consult.getString("VT"));
                ctrOperV.setHospedagem(consult.getString("Hospedagem"));
                ctrOperV.setHora1(consult.getString("Hora1"));
                ctrOperV.setHora2(consult.getString("Hora2"));
                ctrOperV.setHora3(consult.getString("Hora3"));
                ctrOperV.setHora4(consult.getString("Hora4"));
                ctrOperV.setHEDiurna(consult.getString("HEDiurna"));
                ctrOperV.setHENoturna(consult.getString("HENoturna"));
                ctrOperV.setHsItinere(consult.getString("HsItinere"));
                ctrOperV.setRefTipo2(consult.getString("RefTipo2"));
                ctrOperV.setCodSrv(consult.getString("CodSrv"));
                ctrOperV.setNotas(consult.getString("Notas"));
                ctrOperV.setObs(consult.getString("Obs"));
                ctrOperV.setPedido(consult.getString("Pedido"));
                ctrOperV.setIntraj(consult.getString("Intraj"));
                ctrOperV.setDt_Alter(consult.getString("Dt_Alter"));
                ctrOperV.setHr_Alter(consult.getString("Hr_Alter"));
                ctrOperV.setOper_Excl(consult.getString("Oper_Excl"));
                ctrOperV.setDt_Excl(consult.getString("Dt_Excl"));
                ctrOperV.setHr_Excl(consult.getString("Hr_Excl"));
                ctrOperV.setFlag_Caixa(consult.getString("Flag_Caixa"));
                ctrOperV.setFlag_NF(consult.getString("Flag_NF"));
                ctrOperV.setFlag_Excl(consult.getString("Flag_Excl"));
                ctrOperV.setDt_Faturado(consult.getString("Dt_Faturado"));
                ctrOperV.setNumCalculo(consult.getString("NumCalculo"));
            }
            consult.Close();
        } catch (Exception e) {
            ctrOperV = null;
            throw new Exception("CtrOperVDao.selecionaCtrOperV  - " + e.getMessage() + "\r\n"
                    + "SELECT TOP 1 Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                    + "Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV");
        }
        return ctrOperV;
    }

    //Incrementa o campo Numero em 1
    public BigDecimal getNumeroCtrOperV(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        sql = "SELECT top 1 Numero FROM CtrOperV "
                + " where CodFil=? "
                + " order by Numero desc ";
        BigDecimal Num = null;
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                Num = new BigDecimal(consult.getString("Numero"));
            }
            Num = Num.add(new BigDecimal(1));
            consult.Close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.getNumeroCtrOperV  - " + e.getMessage() + "\r\n"
                    + "SELECT top 1 Numero FROM CtrOperV "
                    + " where CodFil= " + CodFil
                    + " order by Numero desc ");
        }
        return Num;
    }

    public boolean validaMatricula(BigDecimal matricula, BigDecimal codfil, Persistencia persistencia) throws Exception {
        boolean valida = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM funcion WHERE matr = ? AND codfil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(matricula);
            consulta.setBigDecimal(codfil);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                valida = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.validaMatricula  - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM funcion WHERE matr = " + matricula + " AND codfil = " + codfil);
        }
        return valida;
    }

    /**
     * Obter o numero para incrementar
     *
     * @param codFil Código da filial
     * @param persistencia Conexão com o banco de dados
     * @return numero incrementado
     * @throws Exception
     */
    public BigDecimal obterNumeroIncremento(BigDecimal codFil, Persistencia persistencia) throws Exception {
        BigDecimal numero = new BigDecimal("1");
        try {
            String sql = "SELECT TOP 1 (numero + 1) numero FROM ctroperv "
                    + "WHERE codfil = ? ORDER BY numero DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                numero = new BigDecimal(consulta.getString("numero"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.obterNumeroIncremento  - " + e.getMessage() + "\r\n"
                    + "SELECT TOP 1 (numero + 1) numero FROM ctroperv "
                    + "WHERE codfil = " + codFil + " ORDER BY numero DESC");
        }
        return numero;
    }

    public List<CtrOperV> listaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<CtrOperV> retorno = new ArrayList();

        try {
            String sql = "SELECT  *  FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY codfil ) AS RowNum, * "
                    + "  FROM      CtrOperV  WHERE CtrOperV.codfil in (select filiais.codfil "
                    + "  from saspw "
                    + "  left join saspwfil on saspwfil.nome = saspw.nome "
                    + "  left join filiais on filiais.codfil = saspwfil.codfilac "
                    + "  left join paramet on paramet.filial_pdr = filiais.codfil "
                    + "  where saspw.codpessoa = ? and paramet.path = ?) AND ";

//            String sql = "select * from CtrOperV "
//                    + "where Codfil = 15 order by CodFil";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "Numero IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ? "
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            CtrOperV ctrOperV;

            while (consult.Proximo()) {

                ctrOperV = new CtrOperV();

                ctrOperV.setNumero(consult.getString("Numero"));
                ctrOperV.setCodFil(consult.getBigDecimal("CodFil"));
                ctrOperV.setData(consult.getString("Data"));

                ctrOperV.setFuncSubs(consult.getString("FuncSubs"));
                ctrOperV.setMotivo_Subs(consult.getString("Motivo_Subs"));
                ctrOperV.setHora_Extra(consult.getString("Hora_Extra"));
                ctrOperV.setVR(consult.getString("VR"));
                ctrOperV.setVT(consult.getString("VT"));
                ctrOperV.setHospedagem(consult.getString("Hospedagem"));
                ctrOperV.setHora1(consult.getString("Hora1"));
                ctrOperV.setHora2(consult.getString("Hora2"));
                ctrOperV.setHora3(consult.getString("Hora3"));
                ctrOperV.setHora4(consult.getString("Hora4"));
                ctrOperV.setHEDiurna(consult.getString("HEDiurna"));
                ctrOperV.setHENoturna(consult.getString("HENoturna"));
                ctrOperV.setNro_HE(consult.getString("Nro_HE"));
                ctrOperV.setMotivo_Aus(consult.getString("Motivo_Aus"));
                ctrOperV.setFuncAus(consult.getString("FuncAus"));
                ctrOperV.setMesario(consult.getString("Mesario"));
                ctrOperV.setOperador(consult.getString("Operador"));
                ctrOperV.setDt_Alter(consult.getString("Dt_Alter"));
                ctrOperV.setHr_Alter(consult.getString("Hr_Alter"));
                ctrOperV.setHr_Excl(consult.getString("Hr_Excl"));
                ctrOperV.setDt_Excl(consult.getString("Dt_Excl"));
                ctrOperV.setOper_Excl(consult.getString("Oper_Excl"));
                ctrOperV.setDt_Faturado(consult.getString("Dt_Faturado"));
                ctrOperV.setFlag_Excl(consult.getString("Flag_Excl"));
                ctrOperV.setFlag_NF(consult.getString("Flag_NF"));
                ctrOperV.setFlag_Caixa(consult.getString("Flag_Caixa"));
                ctrOperV.setPeriodo(consult.getString("Periodo"));
                ctrOperV.setPosto(consult.getString("Posto"));
                ctrOperV.setRefTipo2(consult.getString("RefTipo2"));
                ctrOperV.setNumCalculo(consult.getString("NumCalculo"));
                ctrOperV.setIntraj(consult.getString("Intraj"));
                ctrOperV.setObs(consult.getString("Obs"));
                ctrOperV.setCodSrv(consult.getString("CodSrv"));
                ctrOperV.setPedido(consult.getString("Pedido"));
                ctrOperV.setHsItinere(consult.getString("HsItinere"));
                ctrOperV.setNotas(consult.getString("Notas"));

                retorno.add(ctrOperV);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.listaPaginada - " + e.getMessage());

        }

    }

    public Integer totalMovimentacoesMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from CtrOperV "
                    + " WHERE CtrOperV.codFil in (select filiais.codfil "
                    + "                            from saspw"
                    + "                            left join saspwfil on saspwfil.nome = saspw.nome "
                    + "                            left join filiais on filiais.codfil = saspwfil.codfilac "
                    + "                            left join paramet on paramet.filial_pdr = filiais.codfil "
                    + "                            where saspw.codpessoa = ? and paramet.path = ?) "
                    + "  AND ";
            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "codFil IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);

//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.totalMovimentacoesMobWeb - " + e.getMessage());
        }
    }

    public List<CtrOperV> listaMovimentacao(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<CtrOperV> retorno = new ArrayList();
            String sql = "select Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                    + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV "
                    + " where Codfil = ? ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.select();
            CtrOperV ctrOperV;

            while (consult.Proximo()) {

                ctrOperV = new CtrOperV();

                ctrOperV.setNumero(consult.getString("Numero"));
                ctrOperV.setCodFil(consult.getBigDecimal("CodFil"));
                ctrOperV.setData(consult.getString("Data"));

                ctrOperV.setFuncSubs(consult.getString("FuncSubs"));
                ctrOperV.setMotivo_Subs(consult.getString("Motivo_Subs"));
                ctrOperV.setHora_Extra(consult.getString("Hora_Extra"));
                ctrOperV.setVR(consult.getString("VR"));
                ctrOperV.setVT(consult.getString("VT"));
                ctrOperV.setHospedagem(consult.getString("Hospedagem"));
                ctrOperV.setHora1(consult.getString("Hora1"));
                ctrOperV.setHora2(consult.getString("Hora2"));
                ctrOperV.setHora3(consult.getString("Hora3"));
                ctrOperV.setHora4(consult.getString("Hora4"));
                ctrOperV.setHEDiurna(consult.getString("HEDiurna"));
                ctrOperV.setHENoturna(consult.getString("HENoturna"));
                ctrOperV.setNro_HE(consult.getString("Nro_HE"));
                ctrOperV.setMotivo_Aus(consult.getString("Motivo_Aus"));
                ctrOperV.setFuncAus(consult.getString("FuncAus"));
                ctrOperV.setMesario(consult.getString("Mesario"));
                ctrOperV.setOperador(consult.getString("Operador"));
                ctrOperV.setDt_Alter(consult.getString("Dt_Alter"));
                ctrOperV.setHr_Alter(consult.getString("Hr_Alter"));
                ctrOperV.setHr_Excl(consult.getString("Hr_Excl"));
                ctrOperV.setDt_Excl(consult.getString("Dt_Excl"));
                ctrOperV.setOper_Excl(consult.getString("Oper_Excl"));
                ctrOperV.setDt_Faturado(consult.getString("Dt_Faturado"));
                ctrOperV.setFlag_Excl(consult.getString("Flag_Excl"));
                ctrOperV.setFlag_NF(consult.getString("Flag_NF"));
                ctrOperV.setFlag_Caixa(consult.getString("Flag_Caixa"));
                ctrOperV.setPeriodo(consult.getString("Periodo"));
                ctrOperV.setPosto(consult.getString("Posto"));
                ctrOperV.setRefTipo2(consult.getString("RefTipo2"));
                ctrOperV.setNumCalculo(consult.getString("NumCalculo"));
                ctrOperV.setIntraj(consult.getString("Intraj"));
                ctrOperV.setObs(consult.getString("Obs"));
                ctrOperV.setCodSrv(consult.getString("CodSrv"));
                ctrOperV.setPedido(consult.getString("Pedido"));
                ctrOperV.setHsItinere(consult.getString("HsItinere"));
                ctrOperV.setNotas(consult.getString("Notas"));

                retorno.add(ctrOperV);
            }
            consult.Close();
            return retorno;

        } catch (Exception ex) {
            throw new Exception("CtrOperVDao.listaMovimentacao - " + ex.getMessage() + "\r\n"
                    + "select Numero, Codfil, , Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                    + "Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV"
                    + " where Codfil = " + codfil);
        }
    }

    public List<CtrOperV> listaTeste(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<CtrOperV> retorno = new ArrayList();
            String sql = "select * from CtrOperV "
                    + "where Codfil = ? order by CodFil";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setInt(linhas);
            consult.setInt(primeiro);
            consult.setBigDecimal(codPessoa);
            consult.select();
            CtrOperV ctrOperV;

            while (consult.Proximo()) {

                ctrOperV = new CtrOperV();

                ctrOperV.setNumero(consult.getString("Numero"));
                ctrOperV.setCodFil(consult.getBigDecimal("CodFil"));
                ctrOperV.setData(consult.getString("Data"));

                ctrOperV.setFuncSubs(consult.getString("FuncSubs"));
                ctrOperV.setMotivo_Subs(consult.getString("Motivo_Subs"));
                ctrOperV.setHora_Extra(consult.getString("Hora_Extra"));
                ctrOperV.setVR(consult.getString("VR"));
                ctrOperV.setVT(consult.getString("VT"));
                ctrOperV.setHospedagem(consult.getString("Hospedagem"));
                ctrOperV.setHora1(consult.getString("Hora1"));
                ctrOperV.setHora2(consult.getString("Hora2"));
                ctrOperV.setHora3(consult.getString("Hora3"));
                ctrOperV.setHora4(consult.getString("Hora4"));
                ctrOperV.setHEDiurna(consult.getString("HEDiurna"));
                ctrOperV.setHENoturna(consult.getString("HENoturna"));
                ctrOperV.setNro_HE(consult.getString("Nro_HE"));
                ctrOperV.setMotivo_Aus(consult.getString("Motivo_Aus"));
                ctrOperV.setFuncAus(consult.getString("FuncAus"));
                ctrOperV.setMesario(consult.getString("Mesario"));
                ctrOperV.setOperador(consult.getString("Operador"));
                ctrOperV.setDt_Alter(consult.getString("Dt_Alter"));
                ctrOperV.setHr_Alter(consult.getString("Hr_Alter"));
                ctrOperV.setHr_Excl(consult.getString("Hr_Excl"));
                ctrOperV.setDt_Excl(consult.getString("Dt_Excl"));
                ctrOperV.setOper_Excl(consult.getString("Oper_Excl"));
                ctrOperV.setDt_Faturado(consult.getString("Dt_Faturado"));
                ctrOperV.setFlag_Excl(consult.getString("Flag_Excl"));
                ctrOperV.setFlag_NF(consult.getString("Flag_NF"));
                ctrOperV.setFlag_Caixa(consult.getString("Flag_Caixa"));
                ctrOperV.setPeriodo(consult.getString("Periodo"));
                ctrOperV.setPosto(consult.getString("Posto"));
                ctrOperV.setRefTipo2(consult.getString("RefTipo2"));
                ctrOperV.setNumCalculo(consult.getString("NumCalculo"));
                ctrOperV.setIntraj(consult.getString("Intraj"));
                ctrOperV.setObs(consult.getString("Obs"));
                ctrOperV.setCodSrv(consult.getString("CodSrv"));
                ctrOperV.setPedido(consult.getString("Pedido"));
                ctrOperV.setHsItinere(consult.getString("HsItinere"));
                ctrOperV.setNotas(consult.getString("Notas"));

                retorno.add(ctrOperV);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.listaTeste - " + e.getMessage());
        }

    }

    public void gravaMovimentacoes(CtrOperV ctrOperv, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE CtrOperV SET Data = ?, Periodo = ?, Posto = ?, Mesario = ?, FuncAus = ?, Motivo_Aus = ?, FuncSubs = ?, "
                    + " Motivo_Subs = ?, Hora_Extra = ?, VR = ?, VT = ?, Hospedagem = ?, Hora1 = ?, Hora2 = ?, Hora3 = ?, Hora4 = ?, "
                    + " HEDiurna = ?, HENoturna = ?, Nro_HE = ?, HsItinere = ?, RefTipo2 = ?, CodSrv = ?, Notas = ?, Obs = ?, Pedido = ?, "
                    + " Intraj = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ?, Oper_Excl = ?, Dt_Excl = ?, Hr_Excl = ?, "
                    + " Flag_Caixa = ?, Flag_NF = ?, Flag_Excl = ?, Dt_Faturado = ?, NumCalculo = ? "
                    + " WHERE CodFil = ?";

            PreparedStatement mstat = persistencia.getState(sql);
            mstat.setString(1, ctrOperv.getData());
            mstat.setString(2, ctrOperv.getPeriodo());
            mstat.setString(3, ctrOperv.getPosto());
            mstat.setString(4, ctrOperv.getMesario());
            mstat.setBigDecimal(5, ctrOperv.getFuncAus());
            mstat.setString(6, ctrOperv.getMotivo_Aus());
            mstat.setBigDecimal(7, ctrOperv.getFuncSubs());
            mstat.setString(8, ctrOperv.getMotivo_Subs());
            mstat.setString(9, ctrOperv.getHora_Extra());
            mstat.setBigDecimal(10, ctrOperv.getVR());
            mstat.setBigDecimal(11, ctrOperv.getVT());
            mstat.setBigDecimal(12, ctrOperv.getHospedagem());
            mstat.setString(13, ctrOperv.getHora1());
            mstat.setString(14, ctrOperv.getHora2());
            mstat.setString(15, ctrOperv.getHora3());
            mstat.setString(16, ctrOperv.getHora4());
            mstat.setBigDecimal(17, ctrOperv.getHEDiurna());
            mstat.setBigDecimal(18, ctrOperv.getHENoturna());
            mstat.setBigDecimal(19, ctrOperv.getNro_HE());
            mstat.setBigDecimal(20, ctrOperv.getHsItinere());
            mstat.setString(21, ctrOperv.getRefTipo2());
            mstat.setString(22, ctrOperv.getCodSrv());
            mstat.setString(23, ctrOperv.getNotas());
            mstat.setString(24, ctrOperv.getObs());
            mstat.setString(25, ctrOperv.getPedido());
            mstat.setInt(26, ctrOperv.getIntraj());
            mstat.setString(27, ctrOperv.getOperador());
            mstat.setString(28, ctrOperv.getDt_Alter());
            mstat.setString(29, ctrOperv.getHr_Alter());
            mstat.setString(30, ctrOperv.getOper_Excl());
            mstat.setString(31, ctrOperv.getDt_Excl());
            mstat.setString(32, ctrOperv.getHr_Excl());
            mstat.setString(33, ctrOperv.getFlag_Caixa());
            mstat.setString(34, ctrOperv.getFlag_NF());
            mstat.setString(35, ctrOperv.getFlag_Excl());
            mstat.setString(36, ctrOperv.getDt_Faturado());
            mstat.setBigDecimal(37, ctrOperv.getNumCalculo());
            mstat.setBigDecimal(38, ctrOperv.getCodFil());
            mstat.execute();
            mstat.close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.gravaMovimentacoes  - " + e.getMessage() + "\r\n"
                    + " update CtrOperV set Numero = " + ctrOperv.getNumero() + ", Data = " + ctrOperv.getData() + ", Periodo = " + ctrOperv.getPeriodo() + ", "
                    + " Posto = " + ctrOperv.getPosto() + ", Mesario = " + ctrOperv.getMesario() + ", FuncAus = " + ctrOperv.getFuncAus() + ", "
                    + " Motivo_Aus = " + ctrOperv.getMotivo_Aus() + ", FuncSubs = " + ctrOperv.getFuncSubs() + ", Motivo_Subs = " + ctrOperv.getMotivo_Subs() + ", "
                    + " Hora_Extra = " + ctrOperv.getHora_Extra() + ", VR = " + ctrOperv.getVR() + ", VT = " + ctrOperv.getVT() + ", "
                    + " Hospedagem = " + ctrOperv.getHospedagem() + ", Hora1 = " + ctrOperv.getHora1() + ", Hora2 = " + ctrOperv.getHora2() + ", Hora3 = " + ctrOperv.getHora3() + ", "
                    + " Hora4 = " + ctrOperv.getHora4() + ", HEDiurna = " + ctrOperv.getHEDiurna() + ", HENoturna = " + ctrOperv.getHENoturna() + ", "
                    + " Nro_HE = " + ctrOperv.getNro_HE() + ", HsItinere = " + ctrOperv.getHsItinere() + ", RefTipo2 = " + ctrOperv.getRefTipo2() + ", "
                    + " CodSrv = " + ctrOperv.getCodSrv() + ", Notas = " + ctrOperv.getNotas() + ", Obs = " + ctrOperv.getObs() + ", Pedido = " + ctrOperv.getPedido() + ", "
                    + " Intraj = " + ctrOperv.getIntraj() + ", Operador = " + ctrOperv.getOperador() + ", Dt_Alter = " + ctrOperv.getDt_Alter() + ", "
                    + " Hr_Alter = " + ctrOperv.getHr_Alter() + ", Oper_Excl = " + ctrOperv.getOper_Excl() + ", Dt_Excl = " + ctrOperv.getDt_Excl() + ", Hr_Excl = " + ctrOperv.getHr_Excl() + ", "
                    + " Flag_Caixa = " + ctrOperv.getFlag_Caixa() + ", Flag_NF = " + ctrOperv.getFlag_NF() + ", Flag_Excl = " + ctrOperv.getFlag_Excl() + ", "
                    + " Dt_Faturado = " + ctrOperv.getDt_Faturado() + ", NumCalculo = " + ctrOperv.getNumCalculo() + ""
                    + " where CodFil = " + ctrOperv.getCodFil());

        }
    }

    /**
     * Verifica a existencia de alguma entrada na tabela ctroperv
     *
     * @param data
     * @param codfil
     * @param posto
     * @param matr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeCtrOperV(String data, String codfil, String posto, String matr, Persistencia persistencia) throws Exception {
        try {
            boolean retorno = false;
            String sql = " Select * from CtrOperv "
                    + " Where Data = ? and Codfil = ? and Posto =  ? and FuncSubs = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codfil);
            consulta.setString(posto);
            consulta.setString(matr);
            consulta.select();
            while (consulta.Proximo()) {
                retorno = true;
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.existeCtrOperV - " + e.getMessage() + "\r\n"
                    + " Select * from CtrOperv "
                    + " Where Data = " + data + " and Codfil = " + codfil + " and Posto = " + posto + " and FuncSubs = " + matr);
        }
    }

    /**
     * Deleta virtualmente uma movimentação de CtrOperV
     *
     * @param operador
     * @param dataAtual
     * @param horaAtual
     * @param dtCompet
     * @param codFil
     * @param matr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int deleteCtrOperV(String operador, String dataAtual, String horaAtual,
            String dtCompet, String codFil, String matr, Persistencia persistencia) throws Exception {
        try {
            String sql = " Update CtrOperv Set Flag_Excl = '*', Oper_Excl = ?, Dt_Excl = ?, Hr_Excl = ?"
                    + " where Data = ? and Codfil = ? and FuncSubs = ? and Mesario like 'MOB-Check%' "; //Incluido Like Carlos
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("MOB-" + FuncoesString.RecortaString(operador, 0, 6));
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(dtCompet);
            consulta.setString(codFil);
            consulta.setString(matr);
            int retorno = consulta.update();
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.deleteCtrOperV - " + e.getMessage() + "\r\n"
                    + "Update CtrOperv Set Flag_Excl = '*', Oper_Excl = " + "MOB-" + FuncoesString.RecortaString(operador, 0, 6) + ", Dt_Excl = " + dataAtual + ", Hr_Excl = " + horaAtual
                    + " where Data = " + dtCompet + " and Codfil = " + codFil + " and FuncSubs = " + matr);
        }
    }

    /**
     * Insere uma nova entrada na tabela ctrOperV
     *
     * @param operador
     * @param dataAtual
     * @param horaAtual
     * @param dtCompet
     * @param codFil
     * @param matr
     * @param periodo
     * @param numero
     * @param secao
     * @param horaFim
     * @param heDiurna
     * @param heNoturna
     * @param persistencia
     * @throws Exception
     */
    public void insereCtrOperV(String operador, String dataAtual, String horaAtual,
            String dtCompet, String codFil, String matr, String periodo, String numero, String secao,
            String horaFim, BigDecimal heDiurna, BigDecimal heNoturna,
            Persistencia persistencia) throws Exception {
        try {
            String sql = " Insert into CtrOperv(Numero, CodFil, Data, Periodo, Posto, Mesario, FuncSubs, Motivo_Subs, "
                    + " Hora1, Nro_HE, Hora_Extra, Operador, Dt_Alter, Hr_Alter, Hora4, HEDiurna, HENoturna, Flag_Excl) "
                    + " Values(?, ?, ?, ?, ?, 'MOB-Check', ?, 'N', ?, 0, 'N', ?, ?, ?, ?, ?, ?, '*') ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(numero);
            consulta.setString(codFil);
            consulta.setString(dtCompet);
            consulta.setString(periodo);
            consulta.setString(secao);
            consulta.setString(matr);
            consulta.setString(horaAtual);
            consulta.setString("MOB-" + FuncoesString.RecortaString(operador, 0, 6));
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(horaFim);
            consulta.setBigDecimal(heDiurna);
            consulta.setBigDecimal(heNoturna);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.insereCtrOperV - " + e.getMessage() + "\r\n"
                    + " Insert into CtrOperv(Numero, CodFil, Data, Periodo, Posto, Mesario, FuncSubs, Motivo_Subs, "
                    + " Hora1, Nro_HE, Operador, Dt_Alter, Hr_Alter) "
                    + " Values(" + numero + ", " + codFil + ", " + dtCompet + ", " + periodo + ", " + secao + ", 'MOB', " + matr + ", 'N', " + horaAtual + ", 1, "
                    + "MOB-" + FuncoesString.RecortaString(operador, 0, 6) + ", " + dataAtual + "," + horaAtual + ")");
        }
    }

    /**
     * Busca registros da tabela CtrOperV
     *
     * @param Numr
     * @param ctrOperv - estrutura de dados da classe CtrOperV
     * @param persistencia - Conexão ao Banco
     * @throws java.lang.Exception
     */
    public void adicionaCtrOperV(BigDecimal Numr, CtrOperV ctrOperv, Persistencia persistencia) throws Exception {
        sql = " insert into CtrOperV (Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo) "
                + " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Numr);
            consulta.setBigDecimal(ctrOperv.getCodFil());
            consulta.setString(ctrOperv.getData());
            consulta.setString(ctrOperv.getPeriodo());
            consulta.setString(ctrOperv.getPosto());
            consulta.setString(ctrOperv.getMesario());
            consulta.setBigDecimal(ctrOperv.getFuncAus());
            consulta.setString(ctrOperv.getMotivo_Aus());
            consulta.setBigDecimal(ctrOperv.getFuncSubs());
            consulta.setString(ctrOperv.getMotivo_Subs());
            consulta.setString(ctrOperv.getHora_Extra());
            consulta.setBigDecimal(ctrOperv.getVR());
            consulta.setBigDecimal(ctrOperv.getVT());
            consulta.setBigDecimal(ctrOperv.getHospedagem());
            consulta.setString(ctrOperv.getHora1());
            consulta.setString(ctrOperv.getHora2());
            consulta.setString(ctrOperv.getHora3());
            consulta.setString(ctrOperv.getHora4());
            consulta.setBigDecimal(ctrOperv.getHEDiurna());
            consulta.setBigDecimal(ctrOperv.getHENoturna());
            consulta.setBigDecimal(ctrOperv.getNro_HE());
            consulta.setBigDecimal(ctrOperv.getHsItinere());
            consulta.setString(ctrOperv.getRefTipo2());
            consulta.setString(ctrOperv.getCodSrv());
            consulta.setString(ctrOperv.getNotas());
            consulta.setString(ctrOperv.getObs());
            consulta.setString(ctrOperv.getPedido());
            consulta.setInt(ctrOperv.getIntraj());
            consulta.setString(ctrOperv.getOperador());
            consulta.setString(ctrOperv.getDt_Alter());
            consulta.setString(ctrOperv.getHr_Alter());
            consulta.setString(ctrOperv.getOper_Excl());
            consulta.setString(ctrOperv.getDt_Excl());
            consulta.setString(ctrOperv.getHr_Excl());
            consulta.setString(ctrOperv.getFlag_Caixa());
            consulta.setString(ctrOperv.getFlag_NF());
            consulta.setString(ctrOperv.getFlag_Excl());
            consulta.setString(ctrOperv.getDt_Faturado());
            consulta.setBigDecimal(ctrOperv.getNumCalculo());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CtrOperVDao.adicionaCtrOperV  - " + e.getMessage() + "\r\n"
                    + "insert into CtrOperV (Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
                    + " Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo) "
                    + " values(" + Numr + "," + ctrOperv.getCodFil() + "," + ctrOperv.getData() + "," + ctrOperv.getPeriodo() + "," + ctrOperv.getPosto() + ","
                    + ctrOperv.getMesario() + "," + ctrOperv.getFuncAus() + "," + ctrOperv.getMotivo_Aus() + "," + ctrOperv.getFuncSubs() + ","
                    + ctrOperv.getMotivo_Subs() + "," + ctrOperv.getHora_Extra() + "," + ctrOperv.getVR() + "," + ctrOperv.getVT() + "," + ctrOperv.getHospedagem() + ","
                    + ctrOperv.getHora1() + "," + ctrOperv.getHora2() + "," + ctrOperv.getHora3() + "," + ctrOperv.getHora4() + "," + ctrOperv.getHEDiurna() + ","
                    + ctrOperv.getHENoturna() + "," + ctrOperv.getNro_HE() + "," + ctrOperv.getHsItinere() + "," + ctrOperv.getRefTipo2() + "," + ctrOperv.getCodSrv() + ","
                    + ctrOperv.getNotas() + "," + ctrOperv.getObs() + "," + ctrOperv.getPedido() + "," + ctrOperv.getIntraj() + "," + ctrOperv.getOperador() + ","
                    + ctrOperv.getDt_Alter() + "," + ctrOperv.getHr_Alter() + "," + ctrOperv.getOper_Excl() + "," + ctrOperv.getDt_Excl() + ","
                    + ctrOperv.getHr_Excl() + "," + ctrOperv.getFlag_Caixa() + "," + ctrOperv.getFlag_NF() + "," + ctrOperv.getFlag_Excl() + ","
                    + ctrOperv.getDt_Faturado() + "," + ctrOperv.getNumCalculo() + ")");
        }

    }

    public void editar(CtrOperV ctrOperv, Persistencia persistencia) throws Exception {
        try {
            String sql = " update CtrOperV set Data = ?, Posto = ?, FuncAus = ?, "
                    + " Hora1 = ?, Hora2 = ?, Obs = ?, Operador = ?, "
                    + " where CodFil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(ctrOperv.getData());
            consulta.setString(ctrOperv.getPosto());
            consulta.setBigDecimal(ctrOperv.getFuncAus());
            consulta.setString(ctrOperv.getHora1());
            consulta.setString(ctrOperv.getHora2());
            consulta.setString(ctrOperv.getObs());
            consulta.setString(ctrOperv.getOperador());
            consulta.setBigDecimal(ctrOperv.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao alterar filial - \r:" + e.getMessage());

        }
    }

//    /**
//     * Busca registros da tabela CtrOperV
//     * @param persistencia  - Conexão ao Banco
//     * @return - Lista de registros
//     * @throws Exception 
//     */
//    public CtrOperV selecionaCtrOperV(Persistencia persistencia) throws Exception {
//        sql = "SELECT TOP 1 Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
//                + "Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
//                + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
//                + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
//                + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV";
//        CtrOperV ctrOperV = null;
//        try {
//            Consulta consult = new Consulta(sql, persistencia);
//            consult.select();
//            while (consult.Proximo()) {
//                ctrOperV = new CtrOperV();
//                ctrOperV.setNumero(consult.getString("Numero"));
//                ctrOperV.setCodFil(consult.getString("CodFil"));
//                ctrOperV.setData(consult.getString("Data"));
//                ctrOperV.setPeriodo(consult.getString("Periodo"));
//                ctrOperV.setPosto(consult.getString("Posto"));
//                ctrOperV.setMesario(consult.getString("Mesario"));
//                ctrOperV.setFuncAus(consult.getString("FuncAus"));
//                ctrOperV.setMotivo_Aus(consult.getString("Motivo_Aus"));
//                ctrOperV.setFuncSubs(consult.getString("FuncSubs"));
//                ctrOperV.setMotivo_Subs(consult.getString("Motivo_Subs"));
//                ctrOperV.setHora_Extra(consult.getString("Hora_Extra"));
//                ctrOperV.setVR(consult.getString("VR"));
//                ctrOperV.setVT(consult.getString("VT"));
//                ctrOperV.setHospedagem(consult.getString("Hospedagem"));
//                ctrOperV.setHora1(consult.getString("Hora1"));
//                ctrOperV.setHora2(consult.getString("Hora2"));
//                ctrOperV.setHora3(consult.getString("Hora3"));
//                ctrOperV.setHora4(consult.getString("Hora4"));
//                ctrOperV.setHEDiurna(consult.getString("HEDiurna"));
//                ctrOperV.setHENoturna(consult.getString("HENoturna"));
//                ctrOperV.setHsItinere(consult.getString("HsItinere"));
//                ctrOperV.setRefTipo2(consult.getString("RefTipo2"));
//                ctrOperV.setCodSrv(consult.getString("CodSrv"));
//                ctrOperV.setNotas(consult.getString("Notas"));
//                ctrOperV.setObs(consult.getString("Obs"));
//                ctrOperV.setPedido(consult.getString("Pedido"));
//                ctrOperV.setIntraj(consult.getString("Intraj"));
//                ctrOperV.setDt_Alter(consult.getString("Dt_Alter"));
//                ctrOperV.setHr_Alter(consult.getString("Hr_Alter"));
//                ctrOperV.setOper_Excl(consult.getString("Oper_Excl"));
//                ctrOperV.setDt_Excl(consult.getString("Dt_Excl"));
//                ctrOperV.setHr_Excl(consult.getString("Hr_Excl"));
//                ctrOperV.setFlag_Caixa(consult.getString("Flag_Caixa"));
//                ctrOperV.setFlag_NF(consult.getString("Flag_NF"));
//                ctrOperV.setFlag_Excl(consult.getString("Flag_Excl"));
//                ctrOperV.setDt_Faturado(consult.getString("Dt_Faturado"));
//                ctrOperV.setNumCalculo(consult.getString("NumCalculo"));
//            }
//            consult.Close();
//        } catch (Exception e) {
//            ctrOperV = null;
//            throw new Exception("CtrOperVDao.selecionaCtrOperV  - " + e.getMessage()+"\r\n"
//                    + "SELECT TOP 1 Numero, CodFil, Data, Periodo, Posto, Mesario, FuncAus, Motivo_Aus, FuncSubs, "
//                    + "Motivo_Subs, Hora_Extra, VR, VT, Hospedagem, Hora1, Hora2, Hora3, Hora4, "
//                    + " HEDiurna, HENoturna, Nro_HE, HsItinere, RefTipo2, CodSrv, Notas, Obs, Pedido, "
//                    + " Intraj, Operador, Dt_Alter, Hr_Alter, Oper_Excl, Dt_Excl, Hr_Excl, "
//                    + " Flag_Caixa, Flag_NF, Flag_Excl, Dt_Faturado, NumCalculo FROM CtrOperV");
//        }
//        return ctrOperV;
//    }
//    
//    //Incrementa o campo Numero em 1
//     public BigDecimal getNumeroCtrOperV(BigDecimal CodFil, Persistencia persistencia) throws Exception {
//        sql = "SELECT top 1 Numero FROM CtrOperV "
//                + " where CodFil=? "
//                + " order by Numero desc ";
//        BigDecimal Num = null;
//        try {
//            Consulta consult = new Consulta(sql, persistencia);
//            consult.setBigDecimal(CodFil);
//            consult.select();
//            while (consult.Proximo()) {
//               Num = new BigDecimal (consult.getString("Numero"));
//            }
//            Num = Num.add(new BigDecimal(1));
//            consult.Close();
//        } catch (Exception e) {
//            throw new Exception("CtrOperVDao.getNumeroCtrOperV  - " + e.getMessage()+"\r\n"
//                    + "SELECT top 1 Numero FROM CtrOperV "
//                    + " where CodFil= "+CodFil
//                    + " order by Numero desc ");
//        }
//        return Num;
//    }
//    
//    public boolean validaMatricula(BigDecimal matricula, BigDecimal codfil, Persistencia persistencia) throws Exception{
//        boolean valida = false;
//        try{
//            String sql = "SELECT COUNT(*) qtd FROM funcion WHERE matr = ? AND codfil = ?";
//            
//            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setBigDecimal(matricula);
//            consulta.setBigDecimal(codfil);
//            consulta.select();
//            
//            int quantidade = 0;
//            while(consulta.Proximo()){
//                quantidade = consulta.getInt("qtd");
//            }
//            
//            if(quantidade > 0){
//                valida = true;
//            }
//            consulta.Close();
//        }catch(Exception e){
//            throw new Exception("CtrOperVDao.validaMatricula  - " + e.getMessage()+"\r\n"
//                    + "SELECT COUNT(*) qtd FROM funcion WHERE matr = "+matricula+" AND codfil = "+codfil);
//        }
//        return valida;
//    }
//     
//    /**
//     * Obter o numero para incrementar
//     * @param codFil Código da filial
//     * @param persistencia Conexão com o banco de dados
//     * @return numero incrementado
//     * @throws Exception 
//     */
//    public BigDecimal obterNumeroIncremento(BigDecimal codFil,Persistencia persistencia) throws Exception{
//        BigDecimal numero = new BigDecimal("1");
//        try{
//            String sql = "SELECT TOP 1 (numero + 1) numero FROM ctroperv "
//                    + "WHERE codfil = ? ORDER BY numero DESC";
//            
//            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setBigDecimal(codFil);
//            consulta.select();
//            
//            while(consulta.Proximo()){
//                numero = new BigDecimal(consulta.getString("numero"));
//            }
//            consulta.Close();
//        }catch(Exception e){
//            throw new Exception("CtrOperVDao.obterNumeroIncremento  - " + e.getMessage()+"\r\n"
//                    + "SELECT TOP 1 (numero + 1) numero FROM ctroperv "
//                    + "WHERE codfil = "+codFil+" ORDER BY numero DESC");
//        }
//        return numero;
//    }
}
