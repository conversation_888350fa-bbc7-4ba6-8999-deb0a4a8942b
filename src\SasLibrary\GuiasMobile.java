package SasLibrary;

import Arquivo.ArquivoLog;
import BeansEspeciais.GuiaE;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.FatTVGuias;
import SasBeans.GTV;
import SasBeans.OS_Vig;
import SasBeans.Paramet;
import SasBeans.Rt_Guias;
import SasBeans.TesEntDD;
import SasBeans.TesEntDN;
import SasBeans.TesEntMD;
import SasBeans.TesEntrada;
import SasBeansCompostas.Rt_GuiasRotas;
import SasBeansCompostas.Rt_PercRt_Perc;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxForteDao;
import SasDaos.FatTVGuiasDao;
import SasDaos.GTVDao;
import SasDaos.GTVSeqDao;
import SasDaos.LacresDao;
import SasDaos.OSDao;
import SasDaos.ParametDao;
import SasDaos.PedidoDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import SasDaos.TesEntDDDao;
import SasDaos.TesEntDNDao;
import SasDaos.TesEntMDDao;
import SasDaos.TesEntradaDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;
import javax.servlet.ServletContext;

/**
 *
 * <AUTHOR>
 */
public class GuiasMobile {
    
    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    /**
     * Lista todas as guias da parada
     *
     * @param Pedido - informe o pedido
     * @param Sequencia - informe a seqüência da rota
     * @param Parada - informe a parada
     * @param Hora1 - a Hora prevista
     * @param Codfil - Filial que atende essa parada
     * @param persistencia - classe de conexão
     * @return - lista com as guias
     * @throws Exception - pode gerar exception
     */
    public static List<GuiaE> getGuiasE(long Pedido, String Sequencia, String Parada, String Hora1, String Codfil, Persistencia persistencia) throws Exception {
        List<GuiaE> listGuias = new ArrayList();
        try {
            try {
                Consulta rsgtv = new Consulta("Select "
                        + " Rt_Guias.Guia, Rt_Guias.Serie, Rt_Guias.Valor, Rt_Guias.OS "
                        + " from Rt_Guias "
                        + " Left Join Rt_Perc on  Rt_Perc.Sequencia = Rt_Guias.Sequencia "
                        + " and Rt_perc.Parada    = Rt_Guias.Parada"
                        + " Left Join Rt_Perc Rt_PercDst on Rt_PercDst.Sequencia = Rt_Perc.Sequencia"
                        + " and Rt_PercDst.Parada = Rt_perc.DPar"
                        + " where Rt_PercDst.Sequencia = ?"
                        + " and Rt_PercDst.Parada    = ?", persistencia);
                rsgtv.setString(Sequencia);
                rsgtv.setString(Parada);
                rsgtv.select();
                while (rsgtv.Proximo()) {
                    if ((rsgtv.getFloat(1) > 0) && (!"".equals(rsgtv.getString(2)))) {
                        GuiaE temp = new GuiaE();
                        temp.setGuia(rsgtv.getString(1).substring(0, rsgtv.getString(1).indexOf(".")));
                        temp.setSerie(rsgtv.getString(2));
                        temp.setValor(rsgtv.getString(3));
                        temp.setOS(rsgtv.getString(4));
                        temp.setRtGuias(true);
                        if (!ExisteGuia(temp.getGuia(), temp.getSerie(), listGuias, 1)) {
                            listGuias.add(temp);
                        }
                    }
                }
                rsgtv.Close();
            } catch (Exception e) {
                throw new Exception("-1-" + e.getMessage());
            }
            try {
                Consulta rsgtv = new Consulta("select Guia,Serie,Valor,OS,CliOri "
                        + " from cxfguias "
                        + " where seqrotasai = ?"
                        + " and hora1d = ?", persistencia);
                rsgtv.setString(Sequencia);
                rsgtv.setString(Hora1.replace(":", ""));
                rsgtv.select();
                while (rsgtv.Proximo()) {
                    if ((rsgtv.getFloat(1) > 0) && (!"".equals(rsgtv.getString(2)))) {
                        GuiaE temp = new GuiaE();
                        temp.setGuia(rsgtv.getString(1).substring(0, rsgtv.getString(1).indexOf(".")));
                        temp.setSerie(rsgtv.getString(2));
                        temp.setValor(rsgtv.getString(3));
                        temp.setOS(rsgtv.getString(4));
                        temp.setCliori(rsgtv.getString(5));
                        temp.setCxfGuias(true);
                        if (!ExisteGuia(temp.getGuia(), temp.getSerie(), listGuias, 2)) {
                            listGuias.add(temp);
                        }
                    }
                }
                rsgtv.Close();
            } catch (Exception e) {
                throw new Exception("-2-" + e.getMessage());
            }
            try {
                Consulta rsgtv = new Consulta("Select "
                        + " TesSaidas.Guia, "
                        + " TesSaidas.Serie, "
                        + " TesSaidas.TotalGeral, "
                        + " Pedido.OS "
                        + " from Pedido as Pedido"
                        + " left join TesSaidas as TesSaidas on  TesSaidas.Pedido  = Pedido.Numero "
                        + " and TesSaidas.Codcli2 = Pedido.Codcli2 "
                        + " and TesSaidas.CodFil  = Pedido.CodFil  "
                        + " Left Join Rt_Guias as Rt_Guias  on  Rt_Guias.Guia  = TesSaidas.Guia "
                        + " and Rt_Guias.Serie = TesSaidas.Serie "
                        + " where Pedido.SeqRota = ?"
                        + " and Pedido.Parada  = ?"
                        + " and Pedido.CodFil  = ?", persistencia);
                rsgtv.setString(Sequencia);
                rsgtv.setString(Parada);
                rsgtv.setString(Codfil);
                rsgtv.select();
                while (rsgtv.Proximo()) {
                    GuiaE temp = new GuiaE();
                    temp.setGuia(rsgtv.getString(1).substring(0, rsgtv.getString(1).indexOf(".")));
                    temp.setSerie(rsgtv.getString(2));
                    temp.setValor(rsgtv.getString(3));
                    temp.setOS(rsgtv.getString(4));
                    temp.setTesSaidas(true);
                    if (!ExisteGuia(temp.getGuia(), temp.getSerie(), listGuias, 3)) {
                        listGuias.add(temp);
                    }
                }
            } catch (Exception e) {
                throw new Exception("-3-" + e.getMessage());
            }
            return listGuias;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    /**
     * Verifica a existência da guia na lista
     *
     * @param Guia - número da guia
     * @param Serie - série da guia
     * @param listGuias - a lista onde deseja-se procurar
     * @param tipo - tabela de origem da guia 1-Rt_guias 2-CxfGuias 3-TesSaidas
     * @return
     */
    public static boolean ExisteGuia(String Guia, String Serie, List<GuiaE> listGuias, int tipo) {
        int i = 0;
        while (i < listGuias.size()) {
            if (listGuias.get(i).getGuia().equals(Guia) && listGuias.get(i).getSerie().equals(Serie)) {
                if (tipo == 1) {
                    listGuias.get(i).setRtGuias(true);
                } else if (tipo == 2) {
                    listGuias.get(i).setCxfGuias(true);
                } else {
                    listGuias.get(i).setTesSaidas(true);
                }
                return true;
            } else {
                i++;
            }
        }
        return false;
    }

    /**
     * Faz a baixa das guias passadas
     *
     * @param guias - lista das guias
     * @param Sequencia - seqüência da rota
     * @param Parada - parada da rota
     * @param Codfil - filial
     * @param data - data da baixa
     * @param hora - hora da baixa
     */
    public static void BaixaGuiaParadaE(List<GuiaE> guias, String Sequencia, String Parada, String Codfil, String data, String hora, String CodCliCxf, Persistencia persistencia) throws Exception {
        String sql = "";
        BigDecimal OS, OS_geral;
        String SituacaoGTV = "1";
        try {
            String codcli1 = "", codcli2 = "", codcli3 = "", codclipassar;
            sql = "select rt_perc1.codcli1, rt_perc1.codcli2, "
                    + " rt_perc2.codcli1, rt_perc2.codcli2 "
                    + " from Rt_Perc as rt_perc1 "
                    + " left join Rt_Perc as rt_perc2 on rt_perc1.sequencia=rt_perc2.sequencia "
                    + " and rt_perc1.dpar=rt_perc2.parada "
                    + " where rt_perc1.sequencia=? and rt_perc1.parada=?";
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(Sequencia);
            rs.setString(Parada);
            rs.select();
            while (rs.Proximo()) {
                codcli1 = rs.getString(1);
                codcli2 = rs.getString(2);
                codcli3 = rs.getString(3);
            }
            if (((codcli2 == null) && (codcli3 == null)) || (("".equals(codcli2)) && ("".equals(codcli3))) || ((codcli2 == null) && ("".equals(codcli3))) || (("".equals(codcli2)) && (codcli3 == null))) {
                codclipassar = codcli1;
            } else if ((codcli2 == null) || ("".equals(codcli2)) || (codcli2.equals(codcli3))) {
                codclipassar = codcli3;
            } else {
                codclipassar = codcli2;
            }
            OS_geral = OS = SasLibrary.OS.BuscaOS(codcli1, codclipassar, "", Float.valueOf(Codfil), data, 5, CodCliCxf, persistencia);
            Consulta consulta;
            for (int i = 0; i < guias.size(); i++) {
                try {
                    if (guias.get(i).isCxfGuias() && Float.parseFloat(guias.get(i).getOS()) <= 0) {
                        OS = SasLibrary.OS.BuscaOS(codcli1, guias.get(i).getCliori(), "", Float.valueOf(Codfil), data, 5, CodCliCxf, persistencia);
                        sql = "update cxfguias set OS=" + OS.toString() + " where Codfil=? and Guia=? and Serie=? and SeqRotaSai=?";
                        consulta = new Consulta(sql, persistencia);
                        consulta.setString(Codfil);
                        consulta.setString(guias.get(i).getGuia());
                        consulta.setString(guias.get(i).getSerie());
                        consulta.setString(Sequencia);
                        consulta.update();
                        consulta.close();
                    }

                    boolean existeGTV = false;
                    Consulta rsgtv = new Consulta("Select Situacao from GTV where Guia=? and Serie=?", persistencia);
                    rsgtv.setString(guias.get(i).getGuia());
                    rsgtv.setString(guias.get(i).getSerie());
                    rsgtv.select();
                    while (rsgtv.Proximo()) {
                        SituacaoGTV = rsgtv.getString(1);
                        existeGTV = true;
                        if (!"6".equals(SituacaoGTV) && !"8".equals(SituacaoGTV)) {
                            sql = "Update GTV set "
                                    + " Situacao = '8',"
                                    + " OS = ?"
                                    + " where Guia = ?"
                                    + " and Serie = ?";
                            consulta = new Consulta(sql, persistencia);
                            consulta.setString(OS.toString());
                            consulta.setString(guias.get(i).getGuia());
                            consulta.setString(guias.get(i).getSerie());
                            consulta.update();
                            consulta.close();
                        }
                    }
                    if (!existeGTV) {
                        sql = "Insert into GTV "
                                + "(CodFil, Guia, Serie, DtGeracao, OS, Situacao,"
                                + " Status, Operador, Dt_alter, Hr_alter)"
                                + " Values (?,?,?,?,?,?,?,?,?,?)";
                        consulta = new Consulta(sql, persistencia);
                        consulta.setString(Codfil);
                        consulta.setString(guias.get(i).getGuia());
                        consulta.setString(guias.get(i).getSerie());
                        consulta.setString(data);
                        consulta.setString(OS.toString());
                        consulta.setString("8");
                        consulta.setString("PD");
                        consulta.setString("SATMOB");
                        consulta.setString(data);
                        consulta.setString(hora);
                        consulta.insert();
                        consulta.close();
                    }
                    if (!"6".equals(SituacaoGTV) && !"8".equals(SituacaoGTV)) {
                        if (!guias.get(i).isRtGuias()) {
                            sql = "Insert into Rt_Guias "
                                    + " (Sequencia, Parada, Guia, Serie, OS, Valor, KM, KMTerra, Operador, Dt_Alter, Hr_Alter)"
                                    + " Values (?,?,?,?,?,?,?,?,?,?,?)";
                            consulta = new Consulta(sql, persistencia);
                            consulta.setString(Sequencia);
                            consulta.setString(Parada);
                            consulta.setString(guias.get(i).getGuia());
                            consulta.setString(guias.get(i).getSerie());
                            consulta.setString(OS.toString());
                            consulta.setString(guias.get(i).getValor());
                            consulta.setFloat(0);
                            consulta.setFloat(0);
                            consulta.setString("SATMOB");
                            consulta.setString(data);
                            consulta.setString(hora);
                            consulta.insert();
                            consulta.close();
                        } else {
                            sql = "update Rt_Guias set OS=? where Sequencia=? and Parada=? and Guia=? and Serie=?";
                            consulta = new Consulta(sql, persistencia);
                            consulta.setString(OS.toString());
                            consulta.setString(Sequencia);
                            consulta.setString(Parada);
                            consulta.setString(guias.get(i).getGuia());
                            consulta.setString(guias.get(i).getSerie());
                            consulta.insert();
                            consulta.close();
                        }
                    }
                    SituacaoGTV = "1";
                    OS = OS_geral;
                } catch (Exception ex) {
                    //erro em uma guia mas continua com a baixa das demais
                }
            }
        } catch (Exception e) {
            throw new Exception("Erro Baixa de Guia ao buscar OS: " + e.getMessage() + " -- \n" + sql);
        }

    }

    /**
     * Troca a serie de guia já consolidades para TSR
     *
     * @param persistencia - conexão ao banco de dados
     * @param Guia - Número da Guia
     * @param Serie - Série da Guia
     * @param CodFil - Codigo da filial
     * @param novaserie - Nova série da guia
     * @throws Exception
     */
    public static void troca2TSR(Persistencia persistencia, String Guia, String Serie, String CodFil, String novaserie) throws Exception {
        Rt_GuiasDao rt_guiasdao = new Rt_GuiasDao();
        CxFGuiasDao cxfguiasdao = new CxFGuiasDao();
        CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
        FatTVGuiasDao fattvguiasdao = new FatTVGuiasDao();
        TesEntradaDao tesentradadao = new TesEntradaDao();
        TesEntDNDao tesentdndao = new TesEntDNDao();
        TesEntDDDao tesentdddao = new TesEntDDDao();
        TesEntMDDao tesentmddao = new TesEntMDDao();

        Rt_Guias rt_guias = new Rt_Guias();
        CxFGuias cxfguias = new CxFGuias();
        CxFGuiasVol cxfguiasvol = new CxFGuiasVol();
        FatTVGuias fattvguias = new FatTVGuias();
        TesEntrada tesentrada = new TesEntrada();
        TesEntDN tesentdn = new TesEntDN();
        TesEntDD tesentdd = new TesEntDD();
        TesEntMD tesentmd = new TesEntMD();

        rt_guias.setGuia(Guia);
        rt_guias.setSerie(Serie);
        rt_guias.setCodFil(CodFil);
        cxfguias.setGuia(Guia);
        cxfguias.setSerie(Serie);
        cxfguias.setCodFil(CodFil);
        cxfguiasvol.setGuia(Guia);
        cxfguiasvol.setSerie(Serie);
        cxfguiasvol.setCodFil(CodFil);
        fattvguias.setGuia(Guia);
        fattvguias.setSerie(Serie);
        fattvguias.setCodFil(CodFil);
        tesentrada.setGuia(Guia);
        tesentrada.setSerie(Serie);
        tesentrada.setCodFil(CodFil);
        tesentdn.setGuia(Guia);
        tesentdn.setSerie(Serie);
        tesentdd.setGuia(Guia);
        tesentdd.setSerie(Serie);
        tesentmd.setGuia(Guia);
        tesentmd.setSerie(Serie);

        rt_guiasdao.AtualizaRt_Guia(persistencia, rt_guias, novaserie);
        cxfguiasdao.TrocaSerieGuia(persistencia, cxfguias, novaserie);
        cxfguiasvoldao.TrocaSerieGuia(persistencia, cxfguiasvol, novaserie);
        fattvguiasdao.TrocaSerieGuia(persistencia, fattvguias, novaserie);
        tesentradadao.TrocaSerieGuia(persistencia, tesentrada, novaserie);
        tesentdndao.TrocaSerieGuia(persistencia, tesentdn, novaserie);
        tesentdddao.TrocaSerieGuia(persistencia, tesentdd, novaserie);
        tesentmddao.TrocaSerieGuia(persistencia, tesentmd, novaserie); //desativado até verificar com Marcos
    }

    public String ProcessaGuiaMobile(String lacre, String paradas, String qtguias, String ers, String tiposrvs,
            String guia, String serie, String valor, String qtlacres, String qtvolumes, String valorlacres, String observacaoLacres, String tipoLacres,
            String codFil,
            String sequencia, String chaves, String processar, String nome_servlet,
            String ParamLocal, ArquivoLog logerro, ServletContext context, String sCodPessoa, String dataAtual, String horaAtual,
            Persistencia persistencia) throws Exception {
        try {
            String xml;
            String parada, er, tiposrv, guia_atual, serie_atual, valor_atual, qtlacres_guia, qtvolumes_lacre, valores_lacre,
                    observacao_lacre, tipo_lacre;
            String falhas_processamento;
            String falhas_processamentog = "";
            String falhas_processamentol = "";
            String serie_guia_gravada;
            BigDecimal valor_parada;
            int vGuiasNAK = 0;

            LacresDao lacreDao = new LacresDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();

            StringTokenizer ptk = new StringTokenizer(paradas, ";");
            StringTokenizer ertk = new StringTokenizer(ers, ";");
            StringTokenizer srvtk = new StringTokenizer(tiposrvs, ";");

            StringTokenizer qgtk = new StringTokenizer(qtguias, ";");
            StringTokenizer gtk = new StringTokenizer(guia, ";");
            StringTokenizer stk = new StringTokenizer(serie, ";");
            StringTokenizer vtk = new StringTokenizer(valor, ";");
            //StringTokenizer chtk = new StringTokenizer(chaves,";");

            StringTokenizer qtk = new StringTokenizer(qtlacres, ";");
            StringTokenizer volumesTK = new StringTokenizer(qtvolumes, ";");
            StringTokenizer valoresTK = new StringTokenizer(valorlacres, ";");
            StringTokenizer obsTK = new StringTokenizer(observacaoLacres, ";");
            StringTokenizer tipoTK = new StringTokenizer(tipoLacres, ";");
            StringTokenizer ltk = new StringTokenizer(lacre, ";");
            StringTokenizer processartk = new StringTokenizer(processar, ";");

            int qt_guias, g_atual;

            CxForteDao cxfdao = new CxForteDao();

            CxForte cxforte;

            cxforte = cxfdao.getCxForte(new BigDecimal(codFil), persistencia);

            while (ptk.hasMoreTokens()) {
                parada = ptk.nextToken();
                er = ertk.nextToken();
                tiposrv = srvtk.nextToken();
                qt_guias = Integer.parseInt(qgtk.nextToken());
                g_atual = 1;
                valor_parada = new BigDecimal("0");
                while (g_atual <= qt_guias) {
                    guia_atual = gtk.nextToken();
                    serie_atual = stk.nextToken();
                    valor_atual = vtk.nextToken().replaceAll(",", ".");
                    qtlacres_guia = qtk.nextToken();
                    processar = processartk.nextToken();
                    if (processar.equals("1")) {
                        serie_guia_gravada = null;
                        // Processamento de guias de Assistência técnica
                        // Richard - 11/07/2018
//                        if (er.equals("R")  || (er.equals("E") && !tiposrv.equals("A"))) {
                        try {
                            serie_guia_gravada = this.processamentoGuia(persistencia, sequencia, parada,
                                    guia_atual, serie_atual, codFil, valor_atual, nome_servlet,
                                    ParamLocal, logerro, context, sCodPessoa, dataAtual, horaAtual);
                            //acumula o valor da parada
                            valor_parada = valor_parada.add(new BigDecimal(valor_atual));
                        } catch (Exception e) {
                            falhas_processamentog += Xmls.tag("guia", guia_atual)
                                    + Xmls.tag("serie", serie_atual)
                                    + Xmls.tag("valor", valor_atual)
                                    + Xmls.tag("casua", e.getMessage());
                            serie_guia_gravada = null;
                            vGuiasNAK += 1;
                        }
//                        }
                        int qtlacresGuias;
                        try {
                            qtlacresGuias = Integer.parseInt(qtlacres_guia);
                        } catch (Exception e) {
                            qtlacresGuias = 0;
                        }
                        for (int i = 1; i <= qtlacresGuias; i++) {
                            try {
                                lacre = ltk.nextToken();
                            } catch (Exception e) {
                                lacre = "0";
                            }
                            try {
                                qtvolumes_lacre = volumesTK.nextToken();
                            } catch (Exception e) {
                                qtvolumes_lacre = "1";
                            }
                            try {
                                valores_lacre = valoresTK.nextToken();
                            } catch (Exception e) {
                                valores_lacre = "0.00";
                            }
                            try {
                                observacao_lacre = obsTK.nextToken();
                                if (observacao_lacre.equals("null")) {
                                    observacao_lacre = "";
                                }
                            } catch (Exception e) {
                                observacao_lacre = "";
                            }
                            try {
                                tipo_lacre = tipoTK.nextToken();
                                if (tipo_lacre.equals("null")) {
                                    tipo_lacre = "1";
                                }
                            } catch (Exception e) {
                                tipo_lacre = "1";
                            }

                            if (er.equals("R") || isTranspCacamba(persistencia.getEmpresa())) {
                                try {
                                    if (serie_guia_gravada != null) {
                                        if (!lacreDao.LacreValido(guia_atual, serie_guia_gravada, lacre, codFil, persistencia)) {
                                            int volumeQtd;
                                            try {
                                                volumeQtd = Integer.parseInt(qtvolumes_lacre);
                                            } catch (Exception e) {
                                                volumeQtd = 1;
                                            }
                                            String valLacre = valores_lacre;
                                            if (null == valLacre || valLacre.equals("null")) {
                                                valLacre = "0.00";
                                            }
                                            if (persistencia.getEmpresa().toUpperCase().contains("TRANSVIP")) {
                                                lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia_atual, serie_guia_gravada, i, volumeQtd, lacre, 1, valLacre, persistencia);
                                            } else {
                                                lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia_atual, serie_guia_gravada, i, volumeQtd, lacre,
                                                        tipo_lacre, valLacre, observacao_lacre, persistencia);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    Trace.gerarTrace(context, "ProcessaGuiaMobile", "lacreDao.insereLacre(" + codFil + ", " + cxforte.getCodCli() + ", " + guia_atual + ", "
                                            + serie_guia_gravada + ", i, " + qtvolumes_lacre + ", " + lacre + ", 1, " + valores_lacre + ", persistencia);\r\n"
                                            + e.getMessage(), sCodPessoa, ParamLocal, logerro);
                                    vGuiasNAK += 1;
                                    falhas_processamentol += Xmls.tag("guia", guia_atual)
                                            + Xmls.tag("serie", serie_atual)
                                            + Xmls.tag("valor", valor_atual)
                                            + Xmls.tag("lacre", lacre)
                                            + Xmls.tag("casua", e.getMessage());
                                }
                            }
                        }
                    } else {
                        vGuiasNAK += 1;
                        falhas_processamentol += "processar=0 " + Xmls.tag("guia", guia_atual)
                                + Xmls.tag("serie", serie_atual)
                                + Xmls.tag("valor", valor_atual)
                                + Xmls.tag("lacre", lacre)
                                + Xmls.tag("casua", "processar!=1");
                    }
                    g_atual++;
                }
                //grava total da parada em caso de recolhimento
                if (processar.equals("1") && "R".equals(er)) {
                    rt_percDao.defineValorParada(persistencia, sequencia, valor_parada.toPlainString(), parada);
                }
            }
            falhas_processamento = Xmls.tag("erro", Xmls.tag("guias", falhas_processamentog)
                    + Xmls.tag("lacres", falhas_processamentol));
            String vGuiasNAKStr = String.format("%1$03d", vGuiasNAK);  // Formatacao 3d="000" 4d="0000" 5d = "00000" ...
            xml = vGuiasNAKStr + " " + falhas_processamento;
            return xml;
        } catch (Exception e) {
            throw new Exception("ProcessaGuiaMobile falha geral. Guia(s): " + guia + " Serie: " + serie + " " + e.getMessage());
        }
    }

    public String ProcessaGuiaMobile(String lacre, String paradas, String qtguias, String ers, String tiposrvs,
            String guia, String serie, String valor, String qtlacres, String qtvolumes, String valorlacres, String observacaoLacres, String tipoLacres,
            String codFil,
            String sequencia, String processar,
            String ParamLocal, ArquivoLog logerro, String caminho, String sCodPessoa, String dataAtual, String horaAtual,
            Persistencia persistencia) throws Exception {
        try {
            String xml;
            String parada, er, tiposrv, guia_atual, serie_atual, valor_atual, qtlacres_guia, qtvolumes_lacre, valores_lacre,
                    observacao_lacre, tipo_lacre;
            String falhas_processamento;
            String falhas_processamentog = "";
            String falhas_processamentol = "";
            String serie_guia_gravada;
            BigDecimal valor_parada;
            int vGuiasNAK = 0;

            LacresDao lacreDao = new LacresDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();

            StringTokenizer ptk = new StringTokenizer(paradas, ";");
            StringTokenizer ertk = new StringTokenizer(ers, ";");
            StringTokenizer srvtk = new StringTokenizer(tiposrvs, ";");

            StringTokenizer qgtk = new StringTokenizer(qtguias, ";");
            StringTokenizer gtk = new StringTokenizer(guia, ";");
            StringTokenizer stk = new StringTokenizer(serie, ";");
            StringTokenizer vtk = new StringTokenizer(valor, ";");
            //StringTokenizer chtk = new StringTokenizer(chaves,";");

            StringTokenizer qtk = new StringTokenizer(qtlacres, ";");
            StringTokenizer volumesTK = new StringTokenizer(qtvolumes, ";");
            StringTokenizer valoresTK = new StringTokenizer(valorlacres, ";");
            StringTokenizer obsTK = new StringTokenizer(observacaoLacres, ";");
            StringTokenizer tipoTK = new StringTokenizer(tipoLacres, ";");
            StringTokenizer ltk = new StringTokenizer(lacre, ";");
            StringTokenizer processartk = new StringTokenizer(processar, ";");

            int qt_guias, g_atual;

            CxForteDao cxfdao = new CxForteDao();

            CxForte cxforte;

            cxforte = cxfdao.getCxForte(new BigDecimal(codFil), persistencia);

            while (ptk.hasMoreTokens()) {
                parada = ptk.nextToken();
                er = ertk.nextToken();
                tiposrv = srvtk.nextToken();
                qt_guias = Integer.parseInt(qgtk.nextToken());
                g_atual = 1;
                valor_parada = new BigDecimal("0");
                while (g_atual <= qt_guias) {
                    guia_atual = gtk.nextToken();
                    serie_atual = stk.nextToken();
                    valor_atual = vtk.nextToken().replaceAll(",", ".");
                    qtlacres_guia = qtk.nextToken();
                    processar = processartk.nextToken();
                    if (processar.equals("1")) {
                        serie_guia_gravada = null;
                        // Processamento de guias de Assistência técnica
                        // Richard - 11/07/2018
//                        if (er.equals("R")  || (er.equals("E") && !tiposrv.equals("A"))) {
                        try {
                            serie_guia_gravada = this.processamentoGuia(persistencia, sequencia, parada,
                                    guia_atual, serie_atual, codFil, valor_atual,
                                    logerro, caminho, dataAtual, horaAtual);
                            //acumula o valor da parada
                            valor_parada = valor_parada.add(new BigDecimal(valor_atual));
                        } catch (Exception e) {
                            falhas_processamentog += Xmls.tag("guia", guia_atual)
                                    + Xmls.tag("serie", serie_atual)
                                    + Xmls.tag("valor", valor_atual)
                                    + Xmls.tag("casua", e.getMessage());
                            serie_guia_gravada = null;
                            vGuiasNAK += 1;
                        }
//                        }
                        int qtlacresGuias;
                        try {
                            qtlacresGuias = Integer.parseInt(qtlacres_guia);
                        } catch (Exception e) {
                            qtlacresGuias = 0;
                        }
                        for (int i = 1; i <= qtlacresGuias; i++) {
                            try {
                                lacre = ltk.nextToken();
                            } catch (Exception e) {
                                lacre = "0";
                            }
                            try {
                                qtvolumes_lacre = volumesTK.nextToken();
                            } catch (Exception e) {
                                qtvolumes_lacre = "1";
                            }
                            try {
                                valores_lacre = valoresTK.nextToken();
                            } catch (Exception e) {
                                valores_lacre = "0.00";
                            }
                            try {
                                observacao_lacre = obsTK.nextToken();
                                if (observacao_lacre.equals("null")) {
                                    observacao_lacre = "";
                                }
                            } catch (Exception e) {
                                observacao_lacre = "";
                            }
                            try {
                                tipo_lacre = tipoTK.nextToken();
                                if (tipo_lacre.equals("null")) {
                                    tipo_lacre = "1";
                                }
                            } catch (Exception e) {
                                tipo_lacre = "1";
                            }

                            if (er.equals("R") || isTranspCacamba(persistencia.getEmpresa())) {
                                try {
                                    if (serie_guia_gravada != null) {
                                        if (!lacreDao.LacreValido(guia_atual, serie_guia_gravada, lacre, codFil, persistencia)) {
                                            int volumeQtd;
                                            try {
                                                volumeQtd = Integer.parseInt(qtvolumes_lacre);
                                            } catch (Exception e) {
                                                volumeQtd = 1;
                                            }
                                            String valLacre = valores_lacre;
                                            if (null == valLacre || valLacre.equals("null")) {
                                                valLacre = "0.00";
                                            }
                                            if (persistencia.getEmpresa().toUpperCase().contains("TRANSVIP")) {
                                                lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia_atual, serie_guia_gravada, i, volumeQtd, lacre, 1, valLacre, persistencia);
                                            } else {
                                                lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia_atual, serie_guia_gravada, i, volumeQtd, lacre,
                                                        tipo_lacre, valLacre, observacao_lacre, persistencia);
                                            }
                                        }
                                    }
                                } catch (Exception e) {

                                    logerro.Grava("lacreDao.insereLacre(" + codFil + ", " + cxforte.getCodCli() + ", " + guia_atual + ", "
                                            + serie_guia_gravada + ", i, " + qtvolumes_lacre + ", " + lacre + ", 1, " + valores_lacre + ", persistencia);\r\n"
                                            + e.getMessage(), caminho);
                                    vGuiasNAK += 1;
                                    falhas_processamentol += Xmls.tag("guia", guia_atual)
                                            + Xmls.tag("serie", serie_atual)
                                            + Xmls.tag("valor", valor_atual)
                                            + Xmls.tag("lacre", lacre)
                                            + Xmls.tag("casua", e.getMessage());
                                }
                            }
                        }
                    } else {
                        vGuiasNAK += 1;
                        falhas_processamentol += "processar=0 " + Xmls.tag("guia", guia_atual)
                                + Xmls.tag("serie", serie_atual)
                                + Xmls.tag("valor", valor_atual)
                                + Xmls.tag("lacre", lacre)
                                + Xmls.tag("casua", "processar!=1");
                    }
                    g_atual++;
                }
                //grava total da parada em caso de recolhimento
                if (processar.equals("1") && "R".equals(er)) {
                    rt_percDao.defineValorParada(persistencia, sequencia, valor_parada.toPlainString(), parada);
                }
            }
            falhas_processamento = Xmls.tag("erro", Xmls.tag("guias", falhas_processamentog)
                    + Xmls.tag("lacres", falhas_processamentol));
            String vGuiasNAKStr = String.format("%1$03d", vGuiasNAK);  // Formatacao 3d="000" 4d="0000" 5d = "00000" ...
            xml = vGuiasNAKStr + " " + falhas_processamento;
            return xml;
        } catch (Exception e) {
            throw new Exception("ProcessaGuiaMobile falha geral. Guia(s): " + guia + " Serie: " + serie + " " + e.getMessage());
        }
    }

    public String ProcessaGuiaMobile(String lacre, String paradas, String qtguias, String ers, String tiposrvs,
            String guia, String serie, String valor, String qtlacres, String codFil,
            String sequencia, String senha, String chaves, String processar, String nome_servlet,
            String ParamLocal, ArquivoLog logerro, ServletContext context, String sCodPessoa, String dataAtual, String horaAtual,
            Persistencia persistencia) throws Exception {
        try {
            String xml = "";
            String parada, er, tiposrv, guia_atual, serie_atual, valor_atual, qtlacres_guia;
            String falhas_processamento = "";
            String falhas_processamentog = "";
            String falhas_processamentol = "";
            String serie_guia_gravada = "";
            BigDecimal valor_parada;
            int vGuiasNAK = 0;

            LacresDao lacreDao = new LacresDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();

            StringTokenizer ptk = new StringTokenizer(paradas, ";");
            StringTokenizer ertk = new StringTokenizer(ers, ";");
            StringTokenizer srvtk = new StringTokenizer(tiposrvs, ";");

            StringTokenizer qgtk = new StringTokenizer(qtguias, ";");
            StringTokenizer gtk = new StringTokenizer(guia, ";");
            StringTokenizer stk = new StringTokenizer(serie, ";");
            StringTokenizer vtk = new StringTokenizer(valor, ";");
            //StringTokenizer chtk = new StringTokenizer(chaves,";");

            StringTokenizer qtk = new StringTokenizer(qtlacres, ";");
            StringTokenizer ltk = new StringTokenizer(lacre, ";");
            StringTokenizer processartk = new StringTokenizer(processar, ";");

            int qt_guias, g_atual;

            CxForteDao cxfdao = new CxForteDao();

            CxForte cxforte = cxfdao.getCxForte(new BigDecimal(codFil), persistencia);

            while (ptk.hasMoreTokens()) {
                parada = ptk.nextToken();
                er = ertk.nextToken();
                tiposrv = srvtk.nextToken();
                qt_guias = Integer.parseInt(qgtk.nextToken());
                g_atual = 1;
                valor_parada = new BigDecimal("0");
                while (g_atual <= qt_guias) {
                    guia_atual = gtk.nextToken();
                    serie_atual = stk.nextToken();
                    valor_atual = vtk.nextToken().replaceAll(",", ".");
                    qtlacres_guia = qtk.nextToken();
                    processar = processartk.nextToken();

                    if (processar.equals("1")) {
                        serie_guia_gravada = null;
                        if (er.equals("R")
                                || (er.equals("E") && !tiposrv.equals("A"))) {
                            try {
                                serie_guia_gravada = this.processamentoGuia(persistencia, sequencia, parada,
                                        guia_atual, serie_atual, codFil, valor_atual, nome_servlet,
                                        ParamLocal, logerro, context, sCodPessoa, dataAtual, horaAtual);
                                //acumula o valor da parada
                                valor_parada = valor_parada.add(new BigDecimal(valor_atual));
                            } catch (Exception e) {
                                falhas_processamentog += Xmls.tag("guia", guia_atual)
                                        + Xmls.tag("serie", serie_atual)
                                        + Xmls.tag("valor", valor_atual)
                                        + Xmls.tag("casua", e.getMessage());
                                serie_guia_gravada = null;
                                vGuiasNAK += 1;
                            }
                        }
                        for (int i = 1; i <= Integer.parseInt(qtlacres_guia); i++) {
                            lacre = ltk.nextToken();
                            if (er.equals("R")) {
                                try {
                                    if (serie_guia_gravada != null) {
                                        if (!lacreDao.LacreValido(guia_atual, serie_guia_gravada, lacre, codFil, persistencia)) {
                                            lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia_atual, serie_guia_gravada, i, 1, lacre,
                                                    "1", "0", "", persistencia);
                                        }
                                    }
                                } catch (Exception e) {
                                    Trace.gerarTrace(context, "ProcessaGuiaMobile", e.getMessage(), sCodPessoa, ParamLocal, logerro);
                                    vGuiasNAK += 1;
                                    falhas_processamentol += Xmls.tag("guia", guia_atual)
                                            + Xmls.tag("serie", serie_atual)
                                            + Xmls.tag("valor", valor_atual)
                                            + Xmls.tag("lacre", lacre)
                                            + Xmls.tag("casua", e.getMessage());
                                }
                            }
                        }
                    } else {
                        vGuiasNAK += 1;
                        falhas_processamentol += "processar=0 " + Xmls.tag("guia", guia_atual)
                                + Xmls.tag("serie", serie_atual)
                                + Xmls.tag("valor", valor_atual)
                                + Xmls.tag("lacre", lacre)
                                + Xmls.tag("casua", "processar!=1");
                    }
                    g_atual++;
                }
                //grava total da parada em caso de recolhimento
                if (processar.equals("1") && "R".equals(er)) {
                    rt_percDao.defineValorParada(persistencia, sequencia, valor_parada.toPlainString(), parada);
                }
            }
            falhas_processamento = Xmls.tag("erro", Xmls.tag("guias", falhas_processamentog)
                    + Xmls.tag("lacres", falhas_processamentol));
            String vGuiasNAKStr = String.format("%1$03d", vGuiasNAK);  // Formatacao 3d="000" 4d="0000" 5d = "00000" ...
            xml = vGuiasNAKStr + " " + falhas_processamento;
            return xml;
        } catch (Exception e) {
            throw new Exception("ProcessaGuiaMobile falha geral. Guia(s): " + guia + " Serie: " + serie + " " + e.getMessage());
        }
    }

    private String processamentoGuia(Persistencia persistencia, String sequencia,
            String parada, String guia, String Serie, String codfil, String valor,
            String nome_servlet, String ParamLocal, ArquivoLog logerro, ServletContext context, String sCodPessoa,
            String data_sql, String hora)
            throws Exception {
        try {
            String observacao;
            String serie = Serie;
            String novaserie = "TSR";

            GTVSeqDao validaSerieG = new GTVSeqDao();
            CxForteDao cxfdao = new CxForteDao();
            OSDao osDao = new OSDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();
            Rt_GuiasDao procGuia = new Rt_GuiasDao();
            GTVDao procGTV = new GTVDao();

            GTV vGTV;
            CxForte cxforte;

            Integer novo_ts = 0;

            //busca código de caixa forte da filial
            cxforte = cxfdao.getCxForte(new BigDecimal(codfil), persistencia);

            boolean gravagtv, processa = true;
            int situacao = -1;

            if (!"GTE".equals(serie)) {
                //caso processamento de lote e a serie for inválida, troca para TSR
                //nesse caso o problema será solucionado na base, pelo Satellite
                //mobile não permite mais seríe incorreta
                if (!validaSerieG.validaSerieGuia(persistencia, serie)) {
                    serie = "TSR";
                    novaserie = "TS1";
                    novo_ts++;
                }
                Rt_GuiasRotas guia_rota = procGuia.getGuia(guia, serie, codfil, persistencia);
                LocalDate dtguia = null;
                try {
                    dtguia = LocalDate.parse(guia_rota.getRotas().getData());
                } catch (Exception eDtGuia) {
                }
                if (dtguia != null) {
                    Period periodo = Period.between(dtguia, LocalDate.now());
                    // se o periodo for menor que 7 dias, não processa a guia
                    if (periodo.getDays() <= 7) {
                        processa = false;
                        if (periodo.getDays() == 0) {
                            Trace.gerarTrace(context, nome_servlet, "Guia não processada pois já foi processada nesta data.\r\n"
                                    + "Guia - [" + guia + "]\r\nSerie - [" + serie + "]\r\nRota - [" + guia_rota.getRotas().getRota() + "]"
                                    + "\r\nSequencia - [" + guia_rota.getRt_guias().getSequencia() + "]"
                                    + "\r\nParada - [" + guia_rota.getRt_guias().getParada() + "]", sCodPessoa, ParamLocal, logerro);
                        } else {
                            Trace.gerarTrace(context, nome_servlet, "Guia não processada pois já foi processada " + periodo.getDays() + " dias atrás.\r\n"
                                    + "Guia - [" + guia + "]\r\nSerie - [" + serie + "]\r\nRota - [" + guia_rota.getRotas().getRota() + "]"
                                    + "\r\nSequencia - [" + guia_rota.getRt_guias().getSequencia() + "]"
                                    + "\r\nParada - [" + guia_rota.getRt_guias().getParada() + "]", sCodPessoa, ParamLocal, logerro);
                        }
                    } else if (periodo.getMonths() >= 2) {
                        //if(procGuia.isGuia(sequencia, parada, guia, serie, persistencia)){
                        while (procGuia.isGuia(guia, novaserie, codfil, persistencia)) {
                            novo_ts++;
                            novaserie = FuncoesString.RecortaString("TSR", 0, (3 - novo_ts.toString().length()));
                            novaserie += novo_ts.toString();
                        }
                        SasLibrary.GuiasMobile.troca2TSR(persistencia, guia, serie, codfil, novaserie);
                    } else {
                        if (guia_rota.getRt_guias().getSequencia().compareTo(new BigDecimal(sequencia)) != 0) {
                            while (procGuia.isGuia(guia, novaserie, codfil, persistencia)) {
                                novo_ts++;
                                novaserie = FuncoesString.RecortaString("TSR", 0, (3 - novo_ts.toString().length()));
                                novaserie += novo_ts.toString();
                            }
                            serie = novaserie;
                        }
                    }
                }
                //valida GTV
                vGTV = procGTV.validaGTV(codfil, guia, serie, persistencia);

                Trace.gerarTrace(context, nome_servlet,
                        "validaGTV OS(" + vGTV.getOS() + ") "
                        + "Situacao(" + vGTV.getSituacao() + ") "
                        + "CodFil(" + vGTV.getCodFil() + ") "
                        + "Guia(" + vGTV.getGuia() + ") "
                        + "Serie(" + vGTV.getSerie() + ") ",
                         sCodPessoa, ParamLocal, logerro);

                if (vGTV.getSituacao() == null || ("".equals(vGTV.getSituacao()))) {
                    vGTV.setSituacao("-1");
                }
                situacao = Integer.parseInt(vGTV.getSituacao());
                //analisa situação da GTV 

                if ((situacao >= 0) && (situacao < 9)) {
                    observacao = "";
                    gravagtv = false; //informa que ja existe GTV cadastrada
                } else if (situacao == -1) {
                    observacao = "";
                    gravagtv = true; //informa que nao existe GTV cadastrada
                } else {
                    Trace.gerarTrace(context, nome_servlet, "GUIA CANCELADA.\r\nsituacao - [" + vGTV.getSituacao() + "]\r\nguia - ["
                            + guia + "]\r\nserie - [" + serie + "]", sCodPessoa, ParamLocal, logerro);
                    observacao = "GUIA CANCELADA"; //situacao 9 cancelada, grava obs na gtv observacao
                    gravagtv = false; //informa que ja existe GTV cadastrada
                }
            } else {
                gravagtv = true;
                observacao = "";
                vGTV = null;
            }

            if (processa) {
                if (!gravagtv) { //apenas atualiza gtv

                    Trace.gerarTrace(context, nome_servlet, "apenas atualiza gtv " + guia, sCodPessoa, ParamLocal, logerro);

                    //verifica OS da guia
                    String CodCli1 = rt_percDao.ClienteParada(persistencia, sequencia, parada);
                    Trace.gerarTrace(context, nome_servlet, "CodCli1 " + CodCli1, sCodPessoa, ParamLocal, logerro);

                    //compara cliente OS
                    OS_Vig os = osDao.ClientesOS(persistencia, vGTV.getOS(), codfil);
                    Trace.gerarTrace(context, nome_servlet,
                            "compara cliente OS os.getCliente() (" + os.getCliente() + ")",
                            sCodPessoa, ParamLocal, logerro);

                    if ((os.getCliente() == null) || (!os.getCliente().equals(CodCli1)) || ("".equals(os.getCliente()))) {
                        //caso ocorra divergencia de OS entre a guia e o cliente, o sistema troca a OS automaticamente
                        //buscar OS desse cliente    
                        os = this.busca_OS(sequencia, parada, codfil, data_sql, cxforte.getCodCli(), persistencia);

                        Trace.gerarTrace(context, nome_servlet,
                                "buscar OS desse cliente, os.getCliente() (" + os.getCliente() + ")os.getOS() (" + os.getOS().toString() + ")",
                                sCodPessoa, ParamLocal, logerro);
                        //caso a situação da GTV seja 3, troca serie para TSR
                        if (situacao == 3) {
                            SasLibrary.GuiasMobile.troca2TSR(persistencia, guia, Serie, codfil, novaserie);
                        }
                    }
                    String sOS = "0";
                    String sKM = "0";
                    String sKMTerra = "0";
                    if (os.getOS() != null) {
                        sOS = os.getOS().toString();
                    }
                    if (os.getKM() != null) {
                        sKM = os.getKM().toString();
                    }
                    if (os.getKMTerra() != null) {
                        sKMTerra = os.getKMTerra().toString();
                    }

                    this.trata_Rt_Guias(sequencia, parada, guia, serie, valor, sOS, codfil, data_sql, hora, sKM, sKMTerra,
                            logerro, context, nome_servlet, sCodPessoa, ParamLocal, persistencia);
                    procGTV.atualizaGTV(persistencia, data_sql, hora, observacao, codfil, guia, serie, sOS);
                } else { //inserção de GTV
                    //busca_OS

                    Trace.gerarTrace(context, nome_servlet, "inserção de GTV " + guia, sCodPessoa, ParamLocal, logerro);

                    OS_Vig OS = this.busca_OS(sequencia, parada, codfil, data_sql, cxforte.getCodCli(), persistencia);
                    String sOS = "0";
                    String sKM = "0";
                    String sKMTerra = "0";
                    if (OS.getOS() != null) {
                        sOS = OS.getOS().toString();
                    }
                    if (OS.getKM() != null) {
                        sKM = OS.getKM().toString();
                    }
                    if (OS.getKMTerra() != null) {
                        sKMTerra = OS.getKMTerra().toString();
                    }

                    Trace.gerarTrace(context, nome_servlet,
                            "busca_OS OS.getOS().toString()(" + OS.getOS().toString() + ")",
                            sCodPessoa, ParamLocal, logerro);

                    procGTV.AdicionaGTV(persistencia, codfil, guia, serie, sequencia, parada, sOS, data_sql, hora);
                    this.trata_Rt_Guias(sequencia, parada, guia, serie, valor, sOS, codfil, data_sql, hora, sKM, sKMTerra,
                            logerro, context, nome_servlet, sCodPessoa, ParamLocal, persistencia);
                }
            }
            return serie;
        } catch (Exception e) {
            throw new Exception("Falha ao processar guia - " + e.getMessage());
        }
    }

    public String processamentoGuia(Persistencia persistencia, String sequencia,
            String parada, String guia, String Serie, String codfil, String valor,
            ArquivoLog logerro, String caminho, String data_sql, String hora)
            throws Exception {
        try {
            String observacao;
            String serie = Serie;
            String novaserie = "TSR";

            GTVSeqDao validaSerieG = new GTVSeqDao();
            CxForteDao cxfdao = new CxForteDao();
            OSDao osDao = new OSDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();
            Rt_GuiasDao procGuia = new Rt_GuiasDao();
            GTVDao procGTV = new GTVDao();

            GTV vGTV;
            CxForte cxforte;

            Integer novo_ts = 0;

            //busca código de caixa forte da filial
            cxforte = cxfdao.getCxForte(new BigDecimal(codfil), persistencia);

            boolean gravagtv, processa = true;
            int situacao = -1;

            if (!"GTE".equals(serie)) {
                //caso processamento de lote e a serie for inválida, troca para TSR
                //nesse caso o problema será solucionado na base, pelo Satellite
                //mobile não permite mais seríe incorreta
                if (!validaSerieG.validaSerieGuia(persistencia, serie)) {
                    serie = "TSR";
                    novaserie = "TS1";
                    novo_ts++;
                }
                Rt_GuiasRotas guia_rota = procGuia.getGuia(guia, serie, codfil, persistencia);
                LocalDate dtguia = null;
                try {
                    dtguia = LocalDate.parse(guia_rota.getRotas().getData());
                } catch (Exception eDtGuia) {
                }
                if (dtguia != null) {
                    Period periodo = Period.between(dtguia, LocalDate.now());
                    // se o periodo for menor que 7 dias, não processa a guia
                    if (periodo.getDays() <= 7) {
                        processa = false;
                        if (periodo.getDays() == 0) {
                            logerro.Grava("Guia não processada pois já foi processada nesta data.\r\n"
                                    + "Guia - [" + guia + "]\r\nSerie - [" + serie + "]\r\nRota - [" + guia_rota.getRotas().getRota() + "]"
                                    + "\r\nSequencia - [" + guia_rota.getRt_guias().getSequencia() + "]"
                                    + "\r\nParada - [" + guia_rota.getRt_guias().getParada() + "]", caminho);
                        } else {
                            logerro.Grava("Guia não processada pois já foi processada " + periodo.getDays() + " dias atrás.\r\n"
                                    + "Guia - [" + guia + "]\r\nSerie - [" + serie + "]\r\nRota - [" + guia_rota.getRotas().getRota() + "]"
                                    + "\r\nSequencia - [" + guia_rota.getRt_guias().getSequencia() + "]"
                                    + "\r\nParada - [" + guia_rota.getRt_guias().getParada() + "]", caminho);
                        }
                    } else if (periodo.getMonths() >= 2) {
                        //if(procGuia.isGuia(sequencia, parada, guia, serie, persistencia)){
                        while (procGuia.isGuia(guia, novaserie, codfil, persistencia)) {
                            novo_ts++;
                            novaserie = FuncoesString.RecortaString("TSR", 0, (3 - novo_ts.toString().length()));
                            novaserie += novo_ts.toString();
                        }
                        SasLibrary.GuiasMobile.troca2TSR(persistencia, guia, serie, codfil, novaserie);
                    } else {
                        if (guia_rota.getRt_guias().getSequencia().compareTo(new BigDecimal(sequencia)) != 0) {
                            while (procGuia.isGuia(guia, novaserie, codfil, persistencia)) {
                                novo_ts++;
                                novaserie = FuncoesString.RecortaString("TSR", 0, (3 - novo_ts.toString().length()));
                                novaserie += novo_ts.toString();
                            }
                            serie = novaserie;
                        }
                    }
                }
                //valida GTV
                vGTV = procGTV.validaGTV(codfil, guia, serie, persistencia);

                if (vGTV.getSituacao() == null || ("".equals(vGTV.getSituacao()))) {
                    vGTV.setSituacao("-1");
                }
                situacao = Integer.parseInt(vGTV.getSituacao());
                //analisa situação da GTV 

                if ((situacao > 0) && (situacao < 9)) {
                    observacao = "";
                    gravagtv = false; //informa que ja existe GTV cadastrada
                } else if (situacao == -1) {
                    observacao = "";
                    gravagtv = true; //informa que nao existe GTV cadastrada
                } else {
                    logerro.Grava("GUIA CANCELADA.\r\nsituacao - [" + vGTV.getSituacao() + "]\r\nguia - ["
                            + guia + "]\r\nserie - [" + serie + "]", caminho);
                    observacao = "GUIA CANCELADA"; //situacao 9 cancelada, grava obs na gtv observacao
                    gravagtv = false; //informa que ja existe GTV cadastrada
                }
            } else {
                gravagtv = true;
                observacao = "";
                vGTV = null;
            }

            if (processa) {
                if (!gravagtv) { //apenas atualiza gtv
                    //verifica OS da guia
                    String CodCli1 = rt_percDao.ClienteParada(persistencia, sequencia, parada);

                    //compara cliente OS
                    OS_Vig os = osDao.ClientesOS(persistencia, vGTV.getOS(), codfil);
                    if ((os.getCliente() == null) || (!os.getCliente().equals(CodCli1)) || ("".equals(os.getCliente()))) {
                        //caso ocorra divergencia de OS entre a guia e o cliente, o sistema troca a OS automaticamente
                        //buscar OS desse cliente    
                        os = this.busca_OS(sequencia, parada, codfil, data_sql, cxforte.getCodCli(), persistencia);
                        //caso a situação da GTV seja 3, troca serie para TSR
                        if (situacao == 3) {
                            SasLibrary.GuiasMobile.troca2TSR(persistencia, guia, Serie, codfil, novaserie);
                        }
                    }
                    String sOS = "0";
                    String sKM = "0";
                    String sKMTerra = "0";
                    if (os.getOS() != null) {
                        sOS = os.getOS().toString();
                    }
                    if (os.getKM() != null) {
                        sKM = os.getKM().toString();
                    }
                    if (os.getKMTerra() != null) {
                        sKMTerra = os.getKMTerra().toString();
                    }
                    /*procGuia.AdicionaRt_Guia(persistencia, codfil, sequencia, parada, guia, serie, valor, sOS,
                            sKM, sKMTerra, "SATMOB", data_sql, hora);*/
                    this.trata_Rt_Guias(sequencia, parada, guia, serie, valor, sOS, codfil, data_sql, hora, sKM, sKMTerra, logerro, caminho, persistencia);
                    procGTV.atualizaGTV(persistencia, data_sql, hora, observacao, codfil, guia, serie, sOS);
                } else { //inserção de GTV
                    //busca_OS
                    OS_Vig OS = this.busca_OS(sequencia, parada, codfil, data_sql, cxforte.getCodCli(), persistencia);
                    String sOS = "0";
                    String sKM = "0";
                    String sKMTerra = "0";
                    if (OS.getOS() != null) {
                        sOS = OS.getOS().toString();
                    }
                    if (OS.getKM() != null) {
                        sKM = OS.getKM().toString();
                    }
                    if (OS.getKMTerra() != null) {
                        sKMTerra = OS.getKMTerra().toString();
                    }
                    procGTV.AdicionaGTV(persistencia, codfil, guia, serie, sequencia, parada, sOS, data_sql, hora);
                    this.trata_Rt_Guias(sequencia, parada, guia, serie, valor, sOS, codfil, data_sql, hora, sKM, sKMTerra, logerro, caminho, persistencia);
                }
            }
            return serie;
        } catch (Exception e) {
            throw new Exception("Falha ao processar guia - " + e.getMessage());
        }
    }

    //cuida da gravação na Rt_guia
    private void trata_Rt_Guias(String sequencia, String parada, String guia, String serie,
            String valor, String sOS, String codfil, String data_sql, String hora,
            String sKM, String sKMTerra, ArquivoLog logerro, ServletContext context, String nome_servlet,
            String sCodPessoa, String ParamLocal,
            Persistencia persistencia) throws Exception {

        Rt_GuiasDao procGuia = new Rt_GuiasDao();
        Boolean bVG;
        if ("GTE".equals(serie)) {
            int tentativas = 1;
            while (tentativas <= 50) {
                try {
                    BigDecimal numguia = new BigDecimal("1");
                    numguia = numguia.add(procGuia.MaxGuia(persistencia));
                    procGuia.AdicionaRt_Guia(persistencia, codfil, sequencia, parada, numguia.toPlainString(), serie, valor, sOS,
                            sKM, sKMTerra, "SATMOB", data_sql, hora);
                    tentativas = 51;
                } catch (Exception ex) {
                    Trace.gerarTrace(context, nome_servlet, "ERRO Rt_Guias\r\n"
                            + ex.getMessage() + "\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            sCodPessoa, ParamLocal, logerro);
                } finally {
                    tentativas++;
                }
            }

        } else {
            try {
                bVG = procGuia.isGuia(guia, serie, codfil, persistencia);
                if (!bVG) {
                    procGuia.AdicionaRt_Guia(persistencia, codfil, sequencia, parada, guia, serie, valor, sOS,
                            sKM, sKMTerra, "SATMOB", data_sql, hora);
                    Trace.gerarTrace(context, nome_servlet, "Rt_Guias ADICIONADO.\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            sCodPessoa, ParamLocal, logerro);
                } else {
                    procGuia.AtualizaRt_Guia(persistencia, valor, sequencia, parada, guia, serie, sOS, codfil);
                    Trace.gerarTrace(context, nome_servlet, "Rt_Guias ATUALIZADO.\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            sCodPessoa, ParamLocal, logerro);
                }
            } catch (Exception ex) {
                Trace.gerarTrace(context, nome_servlet, "ERRO Rt_Guias\r\n"
                        + ex.getMessage() + "\r\n"
                        + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                        sCodPessoa, ParamLocal, logerro);
                throw new Exception(ex);
            }
        }

    }

    //cuida da gravação na Rt_guia
    private void trata_Rt_Guias(String sequencia, String parada, String guia, String serie,
            String valor, String sOS, String codfil, String data_sql, String hora,
            String sKM, String sKMTerra, ArquivoLog logerro, String caminho,
            Persistencia persistencia) throws Exception {

        Rt_GuiasDao procGuia = new Rt_GuiasDao();
        Boolean bVG;
        if ("GTE".equals(serie)) {
            int tentativas = 1;
            while (tentativas <= 50) {
                try {
                    BigDecimal numguia = new BigDecimal("1");
                    numguia = numguia.add(procGuia.MaxGuia(persistencia));
                    procGuia.AdicionaRt_Guia(persistencia, codfil, sequencia, parada, numguia.toPlainString(), serie, valor, sOS,
                            sKM, sKMTerra, "SATMOB", data_sql, hora);
                    tentativas = 51;
                } catch (Exception ex) {
                    logerro.Grava("ERRO Rt_Guias\r\n"
                            + ex.getMessage() + "\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            caminho);
                } finally {
                    tentativas++;
                }
            }

        } else {
            try {
                bVG = procGuia.isGuia(guia, serie, codfil, persistencia);
                if (!bVG) {
                    procGuia.AdicionaRt_Guia(persistencia, codfil, sequencia, parada, guia, serie, valor, sOS,
                            sKM, sKMTerra, "SATMOB", data_sql, hora);
                    logerro.Grava("Rt_Guias ADICIONADO.\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            caminho);
                } else {
                    procGuia.AtualizaRt_Guia(persistencia, valor, sequencia, parada, guia, serie, sOS, codfil);
                    logerro.Grava("Rt_Guias ATUALIZADO.\r\n"
                            + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                            caminho);
                }
            } catch (Exception ex) {
                logerro.Grava("ERRO Rt_Guias\r\n"
                        + ex.getMessage() + "\r\n"
                        + " guia: " + guia + "; serie: " + serie + "; sequencia: " + sequencia + "; parada: " + parada + "; valor: " + valor + "; OS: " + sOS,
                        caminho);
                throw new Exception(ex);
            }
        }

    }

    // método auxiliar do metodo ProocessaGuiaMobile do método auxiliar processamentoGuia
    private OS_Vig busca_OS(String sequencia, String parada, String codfil, String data_sql, String Cxf, Persistencia persistencia) throws Exception {
        try {
            String codclipassar;
            Rt_PercRt_Perc rt_rt_perc = new Rt_PercRt_Perc();
            Rt_PercDao rt_percDao = new Rt_PercDao();
            List<Rt_PercRt_Perc> rtperc = rt_percDao.ClientesParada(sequencia, parada, persistencia);
            for (Rt_PercRt_Perc rtPerc : rtperc) {
                rt_rt_perc.setDestino(rtPerc.getDestino());
                rt_rt_perc.setOrigem(rtPerc.getOrigem());
            }
            String Cli1 = rt_rt_perc.getOrigem().getCodCli1();
            String Cli2 = rt_rt_perc.getOrigem().getCodCli2();
            String Cli3 = rt_rt_perc.getDestino().getCodCli1();
            try {
                if (((Cli2 == null) && (Cli3 == null))
                        || (("".equals(Cli2)) && ("".equals(Cli3)))
                        || ((Cli2 == null) && ("".equals(Cli3)))
                        || (("".equals(Cli2)) && (Cli3 == null))) {
                    codclipassar = Cli1;
                } else if ((Cli2 == null) || ("".equals(Cli2)) || (Cli2.equals(Cli3))) {
                    codclipassar = Cli3;
                } else {
                    codclipassar = Cli2;
                }
            } catch (Exception e) {
                codclipassar = Cli1;
            }
            //busca OS
            OSDao buscaOS = new OSDao();
            OS_Vig OS;
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            Boolean pedido = rt_PercDao.existePedido(sequencia, parada, persistencia);
            if (pedido) {
                PedidoDao pedidoDao = new PedidoDao();
                BigDecimal OSPedido = pedidoDao.getOS(sequencia, parada, codfil, persistencia); // Alteração 04/08/2020 buscando número do pedido em rt_perc
                // Caso a OS do pedido venha zerada, faz a busca convencional.
                if (null == OSPedido || OSPedido.compareTo(BigDecimal.ZERO) == 0) {
                    OS = buscaOS.BuscaOS(Cli1, codclipassar, "", codfil, data_sql, 5, Cxf, persistencia);
                } else {
                    OS = buscaOS.ClientesOS(persistencia, OSPedido, codfil);
                }
            } else {
                OS = buscaOS.BuscaOS(Cli1, codclipassar, "", codfil, data_sql, 5, Cxf, persistencia);
            }
            return OS;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }
}
