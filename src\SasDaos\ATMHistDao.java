package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ATMHist;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ATMHistDao {

    // create
    public boolean gravaATMHistDao(ATMHist atmhist, Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "insert into ATMHist (OS,CodFil,Sequencia,Historico,Operador,Dt_Alter,Hr_Alter)"
                + "Values(?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmhist.getOS());
            consulta.setBigDecimal(atmhist.getCodFil());
            consulta.setBigDecimal(atmhist.getSequencia());
            consulta.setString(atmhist.getHistorico());
            consulta.setString(atmhist.getOperador());
            consulta.setString(atmhist.getDt_Alter().toString());
            consulta.setString(atmhist.getHr_Alter());

            consulta.insert();
            consulta.Close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    public List<ATMHist> buscaATMHist(Persistencia persistencia) throws Exception {
        List<ATMHist> listATMHist;
        try {
            ATMHist atmhist;
            Consulta consult = new Consulta("select OS,CodFil,Sequencia,Historico,Operador,Dt_Alter,Hr_Alter "
                    + "from atmhist", persistencia);
            consult.insert();
            listATMHist = new ArrayList();
            while (consult.Proximo()) {
                atmhist = new ATMHist();
                atmhist.setOS(consult.getString("OS"));
                atmhist.setCodFil(consult.getString("CodFil"));
                atmhist.setSequencia(consult.getString("Sequencia"));
                atmhist.setHistorico(consult.getString("Historico"));
                atmhist.setOperador(consult.getString("Operador"));
                atmhist.setDt_Alter(consult.getDate("Dt_Alter").toLocalDate());
                atmhist.setHr_Alter(consult.getString("Hr_Alter"));
                listATMHist.add(atmhist);
            }
            consult.Close();
        } catch (Exception e) {
            listATMHist = null;
            throw new Exception("ATMHistDao.buscaATMHist - " + e.getMessage() + "\r\n"
                    + " select OS,CodFil,Sequencia,Historico,Operador,Dt_Alter,Hr_Alter "
                    + "from atmhist");
        }
        return listATMHist;
    }

    // update
    public void atualizaATMHist(ATMHist atmhist, Persistencia persistencia) throws Exception {
        String sql = "update ATMHist set OS=?, CodFil=?, Sequencia=?, Historico=?, Operador=?, Dt_Alter=?, Hr_Alter=?"
                + "where OS=? and CodFil=? and Sequencia=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmhist.getOS());
            consulta.setBigDecimal(atmhist.getCodFil());
            consulta.setBigDecimal(atmhist.getSequencia());
            consulta.setString(atmhist.getHistorico());
            consulta.setString(atmhist.getOperador());
            consulta.setString(atmhist.getDt_Alter().toString());
            consulta.setString(atmhist.getHr_Alter());
            consulta.setBigDecimal(atmhist.getOS());
            consulta.setBigDecimal(atmhist.getCodFil());
            consulta.setBigDecimal(atmhist.getSequencia());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMHistDao.atualizaATMHist - " + e.getMessage() + "\r\n"
                    + " update ATMHist set OS=" + atmhist.getOS() + ", CodFil=" + atmhist.getCodFil() + ", "
                    + " Sequencia=" + atmhist.getSequencia() + ", Historico=" + atmhist.getHistorico() + ", "
                    + " Operador=" + atmhist.getOperador() + ", Dt_Alter=" + atmhist.getDt_Alter().toString() + ", Hr_Alter=" + atmhist.getHr_Alter()
                    + " where OS=" + atmhist.getOS() + " and CodFil=" + atmhist.getCodFil() + " and Sequencia=" + atmhist.getSequencia());
        }
    }

    // delete
    public void excluirATMHist(ATMHist atmhist, Persistencia persistencia) throws Exception {
        String sql = "delete from atmhist where OS=? and CodFil=? and Sequencia=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmhist.getOS());
            consulta.setBigDecimal(atmhist.getCodFil());
            consulta.setBigDecimal(atmhist.getSequencia());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMHistDao.excluirATMHist - " + e.getMessage() + "\r\n"
                    + "delete from atmhist where OS=" + atmhist.getOS() + " and CodFil=" + atmhist.getCodFil() + " and Sequencia=" + atmhist.getSequencia());
        }
    }
}
