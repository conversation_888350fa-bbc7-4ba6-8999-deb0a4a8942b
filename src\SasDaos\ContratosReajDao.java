/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ContratosReaj;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContratosReajDao {

    private final String sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param primeiro elemento inicial de paginação
     * @param linhas quantidade de elementos de paginação
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<ContratosReaj> listarReajustesPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            List<ContratosReaj> itens = new ArrayList<>();

            String sql = "Select Case \n"
                    + "    when Formula = '1' then 'ABTV      ' \n"
                    + "    when Formula = '2' then 'COMPOSTO  ' \n"
                    + "    else null end as FormulaDesc , ContratosReaj.* from ContratosReaj \n"
                    + "where ContratosReaj.Contrato   = ? \n";
            // + "and ContratosReaj.CodFil = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            sql += "ORDER BY DtBase Desc \n" + sqlPaginacao;
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);
            consulta.select();
            ContratosReaj item;
            while (consulta.Proximo()) {
                item = new ContratosReaj();

                item.setContrato(consulta.getString("Contrato"));
                item.setFormulaDesc(consulta.getString("FormulaDesc"));
                item.setCodFil(consulta.getString("CodFil"));
                item.setDtBase(consulta.getString("DtBase"));
                item.setOrdem(consulta.getString("Ordem"));
                item.setIndiceDF(consulta.getString("IndiceDF"));
                item.setFormula(consulta.getString("Formula"));
                item.setIndMOSolic(consulta.getString("IndMOSolic"));
                item.setIndMOAprov(consulta.getString("IndMOAprov"));
                item.setIndCombSolic(consulta.getString("IndCombSolic"));
                item.setIndCombAprov(consulta.getString("IndCombAprov"));
                item.setIndDFSolic(consulta.getString("IndDFSolic"));
                item.setIndDFAprov(consulta.getString("IndDFAprov"));
                item.setIndiceSolic(consulta.getString("IndiceSolic"));
                item.setIndiceFinal(consulta.getString("IndiceFinal"));
                item.setEmbarques(consulta.getString("Embarques"));
                item.setTE(consulta.getString("TE"));
                item.setEnvelopes(consulta.getString("Envelopes"));
                item.setMilheiros(consulta.getString("Milheiros"));
                item.setKM(consulta.getString("KM"));
                item.setFixoMensal(consulta.getString("FixoMensal"));
                item.setMalotes(consulta.getString("Malotes"));
                item.setCargaDescarga(consulta.getString("CargaDescarga"));
                item.setAST(consulta.getString("AST"));
                item.setKitTroco(consulta.getString("KitTroco"));
                item.setUnidadesProdServ(consulta.getString("UnidadesProdServ"));
                item.setAdValorem(consulta.getString("AdValorem"));
                item.setCodCarta(consulta.getString("CodCarta"));
                item.setDtCarta(consulta.getString("DtCarta"));
                item.setObs(consulta.getString("Obs"));
                item.setOperador(consulta.getString("Operador"));
                item.setDt_Alter(consulta.getString("Dt_Alter"));
                item.setHr_Alter(consulta.getString("Hr_Alter"));
                item.setOperReaj(consulta.getString("OperReaj"));
                item.setDtReaj(consulta.getString("DtReaj"));
                item.setHrReaj(consulta.getString("HrReaj"));

                itens.add(item);
            }

            return itens;
        } catch (Exception e) {
            throw new Exception("ContratosReajDao.listarReajustesPaginada - " + e.getMessage());
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public int contagemReajustes(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "Select COUNT(*) AS total from ContratosReaj \n"
                    + "WHERE ContratosReaj.Contrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int total = 0;
            while (consulta.Proximo()) {
                total = consulta.getInt("total");
            }

            return total;
        } catch (Exception e) {
            throw new Exception("ContratosReajDao.contagemReajustes - " + e.getMessage());
        }
    }
}
