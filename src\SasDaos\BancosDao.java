/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Bancos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BancosDao {

    public Bancos buscarBanco(String bb, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Bancos "
                    + " WHERE Banco = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(bb);
            consulta.select();
            Bancos banco = null;
            if (consulta.Proximo()) {
                banco = new Bancos();
                banco.setBanco(consulta.getString("Banco"));
                banco.setNome(consulta.getString("Nome"));
                banco.setNRed(consulta.getString("NRed"));
                banco.setOperador(consulta.getString("Operador"));
                banco.setDt_Alter(consulta.getString("Dt_Alter"));
                banco.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.close();
            return banco;
        } catch (Exception e) {
            throw new Exception("BancosDao.obterListaBancos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Bancos "
                    + " WHERE Banco = " + bb);
        }
    }

    public List<Bancos> buscarBancos(String query, Persistencia persistencia) throws Exception {
        try {
            List<Bancos> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Bancos "
                    + " WHERE Banco LIKE ? OR Nome LIKE ? OR NRed LIKE ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.select();
            Bancos banco;
            while (consulta.Proximo()) {
                banco = new Bancos();
                banco.setBanco(consulta.getString("Banco"));
                banco.setNome(consulta.getString("Nome"));
                banco.setNRed(consulta.getString("NRed"));
                banco.setOperador(consulta.getString("Operador"));
                banco.setDt_Alter(consulta.getString("Dt_Alter"));
                banco.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(banco);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("BancosDao.obterListaBancos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Bancos "
                    + " WHERE Banco LIKE %" + query + "% OR Nome LIKE %" + query + "% OR NRed LIKE %" + query + "% ");
        }
    }

    public List<Bancos> obterListaBancos(Persistencia persistencia) throws Exception {
        try {
            List<Bancos> retorno = new ArrayList<>();
            String sql = " SELECT * FROM Bancos ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Bancos banco;
            while (consulta.Proximo()) {
                banco = new Bancos();
                banco.setBanco(consulta.getString("Banco"));
                banco.setNome(consulta.getString("Nome"));
                banco.setNRed(consulta.getString("NRed"));
                banco.setOperador(consulta.getString("Operador"));
                banco.setDt_Alter(consulta.getString("Dt_Alter"));
                banco.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.add(banco);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("BancosDao.obterListaBancos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Bancos ");
        }
    }
}
