package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.EmailsEnviar;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EmailsEnviarDao {

    /**
     * Verifica se o email já foi enviado.
     *
     * @param email
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean emailEnviado(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            String sql = " select flag_enviado from emailsenviar where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(email.getSequencia());
            consulta.select();
            String flag = null;
            while (consulta.Proximo()) {
                flag = consulta.getString("flag_enviado");
            }
            consulta.Close();
            return null == flag || flag.equals("*") || flag.equals("e");
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.emailEnviado - " + e.getMessage() + "\r\n"
                    + " select flag_enviado from emaillsenviar where sequencia = " + email.getSequencia());
        }
    }

    public void InserirEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into EmailsEnviar (Sequencia,SMTP,Dest_Email,Dest_Nome,Remet_Email,"
                    + " Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,Porta,Flag_Enviado,DT_Inclusao,Hr_Inclusao,"
                    + " codFil, codCli, codContato, parametro) "
                    + " Values ((Select Max(Sequencia)+1 from EmailsEnviar),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(email.getSmtp());
            consulta.setString(email.getDest_email());
            consulta.setString(email.getDest_nome());
            consulta.setString(email.getRemet_email());
            consulta.setString(email.getRemet_nome());
            consulta.setString(email.getAssunto());
            consulta.setString(email.getMensagem());
            consulta.setString(email.getAut_login());
            consulta.setString(email.getAut_senha());
            consulta.setInt(email.getPorta());
            consulta.setString("");
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(email.getCodFil());
            consulta.setString(email.getCodCli());
            consulta.setString(email.getCodContato());
            consulta.setString(email.getParametro());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.InserirEmail  - " + e.getMessage() + "\r\n"
                    + "Insert into EmailsEnviar (Sequencia,SMTP,Dest_Email,Dest_Nome,Remet_Email,"
                    + " Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,Porta,Flag_Enviado,DT_Inclusao,Hr_Inclusao) "
                    + " Values ((Select Max(Sequencia)+1 from EmailsEnviar)," + email.getSmtp() + "," + email.getDest_email() + "," + email.getDest_nome() + ","
                    + email.getRemet_email() + "," + email.getRemet_nome() + "," + email.getAssunto() + "," + email.getMensagem() + "," + email.getAut_login() + ","
                    + email.getAut_senha() + "," + email.getPorta() + "," + "" + "," + DataAtual.getDataAtual("SQL") + "," + DataAtual.getDataAtual("HORA") + ","
                    + email.getCodFil() + "," + email.getCodCli() + "," + email.getCodContato() + "," + email.getParametro() + ")");
        }
    }

    public String inserirEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        String sequencia = "null";
        try {
            /*sequencia = "Select convert(bigint,Max(Sequencia)+1) seq from EmailsEnviar";
            Consulta consulta = new Consulta(sequencia, persistencia);
            consulta.select();
            while (consulta.Proximo()) {
                sequencia = consulta.getString("seq");
            }
            consulta.Close();*/
            
            String sql = " Insert into EmailsEnviar (Sequencia,SMTP,Dest_Email,Dest_Nome,Remet_Email,"
                    + " Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,Porta,Flag_Enviado,DT_Inclusao,Hr_Inclusao,"
                    + " codFil, codCli, codContato, parametro) "
                    + " Values ((Select convert(bigint,Max(Sequencia)+1) from EmailsEnviar),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\n";

            //sql +=" SELECT @Sequencia seq;";
            Consulta consulta = new Consulta(sql, persistencia);
            //consulta.setString(sequencia);
            consulta.setString(email.getSmtp());
            consulta.setString(email.getDest_email());
            consulta.setString(email.getDest_nome());
            consulta.setString(email.getRemet_email());
            consulta.setString(email.getRemet_nome());
            //consulta.setString(email.getAssunto() + " - " + sequencia.replace(".0", ""));
            consulta.setString(email.getAssunto());
            consulta.setString(email.getMensagem());
            consulta.setString(email.getAut_login());
            consulta.setString(email.getAut_senha());
            consulta.setInt(email.getPorta());
            consulta.setString("");
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(email.getCodFil());
            consulta.setString(email.getCodCli());
            consulta.setString(email.getCodContato());
            consulta.setString(email.getParametro());
            consulta.insert();

            // Capturar sequencia gerada
            sql = "SELECT \n"
                    + " CONVERT(bigint,MAX(sequencia)) seq\n"
                    + " FROM EmailsEnviar\n"
                    + " WHERE parametro   = ?\n"
                    + " AND   Remet_Nome  = ?\n"
                    + " AND   Assunto     = ?\n"
                    + " AND   DT_Inclusao = ?\n"
                    + " AND   Hr_Inclusao = ?\n"
                    + " AND   codFil      = ?";

            consulta = new Consulta(sql, persistencia);
            
            consulta.setString(email.getParametro());
            consulta.setString(email.getRemet_nome());
            consulta.setString(email.getAssunto());
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(email.getCodFil());

            consulta.select();
            while (consulta.Proximo()) {
                sequencia = consulta.getString("seq");
            }

            consulta.close();
            return sequencia;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.inserirEmail  - " + e.getMessage() + "\r\n"
                    + "Insert into EmailsEnviar (Sequencia,SMTP,Dest_Email,Dest_Nome,Remet_Email,"
                    + " Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,Porta,Flag_Enviado,DT_Inclusao,Hr_Inclusao,"
                    + " codFil, codCli, codContato, parametro) "
                    + " Values (" + sequencia + "," + email.getSmtp() + "," + email.getDest_email() + "," + email.getDest_nome() + "," + email.getRemet_email() + ","
                    + email.getRemet_nome() + "," + email.getAssunto() + " - " + sequencia.replace(".0", "") + "," + email.getMensagem() + "," + email.getAut_login() + ","
                    + email.getAut_senha() + "," + email.getPorta() + "," + "" + "," + DataAtual.getDataAtual("SQL") + "," + DataAtual.getDataAtual("HORA") + ","
                    + email.getCodFil() + "," + email.getCodCli() + "," + email.getCodContato() + "," + email.getParametro() + ")");
        }
    }

    public List<EmailsEnviar> emailsCliente(String codCli, String codFil, String data1, String data2, String parametro, Persistencia persistencia) throws Exception {
        List<EmailsEnviar> consulta = new ArrayList<>();
        try {
            String sql = " Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,Hr_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where codCli = ? and codFil = ? and parametro = ? ";

            if (!data1.equals("") || !data2.equals("")) {
                sql = sql + " and Dt_Inclusao between ? and ? ";
            }
            sql = sql + " order by Dt_Inclusao desc, Hr_Inclusao desc ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codCli);
            consult.setString(codFil);
            consult.setString(parametro);

            if (!data1.equals("") || !data2.equals("")) {
                consult.setString(data1);
                consult.setString(data2);
            }

            consult.select();
            EmailsEnviar temp;
            while (consult.Proximo()) {
                temp = new EmailsEnviar();
                temp.setSequencia(consult.getString("Sequencia"));
                temp.setSmtp(consult.getString("Smtp"));
                temp.setDest_email(consult.getString("Dest_Email"));
                temp.setDest_nome(consult.getString("Dest_Nome"));
                temp.setRemet_nome(consult.getString("Remet_Nome"));
                temp.setRemet_email(consult.getString("Remet_Email"));
                temp.setAssunto(consult.getString("Assunto"));
                temp.setMensagem(consult.getString("Mensagem"));
                temp.setAut_login(consult.getString("Aut_Login"));
                temp.setAut_senha(consult.getString("Aut_Senha"));
                temp.setPorta(consult.getInt("Porta"));
                temp.setCodFil(consult.getString("codFil"));
                temp.setCodCli(consult.getString("codCli"));
                temp.setCodContato(consult.getString("codContato"));
                temp.setParametro(consult.getString("parametro"));
                temp.setFlag_Enviado(consult.getString("Flag_Enviado"));
                temp.setDt_Inclusao(consult.getString("Dt_Inclusao"));
                temp.setHr_Inclusao(consult.getString("Hr_Inclusao"));
                temp.setDt_Envio(consult.getString("Dt_Envio"));
                temp.setHr_Envio(consult.getString("HR_Envio"));
                consulta.add(temp);
            }
            consult.Close();
            persistencia.FechaConexao();
            return consulta;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.emailsCliente  - " + e.getMessage() + "\r\n"
                    + "Select Sequencia, Smtp, Dest_Email,Dest_Nome,"
                    + "Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,"
                    + "Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio"
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado <> '*'");
        }
    }

    public void reEnviarEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            String sql = "update emailsenviar set flag_enviado = '', Dest_Email = ?, Dest_Nome = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(email.getDest_email());
            consulta.setString(email.getDest_nome());
            consulta.setBigDecimal(email.getSequencia());
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.enviarEmail - " + e.getMessage() + "\r\n"
                    + "update emailsenviar set flag_enviado = '', Dest_Email = " + email.getDest_email() + ", Dest_Nome = " + email.getDest_nome()
                    + " where sequencia = " + email.getSequencia().toPlainString());
        }
    }

    public void enviarEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            String sql = "update emailsenviar set flag_enviado = '*', dt_envio = ?, hr_envio = ? "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setBigDecimal(email.getSequencia());
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.enviarEmail - " + e.getMessage() + "\r\n"
                    + "update emailsenviar set flag_enviado = '', Dest_Email = " + email.getDest_email() + ", Dest_Nome = " + email.getDest_nome()
                    + " where sequencia = " + email.getSequencia().toPlainString());
        }
    }

    public void naoEnviarEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            String sql = "update emailsenviar set flag_enviado = '*' "
                    + " where sequencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(email.getSequencia());
            consulta.update();
            consulta.close();
            persistencia.FechaConexao();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.enviarEmail - " + e.getMessage() + "\r\n"
                    + "update emailsenviar set flag_enviado = '*' "
                    + " where sequencia = " + email.getSequencia().toPlainString());
        }
    }

    public List<EmailsEnviar> Consulta(Persistencia persistencia) throws Exception {
        List<EmailsEnviar> consulta = new ArrayList();
        Consulta consult;
        try {
            consult = new Consulta("Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado = '' or flag_enviado is null "
                    + " ORDER BY Sequencia", persistencia);

            consult.select();
            EmailsEnviar temp;
            while (consult.Proximo()) {
                temp = new EmailsEnviar();
                temp.setSequencia(consult.getString("Sequencia"));
                temp.setSmtp(consult.getString("Smtp"));
                temp.setDest_email(consult.getString("Dest_Email"));
                temp.setDest_nome(consult.getString("Dest_Nome"));
                temp.setRemet_nome(consult.getString("Remet_Nome"));
                temp.setRemet_email(consult.getString("Remet_Email"));
                temp.setAssunto(consult.getString("Assunto"));
                temp.setMensagem(consult.getString("Mensagem"));
                temp.setAut_login(consult.getString("Aut_Login"));
                temp.setAut_senha(consult.getString("Aut_Senha"));
                temp.setPorta(consult.getInt("Porta"));
                temp.setCodFil(consult.getString("codFil"));
                temp.setCodCli(consult.getString("codCli"));
                temp.setCodContato(consult.getString("codContato"));
                temp.setParametro(consult.getString("parametro"));
                consulta.add(temp);
            }
            consult.Close();
            return consulta;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.Consulta  - " + e.getMessage() + "\r\n"
                    + "Select Sequencia, Smtp, Dest_Email,Dest_Nome,"
                    + "Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha,"
                    + "Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio"
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado = '' or flag_enviado is null ");
        }
    }

    /**
     * Lista os emails dos clientes do grupo GSI
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<EmailsEnviar> listaEmailsGloval(Persistencia persistencia) throws Exception {
        try {
            List<EmailsEnviar> consulta = new ArrayList<>();
            Consulta consult = new Consulta("Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado <> '*' and "
                    + "     parametro in ('SATGLOVAL','SATPROSECUR')", persistencia);

            consult.select();
            while (consult.Proximo()) {
                EmailsEnviar temp = new EmailsEnviar();
                temp.setSequencia(consult.getString("Sequencia"));
                temp.setSmtp(consult.getString("Smtp"));
                temp.setDest_email(consult.getString("Dest_Email"));
                temp.setDest_nome(consult.getString("Dest_Nome"));
                temp.setRemet_nome(consult.getString("Remet_Nome"));
                temp.setRemet_email(consult.getString("Remet_Email"));
                temp.setAssunto(consult.getString("Assunto"));
                temp.setMensagem(consult.getString("Mensagem"));
                temp.setAut_login(consult.getString("Aut_Login"));
                temp.setAut_senha(consult.getString("Aut_Senha"));
                temp.setPorta(consult.getInt("Porta"));
                temp.setCodFil(consult.getString("codFil"));
                temp.setCodCli(consult.getString("codCli"));
                temp.setCodContato(consult.getString("codContato"));
                temp.setParametro(consult.getString("parametro"));
                consulta.add(temp);
            }
            consult.Close();
            return consulta;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.listaEmailsGloval  - " + e.getMessage() + "\r\n"
                    + "Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado <> '*' and "
                    + "     parametro in ('SATGLOVAL','SATPROSECUR')");
        }
    }

    /**
     * Lista os emails
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<EmailsEnviar> listaEmails(Persistencia persistencia) throws Exception {
        try {
            List<EmailsEnviar> consulta = new ArrayList<>();
            Consulta consult = new Consulta("Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado <> '*' and "
                    + "     (parametro not in ('SATGLOVAL','SATPROSECUR') or parametro is null)", persistencia);

            consult.select();
            while (consult.Proximo()) {
                EmailsEnviar temp = new EmailsEnviar();
                temp.setSequencia(consult.getString("Sequencia"));
                temp.setSmtp(consult.getString("Smtp"));
                temp.setDest_email(consult.getString("Dest_Email"));
                temp.setDest_nome(consult.getString("Dest_Nome"));
                temp.setRemet_nome(consult.getString("Remet_Nome"));
                temp.setRemet_email(consult.getString("Remet_Email"));
                temp.setAssunto(consult.getString("Assunto"));
                temp.setMensagem(consult.getString("Mensagem"));
                temp.setAut_login(consult.getString("Aut_Login"));
                temp.setAut_senha(consult.getString("Aut_Senha"));
                temp.setPorta(consult.getInt("Porta"));
                temp.setCodFil(consult.getString("codFil"));
                temp.setCodCli(consult.getString("codCli"));
                temp.setCodContato(consult.getString("codContato"));
                temp.setParametro(consult.getString("parametro"));
                consulta.add(temp);
            }
            consult.Close();
            return consulta;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.listaEmails  - " + e.getMessage() + "\r\n"
                    + "Select Sequencia, Smtp, Dest_Email,Dest_Nome, "
                    + " Remet_Email,Remet_Nome,Assunto,Mensagem,Aut_Login,Aut_Senha, "
                    + " Porta,Flag_Enviado,Dt_Inclusao,Hr_Inclusao,Dt_Envio,HR_Envio, "
                    + " codFil, codCli, codContato, parametro "
                    + " from emailsenviar where flag_enviado <> '*' and "
                    + "     (parametro not in ('SATGLOVAL','SATPROSECUR') or parametro is null)");
        }
    }

    public void exlcluir(String sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "update EmailsEnviar set flag_enviado = '*', "
                    + " dt_envio = ?,"
                    + " hr_envio = ? "
                    + " where Sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.Exlcluir  - " + e.getMessage() + "\r\n"
                    + "update EmailsEnviar set flag_enviado = '*', "
                    + " dt_envio = '" + DataAtual.getDataAtual("SQL") + "',"
                    + " hr_envio = '" + DataAtual.getDataAtual("HORA") + "'"
                    + " where Sequencia = " + sequencia);
        }
    }

    public void falha(String sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "update EmailsEnviar set flag_enviado = 'e', "
                    + " dt_envio = ?,"
                    + " hr_envio = ? "
                    + " where Sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(sequencia);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.falha  - " + e.getMessage() + "\r\n"
                    + "update EmailsEnviar set flag_enviado = 'e', "
                    + " dt_envio = '" + DataAtual.getDataAtual("SQL") + "',"
                    + " hr_envio = '" + DataAtual.getDataAtual("HORA") + "'"
                    + " where Sequencia = " + sequencia);
        }
    }

    /**
     * Obtem a quantidade de registros dos emails que já foram enviandos em uma
     * determinada data
     *
     * @param data data da inclusao
     * @param persistencia Conexao com o banco de dados
     * @return quantidade de registros
     * @throws Exception
     */
    public int quantidadeEmailsEnviados(String data, Persistencia persistencia) throws Exception {
        int quantidade = 0;
        try {
            String sql = "SELECT COUNT(*) qtd FROM emailsenviar"
                    + " WHERE DT_Envio = ? AND Flag_Enviado = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString("*");
            consulta.select();

            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarDao.quantidadeEmailsEnviados  - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM emailsenviar WHERE DT_Envio = " + data
                    + " AND Flag_Enviado = *");
        }
        return quantidade;
    }
}
