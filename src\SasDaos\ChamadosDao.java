package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Chamados;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ChamadosDao {

    /**
     *
     * @param chamados
     * @param persistencia
     * @return
     */
    public boolean gravaChamado(Chamados chamados, Persistencia persistencia) throws SQLException, Exception {
        boolean retorno;
        String sql = "insert into Chamados (Sequencia ,Descricao ,"
                + "detalhes ,CodPessoa ,CodResponsavel ,Situacao ,Tipo ,Empresa ,Dt_<PERSON> ,Hr_<PERSON> ,Dt_<PERSON><PERSON> ,Hr_Fecha) Values (?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            PreparedStatement stm = persistencia.getState(sql);
            stm.setBigDecimal(1, chamados.getSequencia());
            stm.setString(2, chamados.getDescricao());
            stm.setString(3, chamados.getDetalhes());
            stm.setBigDecimal(4, chamados.getCodPessoa());
            stm.setBigDecimal(5, chamados.getCodResponsavel());
            stm.setBigDecimal(6, chamados.getSituacao());
            stm.setBigDecimal(7, chamados.getTipo());
            stm.setString(8, chamados.getEmpresa());
            stm.setString(9, chamados.getDt_Abertura());
            stm.setString(10, chamados.getHr_Abertura());
            stm.setString(11, chamados.getDt_Fecha());
            stm.setString(12, chamados.getHr_Fecha());
            stm.execute();
            stm.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("ChamadoDAO.gravaChamado - " + e.getMessage() + "\r\n"
                    + "insert into Chamados (Sequencia ,Descricao ,detalhes ,CodPessoa ,CodResponsavel ,Situacao ,Tipo ,Empresa ,Dt_Abertura ,Hr_Abertura ,Dt_Fecha ,Hr_Fecha"
                    + "Values (" + chamados.getSequencia() + "," + chamados.getDescricao() + "," + chamados.getDetalhes() + "," + chamados.getCodPessoa() + "," + chamados.getCodResponsavel() + "," + chamados.getSituacao() + "," + chamados.getTipo() + "," + chamados.getEmpresa() + "," + chamados.getDt_Abertura() + "," + chamados.getHr_Abertura() + "," + chamados.getDt_Fecha() + "," + chamados.getHr_Fecha() + ")");
        }
        return retorno;
    }

    public String getSequencia(Persistencia persistencia) throws Exception {
        try {
            String chamados = null;
            BigDecimal sequencia = new BigDecimal("1");
            Consulta consult = new Consulta("select MAX(codigo) + 1 codigo "
                    + "from chamados", persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    sequencia = new BigDecimal(consult.getString("Sequencia"));
                } catch (Exception e) {
                    sequencia = new BigDecimal("1");
                }
            }
            chamados = sequencia.toPlainString();
            consult.Close();
            return chamados;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getCodigo - " + e.getMessage() + "\r\n"
                    + "select MAX(codigo) + 1 codigo "
                    + "from pessoa");
        }
    }

    public List<Chamados> ListaChamados(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<Chamados> retorno = new ArrayList<>();
            String sql = "select Chamados.+,descricao,detalhes,codpessoa,codresponsavel,situacao,tipo,empresa, dt_abertura, hr_abertura,dt_fecha,hr_fecha"
                    + "from Chamados"
                    + " left join pessoa on pessoa.codigo = chamados.codpessoa "
                    + ((null == codPessoa) ? "" : " WHERE chamados.codpessoa = " + codPessoa);
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            Chamados chamados;

            while (consult.Proximo()) {
                chamados = new Chamados();
                chamados.setSequencia(consult.getBigDecimal("Sequencia"));
                chamados.setDescricao(consult.getString("Descricao"));
                chamados.setDetalhes(consult.getString("Detalhes"));
                chamados.setCodPessoa(consult.getBigDecimal("CodPessoa"));
                chamados.setCodResponsavel(consult.getBigDecimal("CodResponsavel"));
                chamados.setSituacao(consult.getBigDecimal("Situacao"));
                chamados.setTipo(consult.getBigDecimal("Tipo"));
                chamados.setEmpresa(consult.getString("Empresa"));
                chamados.setDt_Abertura(consult.getString("Dt_Abertura"));
                chamados.setHr_Abertura(consult.getString("Hr_Abertura"));
                chamados.setDt_Fecha(consult.getString("Dt_Fecha"));
                chamados.setHr_Fecha(consult.getString("Hr_Fecha"));

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de chamados - \r\n" + e.getMessage());
        }
    }

    public Chamados BuscarChamadosSequencia(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "select Sequencia, Descricao, Detalhes, CodPessoa, CodResponsavel, Situacao, Tipo, Empresa, Dt_Abertura, Hr_Abertura, Dt_Fecha, Hr_Fecha,"
                    + " from chamados"
                    + " where sequencia=?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(sequencia);
            consult.select();
            Chamados chamados = new Chamados();
            while (consult.Proximo()) {
                chamados.setSequencia(consult.getBigDecimal("Sequencia"));
                chamados.setDescricao(consult.getString("Descricao"));
                chamados.setDetalhes(consult.getString("Detalhes"));
                chamados.setCodPessoa(consult.getBigDecimal("CodPessoa"));
                chamados.setCodResponsavel(consult.getBigDecimal("CodResponsavel"));
                chamados.setSituacao(consult.getBigDecimal("Situacao"));
                chamados.setTipo(consult.getBigDecimal("Tipo"));
                chamados.setEmpresa(consult.getString("Empresa"));
                chamados.setDt_Abertura(consult.getString("Dt_Abertura"));
                chamados.setHr_Abertura(consult.getString("Hr_Abertura"));
                chamados.setDt_Fecha(consult.getString("Dt_Fecha"));
                chamados.setHr_Fecha(consult.getString("Hr_Fecha"));
                return chamados;
            }
            chamados.setSequencia(sequencia);
            consult.Close();
            return chamados;
        } catch (Exception e) {
            throw new Exception("ChamadosDao.BuscarChamadosSequencia - " + e.getMessage() + "\r\n"
                    + "select sequencia,descricao,detalhes,codpessoa,codresponsavel,situacao,tipo, empresa, dt_abertura,hr_abertura,dt_fecha,hr_fecha,"
                    + " from chamados"
                    + " where sequencia=" + sequencia);
        }
    }
//     public Chamados getChamados(BigDecimal Sequencia, Persistencia persistencia) throws Exception{
//         try{
//             Chamados retorno = new Chamados();
//             BigDecimal sql = "select sequencia from chamados where codigo = ?";
//             Consulta consult = new Consulta(sql, persistencia);
//             
//         }
//     }

    public Integer TotalChamados(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from chamados"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ChamadosDao.TotalChamados - " + e.getMessage());
        }
    }
}
