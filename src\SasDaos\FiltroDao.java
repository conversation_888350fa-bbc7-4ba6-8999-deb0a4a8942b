/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Filtrar;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FiltroDao {

    public List<Filtrar> getEntidadeFiltro(String sEntidade, Persistencia persistencia) throws Exception {

        String sql = "SELECT "
                + "    COLUNAS.NAME AS COLUNA, "
                + "    TIPOS.NAME AS TIPO, "
                + "    COLUNAS.LENGTH AS TAMANHO, "
                + "    COLUNAS.ISNULLABLE AS EH_NULO "
                + "FROM "
                + "    SYSOBJECTS AS TABELAS, "
                + "    SYSCOLUMNS AS COLUNAS, "
                + "    SYSTYPES   AS TIPOS "
                + "WHERE  "
                + "    TABELAS.ID = COLUNAS.ID "
                + "    AND COLUNAS.USERTYPE = TIPOS.USERTYPE "
                + "    AND TABELAS.NAME = ?";

        List<Filtrar> lFiltro = new ArrayList<>();

        try {
            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(sEntidade);
            consult.select();
            while (consult.Proximo()) {
                Filtrar oFiltro = new Filtrar();
                oFiltro.setColuna(consult.getString("COLUNA"));
                oFiltro.setTipo(consult.getString("TIPO"));
                oFiltro.setTamanho(consult.getInt("TAMANHO"));
                oFiltro.setEh_nulo(consult.getInt("EH_NULO"));
                lFiltro.add(oFiltro);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao pesquisar Saspw - " + e.getMessage());
        }
        return lFiltro;

    }
}
