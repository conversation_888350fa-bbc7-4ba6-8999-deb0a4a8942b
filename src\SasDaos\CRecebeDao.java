/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Bancos;
import SasBeans.CRecIntegra;
import SasBeans.CReceber;
import SasBeans.Rotas;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CRecebeDao {

    public List<CReceber> listaContasReceber(String dataTela, String dataInicio, String dataFim, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "DECLARE @DataAtual AS VARCHAR(10);\n"
                    + "SET @DataAtual = ?;\n"
                    + "\n"
                    + "Select CReceber.Codfil, ClientesEag.Nome, ClientesEag.Nred, ClientesEag.CNPJ <PERSON>, Creceber.NF, NFiscal.Data DataNF, \n"
                    + " CReceber.DtVenc, CReceber.DtPrevPg, CReceber.Valor, Creceber.Obs Detalhes, CReceber.DtPagto, CASE WHEN CReceber.DtPagto IS NULL AND DATEDIFF(day, CReceber.DtVenc,@DataAtual) > 0 THEN DATEDIFF(day, CReceber.DtVenc,@DataAtual) ELSE 0 END DiasAtraso    \n"
                    + " from CReceber \n"
                    + " Left Join ClientesEag on ClientesEag.Codigo = Creceber.CodCli \n"
                    + "                      and ClientesEag.CodFil = CReceber.CodFil \n"
                    + " Left Join NFiscal on NFiscal.Numero = CReceber.NF \n"
                    + "                  and NFiscal.Praca  = CReceber.Praca \n"
                    + "  Left Join TiposTit on TiposTit.codigo = CReceber.TipoTit \n"
                    + " Where CReceber.DtVenc >= ?\n"
                    + "   and CReceber.DtVenc <= ?\n"
                    + "   and CReceber.Codfil = ?\n"
                    + "   and (CReceber.ValorPago is null or CReceber.ValorPago = 0) \n"
                    + "   and TiposTit.Descricao not like '%REGATE%'\n"
                    + "   and TiposTit.Descricao not like '%APLICA%'\n"
                    + "   and TiposTit.Descricao not like '%GARANTIDA'\n"
                    + "   order by CReceber.DtVenc, ClientesEag.Nome";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataTela);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFil);
            consulta.select();

            CReceber cReceber;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setCodFil(consulta.getString("Codfil"));
                cReceber.setCliente(consulta.getString("Nome"));
                cReceber.setNRed(consulta.getString("NRed"));
                cReceber.setCMC7(consulta.getString("CNPJCli"));
                cReceber.setNF(consulta.getString("NF"));
                cReceber.setDataNF(consulta.getString("DataNF"));
                cReceber.setDtPagto(consulta.getString("DtPagto"));
                cReceber.setDtVenc(consulta.getString("DtVenc"));
                cReceber.setDtPrevPg(consulta.getString("DtPrevPg"));
                cReceber.setValor(consulta.getString("Valor"));
                cReceber.setObs(consulta.getString("Detalhes"));
                cReceber.setDiasAtraso(consulta.getString("DiasAtraso"));

                retorno.add(cReceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.listaContasReceber - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixaDet(String dataInicio, String dataFim, String codFil, String rota, String formaPagamento, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "Rotas.Rota,\n"
                    + "Clientes.Nred,\n"
                    + "COUNT(DISTINCT CxFGuiasVol.Lacre) Quantidade,\n"
                    + "SUM(Rt_GuiasFat.ValorTot) ValorTot,\n"
                    + "ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao,\n"
                    + "SUM(CRecIntegra.Total) OnePayTotal,\n"
                    + "SUM(CRecIntegra.TaxaTotal) OnePayTaxa,\n"
                    + "SUM(CRecIntegra.Liquido) OnePayLiquido\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "LEFT JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "LEFT JOIN CxFGuiasVol\n"
                    + "  ON Rt_Guias.Guia = CxFGuiasVol.Guia\n"
                    + " AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "LEFT JOIN Clientes\n"
                    + "  ON Rt_Perc.CodCli1 = Clientes.Codigo\n"
                    + "LEFT JOIN CRecIntegra\n"
                    + "   ON Rt_Perc.Sequencia = CRecIntegra.SeqRota\n"
                    + " AND Rt_Perc.Parada = CRecIntegra.Parada\n"
                    + "WHERE Rotas.Data BETWEEN ? AND ?\n";
            if (null != codFil && !codFil.equals("0")) {
                sql += "AND   Rotas.CodFil = ?\n";
            }

            if (null != rota && !rota.equals("")) {
                sql += "AND   Rotas.Rota = ?\n";
            }

            if (null != formaPagamento && !formaPagamento.equals("0")) {
                sql += "AND   FormasPgto.Codigo = ?\n";
            }

            sql += "GROUP BY Rotas.Rota, Clientes.Nred, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY Rotas.Rota, Clientes.Nred, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            if (null != rota && !rota.equals("")) {
                consulta.setString(rota);
            }

            if (null != formaPagamento && !formaPagamento.equals("")) {
                consulta.setString(formaPagamento);
            }

            consulta.select();

            CReceber cReceber;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setRota(consulta.getString("Rota"));
                cReceber.setCliente(consulta.getString("Nred"));
                cReceber.setQtde(consulta.getString("Quantidade"));
                cReceber.setValor(consulta.getString("ValorTot"));
                cReceber.setFormaPgtoDescr(consulta.getString("Descricao"));
                cReceber.setOnePayLiquido(consulta.getFloat("OnePayLiquido"));
                cReceber.setOnePayTaxa(consulta.getFloat("OnePayTaxa"));
                cReceber.setOnePayTotal(consulta.getFloat("OnePayTotal"));

                retorno.add(cReceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaDet - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixaDetSeqRota(String sqeRota, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "Clientes.Nred,\n"
                    + "Rotas.Rota,\n"
                    + "COUNT(DISTINCT CxFGuiasVol.Lacre) Quantidade,\n"
                    + "SUM(Rt_GuiasFat.ValorTot) ValorTot,\n"
                    + "ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "LEFT JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "LEFT JOIN CxFGuiasVol\n"
                    + "  ON Rt_Guias.Guia = CxFGuiasVol.Guia\n"
                    + " AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "LEFT JOIN Clientes\n"
                    + "  ON Rt_Perc.CodCli1 = Clientes.Codigo\n"
                    + "WHERE Rotas.Sequencia =  ?\n"
                    + "GROUP BY Clientes.Nred, Rotas.Rota, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY Clientes.Nred, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(sqeRota);

            consulta.select();

            CReceber cReceber;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setCliente(consulta.getString("Nred"));
                cReceber.setNRed(consulta.getString("Rota"));
                cReceber.setQtde(consulta.getString("Quantidade"));
                cReceber.setValor(consulta.getString("ValorTot"));
                cReceber.setFormaPgtoDescr(consulta.getString("Descricao"));

                retorno.add(cReceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaDetSeqRota - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixa(String dataInicio, String dataFim, String codFil, String rota, String formaPagamento, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "COUNT(DISTINCT CxFGuiasVol.Lacre) Quantidade,\n"
                    + "SUM(Rt_GuiasFat.ValorTot) ValorTot,\n"
                    + "ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "LEFT JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "LEFT JOIN CxFGuiasVol\n"
                    + "  ON Rt_Guias.Guia = CxFGuiasVol.Guia\n"
                    + " AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "WHERE Rotas.Data BETWEEN ? AND ?\n";
            if (null != codFil && !codFil.equals("0")) {
                sql += "AND   Rotas.CodFil = ?\n";
            }

            if (null != rota && !rota.equals("")) {
                sql += "AND   Rotas.Rota = ?\n";
            }

            if (null != formaPagamento && !formaPagamento.equals("0")) {
                sql += "AND   FormasPgto.Codigo = ?\n";
            }

            sql += "GROUP BY ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            if (null != rota && !rota.equals("")) {
                consulta.setString(rota);
            }

            if (null != formaPagamento && !formaPagamento.equals("")) {
                consulta.setString(formaPagamento);
            }

            consulta.select();

            CReceber cReceber;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setQtde(consulta.getString("Quantidade"));
                cReceber.setValor(consulta.getString("ValorTot"));
                cReceber.setFormaPgtoDescr(consulta.getString("Descricao"));

                retorno.add(cReceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixa - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixaSeqRota(String sqeRota, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "COUNT(DISTINCT CxFGuiasVol.Lacre) Quantidade,\n"
                    + "SUM(Rt_GuiasFat.ValorTot) ValorTot,\n"
                    + "ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "LEFT JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "LEFT JOIN CxFGuiasVol\n"
                    + "  ON Rt_Guias.Guia = CxFGuiasVol.Guia\n"
                    + " AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "WHERE Rotas.Sequencia = ?\n";

            sql += "GROUP BY ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(sqeRota);

            consulta.select();

            CReceber cReceber;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setQtde(consulta.getString("Quantidade"));
                cReceber.setValor(consulta.getString("ValorTot"));
                cReceber.setFormaPgtoDescr(consulta.getString("Descricao"));

                retorno.add(cReceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaSeqRota - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<Rotas> resumoCaixaRotas(String dataInicio, String dataFim, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<Rotas> retorno = new ArrayList<>();

        try {
            sql = "SELECT DISTINCT\n"
                    + "Rotas.Rota\n"
                    + "FROM Rotas\n"
                    + "WHERE Rotas.Data BETWEEN ? AND ?\n";
            if (null != codFil && !codFil.equals("0")) {
                sql += "AND   Rotas.CodFil = ?\n";
            }
            sql += "ORDER BY Rotas.Rota\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            consulta.select();

            Rotas rotas;

            while (consulta.Proximo()) {
                rotas = new Rotas();
                rotas.setRota(consulta.getString("Rota"));

                retorno.add(rotas);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaRotas - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixaFormas(String dataInicio, String dataFim, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "FormasPgto.Codigo, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "WHERE Rotas.Data BETWEEN ? AND ?\n";
            if (null != codFil && !codFil.equals("0")) {
                sql += "AND   Rotas.CodFil = ?\n";
            }

            sql += "GROUP BY FormasPgto.Codigo, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            consulta.select();

            CReceber creceber;

            while (consulta.Proximo()) {
                creceber = new CReceber();
                creceber.setFormaPgtoDescr(consulta.getString("Descricao"));
                creceber.setFormaPgto(consulta.getInt("Codigo"));

                retorno.add(creceber);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaFormas - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CRecIntegra> listaTransacoes(Persistencia persistencia) throws Exception {
        String sql = "";
        List<CRecIntegra> retorno = new ArrayList<>();

        try {
            sql = "SELECT * FROM CrecIntegra WHERE SeqRota IS NULL OR Parada IS NULL ORDER BY Created DESC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            CRecIntegra cRecIntegra;

            while (consulta.Proximo()) {
                try {
                    cRecIntegra = new CRecIntegra();
                    cRecIntegra.setBandeira(consulta.getString("Bandeira"));
                    cRecIntegra.setCreated(consulta.getString("Created"));
                    cRecIntegra.setDataRecebimento(consulta.getString("DataRecebimento"));
                    cRecIntegra.setDocumento(consulta.getString("Documento"));
                    cRecIntegra.setDt_Alter(consulta.getString("Dt_alter"));
                    cRecIntegra.setHr_Alter(consulta.getString("Hr_alter"));
                    cRecIntegra.setId(consulta.getString("Id"));
                    cRecIntegra.setLiquido(consulta.getFloat("Liquido"));
                    cRecIntegra.setNome(consulta.getString("Nome"));
                    cRecIntegra.setNome_titular(consulta.getString("Nome_titular"));
                    cRecIntegra.setOperador(consulta.getString("Operador"));
                    cRecIntegra.setParada(consulta.getString("Parada"));
                    cRecIntegra.setParcelas(consulta.getString("Parcelas"));
                    cRecIntegra.setRepresentante(consulta.getString("Representante"));
                    cRecIntegra.setSeqRota(consulta.getString("SeqRota"));
                    cRecIntegra.setSequencia(consulta.getString("Sequencia"));
                    cRecIntegra.setTaxa(consulta.getFloat("Taxa"));
                    cRecIntegra.setTaxatotal(consulta.getFloat("Taxatotal"));
                    cRecIntegra.setTipo_pagamento(consulta.getString("Tipo_pagamento"));
                    cRecIntegra.setTipo_pagamento_id(consulta.getString("Tipo_pagamento_id"));
                    cRecIntegra.setTipo_pagamento_tit(consulta.getString("Tipo_pagamento_tit"));
                    cRecIntegra.setTotal(consulta.getFloat("Total"));
                    cRecIntegra.setZoopTransactionId(consulta.getString("ZoopTransactionId"));

                    retorno.add(cRecIntegra);
                } catch (Exception ex) {

                }
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.listaTransacoes - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CReceber> resumoCaixaForVinculo(String dataInicio, String dataFim, String codFil, String rota, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CReceber> retorno = new ArrayList<>();

        try {
            sql = "SELECT\n"
                    + "Rotas.Rota,\n"
                    + "Rotas.Data,\n"
                    + "RT_Perc.Sequencia,\n"
                    + "Rt_Perc.Parada,\n"
                    + "Rt_Perc.ER,\n"
                    + "Clientes.Nred,\n"
                    + "COUNT(DISTINCT CxFGuiasVol.Lacre) Quantidade,\n"
                    + "SUM(Rt_GuiasFat.ValorTot) ValorTot,\n"
                    + "ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO') Descricao\n"
                    + "FROM Rotas \n"
                    + "JOIN Rt_Perc\n"
                    + "  ON Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "JOIN Rt_Guias \n"
                    + "  ON Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "LEFT JOIN Rt_GuiasFat\n"
                    + " ON Rt_Perc.Sequencia = Rt_GuiasFat.Sequencia\n"
                    + " AND Rt_Perc.Parada = Rt_GuiasFat.Parada\n"
                    + "LEFT JOIN FormasPgto\n"
                    + " ON Rt_GuiasFat.FormaPgto = FormasPgto.Codigo\n"
                    + "LEFT JOIN CxFGuiasVol\n"
                    + "  ON Rt_Guias.Guia = CxFGuiasVol.Guia\n"
                    + " AND Rt_Guias.Serie = CxFGuiasVol.Serie\n"
                    + "LEFT JOIN Clientes\n"
                    + "  ON Rt_Perc.CodCli1 = Clientes.Codigo\n"
                    + "WHERE Rotas.Data BETWEEN ? AND ?\n"
                    + "AND   Clientes.Codigo <> '9996015'\n"
                    + "AND   (Clientes.PrdApoio IS NULL OR Clientes.PrdApoio = '' OR Clientes.PrdApoio = 'N') \n";
                    //+ "AND   (FormasPgto.Descricao LIKE '%CARTAO%' OR FormasPgto.Descricao LIKE '%RECEBER ATE A REM%')";
            if (null != codFil && !codFil.equals("0")) {
                sql += "AND   Rotas.CodFil = ?\n";
            }

            if (null != rota && !rota.equals("")) {
                sql += "AND   Rotas.Rota = ?\n";
            }

            sql += "GROUP BY Rotas.Data,Clientes.codigo,RT_Perc.Sequencia,Rt_Perc.Parada,Rt_Perc.ER,Rotas.Rota, Clientes.Nred, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')\n"
                    + "ORDER BY Rotas.Data DESC, Rotas.Rota, Clientes.Nred, ISNULL(FormasPgto.Descricao, 'NÃO INFORMADO')";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            if (null != rota && !rota.equals("")) {
                consulta.setString(rota);
            }

            consulta.select();

            CReceber cReceber;
            int marcador = 0;

            while (consulta.Proximo()) {
                cReceber = new CReceber();
                cReceber.setRota(consulta.getString("Rota"));
                cReceber.setCMC7(consulta.getString("ER"));
                cReceber.setCliente(consulta.getString("Nred"));
                cReceber.setQtde(consulta.getString("Quantidade"));
                cReceber.setValor(consulta.getString("ValorTot"));
                cReceber.setFormaPgtoDescr(consulta.getString("Descricao"));
                cReceber.setDataNF(consulta.getString("Data"));
                cReceber.setPraca(consulta.getString("Parada"));
                cReceber.setSequencia(consulta.getString("Sequencia"));
                cReceber.setContaFin(marcador);
                retorno.add(cReceber);

                marcador++;
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CRecebeDao.resumoCaixaForVinculo - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void atualizarTransacao(CReceber selecao, CRecIntegra transacao, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "UPDATE CrecIntegra"
                    + " SET SeqRota = ?,"
                    + "     Parada  = ?"
                    + " WHERE Sequencia = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(selecao.getSequencia());
            consulta.setBigDecimal(selecao.getPraca());
            consulta.setString(transacao.getSequencia());
            consulta.update();
            consulta.close();
        } catch (Exception ex) {
            throw new Exception("CRecebeDao.atualizarTransacao - " + ex.getMessage() + "\r\n"
                    + sql);
        }
    }
}
