package SasLibrary;

import Dados.Persistencia;
import SasBeansCompostas.CarregaFuncs;
import SasDaos.FuncaoDao;
import java.util.List;

/**
 *
 * <AUTHOR> - (10/09/2015)
 */
public class Funcionarios {

    public static String CarregaFuncs(String sPagN, String sLinP, String sSecao, String sSituacao, String SCodFil, Persistencia persistencia) {
        String ret = "<?xml version=\"1.0\"?>";
        try {
            //carrega lista de funcionários
            List<CarregaFuncs> lCarregaFuncs;
            FuncaoDao oCarregaFuncAusenteDao = new FuncaoDao();
            try {
                lCarregaFuncs = oCarregaFuncAusenteDao.getFucao(sPagN, sLinP, sSecao, sSituacao, SCodFil, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar Funcionários  - " + e.getMessage());
            }
            if (lCarregaFuncs.isEmpty()) {
                ret += "<resp>CarregaFunc_2</resp>";
                return ret;
            }
            String xml = "<?xml version=\"1.0\"?><nreg>" + lCarregaFuncs.size() + "</nreg>";

            for (int i = 0; i < lCarregaFuncs.size(); i++) {
                xml += "<Funcion>";
                xml += "<matr>" + lCarregaFuncs.get(i).getFuncion().getMatr() + "</matr>";
                xml += "<nome>" + lCarregaFuncs.get(i).getFuncion().getNome() + "</nome>";
                xml += "<descargo>" + lCarregaFuncs.get(i).getCargos().getDescricao() + "</descargo>";
                xml += "<local>" + lCarregaFuncs.get(i).getPstServ().getLocal() + "</local>";
                xml += "<nomeguer>" + lCarregaFuncs.get(i).getFuncion().getNome_Guer() + "</nomeguer>";
                xml += "<regional>" + lCarregaFuncs.get(i).getFuncion().getRegional() + "</regional>";
                xml += "<secao>" + lCarregaFuncs.get(i).getFuncion().getSecao() + "</secao>";
                xml += "<ccusto>" + lCarregaFuncs.get(i).getFuncion().getCCusto() + "</ccusto>";
                xml += "<codponto>" + lCarregaFuncs.get(i).getFuncion().getCodPonto() + "</codponto>";
                xml += "<dtadmis>" + lCarregaFuncs.get(i).getFuncion().getDt_Admis() + "</dtadmis>";
                xml += "<cargo>" + lCarregaFuncs.get(i).getFuncion().getCargo() + "</cargo>";
                xml += "<codcargo>" + lCarregaFuncs.get(i).getFuncion().getCodCargo() + "</codcargo>";
                xml += "<apresen>" + lCarregaFuncs.get(i).getFuncion().getApresen() + "</apresen>";
                xml += "<situacao>" + lCarregaFuncs.get(i).getFuncion().getSituacao() + "</situacao>";
                xml += "<dtsituacao>" + lCarregaFuncs.get(i).getFuncion().getDt_Situac() + "</dtsituacao>";
                xml += "<escala>" + lCarregaFuncs.get(i).getFuncion().getEscala() + "</escala>";
                xml += "<horario>" + lCarregaFuncs.get(i).getFuncion().getHorario() + "</horario>";
                xml += "<grpescala>" + lCarregaFuncs.get(i).getFuncion().getGrpEscala() + "</grpescala>";
                xml += "<funcao>" + lCarregaFuncs.get(i).getFuncion().getFuncao() + "</funcao>";
                xml += "<formini>" + lCarregaFuncs.get(i).getFuncion().getDt_FormIni() + "</formini>";
                xml += "<formfim>" + lCarregaFuncs.get(i).getFuncion().getDt_FormFim() + "</formfim>";
                xml += "<localform>" + lCarregaFuncs.get(i).getFuncion().getLocalForm() + "</localform>";
                xml += "<certific>" + lCarregaFuncs.get(i).getFuncion().getCertific() + "</certific>";
                xml += "<refpf>" + lCarregaFuncs.get(i).getFuncion().getReg_PF() + "</refpf>";
                xml += "<localpf>" + lCarregaFuncs.get(i).getFuncion().getLocal_PF() + "</localpf>";
                xml += "<regpf>" + lCarregaFuncs.get(i).getFuncion().getReg_PF() + "</regpf>";
                xml += "<regpfdt>" + lCarregaFuncs.get(i).getFuncion().getReg_PFDt() + "</regpfdt>";
                xml += "<carnacvig>" + lCarregaFuncs.get(i).getFuncion().getCarNacVig() + "</carnacvig>";
                xml += "<dtvalcnv>" + lCarregaFuncs.get(i).getFuncion().getDtValCNV() + "</dtvalcnv>";
                xml += "<regmt>" + lCarregaFuncs.get(i).getFuncion().getReg_MT() + "</regmt>";
                xml += "<dtrecicl>" + lCarregaFuncs.get(i).getFuncion().getDt_Recicl() + "</dtrecicl>";
                xml += "<dtvencurs>" + lCarregaFuncs.get(i).getFuncion().getDt_VenCurs() + "</dtvencurs>";
                xml += "<dtexameme>" + lCarregaFuncs.get(i).getFuncion().getDt_ExameMe() + "</dtexameme>";
                xml += "<dtpsico>" + lCarregaFuncs.get(i).getFuncion().getDt_Psico() + "</dtpsico>";
                xml += "<extensaotv>" + lCarregaFuncs.get(i).getFuncion().getExtensaoTV() + "</extensaotv>";
                xml += "<extsegpes>" + lCarregaFuncs.get(i).getFuncion().getExtSegPes() + "</extsegpes>";
                xml += "<extescolta>" + lCarregaFuncs.get(i).getFuncion().getExtEscolta() + "</extescolta>";
                xml += "<gruposang>" + lCarregaFuncs.get(i).getFuncion().getGrupoSang() + "</gruposang>";
                xml += "<instrucao>" + lCarregaFuncs.get(i).getFuncion().getInstrucao() + "</instrucao>";
                xml += "<raca>" + lCarregaFuncs.get(i).getFuncion().getRaca() + "</raca>";
                xml += "<estcivil>" + lCarregaFuncs.get(i).getFuncion().getEstCivil() + "</estcivil>";
                xml += "<endereco>" + lCarregaFuncs.get(i).getFuncion().getEndereco() + "</endereco>";
                xml += "<numero>" + lCarregaFuncs.get(i).getFuncion().getNumero() + "</numero>";
                xml += "<complemento>" + lCarregaFuncs.get(i).getFuncion().getComplemento() + "</complemento>";
                xml += "<bairro>" + lCarregaFuncs.get(i).getFuncion().getBairro() + "</bairro>";
                xml += "<cidade>" + lCarregaFuncs.get(i).getFuncion().getCidade() + "</cidade>";
                xml += "<uf>" + lCarregaFuncs.get(i).getFuncion().getUF() + "</uf>";
                xml += "<cep>" + lCarregaFuncs.get(i).getFuncion().getCEP() + "</cep>";
                xml += "<fone1>" + lCarregaFuncs.get(i).getFuncion().getFone1() + "</fone1>";
                xml += "<fone2>" + lCarregaFuncs.get(i).getFuncion().getFone2() + "</fone2>";
                xml += "<email>" + lCarregaFuncs.get(i).getFuncion().getEmail() + "</email>";
                xml += "<dtnasc>" + lCarregaFuncs.get(i).getFuncion().getDt_Nasc() + "</dtnasc>";
                xml += "<sexo>" + lCarregaFuncs.get(i).getFuncion().getSexo() + "</sexo>";
                xml += "<naturalid>" + lCarregaFuncs.get(i).getFuncion().getNaturalid() + "</naturalid>";
                xml += "<pai>" + lCarregaFuncs.get(i).getFuncion().getPai() + "</pai>";
                xml += "<mae>" + lCarregaFuncs.get(i).getFuncion().getMae() + "</mae>";
                xml += "<conjuge>" + lCarregaFuncs.get(i).getFuncion().getConjuge() + "</conjuge>";
                xml += "<cnh>" + lCarregaFuncs.get(i).getFuncion().getCNH() + "</cnh>";
                xml += "<dtvenccnh>" + lCarregaFuncs.get(i).getFuncion().getDt_VenCNH() + "</dtvenccnh>";
                xml += "<catgoria>" + lCarregaFuncs.get(i).getFuncion().getCategoria() + "</catgoria>";
                xml += "<rg>" + lCarregaFuncs.get(i).getFuncion().getRG() + "</rg>";
                xml += "<orgemis>" + lCarregaFuncs.get(i).getFuncion().getOrgEmis() + "</orgemis>";
                xml += "<rgdtemis>" + lCarregaFuncs.get(i).getFuncion().getRgDtEmis() + "</rgdtemis>";
                xml += "<cpf>" + lCarregaFuncs.get(i).getFuncion().getCPF() + "</cpf>";
                xml += "<pis>" + lCarregaFuncs.get(i).getFuncion().getPIS() + "</pis>";
                xml += "<reservista>" + lCarregaFuncs.get(i).getFuncion().getReservista() + "</reservista>";
                xml += "<reservcat>" + lCarregaFuncs.get(i).getFuncion().getReservCat() + "</reservcat>";
                xml += "<ctps>" + lCarregaFuncs.get(i).getFuncion().getCTPS_Nro() + "</ctps>";
                xml += "<ctpsserie>" + lCarregaFuncs.get(i).getFuncion().getCTPS_Serie() + "</ctpsserie>";
                xml += "<ctpsuf>" + lCarregaFuncs.get(i).getFuncion().getCTPS_UF() + "</ctpsuf>";
                xml += "<ctpsemis>" + lCarregaFuncs.get(i).getFuncion().getCTPS_Emis() + "</ctpsemis>";
                xml += "<titeleit>" + lCarregaFuncs.get(i).getFuncion().getTitEleit() + "</titeleit>";
                xml += "<ezona>" + lCarregaFuncs.get(i).getFuncion().getTitEZona() + "</ezona>";
                xml += "<titsessao>" + lCarregaFuncs.get(i).getFuncion().getTitSecao() + "</titsessao>";
                xml += "<ctbanco>" + lCarregaFuncs.get(i).getFuncion().getCt_Banco() + "</ctbanco>";
                xml += "<ctagencia>" + lCarregaFuncs.get(i).getFuncion().getCt_Agencia() + "</ctagencia>";
                xml += "<ctconta>" + lCarregaFuncs.get(i).getFuncion().getCt_Conta() + "</ctconta>";
                xml += "<ctcodoper>" + lCarregaFuncs.get(i).getFuncion().getCt_CodOper() + "</ctcodoper>";
                xml += "<obs>" + lCarregaFuncs.get(i).getFuncion().getObs() + "</obs>";
                xml += "<salario>" + lCarregaFuncs.get(i).getFuncion().getSalario() + "</salario>";
                xml += "<sindicato>" + lCarregaFuncs.get(i).getFuncion().getSindicato() + "</sindicato>";
                xml += "<chmes>" + lCarregaFuncs.get(i).getFuncion().getCHMes() + "</chmes>";
                xml += "<chsemana>" + lCarregaFuncs.get(i).getFuncion().getCHSeman() + "</chsemana>";
                xml += "<heperiodo>" + lCarregaFuncs.get(i).getFuncion().getHe_Periodo() + "</heperiodo>";
                xml += "<depir>" + lCarregaFuncs.get(i).getFuncion().getDepIR() + "</depir>";
                xml += "<depsf>" + lCarregaFuncs.get(i).getFuncion().getDepSF() + "</depsf>";
                xml += "<fgtsopcao>" + lCarregaFuncs.get(i).getFuncion().getFGTSOpcao() + "</fgtsopcao>";
                xml += "<fgtsbano>" + lCarregaFuncs.get(i).getFuncion().getFGTSBanco() + "</fgtsbano>";
                xml += "<fgtsag>" + lCarregaFuncs.get(i).getFuncion().getFGTSAg() + "</fgtsag>";
                xml += "<pgctsin>" + lCarregaFuncs.get(i).getFuncion().getPgCtSin() + "</pgctsin>";
                xml += "<assmedic>" + lCarregaFuncs.get(i).getFuncion().getAssMedic() + "</assmedic>";
                xml += "<depassmed>" + lCarregaFuncs.get(i).getFuncion().getDepAssMed() + "</depassmed>";
                xml += "<cestabas>" + lCarregaFuncs.get(i).getFuncion().getCestaBas() + "</cestabas>";
                xml += "<valeref>" + lCarregaFuncs.get(i).getFuncion().getValeRef() + "</valeref>";
                xml += "<convfarma>" + lCarregaFuncs.get(i).getFuncion().getConvFarma() + "</convfarma>";
                xml += "<segvida>" + lCarregaFuncs.get(i).getFuncion().getSegVida() + "</segvida>";
                xml += "<tipoadm>" + lCarregaFuncs.get(i).getFuncion().getTipoAdm() + "</tipoadm>";
                xml += "<deffis>" + lCarregaFuncs.get(i).getFuncion().getDefFis() + "</deffis>";
                xml += "<defistipo>" + lCarregaFuncs.get(i).getFuncion().getDefFisTipo() + "</defistipo>";
                xml += "<deffisdesc>" + lCarregaFuncs.get(i).getFuncion().getDefFisDesc() + "</deffisdesc>";
                xml += "<nacionalid>" + lCarregaFuncs.get(i).getFuncion().getNacionalid() + "</nacionalid>";
                xml += "<anocheg>" + lCarregaFuncs.get(i).getFuncion().getAnoCheg() + "</anocheg>";
                xml += "<folhalivro>" + lCarregaFuncs.get(i).getFuncion().getFolhaLivro() + "</folhalivro>";
                xml += "<pginss>" + lCarregaFuncs.get(i).getFuncion().getPgINSS() + "</pginss>";
                xml += "<pgir>" + lCarregaFuncs.get(i).getFuncion().getPgIR() + "</pgir>";
                xml += "<sefiopocor>" + lCarregaFuncs.get(i).getFuncion().getSEFIPOcor() + "</sefiopocor>";
                xml += "<contactb>" + lCarregaFuncs.get(i).getFuncion().getConta_Ctb() + "</contactb>";
                xml += "<altura>" + lCarregaFuncs.get(i).getFuncion().getAltura() + "</altura>";
                xml += "<peso>" + lCarregaFuncs.get(i).getFuncion().getPeso() + "</peso>";
                xml += "<dtdemis>" + lCarregaFuncs.get(i).getFuncion().getDt_Demis() + "</dtdemis>";
                xml += "<codcidade>" + lCarregaFuncs.get(i).getFuncion().getCodCidade() + "</codcidade>";
                xml += "<codnaturalid>" + lCarregaFuncs.get(i).getFuncion().getCodNaturalid() + "</codnaturalid>";
                xml += "<expgesp>" + lCarregaFuncs.get(i).getFuncion().getExpGESP() + "</expgesp>";
                xml += "<vinculo>" + lCarregaFuncs.get(i).getFuncion().getVinculo() + "</vinculo>";
                xml += "<formapgto>" + lCarregaFuncs.get(i).getFuncion().getFormaPgto() + "</formapgto>";
                xml += "<jornada>" + lCarregaFuncs.get(i).getFuncion().getJornada() + "</jornada>";
                xml += "<segdesemp>" + lCarregaFuncs.get(i).getFuncion().getSegDesemp() + "</segdesemp>";
                xml += "<operador>" + lCarregaFuncs.get(i).getFuncion().getOperador() + "</operador>";
                xml += "<dtalter>" + lCarregaFuncs.get(i).getFuncion().getDt_Alter() + "</dtalter>";
                xml += "<hralter>" + lCarregaFuncs.get(i).getFuncion().getHr_Alter() + "</hralter>";
                xml += "</Funcion>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
