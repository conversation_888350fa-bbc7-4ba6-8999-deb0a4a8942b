/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Rh_Ctrl;
import SasDaos.Rh_CtrlDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_Controles {

    public static String CarregaRhCtrl_MatrCodFil(String SCodFil, String sMatr, Persistencia persistencia) {
        String ret = "<?xml version=\"1.0\"?>";
        try {
            //carrega lista de RH_Ctr
            List<Rh_Ctrl> lRh_Ctrl;
            Rh_CtrlDao oRh_Ctrl = new Rh_CtrlDao();
            try {
                lRh_Ctrl = oRh_Ctrl.getRHCtrl_Matr(SCodFil, sMatr, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar RH_Ctr  - " + e.getMessage());
            }
            if (lRh_Ctrl.isEmpty()) {
                ret += "<resp>Rh_Ctrl_2</resp>";
                return ret;
            }
            String xml = "<?xml version=\"1.0\"?><nreg>" + lRh_Ctrl.size() + "</nreg>";

            for (int i = 0; i < lRh_Ctrl.size(); i++) {
                xml += "<rhctr>";
                xml += "<matr>" + lRh_Ctrl.get(i).getMatr() + "</matr>";
                xml += "<codfil>" + lRh_Ctrl.get(i).getCodFil() + "</codfil>";
                xml += "<nomeguer>" + lRh_Ctrl.get(i).getNome_Guer() + "</nomeguer>";
                xml += "<situacao>" + lRh_Ctrl.get(i).getSituacao() + "</situacao>";
                xml += "<dtini>" + lRh_Ctrl.get(i).getDt_Ini() + "</dtini>";
                xml += "<dtfim>" + lRh_Ctrl.get(i).getDt_Fim() + "</dtfim>";
                xml += "<supervdiu>" + lRh_Ctrl.get(i).getSupervDiu() + "</supervdiu>";
                xml += "<supervnot>" + lRh_Ctrl.get(i).getSupervNot() + "</supervnot>";
                xml += "<horasextr>" + lRh_Ctrl.get(i).getHorasExtr() + "</horasextr>";
                xml += "<hsneg>" + lRh_Ctrl.get(i).getHsNeg() + "</hsneg>";
                xml += "<he50>" + lRh_Ctrl.get(i).getHE50() + "</he50>";
                xml += "<he100>" + lRh_Ctrl.get(i).getHE100() + "</he100>";
                xml += "<he70>" + lRh_Ctrl.get(i).getHE70() + "</he70>";
                xml += "<he3>" + lRh_Ctrl.get(i).getHE3() + "</he3>";
                xml += "<hecc1>" + lRh_Ctrl.get(i).getHECC1() + "</hecc1>";
                xml += "<hecc2>" + lRh_Ctrl.get(i).getHECC1() + "</hecc2>";
                xml += "<hecc3>" + lRh_Ctrl.get(i).getHECC3() + "</hecc3>";
                xml += "<hesup>" + lRh_Ctrl.get(i).getHECC3() + "</hesup>";
                xml += "<adnot>" + lRh_Ctrl.get(i).getAdNot() + "</adnot>";
                xml += "<hsnotred>" + lRh_Ctrl.get(i).getHsNotRed() + "</hsnotred>";
                xml += "<hsadnotprorrog>" + lRh_Ctrl.get(i).getHsAdNotProrrog() + "</hsadnotprorrog>";
                xml += "<intraj>" + lRh_Ctrl.get(i).getIntraJ() + "</intraj>";
                xml += "<diastrab>" + lRh_Ctrl.get(i).getDiasTrab() + "</diastrab>";
                xml += "<diasfolga>" + lRh_Ctrl.get(i).getDiasFolga() + "</diasfolga>";
                xml += "<faltas>" + lRh_Ctrl.get(i).getFaltas() + "</faltas>";
                xml += "<suspensao>" + lRh_Ctrl.get(i).getSuspensao() + "</suspensao>";
                xml += "<diasferias>" + lRh_Ctrl.get(i).getDiasFerias() + "</diasferias>";
                xml += "<dtretorno>" + lRh_Ctrl.get(i).getDtRetorno() + "</dtretorno>";
                xml += "<dtinifer>" + lRh_Ctrl.get(i).getDtIniFer() + "</dtinifer>";
                xml += "<dtfimfer>" + lRh_Ctrl.get(i).getDtFimFer() + "</dtfimfer>";
                xml += "<hsabnfalta>" + lRh_Ctrl.get(i).getHsAbnFalta() + "</hsabnfalta>";
                xml += "<hsabnferiado>" + lRh_Ctrl.get(i).getHsAbnFeriado() + "</hsabnferiado>";
                xml += "<hsprojecao>" + lRh_Ctrl.get(i).getHsProjecao() + "</hsprojecao>";
                xml += "<hsinc>" + lRh_Ctrl.get(i).getHsInc() + "</hsinc>";
                xml += "<faltasjust>" + lRh_Ctrl.get(i).getFaltasJust() + "</faltasjust>";
                xml += "<hsatmedico>" + lRh_Ctrl.get(i).getHsAtMedico() + "</hsatmedico>";
                xml += "<reciclagem>" + lRh_Ctrl.get(i).getReciclagem() + "</reciclagem>";
                xml += "<sindicato>" + lRh_Ctrl.get(i).getSindicato() + "</sindicato>";
                xml += "<transito>" + lRh_Ctrl.get(i).getTransito() + "</transito>";
                xml += "<diasinsal>" + lRh_Ctrl.get(i).getDiasInsal() + "</diasinsal>";
                xml += "<diasperic>" + lRh_Ctrl.get(i).getDiasPeric() + "</diasperic>";
                xml += "<diasronda>" + lRh_Ctrl.get(i).getDiasRonda() + "</diasronda>";
                xml += "<diaschsup>" + lRh_Ctrl.get(i).getDiasCHSup() + "</diaschsup>";
                xml += "<diasescnormal>" + lRh_Ctrl.get(i).getDiasEscNormal() + "</diasescnormal>";
                xml += "<diasfertrab>" + lRh_Ctrl.get(i).getDiasFerTrab() + "</diasfertrab>";
                xml += "<heferiado>" + lRh_Ctrl.get(i).getHEFeriado() + "</heferiado>";
                xml += "<hsferiado>" + lRh_Ctrl.get(i).getHSFeriado() + "</hsferiado>";
                xml += "<secao>" + lRh_Ctrl.get(i).getSecao() + "</secao>";
                xml += "<local>" + lRh_Ctrl.get(i).getLocal() + "</local>";
                xml += "<regional>" + lRh_Ctrl.get(i).getRegional() + "</regional>";
                xml += "<descdsr>" + lRh_Ctrl.get(i).getDescDSR() + "</descdsr>";
                xml += "<calculo>" + lRh_Ctrl.get(i).getCalculo() + "</calculo>";
                xml += "<pel>" + lRh_Ctrl.get(i).getPEL() + "</pel>";
                xml += "<qtdeft>" + lRh_Ctrl.get(i).getQtdeFT() + "</qtdeft>";
                xml += "<operador>" + lRh_Ctrl.get(i).getOperador() + "</operador>";
                xml += "<dtalter>" + lRh_Ctrl.get(i).getDt_Alter() + "</dtalter>";
                xml += "<hralter>" + lRh_Ctrl.get(i).getHr_Alter() + "</hralter>";
                xml += "</rhctr>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
