package SasLibrary;

import Dados.Persistencia;
import SasBeans.TmktDetPst;
import SasDaos.TmktDetPstDao;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class TmktDetPsts {

    public static BigDecimal InsereTmktDetPst(String sMatr, TmktDetPst oTmktDetPst, String Senha, Persistencia persistencia) throws Exception {
        BigDecimal seq = null;

        TmktDetPstDao oTmktDetPstDao = new TmktDetPstDao();
        //valida login e senha do usuário
//        List<LoginRota> list_lrota;
//        try {
//            list_lrota = LoginDao.LoginRota(sMatr, Senha, persistencia);
//
//        } catch (Exception e) {
//            throw new Exception("Falha no login - " + e.getMessage());
//        }
//        try{
//            for (LoginRota list_lrota1 : list_lrota) {
//                if (!list_lrota1.getPessoa().getPWWeb().equals(Senha)) {
//                    seq = null;
//                } else {
        boolean repete = false;
        int cont = 1;
        while (!repete) {
            //Seleciona sequencia incrementada
            seq = oTmktDetPstDao.getSequencia(persistencia);
            repete = oTmktDetPstDao.insereTmktDetPst(seq, sMatr, oTmktDetPst, persistencia);
            if (cont == 10) {
                repete = true;
            }
            cont++;
        }
//                }
//            }
//        }
//        catch(Exception e){
//            throw new Exception("Falha ao gravar tmktdetpst - "+e.getMessage());
//        }
        return seq;
    }

    public static BigDecimal InsereTmktDetPst(String sMatr, TmktDetPst oTmktDetPst, String Senha, String dataAtual, String hora, String dataFormatada, Persistencia persistencia) throws Exception {
        BigDecimal seq = null;

        TmktDetPstDao oTmktDetPstDao = new TmktDetPstDao();
        boolean repete = false;
        int cont = 1;
        while (!repete) {
            //Seleciona sequencia incrementada
            seq = oTmktDetPstDao.getSequencia(persistencia);
            oTmktDetPst.setSequencia(seq.toPlainString());
            repete = oTmktDetPstDao.insereTmktDetPst(oTmktDetPst, dataAtual, hora, dataFormatada, persistencia);
            if (cont == 10) {
                repete = true;
            }
            cont++;
        }
        return seq;
    }

    public static BigDecimal editaSuperv(BigDecimal Seq, String sCodPessoa, TmktDetPst oTmktDetPst, String Senha, Persistencia persistencia) throws Exception {
        TmktDetPstDao oTmktDetPstDao = new TmktDetPstDao();
//        boolean ret = false;
//        //valida login e senha do usuário
//        List<LoginRota> list_lrota;
//        try {
//            list_lrota = LoginDao.LoginRota(sCodPessoa, Senha, persistencia);
//
//        } catch (Exception e) {
//            throw new Exception("Falha no login - " + e.getMessage());
//        }
//        for (LoginRota list_lrota1 : list_lrota) {
//            if (!list_lrota1.getPessoa().getPWWeb().equals(Senha)) {
//               ret = false;
//            } else {
//                ret = true;
        BigDecimal retorno = oTmktDetPstDao.getSequenciaEdicaoSupervisao(Seq, oTmktDetPst.getSecao(), persistencia);
        oTmktDetPstDao.updateTmkt(retorno, oTmktDetPst, persistencia);

        return retorno;
//            }
//        }
    }

    public static BigDecimal editaSuperv(BigDecimal Seq, String sCodPessoa, TmktDetPst oTmktDetPst, String Senha, String dataAtual, String hora, String dataFormatada, Persistencia persistencia) throws Exception {
        TmktDetPstDao oTmktDetPstDao = new TmktDetPstDao();

        BigDecimal retorno = oTmktDetPstDao.getSequenciaEdicaoSupervisao(Seq, oTmktDetPst.getSecao(), persistencia);
        oTmktDetPst.setSequencia(retorno.toPlainString());
        oTmktDetPstDao.updateTmkt(oTmktDetPst, dataAtual, hora, persistencia);

        return retorno;
    }
}
