/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package utilitario;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class GravadorLog {

    private static final String LOG_FILE_PATH = "c:\\temporario\\log.txt";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void log(String texto) {
        Path logFilePath = Paths.get(LOG_FILE_PATH);
        Path logDirectory = logFilePath.getParent();

        if (logDirectory != null && !Files.exists(logDirectory)) {
            try {
                Files.createDirectories(logDirectory);
            } catch (IOException e) {
                System.err.println("Erro ao criar o diretório de log: " + e.getMessage());
                // Se não conseguir criar o diretório, tenta gravar diretamente no caminho especificado
            }
        }        
        
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DATE_TIME_FORMATTER);
        String logEntry = String.format("[%s] %s%n", timestamp, texto);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(LOG_FILE_PATH, true))) {
            writer.write(logEntry);
        } catch (IOException e) {
            System.err.println("Erro ao gravar no arquivo de log: " + e.getMessage());
        }
    }
}