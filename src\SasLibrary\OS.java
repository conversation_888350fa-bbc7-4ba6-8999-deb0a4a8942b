package SasLibrary;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigDecimal;
import java.sql.ResultSet;

/**
 *
 * <AUTHOR>
 */
public class OS {

    /**
     * Encontra a OS do serviço
     *
     * @param CliOriPesq - Cliente de origem
     * @param CliDestPesq - Cliente de destino
     * @param CliFatPesq - Cliente a faturar
     * @param Filial - Filial
     * @param DtAtend - Data de atendimento
     * @param TipoOS - Tipo de OS que se quer buscar
     * @param CodCliCxf - Código do Caixa Forte
     * @param persistencia - Classe de conexão
     * @return - Número da OS
     * @throws java.lang.Exception - pode gerar exception
     */
    public static BigDecimal BuscaOS(String CliOriPesq, String CliDestPesq, String CliFatPesq, float Filial, String DtAtend, int TipoOS, String CodCliCxf, Persistencia persistencia) throws Exception {
        BigDecimal retorno = new BigDecimal("0");
        String SQLFiltro;
        ResultSet rs;
        String sql;
        sql = "select top 1 OS, CliDst, Cliente, NRed, NRedDst from Os_Vig "
                + " where CodFil = " + Filial + " and Situacao = 'A' and "
                + " DtInicio <='" + DtAtend + "' and DtFim >= '" + DtAtend + "' ";
        if ((TipoOS <= 2) || (TipoOS > 4)) {
            sql += " and (TipoOS = '1' or TipoOS = '2' or TipoOs = '6') ";
        } else if (TipoOS == 3) {
            sql += " and TipoOS = '3' ";
        } else if (TipoOS == 4) {
            sql += " and TipoOS = '4' ";
        }
        if (!"".equals(CliFatPesq)) {
            sql += " and CliFat = '" + CliFatPesq + "' ";
        }
        if (CliOriPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = '" + CliDestPesq + "' or CliDst = '" + CliDestPesq + "') and ViaCxF = 'S' ";
        } else if (CliDestPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = '" + CliOriPesq + "' or CliDst = '" + CliOriPesq + "' and ViaCxf = 'S') ";
        } else {
            SQLFiltro = " and ((Cliente = '" + CliOriPesq + "' and CliDst = '" + CliDestPesq + "') "
                    + " or (Cliente = '" + CliDestPesq + "' and CliDst = '" + CliOriPesq + "')) ";
        }
        try {
            Consulta consulta = new Consulta(sql + SQLFiltro, persistencia);
            consulta.select();
            boolean vazio = true;
            while (consulta.Proximo()) {
                vazio = false;
                retorno = new BigDecimal(consulta.getString("OS"));
            }
            if (vazio) {
                SQLFiltro = " and Cliente = '" + CliOriPesq + "' ";
                consulta = new Consulta(sql + SQLFiltro, persistencia);
                consulta.select();
                while (consulta.Proximo()) {
                    vazio = false;
                    retorno = new BigDecimal(consulta.getString("OS"));
                }
                if (vazio) {
                    SQLFiltro = " and Cliente = '" + CliDestPesq + "' ";
                    consulta = new Consulta(sql + SQLFiltro, persistencia);
                    consulta.select();
                    while (consulta.Proximo()) {
                        retorno = new BigDecimal(consulta.getString("OS"));
                    }
                }
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception(e.getMessage() + " -- " + sql);
        }
    }

    /**
     * Encontra a OS do serviço
     *
     * @param CliOriPesq - Cliente de origem
     * @param CliDestPesq - Cliente de destino
     * @param CliFatPesq - Cliente a faturar
     * @param Filial - Filial
     * @param DtAtend - Data de atendimento
     * @param TipoOS - Tipo de OS que se quer buscar
     * @param CodCliCxf - Código do Caixa Forte
     * @param persistencia - Classe de conexão
     * @return - Número da OS
     * @throws java.lang.Exception - pode gerar exception
     */
    public static BigDecimal BuscaOS(String CliOriPesq, String CliDestPesq, String CliFatPesq, String Filial, String DtAtend, int TipoOS, String CodCliCxf, Persistencia persistencia) throws Exception {
        BigDecimal retorno = new BigDecimal("0");
        String SQLFiltro;
        String sql;
        sql = "select top 1 OS, CliDst, Cliente, NRed, NRedDst from Os_Vig "
                + " where CodFil = " + Filial + " and Situacao = 'A' and "
                + " DtInicio <='" + DtAtend + "' and DtFim >= '" + DtAtend + "' ";
        if ((TipoOS <= 2) || (TipoOS > 4)) {
            sql += " and (TipoOS = '1' or TipoOS = '2' or TipoOs = '6') ";
        } else if (TipoOS == 3) {
            sql += " and TipoOS = '3' ";
        } else if (TipoOS == 4) {
            sql += " and TipoOS = '4' ";
        }

        if (!"".equals(CliFatPesq)) {
            sql += " and CliFat = '" + CliFatPesq + "' ";
        }

        if (CliOriPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = '" + CliDestPesq + "' or CliDst = '" + CliDestPesq + "') and ViaCxF = 'S' ";
        } else if (CliDestPesq.equals(CodCliCxf)) {
            SQLFiltro = " and (Cliente = '" + CliOriPesq + "' or CliDst = '" + CliOriPesq + "' and ViaCxf = 'S') ";
        } else {
            SQLFiltro = " and ((Cliente = '" + CliOriPesq + "' and CliDst = '" + CliDestPesq + "') "
                    + " or (Cliente = '" + CliDestPesq + "' and CliDst = '" + CliOriPesq + "')) ";
        }
        try {
            Consulta consulta = new Consulta(sql + SQLFiltro, persistencia);
            consulta.select();
            boolean vazio = true;
            while (consulta.Proximo()) {
                vazio = false;
                retorno = new BigDecimal(consulta.getString("OS"));
            }
            if (vazio) {
                SQLFiltro = " and Cliente = '" + CliOriPesq + "' ";
                consulta = new Consulta(sql + SQLFiltro, persistencia);
                consulta.select();
                while (consulta.Proximo()) {
                    vazio = false;
                    retorno = new BigDecimal(consulta.getString("OS"));
                }
                if (vazio) {
                    SQLFiltro = " and Cliente = '" + CliOriPesq + "' ";
                    consulta = new Consulta(sql + SQLFiltro, persistencia);
                    consulta.select();
                    while (consulta.Proximo()) {
                        retorno = new BigDecimal(consulta.getString("OS"));
                    }
                }
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception(e.getMessage() + " -- " + sql);
        }
    }
}
