/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CCusto;
import SasBeans.Cargos;
import SasBeans.FPLancamentos;
import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.Verbas;
import SasBeansCompostas.ContraCheque;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContraChequeCorpvsDao {

    protected String codColigada;

    public ContraChequeCorpvsDao(String codFil) {
        switch (codFil) {
            case "1":
            case "2":
            case "11":
            case "13":
            case "15":
                codColigada = "5";
                break;
            case "17":
            case "4":
                codColigada = "6";
                break;
            default:
                codColigada = "1";
                break;
        }
    }

    public List<FPPeriodos> getFPs(String matricula, Persistencia persistencia) throws Exception {
        List<FPPeriodos> lFpPeriodos = new ArrayList();
        String sql = "Select top 6"
                + " Convert(Varchar,AnoComp)+'-'+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'-01'  DtInicio, "
                + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP "
                + " from corporeRM.dbo.Pffinanc "
                + " where chapa = ? "
                + " and codcoligada = ? "
                + " group by MesComp, AnoComp "
                + " order by AnoComp desc, MesComp desc ";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setBigDecimal(new BigDecimal(matricula));
            cFpPeriodos.setString(this.codColigada);
            cFpPeriodos.contraChequeCORPVS();

            while (cFpPeriodos.Proximo()) {
                FPPeriodos oFpPeriodos = new FPPeriodos();
                LocalDate data = cFpPeriodos.getDate("DtInicio").toLocalDate();

                if (data.getMonthValue() != LocalDate.now().getMonthValue()
                        && data.getMonthValue() != LocalDate.now().getMonthValue() - 1) {
                    oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                    oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").contains(".0")
                            ? cFpPeriodos.getString("CodMovFP").replace(".0", "") : cFpPeriodos.getString("CodMovFP"));
                    lFpPeriodos.add(oFpPeriodos);
                } else if (data.getMonthValue() == LocalDate.now().getMonthValue() - 1
                        && DataAtual.diasUteis(LocalDate.now()) >= 6) {
                    oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                    oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").contains(".0")
                            ? cFpPeriodos.getString("CodMovFP").replace(".0", "") : cFpPeriodos.getString("CodMovFP"));
                    lFpPeriodos.add(oFpPeriodos);
                }
            }
            cFpPeriodos.Close();
            return lFpPeriodos;
        } catch (Exception e) {
            throw new Exception("ContraChequeCorpvsDao.getFPs - " + e.getMessage() + "\r\n"
                    + "Select top 6"
                    + " Convert(Varchar,AnoComp)+'-'+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'-01'  DtInicio, "
                    + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP "
                    + " from corporeRM.dbo.Pffinanc "
                    + " where chapa = " + matricula
                    + " and codcoligada = " + this.codColigada
                    + " group by MesComp, AnoComp "
                    + " order by AnoComp desc, MesComp desc ");
        }

    }

    public List<FPPeriodos> getFP(String matricula, String codMovFP, Persistencia persistencia) throws Exception {
        List<FPPeriodos> lFpPeriodos = new ArrayList();
        String sql = "Select "
                + " Convert(Varchar,AnoComp)+'-'+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'-01'  DtInicio, \n"
                + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP \n"
                + " from corporeRM.dbo.Pffinanc \n"
                + " where Substring(Convert(Varchar,AnoComp),3,2) = ? \n"
                + " and (Convert(BigInt,pffinanc.MesComp)) = ? \n"
                + " and chapa = ? \n"
                + " and codcoligada = ? \n"
                + " group by MesComp, AnoComp \n"
                + " order by AnoComp desc, MesComp desc ";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setString(codMovFP.substring(0, 2));
            cFpPeriodos.setString(codMovFP.substring(2));
            cFpPeriodos.setString(matricula);
            cFpPeriodos.setString(this.codColigada);
            cFpPeriodos.contraChequeCORPVS();

            while (cFpPeriodos.Proximo()) {
                FPPeriodos oFpPeriodos = new FPPeriodos();
                LocalDate data = cFpPeriodos.getDate("DtInicio").toLocalDate();

                if (data.getMonthValue() != LocalDate.now().getMonthValue()
                        && data.getMonthValue() != LocalDate.now().getMonthValue() - 1) {
                    oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                    oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").contains(".0")
                            ? cFpPeriodos.getString("CodMovFP").replace(".0", "") : cFpPeriodos.getString("CodMovFP"));
                    lFpPeriodos.add(oFpPeriodos);
                } else if (data.getMonthValue() == LocalDate.now().getMonthValue() - 1
                        && DataAtual.diasUteis(LocalDate.now()) >= 6) {
                    oFpPeriodos.setDtInicioF(cFpPeriodos.getDate("DtInicio").toLocalDate());
                    oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP").contains(".0")
                            ? cFpPeriodos.getString("CodMovFP").replace(".0", "") : cFpPeriodos.getString("CodMovFP"));
                    lFpPeriodos.add(oFpPeriodos);
                }
            }
            cFpPeriodos.Close();
            return lFpPeriodos;
        } catch (Exception e) {
            throw new Exception("ContraChequeCorpvsDao.getFPs - " + e.getMessage() + "\r\n"
                    + "Select top 6"
                    + " Convert(Varchar,AnoComp)+'-'+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'-01'  DtInicio, "
                    + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP "
                    + " from corporeRM.dbo.Pffinanc "
                    + " where chapa = " + matricula
                    + " and codcoligada = " + this.codColigada
                    + " group by MesComp, AnoComp "
                    + " order by AnoComp desc, MesComp desc ");
        }

    }

    public List<FPMensal> listaFPMensal(String sCodmovfp, String sCodmovfpF, String sMatr, Persistencia persistencia, Persistencia persistenciaCorpvsSasw) throws Exception {
        // Verificar se já pode mostrar ao usuário
        boolean validacao = false;
        String sql = "SELECT CASE WHEN dtpublicweb < GETDATE() THEN 'S' ELSE 'N' END validacao FROM fpperiodos where codmovfp = ?";

        Consulta consulta = new Consulta(sql, persistenciaCorpvsSasw);
        consulta.setString(sCodmovfp);

        consulta.select();

        while (consulta.Proximo()) {
            if (consulta.getString("validacao").equals("S")) {
                validacao = true;
            }
        }

        consulta.close();

        // Consulta dados
        List<FPMensal> lFPMensal = new ArrayList<>();

        if (validacao) {
            sql = "Select"
                    + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP, "
                    + " Convert(Varchar,NroPeriodo) TipoFp, "
                    + " 'Período '+Convert(Varchar,NroPeriodo) TipoFPFormatado "
                    + " from corporeRM.dbo.pffinanc "
                    + " where Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) = ? "
                    + " and chapa = ? "
                    + " and codcoligada = ? "
                    + " group by NroPeriodo, AnoComp, MesComp "
                    + " order by NroPeriodo ";
            try {
                Consulta stm = new Consulta(sql, persistencia);
                stm.setString(sCodmovfp);
                stm.setBigDecimal(new BigDecimal(sMatr));
                stm.setString(this.codColigada);
                stm.contraChequeCORPVS();

                FPMensal oFPMensal;
                while (stm.Proximo()) {
                    oFPMensal = new FPMensal();
                    oFPMensal.setMatr(sMatr);
                    oFPMensal.setCodFil(this.codColigada);
                    oFPMensal.setTipoFP(stm.getString("tipofp"));
                    oFPMensal.setCodMovFP(stm.getString("codmovfp"));
                    oFPMensal.setTipoFpFormatado(sCodmovfpF + " - " + stm.getString("TipoFpFormatado"));
                    lFPMensal.add(oFPMensal);
                }
                stm.Close();
                return lFPMensal;
            } catch (Exception e) {
                throw new Exception("ContraChequeCorpvsDao.listaFPMensal - " + e.getMessage() + "\r\n"
                        + "Select"
                        + " Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) CodMovFP, "
                        + " Convert(Varchar,NroPeriodo) TipoFp, "
                        + " 'Período '+Convert(Varchar,NroPeriodo) TipoFPFormatado "
                        + " from corporeRM.dbo.pffinanc "
                        + " where Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) = " + sCodmovfp
                        + " and chapa = " + sMatr
                        + " and codcoligada = " + this.codColigada
                        + " group by NroPeriodo, AnoComp, MesComp "
                        + " order by NroPeriodo ");
            }
        } else {
            return lFPMensal;
        }
    }

    public List<ContraCheque> getCCComposicao(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        try {
            String sql = "Select"
                    + "      Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'/'+Convert(Varchar,pffinanc.AnoComp) CodMovFP,   "
                    + "      'Período '+Convert(Varchar,nroperiodo) TipoFP,"
                    + "      pFFinanc.CodEvento Verba,     "
                    + "      pevento.Descricao,"
                    + "      pffinanc.Ref Valor,"
                    + "      Replace(PEvento.ProvDescBase,'P','V') Tipo,"
                    + "      pFFinanc.Valor ValorCalc "
                    + "      from corporeRM.dbo.pffinanc as pffinanc"
                    + "      Left join corporeRM.dbo.PEvento as PEvento on PEvento.Codigo = PFFinanc.CodEvento"
                    + "      and PEvento.CodColigada = PFFinanc.CodColigada"
                    + "      where chapa = ?"
                    + "      and Substring(Convert(Varchar,pffinanc.AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) = ?"
                    + "      and pFFinanc.nroperiodo = ?"
                    + "      and PFFinanc.codcoligada = ?"
                    + "      and PEvento.ProvDescBase <> 'B'"
                    + "      order by PEvento.ProvDescBase desc, CodEvento";
            Consulta stm = new Consulta(sql, persistencia);
            stm.setBigDecimal(new BigDecimal(sMatr));
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(this.codColigada);
            stm.contraChequeCORPVS();
            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPLancamentos oFPLancamentos = new FPLancamentos();
                Verbas oVerbas = new Verbas();
                oFPLancamentos.setCodMovFP(stm.getString("CodMovFP"));
                oFPLancamentos.setTipoFP(stm.getString("TipoFP"));
                oFPLancamentos.setVerba(stm.getString("Verba"));
                oVerbas.setDescricao(stm.getString("Descricao"));
                oFPLancamentos.setValor(stm.getString("Valor"));
                oFPLancamentos.setTipo(stm.getString("Tipo"));
                oFPLancamentos.setValorCalc(stm.getString("ValorCalc"));

                oContraCheque.setfPLancamentos(oFPLancamentos);
                oContraCheque.setVerbas(oVerbas);

                lContraCheque.add(oContraCheque);
            }
            stm.Close();
            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeCorpvsDao.getCCComposicao - " + e.getMessage() + "\r\n"
                    + "Select"
                    + "      Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp)+'/'+Convert(Varchar,pffinanc.AnoComp) CodMovFP,   "
                    + "      'Período '+Convert(Varchar,nroperiodo) TipoFP,"
                    + "      pFFinanc.CodEvento Verba,     "
                    + "      pevento.Descricao,"
                    + "      pffinanc.Ref Valor,"
                    + "      Replace(PEvento.ProvDescBase,'P','V') Tipo,"
                    + "      pFFinanc.Valor ValorCalc "
                    + "      from corporeRM.dbo.pffinanc as pffinanc"
                    + "      Left join corporeRM.dbo.PEvento as PEvento on PEvento.Codigo = PFFinanc.CodEvento"
                    + "      and PEvento.CodColigada = PFFinanc.CodColigada"
                    + "      where chapa = " + sMatr
                    + "      and Substring(Convert(Varchar,pffinanc.AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,pffinanc.MesComp)))+Convert(Varchar,pffinanc.MesComp) = ?" + sCodmovfp
                    + "      and pFFinanc.nroperiodo = " + sTipoFP
                    + "      and PFFinanc.codcoligada = " + this.codColigada
                    + "      and PEvento.ProvDescBase <> 'B'"
                    + "      order by PEvento.ProvDescBase desc, CodEvento");
        }
    }

    public List<ContraCheque> getBaseCalcs(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        String sql = "Select "
                + " Sum(BASEINSS+BASEINSS13+BASEINSS13OUTRO) AS 'BaseINSS', "
                + " Sum(BASEFGTS+BASEFGTS13) AS 'BaseFGTS', "
                + " Sum(BASEFGTS+BASEFGTS13)*0.080 AS 'FGTS', "
                + " Sum(pfperff.BASEIRRF+pfperff.BASEIRRFPART+pfperff.BASEIRRFFERIAS+pfperff.BASEIRRF13) AS 'BaseIR' "
                + " FROM corporeRM.dbo.pfperff as pfperff "
                + " where Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,MesComp)))+Convert(Varchar,MesComp) = ? "
                + " and pfperff.chapa = ? "
                + " and pfperff.nroperiodo = ? "
                + " and pfperff.codcoligada = ?";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setBigDecimal(new BigDecimal(sMatr));
            stm.setString(sTipoFP);
            stm.setString(this.codColigada);
            stm.contraChequeCORPVS();

            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPMensal oFPMensal = new FPMensal();
                oFPMensal.setBaseINSS(stm.getString("BaseINSS"));
                oFPMensal.setBaseFGTS(stm.getString("BaseFGTS"));
                oFPMensal.setFGTS(stm.getString("FGTS"));
                oFPMensal.setBaseIR(stm.getString("BaseIR"));

                oContraCheque.setfPMensal(oFPMensal);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeCorpvsDao.getBaseCalcs - " + e.getMessage() + "\r\n"
                    + "Select "
                    + " Sum(BASEINSS+BASEINSS13+BASEINSS13OUTRO) AS 'BaseINSS', "
                    + " Sum(BASEFGTS+BASEFGTS13) AS 'BaseFGTS', "
                    + " Sum(BASEFGTS+BASEFGTS13)*0.080 AS 'FGTS', "
                    + " Sum(pfperff.BASEIRRF+pfperff.BASEIRRFPART+pfperff.BASEIRRFFERIAS+pfperff.BASEIRRF13) AS 'BaseIR' "
                    + " FROM corporeRM.dbo.pfperff as pfperff "
                    + " where Substring(Convert(Varchar,AnoComp),3,2)+Replicate('0',2-Len(Convert(Varchar,MesComp)))+Convert(Varchar,MesComp) = " + sCodmovfp
                    + " and pfperff.chapa = " + sMatr
                    + " and pfperff.nroperiodo = " + sTipoFP
                    + " and pfperff.codcoligada = " + this.codColigada);
        }
    }

    public List<ContraCheque> getCCcabecalho(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        String sql = "Select top 1 "
                + " Funcion.Matr, "
                + " Funcion.Nome, "
                + " PstServ.Local LocalPosto, "
                + " PstServ.Secao, "
                + " Cargos.Descricao DescCargo, "
                + " Filiais.Descricao DescFil, "
                + " Funcion.CPF, "
                + " Funcion.RG, "
                + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                + " Funcion.Pis, "
                + " Funcion.Codfil funcioncodfil, "
                + " Funcion.Bairro funcionbairro, "
                + " Funcion.Cidade funcioncidade, "
                + " Funcion.Cep funcioncep, "
                + " Funcion.UF funcionuf, "
                + " Funcion.Vinculo funcionvinculo, "
                + " Funcion.DepIr, "
                + " Funcion.DepSf, "
                + " Filiais.RazaoSocial," //21
                + " Filiais.Endereco filiaisendereco,"
                + " Filiais.Bairro filiaisbairro,"
                + " Filiais.Cidade filiaiscidade,"
                + " Filiais.UF filiaisuf,"
                + " Filiais.CEP filiaiscep,"
                + " Filiais.CNPJ"
                + " from Funcion "
                + " Left join PstServ as PstServ on PstServ.Secao = Funcion.Secao and PstServ.CodFil = Funcion.CodFil "
                + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                + " where Funcion.Matr = ? ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sMatr);
            ContraCheque oContraCheque = null;
            stm.contraChequeCORPVS();

            while (stm.Proximo()) {
                Funcion oFuncion = new Funcion();
                PstServ oPstServ = new PstServ();
                Cargos oCargos = new Cargos();
                Filiais oFiliais = new Filiais();
                CCusto oCCusto = new CCusto();
                //FPMensalOP oFPMensalOP = new FPMensalOP();
                oContraCheque = new ContraCheque();

                oFuncion.setMatr(stm.getString("Matr"));
                oFuncion.setNome(stm.getString("Nome"));
                oPstServ.setLocal(stm.getString("LocalPosto"));
                oPstServ.setSecao(stm.getString("Secao"));
                oCargos.setDescricao(stm.getString("DescCargo"));
                oFiliais.setDescricao(stm.getString("DescFil"));
                oFuncion.setCPF(stm.getString("CPF"));
                oFuncion.setRG(stm.getString("RG"));
                oFuncion.setDt_Admis(stm.getString("Dt_Admis"));
                oFuncion.setPIS(stm.getString("Pis"));
                oFuncion.setCodFil(stm.getString("funcioncodfil"));
                oCCusto.setDescricao("");
                oFuncion.setBairro(stm.getString("funcionbairro"));
                oFuncion.setCidade(stm.getString("funcioncidade"));
                oFuncion.setCEP(stm.getString("funcioncep"));
                oFuncion.setUF(stm.getString("funcionUF"));
                oFuncion.setVinculo(stm.getString("funcionvinculo"));
                oFuncion.setDepIR(stm.getString("DepIr"));
                oFuncion.setDepSF(stm.getString("DepSf"));
                oFiliais.setRazaoSocial(stm.getString("RazaoSocial")); //21
                oFiliais.setEndereco(stm.getString("filiaisendereco"));
                oFiliais.setBairro(stm.getString("filiaisbairro"));
                oFiliais.setCidade(stm.getString("filiaiscidade"));
                oFiliais.setUF(stm.getString("filiaisUF"));
                oFiliais.setCEP(stm.getString("filiaisCEP"));
                oFiliais.setCNPJ(stm.getString("CNPJ"));
                oCargos.setCBO("");

                oContraCheque.setFuncion(oFuncion);
                oContraCheque.setPstServ(oPstServ);
                oContraCheque.setCargos(oCargos);
                oContraCheque.setFiliais(oFiliais);
                oContraCheque.setcCusto(oCCusto);
                //oContraCheque.setfPMensalOP(oFPMensalOP);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeCorpvsDao.getCCcabecalho - " + e.getMessage() + "\r\n"
                    + "Select top 1 "
                    + " Funcion.Matr, "
                    + " Funcion.Nome, "
                    + " PstServ.Local LocalPosto, "
                    + " PstServ.Secao, "
                    + " Cargos.Descricao DescCargo, "
                    + " Filiais.Descricao DescFil, "
                    + " Funcion.CPF, "
                    + " Funcion.RG, "
                    + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                    + " Funcion.Pis, "
                    + " Funcion.Codfil funcioncodfil, "
                    + " Funcion.Bairro funcionbairro, "
                    + " Funcion.Cidade funcioncidade, "
                    + " Funcion.Cep funcioncep, "
                    + " Funcion.UF funcionuf, "
                    + " Funcion.Vinculo funcionvinculo, "
                    + " Funcion.DepIr, "
                    + " Funcion.DepSf, "
                    + " Filiais.RazaoSocial," //21
                    + " Filiais.Endereco filiaisendereco,"
                    + " Filiais.Bairro filiaisbairro,"
                    + " Filiais.Cidade filiaiscidade,"
                    + " Filiais.UF filiaisuf,"
                    + " Filiais.CEP filiaiscep,"
                    + " Filiais.CNPJ"
                    + " from Funcion "
                    + " Left join PstServ as PstServ on PstServ.Secao = Funcion.Secao and PstServ.CodFil = Funcion.CodFil "
                    + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                    + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                    + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                    + " where Funcion.Matr = " + sMatr);
        }
    }
}
