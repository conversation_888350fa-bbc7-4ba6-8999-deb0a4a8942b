/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ClientesEmail;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClientesEmailDao {

    public void InserirEmail(ClientesEmail emails, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into ClientesEmail (CodCli, CodFil, Email, Nome, Operador, Dt_Alter, Hr_Alter)"
                    + " values (?, ?, ?, ?, ?, ?, ?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(emails.getCodCli());
            consulta.setBigDecimal(emails.getCodFil());
            consulta.setString(emails.getEmail());
            consulta.setString(emails.getNome());
            consulta.setString(emails.getOperador());
            consulta.setDate(DataAtual.LC2Date(emails.getDt_Alter()));
            consulta.setString(emails.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesEmailDao.Inserir - " + e.getMessage() + "\n insert into ClientesEmail (CodCli, CodFil, Email, Nome, Operador, Dt_Alter, Hr_Alter)"
                    + " values (" + emails.getCodCli() + "," + emails.getCodFil() + "," + emails.getEmail() + "," + emails.getNome() + "," + emails.getOperador() + "," + emails.getDt_Alter() + "," + emails.getHr_Alter() + ")");
        }
    }

    public List<ClientesEmail> selecionarEmail(String codCli, Persistencia persistencia) throws Exception {
        List<ClientesEmail> retorno = new ArrayList<>();
        try {
            String sql = "select * from ClientesEmail"
                    + " where CodCli = ? order by Nome";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.select();

            ClientesEmail CE;

            while (consulta.Proximo()) {
                CE = new ClientesEmail();
                CE.setCodCli(consulta.getString("CodCli"));
                CE.setCodFil(consulta.getString("CodFil"));
                CE.setEmail(consulta.getString("Email"));
                CE.setNome(consulta.getString("Nome"));
                CE.setOperador(consulta.getString("Operador"));
                CE.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                CE.setHr_Alter(consulta.getString("Hr_Alter"));

                retorno.add(CE);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesEmailDao.SelecionaEmail - " + e.getMessage()
                    + "\n select * from ClientesEmail"
                    + "\n where ClientesEmail = " + codCli
            );
        }
    }

    public Boolean existeEmail(ClientesEmail emails, Persistencia persistencia) throws Exception {
        try {
            String sql = "select CodCli, Email"
                    + " from ClientesEmail "
                    + "where CodCli = ? and Email = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(emails.getCodCli());
            consulta.setString(emails.getEmail());
            consulta.select();

            ClientesEmail clientesEmail = null;

            while (consulta.Proximo()) {
                clientesEmail = new ClientesEmail();

                clientesEmail.setCodCli(consulta.getString("CodCli"));
                clientesEmail.setEmail(consulta.getString("Email"));
            }
            consulta.Close();

            return null != clientesEmail;

        } catch (Exception e) {
            throw new Exception("ClientesEmailDao.ExisteEmail - " + e.getMessage()
                    + "\n select Email from ClientesEmail where CodCli =" + emails.getCodCli() + " and " + emails.getEmail());
        }
    }

    public void deletarEmail(ClientesEmail emails, Persistencia persistencia) throws Exception {

        String sql = "delete from ClientesEmail where Email = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(emails.getEmail());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao deletar email: " + e.getMessage() + "\n"
                    + "delete from ClientesEmail where Email = " + emails + "");
        }
    }
}
