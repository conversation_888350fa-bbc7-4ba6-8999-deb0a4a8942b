package SasDaos;

import Arquivo.LeArq;
import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FPMensal;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 *
 * <AUTHOR>
 */
public class FPMensalDao {

    String sql;

    /**
     * Retorna dados da FPMensal
     *
     * @param sCodmovfp -
     * @param sMatr - Código matrícula
     * @param persistencia - conexão com o banco
     * @return - retorna lista com tipoFP
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<FPMensal> getFPMensal(String sCodmovfp, String sMatr, Persistencia persistencia) throws Exception {

        List<FPMensal> lFPMensal = new ArrayList();

        sql = "select CodMovFP, tipofp from fpmensal "
                + " where  codmovfp=? and matr=?";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                FPMensal oFPMensal = new FPMensal();

                oFPMensal.setTipoFP(stm.getString("tipofp"));
                oFPMensal.setCodMovFP(stm.getString("codmovfp"));
                oFPMensal.setTipoFpFormatado(this.tabela_tipo_fp(stm.getString("tipofp")));
                lFPMensal.add(oFPMensal);

            }
            stm.Close();
            return lFPMensal;
        } catch (Exception e) {
            throw new Exception("FPMensalDao.getFPMensal  - " + e.getMessage() + "\r\n"
                    + "select CodMovFP, tipofp from fpmensal "
                    + " where  codmovfp=" + sCodmovfp + " and matr=" + sMatr);
        }

    }

    private String tabela_tipo_fp(String TipoFP) {
        String bufTabela = "";
        String tipofp = "";
        boolean encontrado = false;
        LeArq tabela = new LeArq();
        try {
            String sArquivo = "C:\\Tomcat 8.0\\webapps\\nsat\\tipos_folha.txt";
            //String sArquivo = "D:\\Projetos\\Web\\nSat\\build\\web\\tipos_folha.txt";
            tabela.AbreArquivo(sArquivo);

            while ((bufTabela != null) && (!encontrado)) {
                bufTabela = tabela.getLinha();
                StringTokenizer tk = new StringTokenizer(bufTabela, "|");
                if (tk.nextToken().equals(TipoFP)) {
                    tipofp = tk.nextToken();
                    encontrado = true;
                }
            }
            tabela.FechaArquivo();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return tipofp;
    }

    public List<FPMensal> getFPMensal(String sCodmovfp, String sTipoFP, String sMatr, Persistencia persistencia) throws Exception {

        List<FPMensal> lFPMensal = new ArrayList();

        sql = "select "
                + " BaseINSS, "
                + " BaseFGTS, "
                + " FGTS,"
                + " BaseIR"
                + " from FPMensal "
                + " where "
                + " codmovfp=? and"
                + " TipoFP=?"
                + " and Matr=?";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                FPMensal oFPMensal = new FPMensal();

                oFPMensal.setTipoFP(stm.getString("tipofp"));
                lFPMensal.add(oFPMensal);

            }
            stm.Close();
            return lFPMensal;
        } catch (Exception e) {
            throw new Exception("FPMensalDao.getFPMensal  - " + e.getMessage() + "\r\n"
                    + "select BaseINSS, BaseFGTS, FGTS, BaseIR from FPMensal "
                    + " where codmovfp=" + sCodmovfp + " and TipoFP=" + sTipoFP + " and Matr=" + sMatr);
        }

    }

    /**
     *
     * @param sCodmovfp - periodo yyMM
     * @param sCodmovfpF - periodo formatado MM/yyyy
     * @param sMatr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<FPMensal> listaFPMensal(String sCodmovfp, String sCodmovfpF, String sMatr, Persistencia persistencia) throws Exception {
        try {
            List<FPMensal> lFPMensal = new ArrayList();
            String sssql = "select \n"
                    + "    fpmensal.Liquido, fpmensal.CodMovFP, fpmensal.tipofp, fpmensal.codFil, fpmensal.matr, \n"
                    + "    case when fpmensal.tipofp = 'MEN' then 'Mensal' \n"
                    + "        when fpmensal.tipofp = 'ADT' then 'Adiantamento' \n"
                    + "        when fpmensal.tipofp = 'FER' then 'Férias' \n"
                    + "        when fpmensal.tipofp = 'RES' then 'Rescisão' \n"
                    + "        when fpmensal.tipofp = 'CPL' then 'Complementar' \n"
                    + "        when fpmensal.tipofp = '131' then 'Décimo Terceiro 1' \n"
                    + "        when fpmensal.tipofp = '132' then 'Décimo Terceiro 2' \n"
                    + "        when fpmensal.tipofp = '133' then 'Coplementar Décimo Terceiro' \n"
                    + "        else '' \n"
                    + "    end TipoFpFormatado\n"
                    + "from \n"
                    + "    fpmensal \n"
                    + "LEFT JOIN\n"
                    + "    fpperiodos ON fpperiodos.codmovfp = fpmensal.codmovfp\n"
                    + "where  \n"
                    + "    fpmensal.codmovfp = ? and fpmensal.matr in (Select x.Matr from Funcion x where x.CPF in (Select z.CPF From Funcion z WHERE z.Matr = ?) ) \n"
                    + "    and fpmensal.tipofp <> 'FEJ' \n"
                    + "    and fpmensal.tipofp <> 'DIR' \n"
                    + "    and FPMensal.liquido is not null\n"
                    + "    and fpperiodos.dtpublicweb < ?";
            if(persistencia.getEmpresa().contains("SERVITE")
                     || persistencia.getEmpresa().contains("INTERFORT")
                     || persistencia.getEmpresa().contains("INVLMT")){
                sssql += "\n    and fpmensal.tipofp <> 'CPL' ";
            }
            Consulta stm = new Consulta(sssql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sMatr);
            stm.setString(getDataAtual("SQL"));
            stm.select();

            FPMensal oFPMensal;
            while (stm.Proximo()) {
                oFPMensal = new FPMensal();
                oFPMensal.setLiquido(stm.getString("Liquido"));
                oFPMensal.setTipoFP(stm.getString("tipofp"));
                oFPMensal.setCodMovFP(stm.getString("codmovfp"));
                oFPMensal.setCodFil(stm.getString("codFil"));
                oFPMensal.setMatr(stm.getString("matr"));
                oFPMensal.setTipoFpFormatado(sCodmovfpF + " - " + stm.getString("TipoFpFormatado"));
                // Se TipoFP = CPL e Liquido = 0, não faz nada.
                if (oFPMensal.getTipoFP().equals("CPL") && oFPMensal.getLiquido().compareTo(BigDecimal.ZERO) == 0) {
                } else {
                    lFPMensal.add(oFPMensal);
                }

            }
            stm.Close();
            return lFPMensal;
        } catch (Exception e) {
            throw new Exception("FPMensalDao.listaFPMensal - " + e.getMessage() + "\r\n"
                    + "select \n"
                    + "    fpmensal.Liquido, fpmensal.CodMovFP, fpmensal.tipofp, fpmensal.codFil, fpmensal.matr, \n"
                    + "    case when fpmensal.tipofp = 'MEN' then 'Mensal' \n"
                    + "        when fpmensal.tipofp = 'ADT' then 'Adiantamento' \n"
                    + "        when fpmensal.tipofp = 'FER' then 'Férias' \n"
                    + "        when fpmensal.tipofp = 'RES' then 'Rescisão' \n"
                    + "        when fpmensal.tipofp = 'CPL' then 'Complementar' \n"
                    + "        when fpmensal.tipofp = '131' then 'Décimo Terceiro 1' \n"
                    + "        when fpmensal.tipofp = '132' then 'Décimo Terceiro 2' \n"
                    + "        when fpmensal.tipofp = '133' then 'Coplementar Décimo Terceiro' \n"
                    + "        else '' \n"
                    + "    end TipoFpFormatado\n"
                    + "from \n"
                    + "    fpmensal \n"
                    + "LEFT JOIN\n"
                    + "    fpperiodos ON fpperiodos.codmovfp = fpmensal.codmovfp\n"
                    + "where  \n"
                    + "    fpmensal.codmovfp = " + sCodmovfp + "  and fpmensal.matr in (Select x.Matr from Funcion x where x.CPF in (Select z.CPF From Funcion z WHERE z.Matr = " + sMatr + ") ) \n"
                    + " and fpmensal.tipofp <> 'FEJ' \n"
                    + "    and fpmensal.tipofp <> 'DIR' \n"
                    + "    and FPMensal.liquido is not null\n"
                    + "    and fpperiodos.dtpublicweb < " + getDataAtual("SQL"));
        }
    }
}
