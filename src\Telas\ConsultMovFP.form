<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.8" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.ButtonGroup" name="buttonGroup1">
    </Component>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="2"/>
    <Property name="title" type="java.lang.String" value="Movimenta&#xe7;&#xe3;o de Folha de Pagamento"/>
    <Property name="alwaysOnTop" type="boolean" value="true"/>
    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
      <Color blue="9b" green="5b" red="14" type="rgb"/>
    </Property>
    <Property name="resizable" type="boolean" value="false"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <Events>
    <EventHandler event="windowOpened" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowOpened"/>
    <EventHandler event="windowClosing" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowClosing"/>
  </Events>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="Tconsult" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="32767" attributes="0"/>
          </Group>
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Component id="TitPesq" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="DtIni" min="-2" pref="109" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="20" max="-2" attributes="0"/>
              <Component id="TitPesq1" min="-2" pref="33" max="-2" attributes="0"/>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="DtFim" min="-2" pref="109" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
              <Component id="jButton1" min="-2" pref="52" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
          <Component id="jScrollPane1" alignment="0" pref="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="1" attributes="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="DtIni" max="32767" attributes="0"/>
                  <Component id="jButton1" alignment="0" max="32767" attributes="0"/>
                  <Component id="TitPesq" alignment="0" max="32767" attributes="0"/>
                  <Component id="TitPesq1" alignment="0" max="32767" attributes="0"/>
                  <Component id="DtFim" max="32767" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="558" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="Tconsult" min="-2" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="ConsultMovFP">
          <Properties>
            <Property name="autoCreateRowSorter" type="boolean" value="true"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Tahoma" size="14" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="0">
                <Column editable="true" title="CodFil" type="java.lang.Object"/>
                <Column editable="true" title="CodMovFP" type="java.lang.Object"/>
                <Column editable="true" title="TipoFP" type="java.lang.Object"/>
                <Column editable="true" title="DtPagto" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="autoResizeMode" type="int" value="0"/>
            <Property name="rowHeight" type="int" value="30"/>
            <Property name="showVerticalLines" type="boolean" value="false"/>
          </Properties>
          <Events>
            <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="ConsultMovFPKeyPressed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="TitPesq">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Data:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
          <Image iconType="3" name="/Figuras/icone_pesquisar.png"/>
        </Property>
        <Property name="toolTipText" type="java.lang.String" value="Toque para pesquisar"/>
        <Property name="borderPainted" type="boolean" value="false"/>
        <Property name="contentAreaFilled" type="boolean" value="false"/>
      </Properties>
      <Events>
        <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jButton1MouseClicked"/>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="Tconsult">
    </Component>
    <Component class="javax.swing.JLabel" name="TitPesq1">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="text" type="java.lang.String" value="a"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JFormattedTextField" name="DtIni">
      <Properties>
        <Property name="formatterFactory" type="javax.swing.JFormattedTextField$AbstractFormatterFactory" editor="org.netbeans.modules.form.editors.AbstractFormatterFactoryEditor" preCode="try {" postCode="} catch (java.text.ParseException ex) {&#xa;ex.printStackTrace();&#xa;}">
          <Format format="##/##/####" subtype="-1" type="5"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="focusable" type="boolean" value="false"/>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="focusLost" listener="java.awt.event.FocusListener" parameters="java.awt.event.FocusEvent" handler="DtIniFocusLost"/>
        <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="DtIniKeyPressed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JFormattedTextField" name="DtFim">
      <Properties>
        <Property name="formatterFactory" type="javax.swing.JFormattedTextField$AbstractFormatterFactory" editor="org.netbeans.modules.form.editors.AbstractFormatterFactoryEditor" preCode="try {" postCode="} catch (java.text.ParseException ex) {&#xa;ex.printStackTrace();&#xa;}">
          <Format format="##/##/####" subtype="-1" type="5"/>
        </Property>
        <Property name="horizontalAlignment" type="int" value="0"/>
        <Property name="focusable" type="boolean" value="false"/>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="focusLost" listener="java.awt.event.FocusListener" parameters="java.awt.event.FocusEvent" handler="DtFimFocusLost"/>
        <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="DtFimKeyPressed"/>
      </Events>
    </Component>
  </SubComponents>
</Form>
