package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CxFGuiasVolDao {

    private Persistencia persistencia;

    public CxFGuiasVolDao() {
    }

    public CxFGuiasVolDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Lista a movimentação de containers para um cliente específico no
     * intervalo dado. abstração para containers.
     *
     * @param codCli
     * @param codFil
     * @param dtInicio
     * @param dtFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<MovimentacaoContainer> listarMovimentacaoContainerCliente(String codCli, String codFil,
            String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Rt_Perc.ER TpServico,\n"
                    + "Rotas.CodFil, CxfGuiasVol.Lacre Container, Rotas.Data, Rotas.Rota, Rt_Perc.HrCheg HrServico, Clientes.Nome, Clientes.Nred, \n"
                    + "Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, Funcion.Nome Motorista, Veiculos.Placa\n"
                    + "from CxfGuiasVol\n"
                    + "Left join Rt_Guias  on Rt_Guias.Guia = CxfGuiasVol.Guia\n"
                    + "                   and Rt_Guias.Serie = CxfGuiasVol.Serie\n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rt_Guias.Sequencia \n"
                    + "                  and Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "Left join Rotas   on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left join Escala  on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left join Funcion  on Funcion.Matr = Escala.MatrMot\n"
                    + "Left join Veiculos  on Veiculos.Numero = Escala.Veiculo\n"
                    + "Where Rotas.Data between ? and ?\n"
                    + "  and Rotas.CodFil = ?\n"
                    + "  and Clientes.CodFil = ?\n"
                    + "  and Clientes.Codigo = ?\n"
                    + "ORDER BY Rotas.Data desc, Rt_Perc.HrCheg desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codFil);
            consulta.setString(codFil);
            consulta.setString(codCli);
            consulta.select();
            List<MovimentacaoContainer> retorno = new ArrayList<>();
            MovimentacaoContainer movimentacaoContainer;
            while (consulta.Proximo()) {
                movimentacaoContainer = new MovimentacaoContainer();
                movimentacaoContainer.setCodFil(consulta.getString("CodFil"));
                movimentacaoContainer.setContainer(consulta.getString("Container"));
                movimentacaoContainer.setData(consulta.getString("Data"));
                movimentacaoContainer.setRota(consulta.getString("Rota"));
                movimentacaoContainer.setHrServico(consulta.getString("HrServico"));
                movimentacaoContainer.setNome(consulta.getString("Nome"));
                movimentacaoContainer.setNred(consulta.getString("Nred"));
                movimentacaoContainer.setEnde(consulta.getString("Ende"));
                movimentacaoContainer.setBairro(consulta.getString("Bairro"));
                movimentacaoContainer.setCidade(consulta.getString("Cidade"));
                movimentacaoContainer.setEstado(consulta.getString("Estado"));
                movimentacaoContainer.setMotorista(consulta.getString("Motorista"));
                movimentacaoContainer.setPlaca(consulta.getString("Placa"));
                movimentacaoContainer.setTpServico(consulta.getString("TpServico"));
                retorno.add(movimentacaoContainer);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.listarMovimentacaoContainer - " + e.getMessage() + "\r\n"
                    + "Select Rt_Perc.ER TpServico,\n"
                    + "Rotas.CodFil, CxfGuiasVol.Lacre Container, Rotas.Data, Rotas.Rota, Rt_Perc.HrCheg HrServico, Clientes.Nome, Clientes.Nred, \n"
                    + "Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, Funcion.Nome Motorista, Veiculos.Placa\n"
                    + "from CxfGuiasVol\n"
                    + "Left join Rt_Guias  on Rt_Guias.Guia = CxfGuiasVol.Guia\n"
                    + "                   and Rt_Guias.Serie = CxfGuiasVol.Serie\n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rt_Guias.Sequencia \n"
                    + "                  and Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "Left join Rotas   on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left join Escala  on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left join Funcion  on Funcion.Matr = Escala.MatrMot\n"
                    + "Left join Veiculos  on Veiculos.Numero = Escala.Veiculo\n"
                    + "Where Rotas.Data between " + dtInicio + " and " + dtFim + "\n"
                    + "  and Rotas.CodFil = " + codFil + "\n"
                    + "  and Clientes.CodFil = " + codFil + "\n"
                    + "  and Clientes.Codigo = " + codCli);
        }
    }

    /**
     * Lista a movimentação de um lacre específico no intervalo dado. abstração
     * para containers.
     *
     * @param container
     * @param codFil
     * @param dtInicio
     * @param dtFim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<MovimentacaoContainer> listarMovimentacaoContainer(String container, String codFil,
            String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Rt_Perc.ER TpServico,\n"
                    + "Rotas.CodFil, CxfGuiasVol.Lacre Container, Rotas.Data, Rotas.Rota, Rt_Perc.HrCheg HrServico, Clientes.Nome, Clientes.Nred, \n"
                    + "Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, Funcion.Nome Motorista, Veiculos.Placa\n"
                    + "from CxfGuiasVol\n"
                    + "Left join Rt_Guias  on Rt_Guias.Guia = CxfGuiasVol.Guia\n"
                    + "                   and Rt_Guias.Serie = CxfGuiasVol.Serie\n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rt_Guias.Sequencia \n"
                    + "                  and Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "Left join Rotas   on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left join Escala  on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left join Funcion  on Funcion.Matr = Escala.MatrMot\n"
                    + "Left join Veiculos  on Veiculos.Numero = Escala.Veiculo\n"
                    + "Where Rotas.Data between ? and ?\n"
                    + "  and Rotas.CodFil = ?\n"
                    + "  and CxfGuiasVol.Lacre = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dtInicio);
            consulta.setString(dtFim);
            consulta.setString(codFil);
            consulta.setString(container);
            consulta.select();
            List<MovimentacaoContainer> retorno = new ArrayList<>();
            MovimentacaoContainer movimentacaoContainer;
            while (consulta.Proximo()) {
                movimentacaoContainer = new MovimentacaoContainer();
                movimentacaoContainer.setCodFil(consulta.getString("CodFil"));
                movimentacaoContainer.setContainer(consulta.getString("Container"));
                movimentacaoContainer.setData(consulta.getString("Data"));
                movimentacaoContainer.setRota(consulta.getString("Rota"));
                movimentacaoContainer.setHrServico(consulta.getString("HrServico"));
                movimentacaoContainer.setNome(consulta.getString("Nome"));
                movimentacaoContainer.setNred(consulta.getString("Nred"));
                movimentacaoContainer.setEnde(consulta.getString("Ende"));
                movimentacaoContainer.setBairro(consulta.getString("Bairro"));
                movimentacaoContainer.setCidade(consulta.getString("Cidade"));
                movimentacaoContainer.setEstado(consulta.getString("Estado"));
                movimentacaoContainer.setMotorista(consulta.getString("Motorista"));
                movimentacaoContainer.setPlaca(consulta.getString("Placa"));
                movimentacaoContainer.setTpServico(consulta.getString("TpServico"));
                retorno.add(movimentacaoContainer);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.listarMovimentacaoContainer - " + e.getMessage() + "\r\n"
                    + "Select Rt_Perc.ER TpServico,\n"
                    + "Rotas.CodFil, CxfGuiasVol.Lacre Container, Rotas.Data, Rotas.Rota, Rt_Perc.HrCheg HrServico, Clientes.Nome, Clientes.Nred, \n"
                    + "Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, Funcion.Nome Motorista, Veiculos.Placa\n"
                    + "from CxfGuiasVol\n"
                    + "Left join Rt_Guias  on Rt_Guias.Guia = CxfGuiasVol.Guia\n"
                    + "                   and Rt_Guias.Serie = CxfGuiasVol.Serie\n"
                    + "Left join Rt_Perc  on Rt_Perc.Sequencia = Rt_Guias.Sequencia \n"
                    + "                  and Rt_Perc.Parada = Rt_Guias.Parada\n"
                    + "Left join Rotas   on Rotas.Sequencia = Rt_Perc.Sequencia\n"
                    + "Left join Clientes  on Clientes.Codigo = Rt_Perc.CodCli1\n"
                    + "                   and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left join Escala  on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left join Funcion  on Funcion.Matr = Escala.MatrMot\n"
                    + "Left join Veiculos  on Veiculos.Numero = Escala.Veiculo\n"
                    + "Where Rotas.Data between " + dtInicio + " and " + dtFim + "\n"
                    + "  and Rotas.CodFil = " + codFil + "\n"
                    + "  and CxfGuiasVol.Lacre = " + container);
        }
    }

    /**
     * Lista todos os lacres de uma filial. abstração de containers.
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CxFGuiasVol> listarContainers(String codFil, Persistencia persistencia) throws Exception {
        try {
            List<CxFGuiasVol> retorno = new ArrayList<>();
            String sql = "select CxfGuiasVol.Lacre, CxfGuiasVol.CodFil from CxfGuiasVol where codfil = ? group by CxfGuiasVol.Lacre,CxfGuiasVol.CodFil ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();
            CxFGuiasVol cxfGuiasVol;
            while (consulta.Proximo()) {
                cxfGuiasVol = new CxFGuiasVol();
                cxfGuiasVol.setLacre(consulta.getString("lacre"));
                cxfGuiasVol.setCodFil(consulta.getString("codFil"));
                retorno.add(cxfGuiasVol);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.listarContainers - " + e.getMessage() + "\r\n"
                    + "select CxfGuiasVol.Lacre, CxfGuiasVol.CodFil from CxfGuiasVol where codfil = " + codFil + " group by CxfGuiasVol.Lacre,CxfGuiasVol.CodFil ");
        }
    }

    /**
     * Lista de volumes
     *
     * @param guia numero da guia
     * @param serie serie da guia
     * @param persistencia conexao com o banco de dados
     * @return lista de volumes
     * @throws Exception
     */
    public List<CxFGuiasVol> listaDeVolumes(String guia, String serie, Persistencia persistencia) throws Exception {
        List<CxFGuiasVol> cxfGuiasVols = new ArrayList<>();
        try {
            String sql = "SELECT * FROM CxFGuiasVol WHERE Guia = ? AND Serie = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            CxFGuiasVol cxfGuiasVol = null;
            while (consulta.Proximo()) {
                cxfGuiasVol = new CxFGuiasVol();
                cxfGuiasVol.setCodFil(consulta.getString("Codfil"));
                cxfGuiasVol.setCodCli(consulta.getString("codCli"));
                cxfGuiasVol.setGuia(consulta.getString("guia"));
                cxfGuiasVol.setSerie(consulta.getString("serie"));
                cxfGuiasVol.setLacre(consulta.getString("lacre"));
                cxfGuiasVol.setValor(consulta.getString("valor"));
                cxfGuiasVol.setQtde(consulta.getString("Qtde"));
                cxfGuiasVol.setObs(consulta.getString("Obs"));
                cxfGuiasVol.setTipo(consulta.getString("Tipo"));
                cxfGuiasVols.add(cxfGuiasVol);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.listaDeVolumes - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM CxFGuiasVol WHERE Guia = " + guia + " AND Serie = " + serie);
        }
        return cxfGuiasVols;
    }

    /**
     * Lista de volumes
     *
     * @param guia numero da guia
     * @param serie serie da guia
     * @param persistencia conexao com o banco de dados
     * @return lista de volumes
     * @throws Exception
     */
    public List<CxFGuiasVol> listaDeVolumesPreOrder(String guia, String serie, Persistencia persistencia) throws Exception {
        List<CxFGuiasVol> cxfGuiasVols = new ArrayList<>();
        try {
            String sql = "SELECT CxFGuiasVol.*, PreOrderVol.Obs ObsLacre \n"
                    + "FROM CxFGuiasVol \n"
                    + "LEFT JOIN PreOrder on PreORder.Guia = CxFGuiasVol.Guia\n"
                    + "                 and PreORder.Serie = CxFGuiasVol.Serie\n"
                    + "                and PreORder.CodFil = CxFGuiasVol.CodFil\n"
                    + "LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "                  and PreOrderVol.CodFil = PreORder.CodFil\n"
                    + "WHERE CxFGuiasVol.Guia = ? AND CxFGuiasVol.Serie = ?\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();

            CxFGuiasVol cxfGuiasVol;
            while (consulta.Proximo()) {
                cxfGuiasVol = new CxFGuiasVol();
                cxfGuiasVol.setCodFil(consulta.getString("Codfil"));
                cxfGuiasVol.setCodCli(consulta.getString("codCli"));
                cxfGuiasVol.setGuia(consulta.getString("guia"));
                cxfGuiasVol.setSerie(consulta.getString("serie"));
                cxfGuiasVol.setLacre(consulta.getString("lacre"));
                cxfGuiasVol.setValor(consulta.getString("valor"));
                cxfGuiasVol.setQtde(consulta.getString("Qtde"));
                cxfGuiasVol.setObs(consulta.getString("Obs"));
                cxfGuiasVol.setOrdem(consulta.getString("Ordem"));
                cxfGuiasVol.setTipo(consulta.getString("Tipo"));
                cxfGuiasVols.add(cxfGuiasVol);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.listaDeVolumes - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM CxFGuiasVol.*, \n"
                    + "case when PreOrderVol.Obs is null then CxFguiasVol.Obs ELSE PreOrderVol.Obs END ObsLacre \n"
                    + "FROM CxFGuiasVol \n"
                    + "LEFT JOIN PreOrder on PreORder.Guia = CxFGuiasVol.Guia\n"
                    + "                 and PreORder.Serie = CxFGuiasVol.Serie\n"
                    + "                and PreORder.CodFil = CxFGuiasVol.CodFil\n"
                    + "LEFT JOIN PreOrderVol on PreOrderVol.Sequencia = PreOrder.Sequencia\n"
                    + "                  and PreOrderVol.CodFil = PreORder.CodFil\n"
                    + " WHERE Guia = " + guia + " AND Serie = " + serie);
        }
        return cxfGuiasVols;
    }

    /**
     * Delete Guias de volume cxf
     *
     * @param guia - Série da guia
     * @param serie - Número da guia
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void deletaCxfguiasvol(Persistencia persistencia, String guia, String serie) throws Exception {

        String sql = "delete from cxfguiasvol "
                + "where guia=? and Serie=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.deletaCxfguiasvol - " + e.getMessage() + "\r\n"
                    + "delete from cxfguiasvol "
                    + "where guia=" + guia + " and Serie=" + serie);
        }
    }

    public List<CxFGuiasVol> getCxfGuiasVolEntrega(String sequencia, String hora1, Persistencia persistencia) throws Exception {
        List<CxFGuiasVol> listCxFGuiasvol = new ArrayList();
        try {
            Consulta rsgtv = new Consulta("select Guia,Serie,Valor,Codcli "
                    + " from cxfguiasvol "
                    + " where seqrotasai = ?"
                    + " and hora1d = ?", persistencia);
            rsgtv.setString(sequencia);
            rsgtv.setString(hora1.replace(":", ""));
            rsgtv.select();
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("hora1").equals(""))) {
                    CxFGuiasVol temp = new CxFGuiasVol();
                    temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setCodCli(rsgtv.getString("Codcli"));
                    listCxFGuiasvol.add(temp);
                }
            }
            rsgtv.Close();
            return listCxFGuiasvol;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.getCxfGuiasVolEntrega - " + e.getMessage() + "\r\n"
                    + "select Guia,Serie,Valor,Codcli "
                    + " from cxfguiasvol "
                    + " where seqrotasai = " + sequencia
                    + " and hora1d = " + hora1.replace(":", ""));
        }
    }

    public List<CxFGuiasVol> getLacres(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {

        String sql = "select * from cxfguiasvol "
                + "where guia=? and serie=? and codfil=?";
        try {
            Consulta consultguia = new Consulta(sql, persistencia);
            consultguia.setBigDecimal(guia);
            consultguia.setString(serie);
            consultguia.setString(codFil);
            consultguia.select();

            List<CxFGuiasVol> cxf_GuiasVol = new ArrayList<>();
            CxFGuiasVol cxfGuiasVol;
            while (consultguia.Proximo()) {
                cxfGuiasVol = new CxFGuiasVol();
                cxfGuiasVol.setCodFil(consultguia.getString("CodFil"));
                cxfGuiasVol.setCodCli(consultguia.getString("CodCli"));
                cxfGuiasVol.setGuia(consultguia.getString("Guia"));
                cxfGuiasVol.setSerie(consultguia.getString("Serie"));
                cxfGuiasVol.setOrdem(consultguia.getString("Ordem"));
                cxfGuiasVol.setQtde(consultguia.getString("Qtde"));
                cxfGuiasVol.setLacre(consultguia.getString("Lacre"));
                cxfGuiasVol.setTipo(consultguia.getString("Tipo"));
                cxfGuiasVol.setValor(consultguia.getString("Valor"));
                cxfGuiasVol.setObs(consultguia.getString("Obs"));

                cxf_GuiasVol.add(cxfGuiasVol);
            }
            consultguia.Close();
            return cxf_GuiasVol;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.getLacre - " + e.getMessage() + "\r\n"
                    + "select lacre from cxfguiasvol "
                    + "where guia=" + guia + " and serie=" + serie + " and codfil=" + codFil);
        }
    }

    /**
     * Carrega informação da guia pelo lacre
     *
     * @param lacre
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public CxFGuiasVol buscarGuiaLacre(String lacre, String codFil, Persistencia persistencia) throws Exception {

        try {
            String sql = "select * from cxfguiasvol "
                    + "where lacre = ? and codfil = ?";
            Consulta consultguia = new Consulta(sql, persistencia);
            consultguia.setBigDecimal(lacre);
            consultguia.setString(codFil);
            consultguia.select();

            CxFGuiasVol cxfGuiasVol = null;
            if (consultguia.Proximo()) {
                cxfGuiasVol = new CxFGuiasVol();
                cxfGuiasVol.setCodFil(consultguia.getString("CodFil"));
                cxfGuiasVol.setCodCli(consultguia.getString("CodCli"));
                cxfGuiasVol.setGuia(consultguia.getString("Guia"));
                cxfGuiasVol.setSerie(consultguia.getString("Serie"));
                cxfGuiasVol.setOrdem(consultguia.getString("Ordem"));
                cxfGuiasVol.setQtde(consultguia.getString("Qtde"));
                cxfGuiasVol.setLacre(consultguia.getString("Lacre"));
                cxfGuiasVol.setTipo(consultguia.getString("Tipo"));
                cxfGuiasVol.setValor(consultguia.getString("Valor"));
                cxfGuiasVol.setObs(consultguia.getString("Obs"));
            }
            consultguia.Close();
            return cxfGuiasVol;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.getLacre - " + e.getMessage() + "\r\n"
                    + "select lacre from cxfguiasvol "
                    + "where lacre=" + lacre + " and codfil=" + codFil);
        }
    }

    /**
     * Apaga os volumes lançados para a guia
     *
     * @param codFil - Parada da Rota
     * @param guia - Número da Guia a verificar
     * @param serie - Série da Guia a verificar
     * @param persistencia - Conexão ao banco
     * @throws Exception
     */
    public void apagaVols(Persistencia persistencia, String guia, String serie, String codFil) throws Exception {
        String sql = "delete from cxfguiasvol where "
                + " guia=? "
                + " and serie=? "
                + " and codfil=? ";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.apagaVols - " + e.getMessage() + "\r\n"
                    + "delete from cxfguiasvol where "
                    + " guia=" + guia
                    + " and serie=" + serie
                    + " and codfil=" + codFil);
        }
    }

    public List<CxFGuiasVol> getCxFGuiasVolsRota(String sequencia, Persistencia persistencia) throws Exception {
        String sql;
        List<CxFGuiasVol> listvol = new ArrayList();
        try {
            sql = "select cxfguiasvol.guia, cxfguiasvol.serie, cxfguiasvol.ordem, cxfguiasvol.lacre"
                    + " from cxfguiasvol"
                    + " left join cxfguias on cxfguias.guia = cxfguiasvol.guia"
                    + "                   and cxfguias.serie = cxfguiasvol.serie"
                    + "                   and cxfguias.codfil = cxfguiasvol.codfil"
                    + " left join rt_perc on rt_perc.sequencia = cxfguias.seqrotasai"
                    + "                  and rt_perc.codfil = cxfguias.codfil"
                    + " where cxfguias.seqrotasai = ?"
                    + " and (rt_perc.hr_fech is null or rt_perc.hr_fech='')";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sequencia);
            consult.select();
            while (consult.Proximo()) {
                CxFGuiasVol vol = new CxFGuiasVol();
                vol.setGuia(consult.getString("guia"));
                vol.setSerie(consult.getString("serie"));
                vol.setOrdem(consult.getString("ordem"));
                vol.setLacre(consult.getString("lacre"));
                listvol.add(vol);
            }
            consult.Close();
            return listvol;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.getCxFGuiasVolsRota - " + e.getMessage() + "\r\n"
                    + "select cxfguiasvol.guia, cxfguiasvol.serie, cxfguiasvol.ordem, cxfguiasvol.lacre"
                    + " from cxfguiasvol"
                    + " left join cxfguias on cxfguias.guia = cxfguiasvol.guia"
                    + "                   and cxfguias.serie = cxfguiasvol.serie"
                    + "                   and cxfguias.codfil = cxfguiasvol.codfil"
                    + " left join rt_perc on rt_perc.sequencia = cxfguias.seqrotasai"
                    + "                  and rt_perc.codfil = cxfguias.codfil"
                    + " where cxfguias.seqrotasai = " + sequencia
                    + " and (rt_perc.hr_fech is null or rt_perc.hr_fech='')");
        }
    }

    /**
     * Troca a serie da Guia Em Todos os Volumes de Determinada Guia em Caixa
     * Forte
     *
     * @param persistencia - conexão ao banco de dados
     * @param cxfguiasvol - guia de caixa forte Dados obrigatórios - guia,
     * série, codfil
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, CxFGuiasVol cxfguiasvol, String novaserie) throws Exception {
        try {
            String sql = "update CxFGuiasVol set Serie = ?"
                    + " where Guia = ?"
                    + " and Serie = ?"
                    + " and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(cxfguiasvol.getGuia());
            consulta.setString(cxfguiasvol.getSerie());
            consulta.setString(cxfguiasvol.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.TrocaSerieGuia - " + e.getMessage() + "\r\n"
                    + "update CxFGuiasVol set Serie = " + novaserie
                    + " where Guia = " + cxfguiasvol.getGuia()
                    + " and Serie = " + cxfguiasvol.getSerie()
                    + " and CodFil = " + cxfguiasvol.getCodFil());
        }
    }

    public List<CxFGuiasVol> getLacres(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql;
        List<CxFGuiasVol> listvol = new ArrayList();
        try {
            sql = "Select Lacre, Case When Tipo = '1' then 'Cedulas'"
                    + "                   When Tipo = '2' then 'Cheques'"
                    + "                   When Tipo = '3' then 'Moedas'"
                    + "                   When Tipo = '4' then 'MetaisPreciosos'"
                    + "                   When Tipo = '5' then 'MoedaExtrangeira'"
                    + "                   When Tipo = '9' then 'Outros'"
                    + "                   else Tipo end TpMateria,"
                    + " Valor, Obs"
                    + " from CXfGuiasVol"
                    + " Where Guia = ? and Serie = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(guia);
            consult.setString(serie);
            consult.select();
            CxFGuiasVol vol;
            while (consult.Proximo()) {
                vol = new CxFGuiasVol();
                vol.setValor(consult.getString("valor"));
                vol.setTipo(consult.getString("TpMateria"));
                vol.setLacre(consult.getString("lacre"));
                vol.setObs(consult.getString("Obs"));
                listvol.add(vol);
            }
            consult.Close();
            return listvol;
        } catch (Exception e) {
            throw new Exception("CxfguiasvolDao.getLacres - " + e.getMessage() + "\r\n"
                    + "Select Lacre, Case When Tipo = 1 then 'Cedulas'"
                    + "                   When Tipo = 2 then 'Cheques'"
                    + "                   When Tipo = 3 then 'Moedas'"
                    + "                   When Tipo = 4 then 'MetaisPreciosos'"
                    + "                   When Tipo = 5 then 'MoedaExtrangeira'"
                    + "                   When Tipo = 9 then 'Outros'"
                    + "                   else '' end TpMateria,"
                    + " Valor, Obs"
                    + " from CXfGuiasVol"
                    + " Where Guia = " + guia + " and Serie = " + serie);
        }
    }

    public List<CxFGuiasVol> listarTiposComId(String codFil, String guia, String serie) throws Exception {
        String sql = "SELECT Tipo FROM CxfGuiasVol\n"
                + "WHERE CodFil = ? \n"
                + "AND Guia = ? \n"
                + "AND Serie = ? \n"
                + "ORDER BY Tipo;";

        List<CxFGuiasVol> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.select();

            while (consulta.Proximo()) {
                CxFGuiasVol guiaVol = new CxFGuiasVol();
                guiaVol.setTipo(consulta.getString("Tipo"));

                lista.add(guiaVol);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    public List<CxFGuiasVol> listarQuantidadesEValores(String codFil, String guia, String serie) throws Exception {
        String sql = "SELECT COUNT(*) Qtde, SUM(Valor) Valor FROM CxfGuiasVol\n"
                + "WHERE CodFil = ? \n"
                + "AND Guia = ? \n"
                + "AND Serie = ? ;";

        List<CxFGuiasVol> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.setBigDecimal(guia);
            consulta.setString(serie);
            consulta.select();

            while (consulta.Proximo()) {
                CxFGuiasVol guiaVol = new CxFGuiasVol();
                guiaVol.setQtde(consulta.getString("Qtde"));
                guiaVol.setValor(consulta.getString("Valor"));

                lista.add(guiaVol);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return lista;
    }

    public List<CxFGuiasVol> buscarVolumesGuia(String guia, String serie, String tipo, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Guia, Serie, Sum(Qtde) Qtde, Lacre, Tipo, \n"
                    + " Sum(Valor) ValorVol from CxfGuiasVol \n"
                    + " where Guia  = ? \n"
                    + "   and Serie = ? \n"
                    + "   and Tipo = ? \n"
                    + " Group By Guia, Serie, Lacre, Tipo";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(tipo);
            consulta.select();
            List<CxFGuiasVol> retorno = new ArrayList<>();
            CxFGuiasVol cxFGuiasVol;
            if (consulta.Proximo()) {
                cxFGuiasVol = new CxFGuiasVol();
                cxFGuiasVol.setCodFil(consulta.getString("CodFil"));
                cxFGuiasVol.setCodCli(consulta.getString("CodCli"));
                cxFGuiasVol.setGuia(consulta.getString("Guia"));
                cxFGuiasVol.setSerie(consulta.getString("Serie"));
                cxFGuiasVol.setOrdem(consulta.getString("Ordem"));
                cxFGuiasVol.setQtde(consulta.getString("Qtde"));
                cxFGuiasVol.setLacre(consulta.getString("Lacre"));
                cxFGuiasVol.setTipo(consulta.getString("Tipo"));
                cxFGuiasVol.setValor(consulta.getString("Valor"));
                cxFGuiasVol.setObs(consulta.getString("Obs"));
                retorno.add(cxFGuiasVol);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxFGuiasVolDao.buscarGuia - " + e.getMessage() + "\r\n"
                    + "elect Guia, Serie, Sum(Qtde) Qtde, Lacre, Tipo, \n"
                    + " Sum(Valor) ValorVol from CxfGuiasVol \n"
                    + " where Guia  = " + guia + " \n"
                    + "   and Serie = " + serie + " \n"
                    + "   and Tipo = " + tipo + " \n"
                    + " Group By Guia, Serie, Lacre, Tipo");
        }
    }

    public BigDecimal getTotalGeral(String guia, String serie, Persistencia persistencia) throws Exception {
        String sql = "SELECT TOP 1 Sum(Valor) TotalGeral\n"
                + "FROM CxfGuiasVol\n"
                + "WHERE Guia = ? \n"
                + "  AND Serie = ? ;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();
            if (consulta.Proximo()) {
                return consulta.getBigDecimal("TotalGeral");
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return null;
    }
}
