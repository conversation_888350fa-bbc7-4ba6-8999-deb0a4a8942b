package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ATMTabOcor;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ATMTabOcorDao {

    // the fields are: Codigo, Descricao, Tipo, Operador, Dt_Alter, HR_Alter
    // create
    public boolean gravaATMTabOcorDao(ATMTabOcor atmtabocor, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into ATMTabOcor (Codigo, Descricao, Tipo, Operador, Dt_Alter, HR_Alter) "
                + "Values (?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmtabocor.getCodigo());
            consulta.setString(atmtabocor.getDescricao());
            consulta.setInt(atmtabocor.getTipo());
            consulta.setString(atmtabocor.getOperador());
            consulta.setString(atmtabocor.getDt_Alter().toString());
            consulta.setString(atmtabocor.getHR_Alter());
            consulta.insert();
            consulta.Close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    public List<ATMTabOcor> buscaATMTabOcor(Persistencia persistencia) throws Exception {
        List<ATMTabOcor> listATMTabOcor;
        try {
            ATMTabOcor atmtabocor;
            Consulta consult = new Consulta("select Codigo, Descricao, Tipo, Operador, Dt_Alter, HR_Alter "
                    + "from atmtabocor", persistencia);
            consult.insert();
            listATMTabOcor = new ArrayList();
            while (consult.Proximo()) {
                atmtabocor = new ATMTabOcor();
                atmtabocor.setCodigo(consult.getString("Codigo"));
                atmtabocor.setDescricao(consult.getString("Descricao"));
                atmtabocor.setTipo(consult.getInt("Tipo"));
                atmtabocor.setOperador(consult.getString("Operador"));
                atmtabocor.setDt_Alter(consult.getDate("Dt_Alter").toLocalDate());
                atmtabocor.setHR_Alter(consult.getString("HR_Alter"));
                listATMTabOcor.add(atmtabocor);
            }
            consult.Close();
        } catch (Exception e) {
            listATMTabOcor = null;
            throw new Exception("ATMTabOcorDao.buscaATMTabOcor - " + e.getMessage() + "\r\n"
                    + "select Codigo, Descricao, Tipo, Operador, Dt_Alter, HR_Alter "
                    + "from atmtabocor");
        }
        return listATMTabOcor;
    }

    // update
    public void atualizaATMTabOcor(ATMTabOcor atmtabocor, Persistencia persistencia) throws Exception {
        String sql = "update ATMTabOcor set Codigo = ?, Descricao = ?, Tipo = ?, Operador = ?, Dt_Alter = ?, HR_Alter = ? "
                + "where Codigo = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmtabocor.getCodigo());
            consulta.setString(atmtabocor.getDescricao());
            consulta.setInt(atmtabocor.getTipo());
            consulta.setString(atmtabocor.getOperador());
            consulta.setString(atmtabocor.getDt_Alter().toString());
            consulta.setString(atmtabocor.getHR_Alter());
            consulta.setBigDecimal(atmtabocor.getCodigo());
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMTabOcorDao.atualizaATMTabOcor - " + e.getMessage() + "\r\n"
                    + "update ATMTabOcor set Codigo = " + atmtabocor.getCodigo() + ", Descricao = " + atmtabocor.getDescricao() + ", "
                    + "Tipo = " + atmtabocor.getTipo() + ", Operador = " + atmtabocor.getOperador() + ", Dt_Alter = " + atmtabocor.getDt_Alter().toString() + ", "
                    + "HR_Alter = " + atmtabocor.getHR_Alter()
                    + "where Codigo = " + atmtabocor.getCodigo());
        }
    }

    // delete
    public void excluirATMTabOcor(ATMTabOcor atmtabocor, Persistencia persistencia) throws Exception {
        String sql = "delete from ATMTabOcor where Codigo=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(atmtabocor.getCodigo());
            consulta.delete();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMTabOcorDao.excluirATMTabOcor " + e.getMessage() + "\r\n"
                    + "delete from ATMTabOcor where Codigo=" + atmtabocor.getCodigo());
        }
    }
}
