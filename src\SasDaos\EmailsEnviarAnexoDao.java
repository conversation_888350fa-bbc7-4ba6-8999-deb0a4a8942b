package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.EmailsEnviarAnexo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EmailsEnviarAnexoDao {

    /**
     * Busca lista de anexos do email
     *
     * @param Sequencia - sequencia do email
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<EmailsEnviarAnexo> buscaAnexos(BigDecimal Sequencia, Persistencia persistencia) throws Exception {
        try {
            List<EmailsEnviarAnexo> retorno = new ArrayList();
            String sql = "select * from emailsenviaranexo where sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(Sequencia);
            consult.select();
            EmailsEnviarAnexo emailanexo;
            while (consult.Proximo()) {
                emailanexo = new EmailsEnviarAnexo();
                emailanexo.setSequencia(consult.getBigDecimal("sequencia"));
                emailanexo.setOrdem(consult.getInt("ordem"));
                emailanexo.setEndAnexo(consult.getString("endanexo"));
                emailanexo.setNomeAnexo(consult.getString("nomeanexo"));
                emailanexo.setDescAnexo(consult.getString("descanexo"));
                retorno.add(emailanexo);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EmailsEnviarAnexoDao.buscaAnexos - " + e.getMessage() + "\r\n"
                    + "select * from emailsenviaranexo where sequencia = " + Sequencia);
        }
    }

    /**
     * Apaga os email a serem anexados
     *
     * @param Sequencia - Sequencia do email a enviar, dos quais se deseja
     * excluir os anexos
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void excluirAnexos(BigDecimal Sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "delete from emailsenviaranexo where sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(Sequencia);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarAnexoDao.excluirAnexos - " + e.getMessage() + "\r\n"
                    + "delete from emailsenviaranexo where sequencia = " + Sequencia);
        }
    }

    /**
     * Insere os email a serem anexados
     *
     * @param emailanexo - Sequencia do email a enviar, dos quais se deseja
     * excluir os anexos
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void inserirAnexos(EmailsEnviarAnexo emailanexo, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into emailsenviaranexo (sequencia,ordem,endanexo,nomeanexo,descanexo)"
                    + " values (?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(emailanexo.getSequencia());
            consulta.setInt(emailanexo.getOrdem());
            consulta.setString(emailanexo.getEndAnexo());
            consulta.setString(emailanexo.getNomeAnexo());
            consulta.setString(emailanexo.getDescAnexo());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarAnexoDao.inserirAnexos - " + e.getMessage() + "\r\n"
                    + "insert into emailsenviaranexo (sequencia,ordem,endanexo,nomeanexo,descanexo)"
                    + " values (" + emailanexo.getSequencia() + "," + emailanexo.getOrdem() + "," + emailanexo.getEndAnexo() + ","
                    + emailanexo.getNomeAnexo() + "," + emailanexo.getDescAnexo() + ")");
        }
    }
}
