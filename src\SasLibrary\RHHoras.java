/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Psthstqst;
import SasBeansCompostas.CarregaRHHoras;
import SasDaos.PstServDao;
import SasDaos.Rh_HorasDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RHHoras {

    public static String CarregaRHHora(String turno, String secao, String codfil, String dataAtual, Persistencia persistencia) throws Exception {
        String ret = null;
        try {
            //carrega a rota
            List<CarregaRHHoras> list_CarregaRHHoras;
            List<Psthstqst> list_TmktDetPst;

            Rh_HorasDao oRh_Hora = new Rh_HorasDao();
            try {

                list_CarregaRHHoras = oRh_Hora.carregaRHHoras(turno, secao, codfil, dataAtual, persistencia);

            } catch (Exception e) {
                throw new Exception("Falha ao carregar funcionários para supervisão - " + e.getMessage());
            }
            if (list_CarregaRHHoras.isEmpty()) {
                //throw new Exception("Sem funcionários para supervisão");
                ret = "<?xml version=\"1.0\"?><resp>FuncionarioSupervisao_2</resp>";//sem funcionario para supervisao
                return ret;
            }
            String xml = "<?xml version=\"1.0\"?><nreg>" + list_CarregaRHHoras.size() + "</nreg>";
            for (CarregaRHHoras list_CarregaRHHoras1 : list_CarregaRHHoras) {

                xml += "<rhhoras>";
                xml += "<matr>" + list_CarregaRHHoras1.getrH_Horas().getMatr() + "</matr>";
                xml += "<nome>" + list_CarregaRHHoras1.getFuncion().getNome() + "</nome>";
                xml += "<cargodesc>" + list_CarregaRHHoras1.getCargos().getDescricao() + "</cargodesc>";
                xml += "<data>" + list_CarregaRHHoras1.getrH_Horas().getData() + "</data>";
                xml += "<hsdiurnas>" + list_CarregaRHHoras1.getrH_Horas().getHsDiurnas() + "</hsdiurnas>";
                xml += "<hsnoturnas>" + list_CarregaRHHoras1.getrH_Horas().getHsNoturnas() + "</hsnoturnas>";
                xml += "<situacao>" + list_CarregaRHHoras1.getrH_Horas().getSituacao() + "</situacao>";
                xml += "<hora>" + list_CarregaRHHoras1.getrHPonto().getHora() + "</hora>";
                xml += "<latfunc>" + list_CarregaRHHoras1.getRastrear().getLatitude() + "</latfunc>";
                xml += "<longfunc>" + list_CarregaRHHoras1.getRastrear().getLongitude() + "</longfunc>";
                xml += "<latcli>" + list_CarregaRHHoras1.getClientes().getLatitude() + "</latcli>";
                xml += "<longcli>" + list_CarregaRHHoras1.getClientes().getLongitude() + "</longcli>";
                xml += "<nred>" + list_CarregaRHHoras1.getClientes().getNRed() + "</nred>";
                xml += "<codcli>" + list_CarregaRHHoras1.getPstServ().getCodCli() + "</codcli>";
                xml += "<precisao>" + list_CarregaRHHoras1.getRastrear().getPrecisao() + "</precisao>";
                xml += "<bhediu>" + list_CarregaRHHoras1.getCtrOperV().getHEDiurna() + "</bhediu>";
                xml += "<bhenot>" + list_CarregaRHHoras1.getCtrOperV().getHENoturna() + "</bhenot>";
                xml += "<sitfunc>" + list_CarregaRHHoras1.getFuncion().getSituacao() + "</sitfunc>";

                PstServDao oPstServ = new PstServDao();
                boolean cond = false;
                list_TmktDetPst = oPstServ.getQuestionario(secao, dataAtual, persistencia);
                for (Psthstqst listQ : list_TmktDetPst) {
                    //compara registro para saber se ja foi efetuado questionário
                    String rhmatr = list_CarregaRHHoras1.getrH_Horas().getMatr().toString();
                    String questmatr = listQ.getMatr().toString();
                    if (rhmatr.equals(questmatr)) {
                        cond = true;
                    }

                }
                if (cond) {
                    xml += "<quest>1</quest>";
                } else {
                    xml += "<quest>0</quest>";
                }
                cond = false;

                xml += " </rhhoras>";
            }
            return xml = xml.replaceAll("&", "&amp;");
        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao funcionários para supervisão - " + e.getMessage());
        }
    }
}
