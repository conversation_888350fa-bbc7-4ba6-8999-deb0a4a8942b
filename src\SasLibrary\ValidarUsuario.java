package SasLibrary;

import Dados.OLD.Persistencia_OLD;
import Dados.OLD.SasPoolPersistencia_OLD;
import Dados.Persistencia;
import SasBeans.Saspw;
import SasBeansCompostas.LoginRota;
import SasDaos.LoginDao;
import SasDaos.SaspwDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ValidarUsuario {

    /**
     * Valida usuario e senha
     *
     * @param codigo
     * @param senha - senha gravada em pwweb
     * @param persistencia - conexão com banco de dados
     * @return - true para usuário válido e false para falha de login ou senha
     * @throws Exception
     */
    public static boolean ValidaUsuarioPonto(String codigo, String senha, Persistencia persistencia) throws Exception {
        List<String> list_lrota;
        try {
            list_lrota = LoginDao.LoginRotaPonto(codigo, persistencia);
            if (list_lrota.stream().anyMatch((list_lrota1) -> (list_lrota1.equals(senha)))) {
                return true;
            } else {
                list_lrota = LoginDao.LoginRotaPontoPwPortal(codigo, persistencia);
                if (list_lrota.stream().anyMatch((list_lrota1) -> (list_lrota1.equals(senha)))) {
                    return true;
                }
            }

        } catch (Exception e) {
            throw new Exception("Falha ao validar usuário - " + e.getMessage() + " - " + persistencia.toString());
        }
        return false;
    }

    /**
     * Valida usuario e senha
     *
     * @param CodPessoa - codigo de pessoa
     * @param senha - senha gravada em pwweb
     * @param persistencia - conexão com banco de dados
     * @return - true para usuário válido e false para falha de login ou senha
     * @throws Exception
     */
    public static boolean ValidaUsuario(String CodPessoa, String senha, Persistencia persistencia) throws Exception {
        List<LoginRota> list_lrota;
        try {
            //por determinação do Marcos 19/01/2016 deve-se ignorar os zeros a esquerda na senha tratamento abaixo
            int se;
            try {
                se = Integer.parseInt(senha);
            } catch (Exception e) {
                se = 0;
            }
            String Senha = String.valueOf(se);
            list_lrota = LoginDao.LoginRota(CodPessoa, Senha, persistencia);
            for (LoginRota list_lrota1 : list_lrota) {
                if (list_lrota1.getPessoa().getPWWeb().equals(Senha) || list_lrota1.getPessoa().getPWWeb().equals(senha)) {
                    return true;
                }
            }

        } catch (Exception e) {
            throw new Exception("Falha ao validar usuário - " + e.getMessage() + " - " + persistencia.toString());
        }
        return false;
    }

    /**
     * Valida usuario e senha
     *
     * @param CodPessoa - codigo de pessoa
     * @param senha - senha gravada em pwweb
     * @param persistencia - conexão com banco de dados
     * @return - true para usuário válido e false para falha de login ou senha
     * @throws Exception
     */
    public static boolean ValidaUsuarioEW(String CodPessoa, String nome, String senha, Persistencia persistencia) throws Exception {
        List<LoginRota> list_lrota;
        try {
            //por determinação do Marcos 19/01/2016 deve-se ignorar os zeros a esquerda na senha tratamento abaixo
            int se;
            try {
                se = Integer.parseInt(senha);
            } catch (Exception e) {
                se = 0;
            }
            String Senha = String.valueOf(se);
            list_lrota = LoginDao.LoginRota(CodPessoa, nome, Senha, persistencia);
            for (LoginRota list_lrota1 : list_lrota) {
                if (list_lrota1.getPessoa().getPWWeb().equals(Senha) || list_lrota1.getPessoa().getPWWeb().equals(senha)) {
                    return true;
                }
            }

        } catch (Exception e) {
            throw new Exception("Falha ao validar usuário - " + e.getMessage() + " - " + persistencia.toString());
        }
        return false;
    }

    /**
     * Valida usuário pelo código de pessoa com a senha criptografada
     *
     * @param CodPessoa - código de pessoa do usuário
     * @param senha - senha criptografada
     * @param persistencia - conexão ao bando de dados
     * @return
     * @throws Exception
     */
    public static boolean ValidaUsuarioSatellite(String CodPessoa, String senha, Persistencia persistencia) throws Exception {
        try {
            Saspw saspw;
            SaspwDao saspwdao = new SaspwDao();
            saspw = saspwdao.BuscaPW(CodPessoa, persistencia);
            if (senha.equals(saspw.getPW())) {
                return true;
            }
        } catch (Exception e) {
            throw new Exception("Falha ao validar usuário - " + e.getMessage());
        }
        return false;
    }

    /**
     * Valida chave passada criptografada
     *
     * @param chave - passa chave criptografada
     * @return - true para chave correta e false para chave incorreta
     * @throws Exception
     */
    public static boolean ValidarChave(String chave) throws Exception {
        try {
            final String senha = "Sasw, hoje quero ficar com meus parametros atualizados";
            chave = br.com.sasw.pacotesuteis.utilidades.SatCripto.Criptografar(chave, "Active Solutions SAS Systems");
            return senha.equals(chave);
        } catch (Exception e) {
            throw new Exception("Falha ao validar chave - " + e.getMessage());
        }
    }

    /**
     * Devolve a conexão de verificação de usuário e senha
     *
     * @param param - parametro de conexão do usuário atual
     * @param pool - pool de conexões
     * @return - conexão com o banco padrão de controle de usuário e senha
     * @throws Exception
     */
    public static Persistencia_OLD getConexaoPadraoUsuario(String param, SasPoolPersistencia_OLD pool) throws Exception {
        try {
            /*Persistencia retorno = null;
            ParametDao parametdao = new ParametDao();
            String param_usuario="";
            List<Paramet> lparamet = parametdao.getParamPadrao(param, pool.getConexao("SATELLITE"));
            if(lparamet.isEmpty()){
                return null;
            }
            else{
                int i=0;
                for(i=0;i<lparamet.size();i++){
                    if(param.equals(lparamet.get(i).getPath().toUpperCase())){
                        if(lparamet.get(i).getSequencia().compareTo(lparamet.get(i).getSeqParamPdr())==0){
                            param_usuario = lparamet.get(i).getPath().toUpperCase();
                        }
                        else{
                            param_usuario = parametdao.getParam(lparamet.get(i).getSequencia().toPlainString(), pool.getConexao("SATELLITE")).getPath().toUpperCase(); 
                        }
                        i = lparamet.size() + 1;
                    }
                }
            }
            //caso não tenha um param padão, devolve o param
            if("".equals(param_usuario)){
                param_usuario = param;
            }
            retorno = pool.getConexao(param_usuario);
            return retorno;*/
            return pool.getConexao(param);
        } catch (Exception e) {
            throw new Exception("Falha ao buscar banco padrão de controle de usuários - \r\n" + e.getMessage());
        }
    }
}
