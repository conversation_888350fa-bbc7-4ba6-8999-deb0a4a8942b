package SasLibrary;

import Dados.Persistencia;
import SasBeans.TipoSrvCli;
import SasDaos.TipoServCliDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TipoSrvClis {

    public static String CarregaTipoSrvCli(Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<TipoSrvCli> list_TipoSrvCli;
            TipoServCliDao oTipoSrvCli = new TipoServCliDao();
            try {
                list_TipoSrvCli = oTipoSrvCli.getTipoSrvCli(persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar tipos de serviços - " + e.getMessage());
            }
            if (list_TipoSrvCli.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>TipoSrvCli_2</resp>";//sem postos de serviços
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_TipoSrvCli.size() + "</nreg>";
            for (int i = 0; i < list_TipoSrvCli.size(); i++) {
                xml += "<TipoSrvCli>";
                xml += "<Codigo>" + list_TipoSrvCli.get(i).getCodigo() + "</Codigo>";
                xml += "<Descricao>" + list_TipoSrvCli.get(i).getDescricao() + "</Descricao>";
                xml += "<Banco>" + list_TipoSrvCli.get(i).getBanco() + "</Banco>";
                xml += "<TAtend>" + list_TipoSrvCli.get(i).getTAtend() + "</TAtend>";
                xml += "<TCob>" + list_TipoSrvCli.get(i).getTCob() + "</TCob>";
                xml += "<TCar>" + list_TipoSrvCli.get(i).getTCar() + "</TCar>";
                xml += "<TipoSrv>" + list_TipoSrvCli.get(i).getTipoSrv() + "</TipoSrv>";
                xml += "<ER>" + list_TipoSrvCli.get(i).getER() + "</ER>";
                xml += "<CodInterf>" + list_TipoSrvCli.get(i).getCodInterf() + "</CodInterf>";
                xml += "<Aditivo>" + list_TipoSrvCli.get(i).getAditivo() + "</Aditivo>";
                xml += "<Exportar>" + list_TipoSrvCli.get(i).getExportar() + "</Exportar>";
                xml += "<SubCentro>" + list_TipoSrvCli.get(i).getSubCentro() + "</SubCentro>";
                xml += "<Operador>" + list_TipoSrvCli.get(i).getOperador() + "</Operador>";
                xml += "<Dt_Alter>" + list_TipoSrvCli.get(i).getDt_Alter() + "</Dt_Alter>";
                xml += "<Hr_Alter>" + list_TipoSrvCli.get(i).getHr_Alter() + "</Hr_Alter>";
                xml += "</TipoSrvCli>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
