package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Filiais;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FiliaisDao {

    public List<String> infoEnvioESocial(String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc "
                    + " from Filiais "
                    + " where Filiais.CodFil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.select();
            List<String> retorno = new ArrayList<>();
            while (consult.Proximo()) {
                retorno.add(consult.getString("ideEmpregador_tpInsc"));
                retorno.add(consult.getString("ideEmpregador_nrInsc"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FiliaisDao.infoEnvioESocial - " + e.getMessage() + "\r\n"
                    + " Select Filiais.TipoPessoa ideEmpregador_tpInsc, Filiais.CNPJ ideEmpregador_nrInsc "
                    + " from Filiais "
                    + " where Filiais.CodFil = " + codFil);
        }
    }

    /**
     * Busca dados da Filial Descricao, Endereco, Bairro, Cidade, UF, Fone, CNPJ
     *
     * @param CodFil - Código da filial desejada
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Filiais getFilial(String CodFil, Persistencia persistencia) throws Exception {
        Filiais retorno = new Filiais();
        try {
            String sql = "Select contato, email, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, Fone, CNPJ, CEP,"
                    + " fone from filiais where codfil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.select();
            while (consult.Proximo()) {
                retorno.setContato(consult.getString("contato"));
                retorno.setEmail(consult.getString("email"));
                retorno.setDescricao(consult.getString("Descricao"));
                retorno.setEndereco(consult.getString("Endereco"));
                retorno.setRazaoSocial(consult.getString("RazaoSocial"));
                retorno.setBairro(consult.getString("Bairro"));
                retorno.setCidade(consult.getString("Cidade"));
                retorno.setUF(consult.getString("UF"));
                retorno.setFone(consult.getString("Fone"));
                retorno.setCNPJ(consult.getString("CNPJ"));
                retorno.setCEP(consult.getString("CEP"));
                retorno.setFone(consult.getString("fone"));
                retorno.setCodFil(CodFil);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados da filial - " + e.getMessage());
        }
        return retorno;
    }

    public List<Filiais> getFiliaisLupa(String sCriterio, Persistencia persistencia) throws Exception {
        List<Filiais> listFiliais;
        String sql;
        try {
            if ("".equals(sCriterio) || sCriterio == null) {
                sql = "SELECT top 50 CodFil, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, CEP, Fone, Fone2, "
                        + "Fax, Contato, Email, EmailCml, CNPJ, InscEst, InscMunic, ISS, Praca, PorteEmp, CAGED1Decl "
                        + ", CAGEDAltDados,   CodGPS, CodRecolh, FPAS, RAT, FAP, Operador, Dt_Alter, Hr_Alter FROM Filiais";
            } else {
                sql = "SELECT CodFil, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, CEP, Fone, Fone2, "
                        + "Fax, Contato, Email, EmailCml, CNPJ, InscEst, InscMunic, ISS, Praca, PorteEmp, CAGED1Decl "
                        + ", CAGEDAltDados,   CodGPS, CodRecolh, FPAS, RAT, FAP, Operador, Dt_Alter, Hr_Alter "
                        + "FROM Filiais where " + sCriterio;
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            listFiliais = new ArrayList();
            while (consult.Proximo()) {
                Filiais filial = new Filiais();
                filial.setCodFil(consult.getString("CodFil").replace(".0", ""));
                filial.setDescricao(consult.getString("Descricao"));
                filial.setRazaoSocial(consult.getString("RazaoSocial"));
                filial.setEndereco(consult.getString("Endereco"));
                filial.setBairro(consult.getString("Bairro"));
                filial.setCidade(consult.getString("Cidade"));
                filial.setUF(consult.getString("UF"));
                filial.setCEP(consult.getString("CEP"));
                filial.setFone(consult.getString("Fone"));
                filial.setFone2(consult.getString("Fone2"));
                filial.setFax(consult.getString("Fax"));
                filial.setContato(consult.getString("Contato"));
                filial.setEmail(consult.getString("Email"));
                filial.setEmailCml(consult.getString("EmailCml"));
                filial.setCNPJ(consult.getString("CNPJ"));
                filial.setInscEst(consult.getString("InscEst"));
                filial.setInscMunic(consult.getString("InscMunic"));
                filial.setISS(consult.getString("ISS"));
                filial.setPraca(consult.getInt("Praca"));
                filial.setCAGED1Decl(consult.getString("CAGED1Decl"));
                filial.setCAGEDAltDados(consult.getString("CAGEDAltDados"));
                filial.setCodGPS(consult.getString("CodGPS"));
                filial.setCodRecolh(consult.getString("CodRecolh"));
                filial.setFPAS(consult.getString("FPAS"));
                filial.setRAT(consult.getString("RAT"));
                filial.setFAP(consult.getString("FAP"));
                filial.setOperador(consult.getString("Operador"));
                filial.setDt_Alter(consult.getString("Dt_Alter"));
                filial.setHr_Alter(consult.getString("Hr_Alter"));

                listFiliais.add(filial);
            }
            consult.Close();
        } catch (Exception e) {
            listFiliais = null;
            throw new Exception("Falha ao buscar filiais- " + e.getMessage());
        }
        return listFiliais;
    }

    public List<Filiais> BuscaFiliais(Persistencia persistencia) throws Exception {
        List<Filiais> listFiliais;
        String sql = "SELECT TOP 1 CodFil, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, CEP, Fone, Fone2, "
                + " Fax, Contato, Email, TipoPessoa, CNPJ, InscEst, InscMunic, ISS, Praca, ICMS, Praca2, "
                + " CodFilFp, CNAE, CAGED1Decl, CAGEDAltDados, PorteEmp, CodRecolh, CodGPS, FPAS, CodOutrasEnt, "
                + " RAT, FAP, Operador, Dt_Alter, Hr_Alter FROM Filiais";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            listFiliais = new ArrayList();
            while (consult.Proximo()) {
                Filiais filial = new Filiais();
                filial.setCodFil(consult.getString("CodFil"));
                filial.setDescricao(consult.getString("Descricao"));
                filial.setRazaoSocial(consult.getString("RazaoSocial"));
                filial.setEndereco(consult.getString("Endereco"));
                filial.setBairro(consult.getString("Bairro"));
                filial.setCidade(consult.getString("Cidade"));
                filial.setUF(consult.getString("UF"));
                filial.setCEP(consult.getString("CEP"));
                filial.setFone(consult.getString("Fone"));
                filial.setFone2(consult.getString("Fone2"));
                filial.setFax(consult.getString("Fax"));
                filial.setContato(consult.getString("Contato"));
                filial.setEmail(consult.getString("Email"));
                filial.setTipoPessoa(consult.getString("TipoPessoa"));
                filial.setCNPJ(consult.getString("CNPJ"));
                filial.setInscEst(consult.getString("InscEst"));
                filial.setInscMunic(consult.getString("InscMunic"));
                filial.setISS(consult.getString("ISS"));
                filial.setPraca(consult.getInt("Praca"));
                filial.setICMS(consult.getString("ICMS"));
                filial.setPraca2(consult.getInt("Praca2"));
                filial.setPraca2(consult.getInt("CodFilFp"));
                filial.setPraca2(consult.getInt("CNAE"));
                filial.setPraca2(consult.getInt("CAGED1Decl"));
                filial.setPraca2(consult.getInt("CAGEDAltDados"));
                filial.setPraca2(consult.getInt("PorteEmp"));
                filial.setPraca2(consult.getInt("CodRecolh"));
                filial.setPraca2(consult.getInt("CodGPS"));
                filial.setPraca2(consult.getInt("FPAS"));
                filial.setPraca2(consult.getInt("CodOutrasEnt"));
                filial.setPraca2(consult.getInt("RAT"));
                filial.setPraca2(consult.getInt("FAP"));
                filial.setPraca2(consult.getInt("Operador"));
                filial.setPraca2(consult.getInt("Dt_Alter"));
                filial.setPraca2(consult.getInt("Hr_Alter"));

                listFiliais.add(filial);
            }
            consult.Close();
        } catch (Exception e) {
            listFiliais = null;
            throw new Exception("Falha ao buscar filiais- " + e.getMessage());
        }
        return listFiliais;
    }

    /**
     * Busca dados da Filial Razão Social e CNPJ
     *
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão com o Banco de Dados
     * @return
     * @throws Exception
     */
    public List<Filiais> BuscaFilial(String CodFil, Persistencia persistencia) throws Exception {
        List<Filiais> listFiliais;
        String sql = "SELECT CodFil, RazaoSocial, CNPJ"
                + " FROM Filiais "
                + " WHERE CodFil=?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.select();
            listFiliais = new ArrayList();
            while (consult.Proximo()) {
                Filiais filial = new Filiais();
                filial.setCodFil(consult.getString("CodFil"));
                filial.setRazaoSocial(consult.getString("RazaoSocial"));
                filial.setCNPJ(consult.getString("CNPJ"));
                listFiliais.add(filial);
            }
            consult.Close();
            return listFiliais;
        } catch (Exception e) {
            throw new Exception("Falha ao buscar dados da filial - " + e.getMessage());
        }
    }

    /**
     * Obter informações sobre as filiais
     *
     * @param codPessoa codigo pessoa
     * @param persistencia conexão com a base de dados
     * @return lista contendo registros das filiais
     * @throws Exception
     */
    public List<Filiais> obterFiliais(String codPessoa, Persistencia persistencia) throws Exception {
        List<Filiais> filiais = new ArrayList<>();
        try {
//            String sql = "SELECT pessoa.Codigo, filiais.CodFil codfil, Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+ "
//                    + " Convert(Varchar,Filiais.Codfil)+' - '+Paramet.Nome_Empr descricao "
//                    + " FROM saspwfil "
//                    + " LEFT JOIN filiais ON filiais.CodFil = SASPWFil.CodfilAc "
//                    + " LEFT JOIN SASPW on SASPW.Nome = SASPWFil.Nome "
//                    + " LEFT JOIN pessoa on pessoa.Codigo = SASPW.CodPessoa "
//                    + " LEFT JOIN Paramet on Paramet.Filial_PDR = filiais.CodFil "
//                    + " WHERE pessoa.Codigo = ?";

            String sql = " select filiais.CodFil codfil, Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+ "
                    + "     Convert(Varchar,Filiais.Codfil)+' - '+Paramet.Nome_Empr descricao  "
                    + " from saspw "
                    + " left join saspwfil on saspwfil.nome = saspw.nome "
                    + " left join filiais on filiais.codfil = saspwfil.codfilac "
                    + " left join paramet on paramet.filial_pdr = filiais.codfil "
                    + " where saspw.codpessoa = ? and paramet.path = ? "
                    + " order by codfil ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            consulta.select();

            Filiais filial = null;
            while (consulta.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consulta.getString("codfil"));
                filial.setDescricao(consulta.getString("descricao"));
                filiais.add(filial);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ocorreu um erro: " + e.getMessage());
        }
        return filiais;
    }

    /**
     * Busca as informações da filial
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Filiais buscarFilial(String codFil, Persistencia persistencia) throws Exception {
        try {
            Filiais filial = null;
            String sql = "SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     Filiais \n"
                    + " WHERE \n"
                    + "     CodFil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.select();
            if (consult.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consult.getString("CodFil"));
                filial.setDescricao(consult.getString("Descricao"));
                filial.setRazaoSocial(consult.getString("RazaoSocial"));
                filial.setEndereco(consult.getString("Endereco"));
                filial.setBairro(consult.getString("Bairro"));
                filial.setCidade(consult.getString("Cidade"));
                filial.setUF(consult.getString("UF"));
                filial.setCEP(consult.getString("CEP"));
                filial.setFone(consult.getString("Fone"));
                filial.setFone2(consult.getString("Fone2"));
                filial.setFax(consult.getString("Fax"));
                filial.setContato(consult.getString("Contato"));
                filial.setEmail(consult.getString("Email"));
                filial.setTipoPessoa(consult.getString("TipoPessoa"));
                filial.setCNPJ(consult.getString("CNPJ"));
                filial.setInscEst(consult.getString("InscEst"));
                filial.setInscMunic(consult.getString("InscMunic"));
                filial.setISS(consult.getString("ISS"));
                filial.setPraca(consult.getInt("Praca"));
                filial.setICMS(consult.getString("ICMS"));
                filial.setPraca2(consult.getInt("Praca2"));
                filial.setCodFilFp(consult.getString("CodFilFp"));
                filial.setCNAE(consult.getString("CNAE"));
                filial.setCAGED1Decl(consult.getString("CAGED1Decl"));
                filial.setCAGEDAltDados(consult.getString("CAGEDAltDados"));
                filial.setPorteEmp(consult.getString("PorteEmp"));
                filial.setCodRecolh(consult.getString("CodRecolh"));
                filial.setCodGPS(consult.getString("CodGPS"));
                filial.setFPAS(consult.getString("FPAS"));
                filial.setCodOutrasEnt(consult.getString("CodOutrasEnt"));
                filial.setRAT(consult.getString("RAT"));
                filial.setFAP(consult.getString("FAP"));
                filial.setOperador(consult.getString("Operador"));
                filial.setDt_Alter(consult.getString("Dt_Alter"));
                filial.setHr_Alter(consult.getString("Hr_Alter"));

            }
            consult.Close();
            return filial;
        } catch (Exception e) {
            throw new Exception("FiliaisDao.buscarFilial - " + e.getMessage() + "\r\n"
                    + "SELECT \n"
                    + "     * \n"
                    + " FROM \n"
                    + "     Filiais \n"
                    + " WHERE \n"
                    + "     CodFil = " + codFil);
        }
    }

    /**
     * Busca CNPJ de uma filial pelo código
     *
     * @param CodFil - Código da filial
     * @param persistencia - conexão ao banco
     * @return - objeto Filiais com CodFil e CNPJ
     * @throws Exception - pode gerar exception
     */
    public List<Filiais> BuscaCNPJ(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        List<Filiais> lfiliais = new ArrayList();
        String sql = "Select CNPJ from Filiais where CodFil = ?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                Filiais fl = new Filiais();
                fl.setCNPJ(consult.getString("CNPJ"));
                fl.setCodFil(CodFil.toString());
                lfiliais.add(fl);
            }
            consult.Close();
            return lfiliais;
        } catch (Exception e) {
            throw new Exception("Falha ao busca CNPJ da Filial - " + e.getMessage());
        }
    }

    public List<Filiais> BuscaFilialPorCNPJ(String CNPJ, Persistencia persistencia) throws Exception {
        List<Filiais> listFiliais;
        String sql = "SELECT TOP 1 CodFil, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, CEP, Fone, Fone2, "
                + " Fax, Contato, Email, TipoPessoa, CNPJ, InscEst, InscMunic, ISS, Praca, ICMS, Praca2, "
                + " CodFilFp, CNAE, CAGED1Decl, CAGEDAltDados, PorteEmp, CodRecolh, CodGPS, FPAS, CodOutrasEnt, "
                + " RAT, FAP, Operador, Dt_Alter, Hr_Alter FROM Filiais WHERE CNPJ=?";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CNPJ);
            consult.select();
            listFiliais = new ArrayList();
            while (consult.Proximo()) {
                Filiais filial = new Filiais();
                filial.setCodFil(consult.getString("CodFil"));
                filial.setDescricao(consult.getString("Descricao"));
                filial.setRazaoSocial(consult.getString("RazaoSocial"));
                filial.setEndereco(consult.getString("Endereco"));
                filial.setBairro(consult.getString("Bairro"));
                filial.setCidade(consult.getString("Cidade"));
                filial.setUF(consult.getString("UF"));
                filial.setCEP(consult.getString("CEP"));
                filial.setFone(consult.getString("Fone"));
                filial.setFone2(consult.getString("Fone2"));
                filial.setFax(consult.getString("Fax"));
                filial.setContato(consult.getString("Contato"));
                filial.setEmail(consult.getString("Email"));
                filial.setTipoPessoa(consult.getString("TipoPessoa"));
                filial.setCNPJ(consult.getString("CNPJ"));
                filial.setInscEst(consult.getString("InscEst"));
                filial.setInscMunic(consult.getString("InscMunic"));
                filial.setISS(consult.getString("ISS"));
                filial.setPraca(consult.getInt("Praca"));
                filial.setICMS(consult.getString("ICMS"));
                filial.setPraca2(consult.getInt("Praca2"));
                filial.setPraca2(consult.getInt("CodFilFp"));
                filial.setPraca2(consult.getInt("CNAE"));
                filial.setPraca2(consult.getInt("CAGED1Decl"));
                filial.setPraca2(consult.getInt("CAGEDAltDados"));
                filial.setPraca2(consult.getInt("PorteEmp"));
                filial.setPraca2(consult.getInt("CodRecolh"));
                filial.setPraca2(consult.getInt("CodGPS"));
                filial.setPraca2(consult.getInt("FPAS"));
                filial.setPraca2(consult.getInt("CodOutrasEnt"));
                filial.setPraca2(consult.getInt("RAT"));
                filial.setPraca2(consult.getInt("FAP"));
                filial.setPraca2(consult.getInt("Operador"));
                filial.setPraca2(consult.getInt("Dt_Alter"));
                filial.setPraca2(consult.getInt("Hr_Alter"));

                listFiliais.add(filial);
            }
            consult.Close();
        } catch (Exception e) {
            listFiliais = null;
            throw new Exception("Falha ao buscar filiais- " + e.getMessage());
        }
        return listFiliais;
    }

    public Filiais buscaFilialPorCNPJ(String CNPJ, Persistencia persistencia) throws Exception {
        String sql = "SELECT * FROM Filiais WHERE CNPJ = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CNPJ);
            consulta.select();
            Filiais filial = new Filiais();
            if (consulta.Proximo()) {
                filial.setCodFil(consulta.getString("CodFil"));
                filial.setDescricao(consulta.getString("Descricao"));
                filial.setRazaoSocial(consulta.getString("RazaoSocial"));
                filial.setEndereco(consulta.getString("Endereco"));
                filial.setBairro(consulta.getString("Bairro"));
                filial.setCidade(consulta.getString("Cidade"));
                filial.setUF(consulta.getString("UF"));
                filial.setCEP(consulta.getString("CEP"));
                filial.setFone(consulta.getString("Fone"));
                filial.setFone2(consulta.getString("Fone2"));
                filial.setFax(consulta.getString("Fax"));
                filial.setContato(consulta.getString("Contato"));
                filial.setEmail(consulta.getString("Email"));
                filial.setTipoPessoa(consulta.getString("TipoPessoa"));
                filial.setCNPJ(consulta.getString("CNPJ"));
                filial.setInscEst(consulta.getString("InscEst"));
                filial.setInscMunic(consulta.getString("InscMunic"));
                filial.setISS(consulta.getString("ISS"));
                filial.setPraca(consulta.getInt("Praca"));
                filial.setICMS(consulta.getString("ICMS"));
                filial.setPraca2(consulta.getInt("Praca2"));
                filial.setCodFilFp(consulta.getString("CodFilFp"));
                filial.setCNAE(consulta.getString("CNAE"));
                filial.setCAGED1Decl(consulta.getString("CAGED1Decl"));
                filial.setCAGEDAltDados(consulta.getString("CAGEDAltDados"));
                filial.setPorteEmp(consulta.getString("PorteEmp"));
                filial.setCodRecolh(consulta.getString("CodRecolh"));
                filial.setCodGPS(consulta.getString("CodGPS"));
                filial.setFPAS(consulta.getString("FPAS"));
                filial.setCodOutrasEnt(consulta.getString("CodOutrasEnt"));
                filial.setRAT(consulta.getString("RAT"));
                filial.setFAP(consulta.getString("FAP"));
                filial.setOperador(consulta.getString("Operador"));
                filial.setDt_Alter(consulta.getString("Dt_Alter"));
                filial.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.Close();
            return filial;
        } catch (Exception e) {
            throw new Exception("FiliaisDao.buscaFilialPorCNPJ - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM Filiais WHERE CNPJ = " + CNPJ);
        }
    }

    public void insereFilial(Filiais filiais, Persistencia persistencia) throws Exception {
        String sql = "INSERT INTO filiais(CodFil, Descricao, RazaoSocial, Endereco, Bairro, Cidade, UF, CEP, Fone, Fone2, "
                + " Fax, Contato, Email, TipoPessoa, CNPJ, InscEst, InscMunic, ISS, Praca, ICMS, Praca2, "
                + " CodFilFp, CNAE, CAGED1Decl, CAGEDAltDados, PorteEmp, CodRecolh, CodGPS, FPAS, CodOutrasEnt, "
                + " RAT, FAP, Operador, Dt_Alter, Hr_Alter) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            PreparedStatement Mstat = persistencia.getState(sql);
            Mstat.setString(1, filiais.getCodFil().toString());
            Mstat.setString(2, filiais.getDescricao());
            Mstat.setString(3, filiais.getRazaoSocial());
            Mstat.setString(4, filiais.getEndereco());
            Mstat.setString(5, filiais.getBairro());
            Mstat.setString(6, filiais.getCidade());
            Mstat.setString(7, filiais.getUF());
            Mstat.setString(8, filiais.getCEP());
            Mstat.setString(9, filiais.getFone());
            Mstat.setString(10, filiais.getFone2());
            Mstat.setString(11, filiais.getFax());
            Mstat.setString(12, filiais.getContato());
            Mstat.setString(13, filiais.getEmail());
            Mstat.setString(14, filiais.getTipoPessoa());
            Mstat.setString(15, filiais.getCNPJ());
            Mstat.setString(16, filiais.getInscEst());
            Mstat.setString(17, filiais.getInscMunic());
            Mstat.setString(18, filiais.getISS().toString());
            Mstat.setInt(19, filiais.getPraca());
            Mstat.setString(20, filiais.getICMS().toString());
            Mstat.setInt(21, filiais.getPraca2());
            Mstat.setString(22, filiais.getCodFilFp().toString());
            Mstat.setString(23, filiais.getCNAE());
            Mstat.setString(24, filiais.getCAGED1Decl());
            Mstat.setString(25, filiais.getCAGEDAltDados());
            Mstat.setString(26, filiais.getPorteEmp());
            Mstat.setString(27, filiais.getCodRecolh());
            Mstat.setString(28, filiais.getCodGPS());
            Mstat.setString(29, filiais.getFPAS());
            Mstat.setString(30, filiais.getCodOutrasEnt());
            Mstat.setString(31, filiais.getRAT().toString());
            Mstat.setString(32, filiais.getFAP().toString());
            Mstat.setString(33, filiais.getOperador());
            Mstat.setString(34, filiais.getDt_Alter());
            Mstat.setString(35, filiais.getHr_Alter());

            Mstat.execute();
            Mstat.close();
        } catch (SQLException e) {
            throw new Exception("Falha ao gravar filial - " + e.getMessage());
        }
    }

    /**
     * Listagem de filiais
     *
     * @param persistencia - conexão ao banco de dados
     * @return - codfil, descricao, razaosocial, operador, dt_alter, hr_alter
     * @throws Exception
     */
    public List<Filiais> listagemFiliais(Persistencia persistencia) throws Exception {
        try {
            List<Filiais> retorno = new ArrayList();
            Filiais filial;
            String sql = "SELECT \n"
                    + "     codfil, descricao, razaosocial, fone2, operador, dt_alter, hr_alter \n"
                    + " FROM \n"
                    + "     Filiais";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consult.getString("codfil"));
                filial.setDescricao(consult.getString("descricao"));
                filial.setRazaoSocial(consult.getString("razaosocial"));
                filial.setFone2(consult.getString("fone2"));
                filial.setOperador(consult.getString("operador"));
                filial.setDt_Alter(consult.getString("dt_alter"));
                filial.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(filial);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("FiliaisDao.listagemFiliais - " + e.getMessage() + "\r\n"
                    + "SELECT \n"
                    + "     codfil, descricao, razaosocial, fone2, operador, dt_alter, hr_alter \n"
                    + " FROM \n"
                    + "     Filiais");
        }
    }

    /**
     * Cadastro de Filiais
     *
     * @param filial - Objeto filial Dados - codfil, descricao, razaosocial,
     * operador
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void InserirFiliais(Filiais filial, Persistencia persistencia) throws Exception {
        try {
            String sql = "DECLARE @Sequencia INT;\n"
                    + "\n"
                    + "SET @Sequencia = (SELECT\n"
                    + "                  ISNULL((MAX(Sequencia)+1), 1)\n"
                    + "                  FROM paramet);\n";

            sql += " insert into filiais "
                    + " (codfil, descricao, razaosocial, operador, dt_alter, hr_alter)"
                    + " values (?,?,?,?,?,?);\n";

            sql += "INSERT INTO paramet (Sequencia, CodEmpresa, Filial_PDR, Nome_empr, Cidade_PDR, UF_PDR, Path, Tipo,Usuario,Senha,HostName,HostNameWEB,BancoDados,PathSatelite,PathEagle,PathFotos,PathDoctos,PathLogoNFE,EscTolerMot,EscTolerChe,EscTolerVig,EscTolerOutros,PtoTolerChe,PtoTolerVig,PtoTolerOutros,AceTolerMot,AceTolerChe,AceTolerVig,AceTolerOutros,FusoHorario,FusoHorarioSEFAZ,LimiteSeg,LimiteCxf,LimiteTes,TrocaSenhaMobile, Operador, Dt_Alter, Hr_Alter)\n"
                    + "SELECT \n"
                    + "@Sequencia,\n"
                    + "LEFT(Descricao,3),\n"
                    + "codfil,\n"
                    + "Descricao, \n"
                    + "ParametRef.Cidade_PDR,\n"
                    + "ParametRef.UF_PDR,\n"
                    + "ParametRef.Path,\n"
                    + "ParametRef.Tipo,\n"
                    + "ParametRef.Usuario,\n"
                    + "ParametRef.Senha,\n"
                    + "ParametRef.HostName,\n"
                    + "ParametRef.HostNameWEB,\n"
                    + "ParametRef.BancoDados,\n"
                    + "ParametRef.PathSatelite,\n"
                    + "ParametRef.PathEagle,\n"
                    + "ParametRef.PathFotos,\n"
                    + "ParametRef.PathDoctos,\n"
                    + "ParametRef.PathLogoNFE,\n"
                    + "EscTolerMot,\n"
                    + "EscTolerChe,\n"
                    + "EscTolerVig,\n"
                    + "EscTolerOutros,\n"
                    + "PtoTolerChe,\n"
                    + "PtoTolerVig,\n"
                    + "PtoTolerOutros,\n"
                    + "AceTolerMot,\n"
                    + "AceTolerChe,\n"
                    + "AceTolerVig,\n"
                    + "AceTolerOutros,\n"
                    + "FusoHorario,\n"
                    + "FusoHorarioSEFAZ,\n"
                    + "LimiteSeg,\n"
                    + "LimiteCxf,\n"
                    + "LimiteTes,\n"
                    + "TrocaSenhaMobile,\n"
                    + "?,\n"
                    + "?,\n"
                    + "?\n"
                    + "FROM (SELECT TOP 1 *\n"
                    + "      FROM paramet\n"
                    + "      WHERE usuario  IS NOT NULL \n"
                    + "      AND   senha    IS NOT NULL \n"
                    + "      AND   HostName IS NOT NULL) AS ParametRef\n"
                    + "JOIN filiais      \n"
                    + "  ON filiais.codfil = ?;";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(filial.getCodFil());
            consulta.setString(filial.getDescricao());
            consulta.setString(filial.getRazaoSocial());
            consulta.setString(filial.getOperador());
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));

            consulta.setString(filial.getOperador());
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setBigDecimal(filial.getCodFil());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Falha ao inserir filial - \r:" + e.getMessage());
        }
    }

    /**
     * Grava alteração de filial
     *
     * @param filial - objeto filial
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravarFilial(Filiais filial, Persistencia persistencia) throws Exception {
        try {
            String sql = "update filiais "
                    + " set descricao = ?, razaosocial = ?, operador = ?, dt_alter = ?, hr_alter = ?"
                    + " where codfil = ?";
            PreparedStatement mstat = persistencia.getState(sql);
            mstat.setString(1, filial.getDescricao());
            mstat.setString(2, filial.getRazaoSocial());
            mstat.setString(3, filial.getOperador());
            mstat.setString(4, DataAtual.getDataAtual("SQL"));
            mstat.setString(5, DataAtual.getDataAtual("HORA"));
            mstat.setBigDecimal(6, filial.getCodFil());
            mstat.execute();
            mstat.close();
        } catch (Exception e) {
            throw new Exception("Falha ao alterar filial - \r:" + e.getMessage());
        }
    }

    public List<Filiais> PesquisarFilial(Filiais s, Persistencia persistencia) throws Exception {
        try {
            List<Filiais> retorno = new ArrayList();
            String sql = "select codfil, descricao, razaosocial, operador, dt_alter, hr_alter "
                    + " from filiais"
                    + " where codfil is not null";
            if (null != s.getCodFil() && !s.getCodFil().equals(new BigDecimal("-1"))) {
                sql = sql + " and codfil = ?";
            }
            if (null != s.getDescricao() && !s.getDescricao().equals("")) {
                sql = sql + " and descricao like ?";
            }
            if (null != s.getRazaoSocial() && !s.getRazaoSocial().equals("")) {
                sql = sql + " and razaosocial like ?";
            }
            Consulta consult = new Consulta(sql, persistencia);
            if (null != s.getCodFil() && !s.getCodFil().equals(new BigDecimal("-1"))) {
                consult.setBigDecimal(s.getCodFil());
            }
            if (null != s.getDescricao() && !s.getDescricao().equals("")) {
                consult.setString("%" + s.getDescricao() + "%");
            }
            if (null != s.getRazaoSocial() && !s.getRazaoSocial().equals("")) {
                consult.setString("%" + s.getRazaoSocial() + "%");
            }
            consult.select();
            Filiais filial;
            while (consult.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consult.getString("codfil"));
                filial.setDescricao(consult.getString("descricao"));
                filial.setRazaoSocial(consult.getString("razaosocial"));
                filial.setOperador(consult.getString("operador"));
                filial.setDt_Alter(consult.getString("dt_alter"));
                filial.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(filial);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Failed to list filiais -\r:" + e.getMessage());
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de funcionários cadastrados no banco
     *
     * @param filtros filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalFiliaisMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from filiais "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            if (!filtros.isEmpty()) {
                for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                    if (!entrada.getValue().equals("")) {
                        sql = sql + entrada.getKey() + " AND ";
                    }
                }
            }
            sql = sql + "codfil IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar filiais - \r\n" + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Filiais> ListaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Filiais> retorno = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY codfil ) AS RowNum, * "
                    + "          FROM      filiais "
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codfil IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";

            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Filiais filial;
            while (consult.Proximo()) {
                filial = new Filiais();
                filial.setCodFil(consult.getString("codfil"));
                filial.setDescricao(consult.getString("descricao"));
                filial.setRazaoSocial(consult.getString("razaosocial"));
                filial.setOperador(consult.getString("operador"));
                filial.setDt_Alter(consult.getString("dt_alter"));
                filial.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(filial);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao efetuar pesquisa de filiais - \r\n" + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
}
