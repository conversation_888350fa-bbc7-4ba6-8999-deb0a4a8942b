package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.DIRFDet;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DIRFDetDao {

    /**
     * Busca os dados da DIRF gerada para imprimir informe de rendimentos
     *
     * @param AnoCompet - ano de competencia
     * @param Matr - Matricula do funcionário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<DIRFDet> BuscaDirf(String AnoCompet, String Matr, Persistencia persistencia) throws Exception {
        try {
            List<DIRFDet> retorno = new ArrayList();
            String sql = "select * from DIRFDet"
                    + " where anocompet = ?"
                    + " and matr  in (Select x.Matr from Funcion x where x.CPF in (Select z.CPF From Funcion z WHERE z.Matr = ?) ) "
                    + "AND publicar = 'S'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(AnoCompet);
            consult.setString(Matr);
            consult.select();
            while (consult.Proximo()) {
                DIRFDet dirf = new DIRFDet();
                dirf.setCodFil(consult.getString("CPF"));
                dirf.setAnoCompet(consult.getString("AnoCompet"));
                dirf.setCPF(consult.getString("CPF"));
                dirf.setMatr(consult.getString("Matr"));
                dirf.setTipoReg(consult.getString("TipoReg"));
                dirf.setValor01(consult.getString("Valor01"));
                dirf.setValor02(consult.getString("Valor02"));
                dirf.setValor03(consult.getString("Valor03"));
                dirf.setValor04(consult.getString("Valor04"));
                dirf.setValor05(consult.getString("Valor05"));
                dirf.setValor06(consult.getString("Valor06"));
                dirf.setValor07(consult.getString("Valor07"));
                dirf.setValor08(consult.getString("Valor08"));
                dirf.setValor09(consult.getString("Valor09"));
                dirf.setValor10(consult.getString("Valor10"));
                dirf.setValor11(consult.getString("Valor11"));
                dirf.setValor12(consult.getString("Valor12"));
                dirf.setValor13(consult.getString("Valor13"));
                dirf.setValorTotal(consult.getString("Total"));
                retorno.add(dirf);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DIRFDetDao.BuscaDirf - " + e.getMessage() + "\r\n"
                    + "select * from DIRFDet"
                    + " where anocompet = " + AnoCompet
                    + " and matr = " + Matr + " AND publicar = 'S'");
        }
    }
}
