package SasLibrary;

import Dados.Persistencia;
import SasBeans.GuiasList;
import br.com.sasw.pacotesuteis.sasdaos.compostas.TesSaidaRtGuiasCxfGuiaDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Guias {

    public static String EntregaGuia(String sequencia, String parada, String codfil, String hora1, Persistencia persistencia) throws Exception {
        String xml = "<?xml version=\"1.0\"?>";
        try {
//            //Carrega guias
//            List<Rt_Guias> LentregaGuias;
//            Rt_GuiasDao entregaGuias = new Rt_GuiasDao();
//            //Carrega guias
//            List<CxFGuias> Lcxfguias;
//            CxfguiasDao cxfguias = new CxfguiasDao();
//            //Carrega guias
            List<GuiasList> LguiasPedidos;
            TesSaidaRtGuiasCxfGuiaDao pedidosguias = new TesSaidaRtGuiasCxfGuiaDao();

//                LentregaGuias = entregaGuias.getRt_GuiasEntrega(sequencia, parada, persistencia);
//                Lcxfguias = cxfguias.getCxfGuiasEntrega(sequencia, hora1, persistencia);
            LguiasPedidos = pedidosguias.getTesSaidasRt_GuiasCxfGuiasEntrega(codfil, hora1, sequencia, parada, persistencia);

//                if (!(LentregaGuias.isEmpty())) {
//                    for (Rt_Guias list_guias : LentregaGuias) {
//                        xml += "<malote>";
//                        xml += "<guia>" + list_guias.getGuia().toString() + "</guia>";
//                        xml += "<serie>" + list_guias.getSerie() + "</serie>";
//                        xml += "<valor>" + list_guias.getValor() + "</valor>";
//                        xml += "</malote>";
//                    }
//                } else if (!(Lcxfguias.isEmpty())) {
//                    for (CxFGuias list_guias : Lcxfguias) {
//                        xml += "<malote>";
//                        xml += "<guia>" + list_guias.getGuia().toString() + "</guia>";
//                        xml += "<serie>" + list_guias.getSerie() + "</serie>";
//                        xml += "<valor>" + list_guias.getValor() + "</valor>";
//                        xml += "</malote>";
//                    }
//                } else {//if (!(LguiasPedidos.isEmpty())) {
            for (GuiasList list_guias : LguiasPedidos) {
                xml += "<malote>";
                xml += "<guia>" + list_guias.getGuia() + "</guia>";
                xml += "<serie>" + list_guias.getSerie() + "</serie>";
                xml += "<valor>" + list_guias.getValor() + "</valor>";
                xml += "</malote>";
            }
//                }

            return xml;
        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar guias - " + e.getMessage());
        }
    }
}
