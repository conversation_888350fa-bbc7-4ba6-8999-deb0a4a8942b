package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.DIRFDetPS;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DIRFDetPSDao {

    /**
     * Busca os dados referente a plano de saúde dos funcionários no ano
     *
     * @param Matr - Matricula do Funcionário
     * @param AnoCompet - Ano de competência
     * @param persistencia - Conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<DIRFDetPS> getPlanoAnual(String Matr, String AnoCompet, Persistencia persistencia) throws Exception {
        try {
            List<DIRFDetPS> retorno = new ArrayList();
            String sql = "select * from dirfdetps "
                    + " where anocompet = ?"
                    + " and matr = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(AnoCompet);
            consult.setString(Matr);
            consult.select();
            while (consult.Proximo()) {
                DIRFDetPS ps = new DIRFDetPS();
                ps.setCodFil(consult.getString("CodFil"));
                ps.setAnoCompet(consult.getString("AnoCompet"));
                ps.setCPF(consult.getString("CPF"));
                ps.setCNPJOPSE(consult.getString("CNPJOPSE"));
                ps.setNomeOPSE(consult.getString("NomeOPSE"));
                ps.setMatr(consult.getString("Matr"));
                ps.setNome(consult.getString("Nome"));
                ps.setCPFDep(consult.getString("CPFDep"));
                ps.setDtNasc(consult.getDate("DtNasc").toLocalDate());
                ps.setTipoDep(consult.getString("TipoDep"));
                ps.setValor(consult.getString("Valor"));
                retorno.add(ps);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DIRFDetPSDao.getPlanoAnual - " + e.getMessage() + "\r\n"
                    + "select * from dirfdetps"
                    + " where anocompet = " + AnoCompet
                    + " and matr = " + Matr);
        }
    }
}
