package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Escala;
import SasBeans.Funcion;
import SasBeans.Pessoa;
import SasBeans.Rotas;
import SasBeans.Veiculos;
import SasBeansCompostas.CarregaEscala;
import SasBeansCompostas.EscalaPessoaDTO;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class EscalaDao {

    private Persistencia persistencia;

    public EscalaDao() {
    }

    public EscalaDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Inserção de registro na tabela escalca
     *
     * @param escala - Objeto escala a ser inserido
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void inserirEscala(Escala escala, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = br.com.sasw.pacotesuteis.utilidades.Sqls.montaInsert(escala);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(escala.getRota());
            consulta.setString(escala.getData());
            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setBigDecimal(escala.getMatrMot());
            consulta.setString(escala.getHrMot());
            consulta.setBigDecimal(escala.getMatrChe());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getMatrVig1());
            consulta.setString(escala.getHrVig1());
            consulta.setBigDecimal(escala.getMatrVig2());
            consulta.setString(escala.getHrVig2());
            consulta.setBigDecimal(escala.getMatrVig3());
            consulta.setString(escala.getHrVig3());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setBigDecimal(escala.getVeiculo());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setString(escala.getSituacao());
            consulta.setDate(DataAtual.LC2Date(escala.getDtUltAckMob()));
            consulta.setString(escala.getHrUltAckMob());
            consulta.setString(escala.getOperador());
            consulta.setDate(DataAtual.LC2Date(escala.getDt_Alter()));
            consulta.setString(escala.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.inserirEscala - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    /**
     * Cadastro inicial de escala
     *
     * @param escala
     * @param persistencia
     * @throws Exception
     */
    public void cadastrarEscala(Escala escala, Persistencia persistencia) throws Exception {
        try {
            String sql = " Insert into Escala (CodFil, Data, Rota, SeqRota) Values (?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getData());
            consulta.setString(escala.getRota());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.cadastrarEscala - " + e.getMessage() + "\r\n"
                    + " Insert into Escala (CodFil, Data, Rota, SeqRota) Values (?,?,?,?) ");
        }
    }

    public void atualizarEscala(Escala escala, Persistencia persistencia) throws Exception {
        try {
            String sql = " Update Escala set \n"
                    + " Hora1 = ?, \n"
                    + " Hora2 = ?, \n"
                    + " Hora3 = ?, \n"
                    + " Hora4 = ?, \n"
                    + " HsInterv = ?, \n"
                    + " HsTot    = ?, \n"
                    + " MatrMot  = ?, \n"
                    + " HrMot    = ?, \n"
                    + " MatrChe  = ?, \n"
                    + " HrChe    = ?, \n"
                    + " MatrVig1 = ?, \n"
                    + " HrVig1   = ?, \n"
                    + " MatrVig2 = ?, \n"
                    + " HrVig2   = ?, \n"
                    + " MatrVig3 = ?, \n"
                    + " HrVig3   = ?, \n"
                    + " Veiculo  = ?, \n"
                    + " Operador = ?, \n"
                    + " Dt_Alter = ?, \n"
                    + " Hr_Alter = ? \n"
                    + " where SeqRota = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setBigDecimal(escala.getMatrMot());
            consulta.setString(escala.getHrMot());
            consulta.setBigDecimal(escala.getMatrChe());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getMatrVig1());
            consulta.setString(escala.getHrVig1());
            consulta.setBigDecimal(escala.getMatrVig2());
            consulta.setString(escala.getHrVig2());
            consulta.setBigDecimal(escala.getMatrVig3());
            consulta.setString(escala.getHrVig3());
            consulta.setBigDecimal(escala.getVeiculo());
            consulta.setString(escala.getOperador());
            consulta.setDate(DataAtual.LC2Date(escala.getDt_Alter()));
            consulta.setString(escala.getHr_Alter());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.atualizarEscala - " + e.getMessage());
        }
    }

    /**
     * Faz a marcação dos campos DtUltAckMob e HrUltAckMob
     *
     * @param SeqRota - Sequencia da rota
     * @param DtUltAckMob - Data a ser gravada
     * @param HrUltAckMob - Hora a ser gravada
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void MarcarAck(BigDecimal SeqRota, LocalDate DtUltAckMob, String HrUltAckMob,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "update escala set DtUltAckMob = ?, HrUltAckMob = ? where SeqRota = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setDate(DataAtual.LC2Date(DtUltAckMob));
            consulta.setString(HrUltAckMob);
            consulta.setBigDecimal(SeqRota);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.MarcarAck - " + e.getMessage() + "\r\n"
                    + "update escala set DtUltAckMob = " + DtUltAckMob + ", HrUltAckMob = " + HrUltAckMob + " where SeqRota = " + SeqRota);
        }
    }

    /**
     * Faz a marcação dos campos DtUltAckMob e HrUltAckMob
     *
     * @param SeqRota - Sequencia da rota
     * @param DtUltAckMob - Data a ser gravada
     * @param HrUltAckMob - Hora a ser gravada
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void marcarAck(String SeqRota, String DtUltAckMob, String HrUltAckMob,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "update escala set DtUltAckMob = ?, HrUltAckMob = ? where SeqRota = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DtUltAckMob);
            consulta.setString(HrUltAckMob);
            consulta.setString(SeqRota);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.marcarAck - " + e.getMessage() + "\r\n"
                    + "update escala set DtUltAckMob = " + DtUltAckMob + ", HrUltAckMob = " + HrUltAckMob + " where SeqRota = " + SeqRota);
        }
    }

    /**
     * Inserir escala da supervisão
     *
     * @param escala - Objeto escala
     * @param data Data
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void inserirEscalaSupervisao(Escala escala, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO escala (rota, data, codfil, hora1, hora2,"
                    + "hora3, hora4, hsinterv, hstot, hrche, codpessoasup,"
                    + "seqrota) VALUES(?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(escala.getRota());
            consulta.setString(data);
            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setBigDecimal(escala.getSeqRota());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.inserirEscalaSupervisao - " + e.getMessage() + "\r\n"
                    + "INSERT INTO escala (rota, data, codfil, hora1, hora2,"
                    + "hora3, hora4, hsinterv, hstot, hrche, codpessoasup,"
                    + "seqrota) VALUES(" + escala.getRota() + "," + data + "," + escala.getCodFil() + "," + escala.getHora1() + "," + escala.getHora2() + ","
                    + escala.getHora3() + "," + escala.getHora4() + "," + escala.getHsInterv() + "," + escala.getHsTot() + "," + escala.getHrChe() + ","
                    + escala.getCodPessoaSup() + "," + escala.getSeqRota() + ")");
        }
    }

    /**
     * Atualiza escala da supervisão
     *
     * @param escala - Objeto escala
     * @param data Data
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void atualizarEscalaSupervisao(Escala escala, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE escala SET data = ?, codfil = ?, hora1 = ?, hora2 = ?,"
                    + "hora3 = ?, hora4 = ?, hsinterv = ?, hstot = ?, hrche = ?, codpessoasup = ?,"
                    + "seqrota = ? WHERE rota = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setString(escala.getRota());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.atualizarEscalaSupervisao - " + e.getMessage() + "\r\n"
                    + " UPDATE escala SET data = " + data + ", codfil = " + escala.getCodFil() + ", hora1 = " + escala.getHora1() + ", hora2 = " + escala.getHora2() + ","
                    + " hora3 = " + escala.getHora3() + ", hora4 = " + escala.getHora4() + ", hsinterv = " + escala.getHsInterv() + ", hstot = " + escala.getHsTot() + ", "
                    + " hrche = " + escala.getHrChe() + ", codpessoasup = " + escala.getCodPessoaSup() + ", seqrota = " + escala.getSeqRota() + " WHERE rota = "
                    + escala.getRota());
        }
    }

    /**
     * Obter sequencia de rotas em supervisão
     *
     * @param data data da rota
     * @param codpessoa codigo pessoa do supervisor
     * @param persistencia conexão com o banco de dados
     * @return sequencia
     * @throws Exception
     */
    public BigDecimal obterSequenciaRotaSupervisao(String data, String codpessoa, Persistencia persistencia) throws Exception {
        BigDecimal sequencia = new BigDecimal("0");
        try {
            String sql = "SELECT SeqRota FROM escala WHERE Data = ?"
                    + " AND CodPessoaSup = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codpessoa);
            consulta.select();

            while (consulta.Proximo()) {
                sequencia = new BigDecimal(consulta.getString("SeqRota"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.obterSequenciaRotaSupervisao - " + e.getMessage() + "\r\n"
                    + "SELECT SeqRota FROM escala WHERE Data = " + data
                    + " AND CodPessoaSup = " + codpessoa);
        }
        return sequencia;
    }

    /**
     * Inserir escala da supervisão do projeto SatMobWeb (Rotas)
     *
     * @param escala - Objeto escala
     * @param data Data
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void inserirEscalaSupervisaoRotaSatMobWeb(Escala escala, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO escala (rota, data, codfil, hora1, hora2, "
                    + " hora3, hora4, hsinterv, hstot, hrche, codpessoasup, "
                    + " seqrota, matrmot, MatrChe, Operador, Dt_Alter, Hr_Alter) "
                    + " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(escala.getRota());
            consulta.setString(data);
            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setBigDecimal(escala.getMatrMot());
            consulta.setBigDecimal(escala.getMatrChe());
            consulta.setString(escala.getOperador());
            consulta.setDate(DataAtual.LC2Date(escala.getDt_Alter()));
            consulta.setString(escala.getHr_Alter());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.inserirEscalaSupervisaoRotaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "INSERT INTO escala (rota, data, codfil, hora1, hora2,"
                    + "hora3, hora4, hsinterv, hstot, hrche, codpessoasup,"
                    + "seqrota, matrmot, MatrChe, Operador, Dt_Alter, Hr_Alter) "
                    + "VALUES(" + escala.getRota() + "," + data + "," + escala.getCodFil() + "," + escala.getHora1() + "," + escala.getHora2() + "," + escala.getHora3() + ","
                    + escala.getHora4() + "," + escala.getHsInterv() + "," + escala.getHsTot() + "," + escala.getHrChe() + "," + escala.getCodPessoaSup() + ","
                    + escala.getSeqRota() + "," + escala.getMatrMot() + "," + escala.getMatrChe() + "," + escala.getOperador() + "," + escala.getDt_Alter() + ","
                    + escala.getHr_Alter() + ")");
        }
    }

    /**
     * Atualiza escala da supervisão (Rotas)
     *
     * @param escala - Objeto escala
     * @param data Data
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void atualizarEscalaSupervisaoRotaSatMobWeb(Escala escala, String data, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE escala SET data = ?,  hora1 = ?, hora2 = ?,"
                    + "hora3 = ?, hora4 = ?, hsinterv = ?, hstot = ?, hrche = ?, CodPessoaSup = ?, "
                    + " matrche=?, operador=?, dt_alter=?, hr_alter=? WHERE seqrota=? and rota = ? and codfil = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(escala.getData());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setString(escala.getHrChe());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setBigDecimal(escala.getMatrChe());
            consulta.setString(escala.getOperador());
            consulta.setDate(DataAtual.LC2Date(escala.getDt_Alter()));
            consulta.setString(escala.getHr_Alter());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setString(escala.getRota());
            consulta.setBigDecimal(escala.getCodFil());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.atualizarEscalaSupervisaoRotaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE escala SET data = " + escala.getData() + ",  hora1 = " + escala.getHora1() + ", hora2 = " + escala.getHora2() + ","
                    + " hora3 = " + escala.getHora3() + ", hora4 = " + escala.getHora4() + ", hsinterv = " + escala.getHsInterv() + ", hstot = " + escala.getHsTot() + ", "
                    + " hrche = " + escala.getHrChe() + ", CodPessoaSup = " + escala.getCodPessoaSup() + ", "
                    + " matrche=" + escala.getMatrChe() + ", operador=" + escala.getOperador() + ", dt_alter=" + escala.getDt_Alter() + ", "
                    + " hr_alter=" + escala.getHr_Alter() + " WHERE seqrota=" + escala.getSeqRota() + " and rota = " + escala.getRota() + " "
                    + " and codfil = " + escala.getCodFil());
        }
    }

    /**
     * Seleciona escala da supervisão
     *
     * @param data Data
     * @param SeqRota Sequência da rota
     * @param persistencia Conexão com o banco de dados
     * @return Escala selecionada
     * @throws Exception
     */
    public Escala getEscala(BigDecimal SeqRota, Persistencia persistencia) throws Exception {
        Escala retorno = new Escala();
        retorno.setSeqRota(null);
        try {
            String sql = "SELECT CONVERT(VARCHAR,data,112) DataC, * "
                    + " FROM escala WHERE seqrota = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(SeqRota);
            consulta.select();

            while (consulta.Proximo()) {
                retorno.setRota(consulta.getString("rota"));
                retorno.setSeqRota(consulta.getString("seqrota"));
                retorno.setData(consulta.getString("DataC"));
                retorno.setCodFil(consulta.getString("codfil"));
                retorno.setHora1(consulta.getString("hora1"));
                retorno.setHora2(consulta.getString("hora2"));
                retorno.setHora3(consulta.getString("hora3"));
                retorno.setHora4(consulta.getString("hora4"));
                retorno.setHsInterv(consulta.getString("HsInterv"));
                retorno.setHsTot(consulta.getString("HsTot"));
                retorno.setMatrMot(consulta.getString("MatrMot"));
                retorno.setMatrChe(consulta.getString("MatrChe"));
                retorno.setMatrVig1(consulta.getString("MatrVig1"));
                retorno.setMatrVig2(consulta.getString("MatrVig2"));
                retorno.setMatrVig3(consulta.getString("MatrVig3"));
                retorno.setHrMot(consulta.getString("HrMot"));
                retorno.setHrChe(consulta.getString("HrChe"));
                retorno.setHrVig1(consulta.getString("HrVig1"));
                retorno.setHrVig2(consulta.getString("HrVig2"));
                retorno.setHrVig3(consulta.getString("HrVig3"));
                retorno.setVeiculo(consulta.getString("Veiculo"));
                retorno.setCodPessoaSup(consulta.getString("CodPessoaSup"));
                retorno.setOperador(consulta.getString("Operador"));
                retorno.setDt_Alter(consulta.getLocalDate("dt_alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
            }
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("EscalaDao.escalaSupervisao - " + e.getMessage() + "\r\n"
                    + "SELECT * "
                    + " FROM escala WHERE seqrota = " + SeqRota);
        }

        return retorno;
    }

    /**
     * Seleciona os turnos do supervisor
     *
     * @param data Data
     * @param codpessoa Código de pessoa
     * @param codfil Código de filial
     * @param persistencia Conexão com o banco de dados
     * @return Escala selecionada
     * @throws Exception
     */
    public List<Escala> VerficaEscala(String data, BigDecimal codpessoa, BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<Escala> escalas = new ArrayList();

        try {
            String sql = "SELECT codpessoasup, "
                    + "hora1, hora2, hora3, hora4 "
                    + "FROM escala WHERE data = ? AND codpessoasup = ?"
                    + "AND codfil = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setBigDecimal(codpessoa);
            consulta.setBigDecimal(codfil);
            consulta.select();

            Escala escala = null;
            while (consulta.Proximo()) {
                escala = new Escala();
                escala.setCodPessoaSup(consulta.getString("CodPessoaSup"));
                escala.setHora1(consulta.getString("hora1"));
                escala.setHora2(consulta.getString("hora2"));
                escala.setHora3(consulta.getString("hora3"));
                escala.setHora4(consulta.getString("hora4"));

                escalas.add(escala);
            }
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("EscalaDao.VerficaEscala - " + e.getMessage() + "\r\n"
                    + "SELECT codpessoasup, "
                    + "hora1, hora2, hora3, hora4 "
                    + "FROM escala WHERE data = " + data + " AND codpessoasup = " + codpessoa
                    + "AND codfil = " + codfil);
        }

        return escalas;
    }

    /**
     * Faz a exclusão dos valores da escala uma vez que a rota está sendo
     * excluida no projeto SatMobWeb
     *
     * @param sequencia Sequencia da rota na escala
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void ExcluirEscalaSatMobWeb(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        try {
            String sql = "DELETE FROM escala WHERE seqrota = ?;\n DELETE FROM Rt_Escala WHERE Sequencia = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(sequencia);
            consulta.setBigDecimal(sequencia);
            consulta.delete();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("EscalaDao.ExcluirEscalaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "DELETE FROM escala WHERE seqrota = " + sequencia);
        }
    }

    /**
     * Lista a escala como no Satellite
     *
     * @param codfil Código de filial
     * @param date Data da escala
     * @param codpessoa Código da pessoa
     * @param excl
     * @param persistencia Conexão com o banco
     * @return Retorna lista de escalas
     * @throws Exception
     */
    public List<CarregaEscala> ListaEscalaSatMobWeb(String codfil, LocalDate date, BigDecimal codpessoa, Boolean excl, Persistencia persistencia) throws Exception {
        List<CarregaEscala> retorno = new ArrayList<>();

        try {
            String sql = "SELECT escala.codfil, escala.rota, escala.veiculo, escala.data, escala.seqrota, "
                    + " escala.hora1, escala.hora2, escala.hora3, escala.hora4, escala.hstot, "
                    + " escala.hsinterv, escala.matrche, escala.hrche, escala.codpessoasup, escala.operador, "
                    + " escala.dt_alter, escala.hr_alter, rotas.tpveic,rotas.flag_excl, veiculos.placa, pessoa.nome  "
                    + " FROM escala "
                    + " LEFT JOIN rotas ON escala.seqrota=rotas.sequencia "
                    + "                 AND escala.codfil=rotas.codfil "
                    + " LEFT JOIN veiculos ON escala.veiculo=veiculos.numero "
                    + "                    AND escala.codfil=veiculos.codfil "
                    + " LEFT JOIN pessoa ON escala.codpessoasup=pessoa.codigo "
                    + " WHERE escala.data = ? AND rotas.tpveic = 'S' ";

            CarregaEscala carregaEscala;
            Escala escala;
            Rotas rotas;
            Pessoa pessoa;
            Veiculos veiculos;

            if (excl) {
                sql = sql + "AND rotas.flag_excl <> '*' ";
            }

            if (!codfil.equals(" ")) {
                sql = sql + "AND escala.codfil = ?";
            } else {
                sql = sql + "and Rotas.CodFil in "
                        + "(select filiais.codfil "
                        + " from saspw"
                        + " inner join saspwfil on saspwfil.nome = saspw.nome"
                        + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                        + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                        + " where saspw.codpessoa = ? and paramet.path = ?)";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setDate(DataAtual.LC2Date(date));

            if (!codfil.equals(" ")) {
                consulta.setString(codfil);
            } else {
                consulta.setBigDecimal(codpessoa);
                consulta.setString(persistencia.getEmpresa());
            }

            consulta.select();

            while (consulta.Proximo()) {
                carregaEscala = new CarregaEscala();
                escala = new Escala();
                rotas = new Rotas();
                pessoa = new Pessoa();
                veiculos = new Veiculos();

                escala.setCodFil(consulta.getString("codfil"));
                escala.setRota(consulta.getString("rota"));
                escala.setSeqRota(consulta.getString("seqrota"));
                rotas.setTpVeic(consulta.getString("tpveic"));
                rotas.setFlag_Excl(consulta.getString("flag_excl"));
                escala.setVeiculo(consulta.getString("veiculo"));
                veiculos.setPlaca(consulta.getString("placa"));
                escala.setData(consulta.getString("data"));
                escala.setHora1(consulta.getString("hora1"));
                escala.setHora2(consulta.getString("hora2"));
                escala.setHora3(consulta.getString("hora3"));
                escala.setHora4(consulta.getString("hora4"));
                escala.setHsTot(consulta.getString("hstot"));
                escala.setHsInterv(consulta.getString("hsinterv"));
                escala.setMatrChe(consulta.getString("matrche"));
                escala.setHrChe(consulta.getString("hrche"));
                escala.setCodPessoaSup(consulta.getString("codpessoasup"));
                pessoa.setNome(consulta.getString("nome"));
                escala.setOperador(consulta.getString("operador"));
                escala.setDt_Alter(consulta.getLocalDate("dt_alter"));
                escala.setHr_Alter(consulta.getString("hr_alter"));

                carregaEscala.setEscala(escala);
                carregaEscala.setPessoa(pessoa);
                carregaEscala.setRotas(rotas);
                carregaEscala.setVeiculos(veiculos);

                retorno.add(carregaEscala);

            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.ListaEscalaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "SELECT escala.codfil, escala.rota, escala.veiculo, escala.data, escala.seqrota, "
                    + " escala.hora1, escala.hora2, escala.hora3, escala.hora4, escala.hstot, "
                    + " escala.hsinterv, escala.matrche, escala.hrche, escala.codpessoasup, escala.operador, "
                    + " escala.dt_alter, escala.hr_alter, rotas.tpveic,rotas.flag_excl, veiculos.placa, pessoa.nome  "
                    + " FROM escala "
                    + " LEFT JOIN rotas ON escala.seqrota=rotas.sequencia "
                    + "                 AND escala.codfil=rotas.codfil "
                    + " LEFT JOIN veiculos ON escala.veiculo=veiculos.numero "
                    + "                    AND escala.codfil=veiculos.codfil "
                    + " LEFT JOIN pessoa ON escala.codpessoasup=pessoa.codigo "
                    + " WHERE escala.data = " + date + " AND rotas.tpveic = 'S' "
                    + (excl ? " AND rotas.flag_excl <> '*' " : "")
                    + (!codfil.equals(" ") ? "AND escala.codfil = " + codfil : "and Rotas.CodFil in "
                    + "(select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = " + codpessoa + " and paramet.path = " + persistencia.getEmpresa() + ")"));
        }
        return retorno;
    }

    public void AtualizaEscalaSatMobWeb(Escala escala, Persistencia persistencia) throws Exception {
        try {

            String sql = "UPDATE escala SET codfil = ?, rota= ?, seqrota= ?, "
                    + " hora1= ?, hora2= ?, hora3= ?, hora4= ?, hstot= ?, hsinterv= ?, data= ?, "
                    + " veiculo= ?, matrche= ?, codpessoasup= ?, hrche= ?, "
                    + " operador= ?, hr_alter= ?, dt_alter= ?  "
                    + " WHERE seqrota = ? AND rota = ? ";
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(escala.getCodFil());
            consulta.setString(escala.getRota());
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setString(escala.getHora1());
            consulta.setString(escala.getHora2());
            consulta.setString(escala.getHora3());
            consulta.setString(escala.getHora4());
            consulta.setBigDecimal(escala.getHsTot());
            consulta.setBigDecimal(escala.getHsInterv());
            consulta.setString(escala.getData());
            consulta.setBigDecimal(escala.getVeiculo());
            consulta.setBigDecimal(escala.getMatrChe());
            consulta.setBigDecimal(escala.getCodPessoaSup());
            consulta.setString(escala.getHrChe());
            consulta.setString(escala.getOperador());
            consulta.setString(escala.getHr_Alter());
            consulta.setDate(DataAtual.LC2Date(escala.getDt_Alter()));
            consulta.setBigDecimal(escala.getSeqRota());
            consulta.setString(escala.getRota());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.AtualizaEscalaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE escala SET codfil = " + escala.getCodFil() + ", rota= " + escala.getRota() + ", seqrota= " + escala.getHora1() + ", "
                    + " hora1= " + escala.getHora1() + ", hora2= " + escala.getHora2() + ", hora3= " + escala.getHora3() + ", hora4= " + escala.getHora4() + ", "
                    + " hstot= " + escala.getHsTot() + ", hsinterv= " + escala.getHsInterv() + ", data= " + escala.getData() + ", "
                    + " veiculo= " + escala.getVeiculo() + ", matrche= " + escala.getMatrChe() + ", codpessoasup= " + escala.getCodPessoaSup() + ", "
                    + " hrche= " + escala.getHrChe() + ", operador= " + escala.getOperador() + ", hr_alter= " + escala.getHr_Alter() + ", "
                    + " dt_alter= " + escala.getDt_Alter() + " WHERE seqrota = " + escala.getSeqRota() + " AND rota = " + escala.getRota());
        }
    }

    public List<CarregaEscala> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<CarregaEscala> retorno = new ArrayList<>();
        try {
            String sql = "SELECT  *  FROM"
                    + "    ( SELECT    ROW_NUMBER() OVER ( ORDER BY escala.data desc, escala.rota desc ) AS RowNum,\n"
                    + " escala.codfil, escala.rota, escala.veiculo, escala.data, escala.seqrota, \n"
                    + " escala.hora1, escala.hora2, escala.hora3, escala.hora4, escala.hstot, \n"
                    + " escala.hsinterv, escala.matrche, escala.hrche, escala.codpessoasup, escala.operador, \n"
                    + " escala.dt_alter, escala.hr_alter, rotas.tpveic,rotas.flag_excl, veiculos.placa, pessoa.nome \n"
                    + " FROM escala \n"
                    + " LEFT JOIN rotas ON escala.seqrota=rotas.sequencia \n"
                    + "                 AND escala.codfil=rotas.codfil \n"
                    + " LEFT JOIN veiculos ON escala.veiculo=veiculos.numero \n"
                    + "                    AND escala.codfil=veiculos.codfil \n"
                    + " LEFT JOIN pessoa ON escala.codpessoasup=pessoa.codigo \n"
                    + " WHERE rotas.tpveic = 'S' \n"
                    + " and escala.CodFil in \n"
                    + "                 (select filiais.codfil \n"
                    + "                     from saspw\n"
                    + "                     inner join saspwfil on saspwfil.nome = saspw.nome\n"
                    + "                     inner join filiais on filiais.codfil = saspwfil.codfilac\n"
                    + "                     inner join paramet on paramet.filial_pdr = filiais.codfil\n"
                    + "                     where saspw.codpessoa = ? and paramet.path = ?) \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + "\n";
                }
            }
            sql = sql + ") AS RowConstrainedResult\n"
                    + " WHERE RowNum >= ? \n"
                    + " AND RowNum < ? \n"
                    + " ORDER BY RowNum\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
                consulta.setString(persistencia.getEmpresa());
//            }

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            CarregaEscala carregaEscala;
            Escala escala;
            Rotas rotas;
            Pessoa pessoa;
            Veiculos veiculos;

            while (consulta.Proximo()) {
                carregaEscala = new CarregaEscala();
                escala = new Escala();
                rotas = new Rotas();
                pessoa = new Pessoa();
                veiculos = new Veiculos();
                escala.setCodFil(consulta.getString("codfil"));
                escala.setRota(consulta.getString("rota"));
                escala.setSeqRota(consulta.getString("seqrota"));
                rotas.setTpVeic(consulta.getString("tpveic"));
                rotas.setFlag_Excl(consulta.getString("flag_excl"));
                escala.setVeiculo(consulta.getString("veiculo"));
                veiculos.setPlaca(consulta.getString("placa"));
                escala.setData(consulta.getString("data"));
                escala.setHora1(consulta.getString("hora1"));
                escala.setHora2(consulta.getString("hora2"));
                escala.setHora3(consulta.getString("hora3"));
                escala.setHora4(consulta.getString("hora4"));
                escala.setHsTot(consulta.getString("hstot"));
                escala.setHsInterv(consulta.getString("hsinterv"));
                escala.setMatrChe(consulta.getString("matrche"));
                escala.setHrChe(consulta.getString("hrche"));
                escala.setCodPessoaSup(consulta.getString("codpessoasup"));
                pessoa.setNome(consulta.getString("nome"));
                escala.setOperador(consulta.getString("operador"));
                escala.setDt_Alter(consulta.getLocalDate("dt_alter"));
                escala.setHr_Alter(consulta.getString("hr_alter"));
                carregaEscala.setEscala(escala);
                carregaEscala.setPessoa(pessoa);
                carregaEscala.setRotas(rotas);
                carregaEscala.setVeiculos(veiculos);
                retorno.add(carregaEscala);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("EscalaDao.ListaPaginada - " + e.getMessage());
        }
        return retorno;
    }

    public Integer TotalEscalasMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from escala "
                    + " LEFT JOIN rotas ON escala.seqrota=rotas.sequencia "
                    + "                 AND escala.codfil=rotas.codfil "
                    + " LEFT JOIN veiculos ON escala.veiculo=veiculos.numero "
                    + "                    AND escala.codfil=veiculos.codfil "
                    + " LEFT JOIN pessoa ON escala.codpessoasup=pessoa.codigo "
                    + " WHERE rotas.tpveic = 'S' "
                    + " and escala.CodFil in "
                    + "                 (select filiais.codfil "
                    + "                     from saspw"
                    + "                     inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                     inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                     inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                     where saspw.codpessoa = ? and paramet.path = ?) ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaDao.TotalEscalasMobWeb - " + e.getMessage());
        }
    }

    public List<Escala> listaRotasValores(String data, String codfil, Persistencia persistencia) throws Exception {
        List<Escala> rotas = new ArrayList<>();
        try {
            String sql = " select rotas.sequencia, escala.codfil "
                    + " from escala "
                    + " left join rotas on rotas.sequencia = escala.seqrota "
                    + "                and rotas.codfil    = escala.codfil"
                    + " where escala.data = ? and rotas.tpveic <> 'S' ";
            if (!codfil.equals("")) {
                sql += " and escala.codfil = ? ";
            }
            sql += " order by escala.rota ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (!codfil.equals("")) {
                consulta.setString(codfil);
            }
            consulta.select();
            Escala rota;
            while (consulta.Proximo()) {
                rota = new Escala();
                rota.setSeqRota(consulta.getString("sequencia"));
                rota.setCodFil(consulta.getString("codfil"));
                rotas.add(rota);
            }
            consulta.Close();
            return rotas;
        } catch (Exception e) {
            throw new Exception("EscalaDao.listaRotasValores - " + e.getMessage() + "\r\n"
                    + "Select rotas.sequencia, escala.codfil "
                    + " from escala "
                    + " left join rotas on rotas.sequencia = escala.seqrota "
                    + "                and rotas.codfil    = escala.codfil"
                    + " where escala.data = " + data + " and rotas.tpveic <> 'S' "
                    + " and escala.codfil = " + codfil
                    + " order by escala.rota");
        }
    }

    /**
     * Busca a sequência de rota através da matrícula do chefe de equipe e data.
     *
     * @param data
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static String obterSequenciaRota(String data, String matricula, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT CASE WHEN RotasPNT.SeqRota =  '' OR RotasPNT.SeqRota IS NULL \n"
                    + "     THEN Escala.SeqRota \n"
                    + "     ELSE RotasPNT.SeqRota END SeqRota \n"
                    + " FROM Escala \n"
                    + " INNER JOIN Rotas ON Rotas.Sequencia = Escala.SeqRota \n"
                    + " INNER JOIN RotasPNT ON RotasPNT.Sequencia = Escala.SeqRota\n"
                    + "                   AND RotasPNT.Data = ? \n"
                    + " WHERE (MatrChe = ? OR MatrMot = ?) \n"
                    + " AND Rotas.DtFim >= ?; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(matricula);
            consulta.setString(matricula);
            consulta.setString(data);
            consulta.select();
            String retorno = null;
            if (consulta.Proximo()) {
                retorno = consulta.getString("SeqRota");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaDao.obterSequenciaRota - " + e.getMessage() + "\r\n"
                    + " SELECT CASE WHEN RotasPNT.SeqRota =  '' OR RotasPNT.SeqRota IS NULL \n"
                    + "     THEN Escala.SeqRota \n"
                    + "     ELSE RotasPNT.SeqRota END SeqRota \n"
                    + " FROM Escala \n"
                    + " INNER JOIN Rotas ON Rotas.Sequencia = Escala.SeqRota \n"
                    + " INNER JOIN RotasPNT ON RotasPNT.Sequencia = Escala.SeqRota\n"
                    + "                   AND RotasPNT.Data = " + data + " \n"
                    + " WHERE (MatrChe = " + matricula + " OR MatrMot = " + matricula + ") \n"
                    + " AND Rotas.DtFim >= " + data + "; ");
        }
    }

    /**
     * Busca a sequência de rota através da matrícula do chefe de equipe, data e
     * filial.
     *
     * @param codfil
     * @param data
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static String obterSequenciaRota(String codfil, String data, String matricula, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT CASE WHEN RotasPNT.SeqRota =  '' OR RotasPNT.SeqRota IS NULL \n"
                    + "     THEN Escala.SeqRota \n"
                    + "     ELSE RotasPNT.SeqRota END SeqRota \n"
                    + " FROM Escala \n"
                    + " INNER JOIN Rotas ON Rotas.Sequencia = Escala.SeqRota \n"
                    + " INNER JOIN RotasPNT ON RotasPNT.Sequencia = Escala.SeqRota\n"
                    + "                   AND RotasPNT.Data = ? \n"
                    + " WHERE Escala.Codfil = ? AND Rotas.flag_excl <> '*'\n"
                    + " AND (MatrChe = ? OR MatrMot = ?) \n"
                    + " AND Rotas.DtFim >= ?; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codfil);
            consulta.setString(matricula);
            consulta.setString(matricula);
            consulta.setString(data);
            consulta.select();
            String retorno = null;
            while (consulta.Proximo()) {
                retorno = consulta.getString("SeqRota");
//                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaDao.obterSequenciaRota - " + e.getMessage() + "\r\n"
                    + " SELECT CASE WHEN RotasPNT.SeqRota =  '' OR RotasPNT.SeqRota IS NULL \n"
                    + "     THEN Escala.SeqRota \n"
                    + "     ELSE RotasPNT.SeqRota END SeqRota \n"
                    + " FROM Escala \n"
                    + " INNER JOIN Rotas ON Rotas.Sequencia = Escala.SeqRota \n"
                    + " INNER JOIN RotasPNT ON RotasPNT.Sequencia = Escala.SeqRota\n"
                    + "                   AND RotasPNT.Data = " + data + " \n"
                    + " WHERE Escala.Codfil = " + codfil + " \n"
                    + " AND (MatrChe = " + matricula + " OR MatrMot = " + matricula + ") \n"
                    + " AND Rotas.DtFim >= " + data + "; ");
        }
    }

    /**
     * Busca a sequência de rota através da matrícula do chefe de equipe, data e
     * filial.
     *
     * @param codfil
     * @param data
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static String obterSequenciaRotaSemRotasPNT(String codfil, String data, String matricula, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Escala.SeqRota \n"
                    + " FROM Escala \n"
                    + " WHERE Escala.Codfil = ? \n"
                    + " AND (MatrChe = ? OR MatrMot = ?) \n"
                    + " AND Escala.Data = ?; ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(matricula);
            consulta.setString(matricula);
            consulta.setString(data);
            consulta.select();
            String retorno = null;
            while (consulta.Proximo()) {
                retorno = consulta.getString("SeqRota");
                break;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaDao.obterSequenciaRota - " + e.getMessage() + "\r\n"
                    + " SELECT CASE WHEN RotasPNT.SeqRota =  '' OR RotasPNT.SeqRota IS NULL \n"
                    + "     THEN Escala.SeqRota \n"
                    + "     ELSE RotasPNT.SeqRota END SeqRota \n"
                    + " FROM Escala \n"
                    + " INNER JOIN Rotas ON Rotas.Sequencia = Escala.SeqRota \n"
                    + " INNER JOIN RotasPNT ON RotasPNT.Sequencia = Escala.SeqRota\n"
                    + "                   AND RotasPNT.Data = " + data + " \n"
                    + " WHERE Escala.Codfil = " + codfil + " \n"
                    + " AND (MatrChe = " + matricula + " OR MatrMot = " + matricula + ") \n"
                    + " AND Rotas.DtFim >= " + data + "; ");
        }
    }

    /**
     * Busca a escala do veículo para determinada data.
     *
     * @param data
     * @param veiculo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Escala escalaVeiculo(String data, String veiculo, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Escala.Veiculo, Escala.SeqRota, Escala.Rota, Escala.Hora4 from Escala join Rotas ON Escala.Seqrota = Rotas.Sequencia\n"
                    + " where Escala.Data = ? and Escala.Veiculo = ? and Rotas.flag_excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(veiculo);
            consulta.select();
            Escala retorno = null;
            if (consulta.Proximo()) {
                retorno = new Escala();
                retorno.setSeqRota(consulta.getString("SeqRota"));
                retorno.setVeiculo(consulta.getString("Veiculo"));
                retorno.setRota(consulta.getString("Rota"));
                retorno.setHora4(consulta.getString("Hora4"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EscalaDao.escalaVeiculo - " + e.getMessage() + "\r\n"
                    + "Select Veiculo, SeqRota, Rota, Hora4 from Escala \n"
                    + " where Data = " + data + " AND Veiculo = " + veiculo);
        }
    }

    public List<EscalaPessoaDTO> listaPaginadaEscala(
            int primeiro,
            int linhas,
            String codFil,
            String data,
            boolean excluded,
            String rotaId,
            String nome,
            String nred
    ) throws Exception {
        List<EscalaPessoaDTO> escalasDTO = new ArrayList();
        String sql = "SELECT * FROM (\n    "
                + "SELECT\n        "
                + "ROW_NUMBER ( ) OVER ( ORDER BY Escala.Rota, Escala.Hora1 DESC) AS RowNum,\n        "
                + "Escala.Rota 'Escala.Rota',\n        "
                + "Escala.Data 'Escala.Data',\n        "
                + "Escala.CodFil 'Escala.CodFil',\n        "
                + "Escala.Hora1 'Escala.Hora1',\n        "
                + "Escala.Hora2 'Escala.Hora2',\n        "
                + "Escala.Hora3 'Escala.Hora3',\n        "
                + "Escala.Hora4 'Escala.Hora4',\n        "
                + "Escala.HsInterv 'Escala.HsInterv',\n        "
                + "Escala.HsTot 'Escala.HsTot',\n        "
                + "Escala.MatrMot 'Escala.MatrMot',\n        "
                + "Escala.HrMot 'Escala.HrMot',\n        "
                + "Escala.MatrChe 'Escala.MatrChe',\n        "
                + "Escala.HrChe 'Escala.HrChe',\n        "
                + "Escala.MatrVig1 'Escala.MatrVig1',\n        "
                + "Escala.HrVig1 'Escala.HrVig1',\n        "
                + "Escala.MatrVig2 'Escala.MatrVig2',\n        "
                + "Escala.HrVig2 'Escala.HrVig2',\n        "
                + "Escala.MatrVig3 'Escala.MatrVig3',\n        "
                + "Escala.HrVig3 'Escala.HrVig3',\n        "
                + "Escala.CodPessoaSup 'Escala.CodPessoaSup',\n        "
                + "Escala.Veiculo 'Escala.Veiculo',\n        "
                + "Escala.SeqRota 'Escala.SeqRota',\n        "
                + "Escala.Situacao 'Escala.Situacao',\n        "
                + "Escala.DtUltAckMob 'Escala.DtUltAckMob',\n        "
                + "Escala.HrUltAckMob 'Escala.HrUltAckMob',\n        "
                + "Escala.Operador 'Escala.Operador',\n        "
                + "Escala.Dt_Alter 'Escala.Dt_Alter',\n        "
                + "Escala.Hr_Alter 'Escala.Hr_Alter',\n        "
                + "Funcion1.Nome_Guer NomeMot,\n        "
                + "Funcion1.CodPonto RegMot,\n        "
                + "Funcion1.Fone1,\n        "
                + "Funcion2.Nome_Guer NomeChe,\n        "
                + "Funcion2.CodPonto RegChe,\n        "
                + "Funcion2.Fone1 Fone2,\n        "
                + "Funcion3.Nome_Guer NomeVig1,\n        "
                + "Funcion3.CodPonto RegVig1,\n        "
                + "Funcion3.Fone1 Fone3,\n        "
                + "Funcion4.Nome_Guer NomeVig2,\n        "
                + "Funcion4.CodPonto RegVig2,\n        "
                + "Funcion4.Fone1 Fone4,\n        "
                + "Funcion5.Nome_Guer NomeVig3,\n        "
                + "Funcion5.CodPonto RegVig3,\n        "
                + "Funcion5.Fone1 Fone5,\n        "
                + "Rotas.TpVeic,\n        "
                + "Rotas.Flag_excl,\n        "
                + "Veiculos.Placa \n"
                + "FROM Escala\n"
                + "LEFT JOIN Rotas            ON  Escala.SeqRota   = Rotas.Sequencia \n"
                + "LEFT JOIN Veiculos         ON  Veiculos.Numero  = Escala.Veiculo \n"
                + "LEFT JOIN Filiais          ON  Escala.CodFil    = Filiais.CodFil \n"
                + "LEFT JOIN Funcion Funcion1 ON (Escala.MatrMot   = Funcion1.Matr) \n"
                + "LEFT JOIN Funcion Funcion2 ON (Escala.MatrChe   = Funcion2.Matr) \n"
                + "LEFT JOIN Funcion Funcion3 ON (Escala.MatrVig1  = Funcion3.Matr) \n"
                + "LEFT JOIN Funcion Funcion4 ON (Escala.MatrVig2  = Funcion4.Matr) \n"
                + "LEFT JOIN Funcion Funcion5 ON (Escala.MatrVig3  = Funcion5.Matr) \n"
                + "WHERE\n"
                + "    Escala.Data = ? \n"
                + "    AND Rotas.TpVeic <> 'S'\n";

        if (excluded) {
            sql += "    AND Rotas.flag_excl <> '*' \n";
        }
        if (codFil != null) {
            sql += "    AND Escala.CodFil = ? \n";
        }
        if (rotaId != null) {
            sql += "    AND Escala.Rota LIKE ? \n";
        }
        if (nome != null) {
            sql += "    AND ( Funcion1.Nome LIKE ? \n"
                    + "        OR Funcion2.Nome LIKE ? \n"
                    + "        OR Funcion3.Nome LIKE ? \n"
                    + "        OR Funcion4.Nome LIKE ? \n"
                    + "        OR Funcion5.Nome LIKE ? \n    )\n";
        }
        if (nred != null) {
            sql += "    AND ( Funcion1.Nome_Guer LIKE ? \n"
                    + "        OR Funcion2.Nome_Guer LIKE ? \n"
                    + "        OR Funcion3.Nome_Guer LIKE ? \n"
                    + "        OR Funcion4.Nome_Guer LIKE ? \n"
                    + "        OR Funcion5.Nome_Guer LIKE ? \n    )\n";
        }

        sql += ") AS RowConstrainedResult\n"
                + "WHERE\n"
                + "    RowNum >= ? \n"
                + "    AND RowNum < ? \n"
                + "ORDER BY RowNum;";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (codFil != null) {
                consulta.setBigDecimal(codFil);
            }

            if (rotaId != null) {
                consulta.setString("%" + rotaId + "%");
            }
            if (nome != null) {
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
            }
            if (nred != null) {
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            while (consulta.Proximo()) {
                Escala escala = new Escala();
                Funcion motorista = new Funcion();
                Funcion chefeEquipe = new Funcion();
                Funcion vigilante1 = new Funcion();
                Funcion vigilante2 = new Funcion();
                Funcion vigilante3 = new Funcion();
                Rotas rota = new Rotas();
                Veiculos veiculo = new Veiculos();
                EscalaPessoaDTO dto = new EscalaPessoaDTO();

                escala.setRota(consulta.getString("Escala.Rota"));
                escala.setData(consulta.getString("Escala.Data"));
                escala.setCodFil(consulta.getString("Escala.CodFil"));
                escala.setHora1(consulta.getString("Escala.Hora1"));
                escala.setHora2(consulta.getString("Escala.Hora2"));
                escala.setHora3(consulta.getString("Escala.Hora3"));
                escala.setHora4(consulta.getString("Escala.Hora4"));
                escala.setHsInterv(consulta.getString("Escala.HsInterv"));
                escala.setHsTot(consulta.getString("Escala.HsTot"));
                escala.setMatrMot(consulta.getString("Escala.MatrMot"));
                escala.setHrMot(consulta.getString("Escala.HrMot"));
                escala.setMatrChe(consulta.getString("Escala.MatrChe"));
                escala.setHrChe(consulta.getString("Escala.HrChe"));
                escala.setMatrVig1(consulta.getString("Escala.MatrVig1"));
                escala.setHrVig1(consulta.getString("Escala.HrVig1"));
                escala.setMatrVig2(consulta.getString("Escala.MatrVig2"));
                escala.setHrVig2(consulta.getString("Escala.HrVig2"));
                escala.setMatrVig3(consulta.getString("Escala.MatrVig3"));
                escala.setHrVig3(consulta.getString("Escala.HrVig3"));
                escala.setCodPessoaSup(consulta.getString("Escala.CodPessoaSup"));
                escala.setVeiculo(consulta.getString("Escala.Veiculo"));
                escala.setSeqRota(consulta.getString("Escala.SeqRota"));
                escala.setSituacao(consulta.getString("Escala.Situacao"));
                escala.setDtUltAckMob(consulta.getLocalDate("Escala.DtUltAckMob"));
                escala.setHrUltAckMob(consulta.getString("Escala.HrUltAckMob"));
                escala.setOperador(consulta.getString("Escala.Operador"));
                escala.setDt_Alter(consulta.getLocalDate("Escala.Dt_Alter"));
                escala.setHr_Alter(consulta.getString("Escala.Hr_Alter"));

                motorista.setNome(consulta.getString("NomeMot"));
                motorista.setMatr(consulta.getString("RegMot"));
                motorista.setFone1(consulta.getString("Fone1"));
                chefeEquipe.setNome(consulta.getString("NomeChe"));
                chefeEquipe.setMatr(consulta.getString("RegChe"));
                chefeEquipe.setFone1(consulta.getString("Fone2"));
                vigilante1.setNome(consulta.getString("NomeVig1"));
                vigilante1.setMatr(consulta.getString("RegVig1"));
                vigilante1.setFone1(consulta.getString("Fone3"));
                vigilante2.setNome(consulta.getString("NomeVig2"));
                vigilante2.setMatr(consulta.getString("RegVig2"));
                vigilante2.setFone1(consulta.getString("Fone4"));
                vigilante3.setNome(consulta.getString("NomeVig3"));
                vigilante3.setMatr(consulta.getString("RegVig3"));
                vigilante3.setFone1(consulta.getString("Fone5"));
                rota.setTpVeic(consulta.getString("TpVeic"));
                rota.setFlag_Excl(consulta.getString("Flag_excl"));
                veiculo.setPlaca(consulta.getString("Placa"));

                dto.setEscala(escala);
                dto.setMotorista(motorista);
                dto.setChefeEquipe(chefeEquipe);
                dto.setVigilante1(vigilante1);
                dto.setVigilante2(vigilante2);
                dto.setVigilante3(vigilante3);
                dto.setRota(rota);
                dto.setVeiculo(veiculo);
                escalasDTO.add(dto);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return escalasDTO;
    }

    public int contagemEscalaPessoa(
            String codFil,
            String data,
            boolean excluded,
            String rotaId,
            String nome,
            String nred) throws Exception {
        String sql = "SELECT COUNT(*) AS total\n"
                + "FROM Escala\n"
                + "LEFT JOIN Rotas            ON  Escala.SeqRota   = Rotas.Sequencia\n"
                + "LEFT JOIN Veiculos         ON  Veiculos.Numero  = Escala.Veiculo\n"
                + "LEFT JOIN Filiais          ON  Escala.CodFil    = Filiais.CodFil\n"
                + "LEFT JOIN Funcion Funcion1 ON (Escala.MatrMot   = Funcion1.Matr)\n"
                + "LEFT JOIN Funcion Funcion2 ON (Escala.MatrChe   = Funcion2.Matr)\n"
                + "LEFT JOIN Funcion Funcion3 ON (Escala.MatrVig1  = Funcion3.Matr)\n"
                + "LEFT JOIN Funcion Funcion4 ON (Escala.MatrVig2  = Funcion4.Matr)\n"
                + "LEFT JOIN Funcion Funcion5 ON (Escala.MatrVig3  = Funcion5.Matr)\n"
                + "WHERE Escala.Data = ? \n"
                + "    AND Rotas.TpVeic <> 'S'\n";

        if (excluded) {
            sql += "    AND Rotas.flag_excl <> '*' \n";
        }
        if (codFil != null) {
            sql += "    AND Escala.CodFil = ? \n";
        }
        if (rotaId != null) {
            sql += "    AND Escala.Rota LIKE ? \n";
        }
        if (nome != null) {
            sql += "    AND ( Funcion1.Nome LIKE ? \n"
                    + "        OR Funcion2.Nome LIKE ? \n"
                    + "        OR Funcion3.Nome LIKE ? \n"
                    + "        OR Funcion4.Nome LIKE ? \n"
                    + "        OR Funcion5.Nome LIKE ? \n    )\n";
        }
        if (nred != null) {
            sql += "    AND ( Funcion1.Nome_Guer LIKE ? \n"
                    + "        OR Funcion2.Nome_Guer LIKE ? \n"
                    + "        OR Funcion3.Nome_Guer LIKE ? \n"
                    + "        OR Funcion4.Nome_Guer LIKE ? \n"
                    + "        OR Funcion5.Nome_Guer LIKE ? \n    )\n";
        }

        Consulta consulta = null;
        int total = 0;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            if (codFil != null) {
                consulta.setBigDecimal(codFil);
            }

            if (rotaId != null) {
                consulta.setString("%" + rotaId + "%");
            }
            if (nome != null) {
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
                consulta.setString("%" + nome + "%");
            }
            if (nred != null) {
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
                consulta.setString("%" + nred + "%");
            }

            consulta.select();
            if (consulta.Proximo()) {
                total = consulta.getInt("total");
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return total;
    }
}
