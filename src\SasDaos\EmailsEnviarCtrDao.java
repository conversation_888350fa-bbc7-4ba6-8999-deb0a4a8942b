/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.EmailsEnviarCtr;

/**
 *
 * <AUTHOR>
 */
public class EmailsEnviarCtrDao {

    /**
     *
     * @param email
     * @param persistencia
     * @throws java.lang.Exception
     */
    public void inserir(EmailsEnviarCtr email, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into emailsenviarctr "
                    + " (sequencia, tipodoc, chave, data, hora, param) "
                    + " values ((select MAX(sequencia) + 1 seq from emailsenviarctr),?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(email.getTipoDoc());
            consulta.setString(email.getChave());
            consulta.setString(email.getData());
            consulta.setString(email.getHora());
            consulta.setString(email.getParam());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EmailsEnviarCtrDao.inserir - " + e.getMessage() + "\r\n"
                    + " insert into emailsenviarctr "
                    + " (sequencia, tipodoc, chave, data, hora, param) "
                    + " values ((select MAX(sequencia) + 1 seq from emailsenviarctr)," + email.getTipoDoc() + "," + email.getChave() + ","
                    + email.getData() + "," + email.getHora() + "," + email.getParam() + ")");
        }
    }
}
