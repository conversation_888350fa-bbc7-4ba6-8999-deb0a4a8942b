package SasLibrary;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;

/**
 *
 * <AUTHOR>
 */
public class BatidaPontoEletronico {

    /**
     * Faz a batida de ponto eletrônico
     *
     * @param persistencia - conexão com o banco
     * @param Codfil - Codfil atual
     * @param Senha - Senha do usuário PWWeb da pessoa
     * @param Matricula - Matricula do funcionário
     * @param Latitude - latitude da batida de ponto
     * @param Longitude - longitude da batida de ponto
     * @param Precisao - raio em metros da precisão da posição
     * @return - Retorna uma String com:
     * SUCESSO<bat></bat><nome_guer></nome_guer><dtcompet></dtcompet><dtatual></dtatual><hratual></hratual>
     * <coderro>1</coderro> FALHA<e>Senha Nao Cadastrada
     * <coderro>2</coderro> FALHA<e>Login/Senha Incorreto
     * <coderro>3</coderro> Falha<e>Ja possui batida neste horario
     * <coderro>4</coderro> Falha<e>Ja possui 4 batidas
     * <coderro>5</coderro>
     * Falha<nome_guer></nome_guer><dtcompet></dtcompet><hrcompet></hrcompet><dtatual></dtatual><hratual></hratual><e>Ja
     * existe uma batida registrada a menos de 15 minutos.
     * <coderro>6</coderro> FALHA<e>Matricula nao Cadastrada"; Legenda <bat> -
     * número da batida 1,2,3,4
     * <nome_guer> - nome de guerra
     * <dtcompet> - data de competência da batida
     * <hrcompet> - horário da última batida
     * <dtatual> - data atual usada para bater o ponto
     * <hratual> - hora atual usada para bater o ponto
     * @throws Exception - Pode gerar uma Exception
     */
    public static String BaterPonto(Persistencia persistencia, String Codfil, String Senha, String Matricula, String Latitude, String Longitude, String Precisao, String Dtatual, String Hratual) throws Exception {
        try {
            Consulta consultponto;
            boolean inserirHoje = true;
            boolean existeFuncionario = false;
            int difbatidas, i;
            int batida[] = new int[6];
            String dtcompet[] = new String[6];
            String hrbatida[] = new String[6];
            String nome_guer = "";
            String sql;
            String dtatual, hratual;
            String difanterior;
            int diftotal;
            Horaminuto conversor = new Horaminuto();
            int codfil = 1;

            dtatual = Dtatual;
            hratual = Hratual;
            //verifica senha digitada
            if (("".equals(Senha)) || ("".equals(Matricula))) {
                return "FALHA<coderro>2</coderro><e>Login/Senha Incorreto";
            }
            sql = "select pessoa.pwweb,funcion.nome_guer, funcion.codfil from pessoa "
                    + " left join funcion on funcion.matr=pessoa.matr"
                    + " where pessoa.matr=?";
            consultponto = new Consulta(sql, persistencia);
            consultponto.setString(Matricula);
            consultponto.select();
            while (consultponto.Proximo()) {
                existeFuncionario = true;
                if (consultponto.getString(1) == null) {
                    return "FALHA<coderro>1</coderro><e>Senha Nao Cadastrada";
                } else if (!Senha.equals(consultponto.getString(1))) {
                    return "FALHA<coderro>2</coderro><e>Login/Senha Incorreto";
                } else {
                    nome_guer = consultponto.getString(2);
                    codfil = Integer.parseInt(consultponto.getString(3).substring(0, consultponto.getString(3).indexOf(".")));
                }
            }
            if (!existeFuncionario) {
                return "FALHA<coderro>6</coderro><e>Matricula nao Cadastrada";
            }
            consultponto.Close();
            //tratando questoes de fusohorario
            sql = "select filial_PDR,fusohorario from paramet where filial_PDR=?";
            Consulta consultfuso = new Consulta(sql, persistencia);
            consultfuso.setFloat(codfil);
            consultfuso.select();
            int Fuso = 0;
            while (consultfuso.Proximo()) {
                Fuso = consultfuso.getInt("fusohorario"); //pega o fuso horario do parametro pode ser positivo ou negativo
            }
            conversor.setHoras(hratual, Fuso + ":00"); //colocar o :00 dos minutos que a classe pede
            if (conversor.iSomaHora1Hora2min() < 0) {//quando a diferença de fuso fizer a data voltar
                dtatual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getData(dtatual, -1, "yyyyMMdd");
            } else if (conversor.iSomaHora1Hora2min() > 1439) { //quando a diferença de fuso fizer a a data avançar
                dtatual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getData(dtatual, 1, "yyyyMMdd");
            }
            hratual = conversor.SomaHora1Hora2(); //pega a hora somada ao fuso

            //trata o tipo de ponto
            sql = "select top 4 dtcompet,batida,hora from rhponto "
                    + " where rhponto.matr=? order by dtcompet desc";
            consultponto = new Consulta(sql, persistencia);
            consultponto.setString(Matricula);
            consultponto.select();
            i = 0;
            while (consultponto.Proximo()) {
                dtcompet[i] = consultponto.getString(1);
                batida[i] = consultponto.getInt(2);
                hrbatida[i] = consultponto.getString(3);
                i++;
            }
            consultponto.Close();
            if (i > 0) {//tem alguma batida registrada
                difbatidas = (int) br.com.sasw.pacotesuteis.utilidades.DataAtual.calcular(dtcompet[0], dtatual, "yyyyMMdd");
                if (difbatidas > 1) {//mais de um dia de diferenca entre o dia de hoje e a ultima batida
                    inserirHoje = true;
                    batida[4] = 1;
                } else if (difbatidas == 1) {//1 dia entre a ultima batida e a data de hoje
                    if (batida[0] == 4) { //dia anterior tem 4 batidas
                        inserirHoje = true;
                        batida[4] = 1;
                    } else if (batida[0] == 3) {//dia anterior tem 3 batidas
                        conversor.setHoras(hrbatida[0], "2359");
                        difanterior = conversor.DifHora1Hora2();
                        conversor.setHoras(difanterior, hratual);
                        diftotal = conversor.iSomaHora1Hora2min();
                        if (diftotal > 360) {//ultima batida maior que 6 horas
                            inserirHoje = true;
                            batida[4] = 1;
                        } else {//ultima batida menor que 6 horas
                            inserirHoje = false;
                            batida[4] = 4;
                        }
                    } else if (batida[0] == 2) {//dia anterior tem 2 batidas
                        conversor.setHoras(hrbatida[0], "2359");
                        difanterior = conversor.DifHora1Hora2();
                        conversor.setHoras(difanterior, hratual);
                        diftotal = conversor.iSomaHora1Hora2min();
                        if (diftotal > 360) {//ultima batida maior que 6 horas
                            inserirHoje = true;
                            batida[4] = 1;
                        } else {//ultima batida menor que 6 horas
                            inserirHoje = false;
                            batida[4] = 3;
                        }
                    } else if (batida[0] == 1) {//dia anterior tem 1 batida
                        conversor.setHoras(hrbatida[0], "2359");
                        difanterior = conversor.DifHora1Hora2();
                        conversor.setHoras(difanterior, hratual);
                        diftotal = conversor.iSomaHora1Hora2min();
                        if (diftotal > 600) { //ultima batida maior que 10 horas
                            inserirHoje = true;
                            batida[4] = 1;
                        } else {//ultima batida menor que 10 horas
                            inserirHoje = false;
                            batida[4] = 2;
                        }
                    }
                } else {//ultima batida é na data de hoje
                    inserirHoje = true;
                    batida[4] = batida[0] + 1;//soma 1 na ultima batida 
                }
            } else {//não possui batidas anteriores
                inserirHoje = true;
                dtcompet[4] = dtatual;
                hrbatida[0] = "00:00";
                batida[4] = 1;
            }
            if (inserirHoje) {//trata a data conforme as condições determinadas na processo anterior
                dtcompet[4] = dtatual;//dia de hoje
                if (batida[4] > 1) {
                    conversor.setHora1(hratual);
                    conversor.setHora2(hrbatida[0]);
                    int dif = conversor.iDifHora1Hora2min();
                    if (dif <= 15) {
                        batida[4] = -1;
                    }
                }
            } else {
                dtcompet[4] = br.com.sasw.pacotesuteis.utilidades.DataAtual.getData(dtatual, -1, "yyyyMMdd");//competencia de ontem
            }
            if ((batida[4] <= 4) && batida[4] > 0) {
                sql = "insert into rhponto (Matr,DtCompet,Batida,Tipo,Hora,DtBatida,Operador,Dt_Alter,Hr_Alter) values "
                        + " (" + Matricula + ",'" + dtcompet[4] + "'," + batida[4] + "," + batida[4] + ",'" + hratual + "','"
                        + dtatual + "','" + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString("MOB" + nome_guer, 0, 10) + "','" + dtatual + "','" + hratual + "')";
//                persistencia.executaSql(sql);

                return "SUCESSO<bat>" + batida[4] + "</bat><nome_guer>" + nome_guer + "</nome_guer><dtcompet>" + dtcompet[4] + "</dtcompet><dtatual>" + dtatual + "</dtatual><hratual>" + hratual + "</hratual>";

            } else if (batida[4] < 0) {
                return "Falha<nome_guer>" + nome_guer + "</nome_guer><dtcompet>" + dtcompet[4] + "</dtcompet><hrcompet>" + hrbatida[0] + "</hrcompet><dtatual>" + dtatual + "</dtatual><hratual>" + hratual + "</hratual><coderro>5</coderro><e>Ja existe uma batida registrada a menos de 15 minutos";
            } else {
                return "Falha<coderro>4</coderro><e>Ja possui 4 batidas";
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }
}
