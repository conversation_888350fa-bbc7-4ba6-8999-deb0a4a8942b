package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;

/**
 *
 * <AUTHOR>
 */
public class ATMDao {

    public boolean grava_OBS_ATM(String OS, String CodFil, String Obs, int Sequencia, String matr,
            Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "Insert into ATMHist (OS,CodFil,Sequencia,Historico,Operador,Dt_Alter,Hr_Alter)"
                + " Values (?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(CodFil);
            consulta.setInt(Sequencia);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcento(Obs));
            consulta.setString(matr);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.Close();
            retorno = true;
        } catch (Exception e) {
            //logerro.Grava(e.getMessage()+" -- "+sql, getServletContext( ).getRealPath( "/msgerros.log" ));
            retorno = false;
        }
        return retorno;
    }

    public boolean grava_OBS_ATM(String OS, String CodFil, String Obs, int Sequencia, String matr,
            String dataAtual, String horaAtual, Persistencia persistencia) {
        boolean retorno;
        String sql;
        sql = "Insert into ATMHist (OS,CodFil,Sequencia,Historico,Operador,Dt_Alter,Hr_Alter)"
                + " Values (?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(OS);
            consulta.setString(CodFil);
            consulta.setInt(Sequencia);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcento(Obs));
            consulta.setString(matr);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);

            consulta.insert();
            consulta.Close();
            retorno = true;
        } catch (Exception e) {
            //logerro.Grava(e.getMessage()+" -- "+sql, getServletContext( ).getRealPath( "/msgerros.log" ));
            retorno = false;
        }
        return retorno;
    }

    public void atualizaATMInter(Persistencia persistencia, String matr, String osfat, String OS, String CodFil) throws Exception {

        String sql = "Update ATMInter set "
                + " Matr=?, DtFech=?, HrFech=?, Situacao=?, OSFat=?"
                + " where OS=?"
                + " and CodFil=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString("OK");
            consulta.setString(osfat);
            consulta.setString(OS);
            consulta.setString(CodFil);

            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMDao.atualizaATMInter - " + e.getMessage() + "\r\nUpdate ATMInter set "
                    + " Matr=" + matr + ", DtFech=?, HrFech=?, Situacao='OK', OSFat=" + osfat
                    + " where OS=" + OS
                    + " and CodFil=" + CodFil);
        }
    }

    public void atualizaATMInter(Persistencia persistencia, String matr, String osfat, String OS, String CodFil, String dataAtual, String horaAtual) throws Exception {

        String sql = "Update ATMInter set "
                + " Matr=?, DtFech=?, HrFech=?, Situacao=?, OSFat=?"
                + " where OS=?"
                + " and CodFil=?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString("OK");
            consulta.setString(osfat);
            consulta.setString(OS);
            consulta.setString(CodFil);

            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ATMDao.atualizaATMInter - " + e.getMessage() + "\r\nUpdate ATMInter set "
                    + " Matr=" + matr + ", DtFech=" + dataAtual + ", HrFech=" + horaAtual + ", Situacao='OK', OSFat=" + osfat
                    + " where OS=" + OS
                    + " and CodFil=" + CodFil);
        }
    }
}
