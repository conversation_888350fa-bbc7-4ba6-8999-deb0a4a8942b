package SasLibrary;

import Dados.Persistencia;
import SasBeans.BDRemotos;
import SasBeans.Paramet;
import SasBeans.Pessoa;
import SasBeans.PessoaLogin;
import SasDaos.BDRemotosDao;
import SasDaos.ParametDao;
import SasDaos.PessoaLoginDao;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoginWeb {

    public static List<BDRemotos> LoginWeb(List<Pessoa> listP, String Senha, Persistencia persistencia) throws Exception {
        try {
            if (listP.size() < 1) {
                throw new Exception("SemUsuario");
            } else if (!Senha.equals(listP.get(0).getPWWeb())) {
                throw new Exception("SemSenha");
            } else {
                List<PessoaLogin> listpl = PessoaLoginDao.getPessoaLogin(listP.get(0).getCodigo().toString(), persistencia);
                List<BDRemotos> listbd = new ArrayList();
                for (PessoaLogin lpl : listpl) {
                    listbd.add(BDRemotosDao.BuscaConexao(lpl.getBancoDados(), persistencia).get(0));
                }
                return listbd;
            }
        } catch (Exception e) {
            if (e.getMessage().equals("SemUsuario") || e.getMessage().equals("SemSenha")) {
                throw new Exception(e.getMessage());
            } else {
                throw new Exception("Falha ao logar - " + e.getMessage());
            }
        }
    }

    /**
     * Busca Parametro de conexão central do cliente, onde encontra-se os
     * usuário e senhas do Satellite
     *
     * @param param - parametro de conexão informado
     * @param persistencia - conexão ao banco de dados (informar o Central da
     * Sasw
     * @return
     * @throws Exception
     */
    public static String ParamCentral(String param, Persistencia persistencia) throws Exception {
        try {
            String param_usuario = "";
            ParametDao parametdao = new ParametDao();
            List<Paramet> lparamet = parametdao.getParamPadrao(param, persistencia);
            if (lparamet.isEmpty()) {
                throw new Exception("Parametro de conexão incorreto");
            } else {
                for (Paramet paramet : lparamet) {
                    param_usuario = paramet.getPath().toUpperCase();

                }
                if ("".equals(param_usuario)) {
                    param_usuario = param.toUpperCase();
                }
                return param_usuario;
            }

        } catch (Exception e) {
            throw new Exception("Falha ao buscar PARAM central do cliente \r\n" + e.getMessage());
        }
    }
}
