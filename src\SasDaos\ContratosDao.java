/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Contratos;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ContratosDao {

    public Contratos buscarContrato(String c, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Contratos WHERE Contrato = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(c);
            consulta.setString(codFil);
            consulta.select();

            Contratos contrato = null;
            if (consulta.Proximo()) {
                contrato = new Contratos();
                contrato.setCodFil(consulta.getString("codFil").replace(".0", ""));
                contrato.setGrpReajuste(consulta.getString("grpReajuste").replace(".0", ""));
                contrato.setGrpPagamento(consulta.getString("grpPagamento").replace(".0", ""));
                contrato.setTipo(consulta.getString("tipo"));
                contrato.setValidade(consulta.getString("validade"));
                contrato.setAssinatura(consulta.getString("assinatura"));
                contrato.setDescricao(consulta.getString("descricao"));
                contrato.setContrato(consulta.getString("contrato"));
                contrato.setSituacao(consulta.getString("situacao"));
                contrato.setIdentif(consulta.getString("identif"));
                contrato.setDt_Inicio(consulta.getString("Dt_Inicio"));
                contrato.setDt_Termino(consulta.getString("Dt_Termino"));
                contrato.setCliFat(consulta.getString("cliFat"));
                contrato.setContratoCli(consulta.getString("contratoCli"));
                contrato.setRefArq(consulta.getString("refArq"));
                contrato.setOBS(consulta.getString("obs"));
                contrato.setProcesso(consulta.getString("processo"));
                contrato.setOperIncl(consulta.getString("operIncl"));
                contrato.setDt_Incl(consulta.getString("dt_Incl"));
                contrato.setHr_Incl(consulta.getString("hr_Incl"));
                contrato.setOperador(consulta.getString("operador"));
                contrato.setDt_Alter(consulta.getString("dt_alter"));
                contrato.setHr_Alter(consulta.getString("hr_alter"));
            }
            consulta.Close();
            return contrato;
        } catch (Exception e) {
            throw new Exception("ContratosDAO.buscarContrato - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Contratos WHERE Contrato = " + c + " AND CodFil = " + codFil);
        }
    }

    public List<Contratos> buscarContratos(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM Contratos WHERE (Contrato like ? OR Descricao like ?) AND CodFil = ? ";
            List<Contratos> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.setString(codFil);
            consulta.select();

            Contratos contrato;
            while (consulta.Proximo()) {
                contrato = new Contratos();
                contrato.setCodFil(consulta.getString("codFil").replace(".0", ""));
                contrato.setGrpReajuste(consulta.getString("grpReajuste").replace(".0", ""));
                contrato.setGrpPagamento(consulta.getString("grpPagamento").replace(".0", ""));
                contrato.setTipo(consulta.getString("tipo"));
                contrato.setValidade(consulta.getString("validade"));
                contrato.setAssinatura(consulta.getString("assinatura"));
                contrato.setDescricao(consulta.getString("descricao"));
                contrato.setContrato(consulta.getString("contrato"));
                contrato.setSituacao(consulta.getString("situacao"));
                contrato.setIdentif(consulta.getString("identif"));
                contrato.setDt_Inicio(consulta.getString("Dt_Inicio"));
                contrato.setDt_Termino(consulta.getString("Dt_Termino"));
                contrato.setCliFat(consulta.getString("cliFat"));
                contrato.setContratoCli(consulta.getString("contratoCli"));
                contrato.setRefArq(consulta.getString("refArq"));
                contrato.setOBS(consulta.getString("obs"));
                contrato.setProcesso(consulta.getString("processo"));
                contrato.setOperIncl(consulta.getString("operIncl"));
                contrato.setDt_Incl(consulta.getString("dt_Incl"));
                contrato.setHr_Incl(consulta.getString("hr_Incl"));
                contrato.setOperador(consulta.getString("operador"));
                contrato.setDt_Alter(consulta.getString("dt_alter"));
                contrato.setHr_Alter(consulta.getString("hr_alter"));
                retorno.add(contrato);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContratosDAO.buscarContratos - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Contratos WHERE (Contrato like %" + query + "% OR Descricao like %" + query + "%) AND CodFil = " + codFil);
        }
    }

    /**
     * Obtem informações sobre o sequencial para ser gerado ao inserir o
     * contrato
     *
     * @param banco Banco do cliente
     * @param tipoContrato tipo de contrato "O - Outros"
     * @param persistencia Conexão com o banco de dados
     * @return sequencial gerado na consulta
     * @throws Exception
     */
    public String obterSequencial(String banco, String tipoContrato, Persistencia persistencia) throws Exception {
        String sequencial = "0";
        try {
            String sql = "Select "
                    + "Max(Isnull(Substring(Contrato,Isnull((Charindex('.',Contrato)+2),0),Len(Substring(Contrato,1,Isnull((Charindex('.',Contrato)+2),0)))),0))+1 sequencia"
                    + " from Contratos"
                    + " where Substring(Contrato,1,Isnull((Charindex('.',Contrato)-1),0)) = ?"
                    + "  and Substring(Contrato,Isnull((Charindex('.',Contrato)+1),0),1) = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(banco);
            consulta.setString(tipoContrato);
            consulta.select();

            while (consulta.Proximo()) {
                sequencial = consulta.getString("sequencia");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.obterSequencial - " + e.getMessage() + "\r\n"
                    + "Select "
                    + "Max(Isnull(Substring(Contrato,Isnull((Charindex('.',Contrato)+2),0),Len(Substring(Contrato,1,Isnull((Charindex('.',Contrato)+2),0)))),0))+1 sequencia"
                    + " from Contratos"
                    + " where Substring(Contrato,1,Isnull((Charindex('.',Contrato)-1),0)) = " + banco
                    + "  and Substring(Contrato,Isnull((Charindex('.',Contrato)+1),0),1) = " + tipoContrato);
        }
        return sequencial;
    }

    /**
     * Obtem informações sobre o sequencial para ser gerado ao inserir o
     * contrato
     *
     * @param banco Banco do cliente
     * @param tipoContrato tipo de contrato "O - Outros"
     * @param persistencia Conexão com o banco de dados
     * @return sequencial gerado na consulta
     * @throws Exception
     */
    public int obterProximoSequencial(
            String banco,
            String tipoContrato,
            Persistencia persistencia) throws Exception {
        int sequencial = 1;
        String sql = "WITH cte1 AS (\n"
                + "    SELECT\n"
                + "        Contrato,\n"
                + "        SUBSTRING ( Contrato, CHARINDEX( '.', Contrato ) + 2, LEN( Contrato ) - CHARINDEX( '.', Contrato ) ) AS caption\n"
                + "    FROM Contratos\n"
                + "),\n"
                + "cte2 AS (\n"
                + "    SELECT Contrato, caption,\n"
                + "    TRY_CAST(SUBSTRING(caption, PATINDEX('%[^0]%', caption + '.'), LEN(caption)) AS INT) AS numero"
                + "    FROM cte1\n"
                + ")\n"
                + "SELECT MAX(numero) + 1 AS sequencia FROM cte2\n"
                + "WHERE SUBSTRING ( Contrato, 1, CHARINDEX( '.', Contrato ) + 1 ) LIKE ?\n";
        Consulta consulta = new Consulta(sql, persistencia);

        try {
            consulta.setString(banco + "." + tipoContrato);
            consulta.select();

            while (consulta.Proximo()) {
                sequencial = consulta.getInt("sequencia");
            }
        } catch (Exception e) {
            throw new Exception("ContratosDAO.obterSequencial - " + e.getMessage() + "\r\n");
        } finally {
            consulta.Close();
        }

        return sequencial;
    }

    /**
     * Inserir o contrato na base e dados
     *
     * @param contrato Objeto do contrato
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void inserirRegistros(Contratos contrato, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Contratos (contrato,codfil,descricao,tipo,"
                    + "situacao,identif,validade,clifat,contratocli,refarq,"
                    + "grpreajuste, grppagamento,obs,assinatura,hr_incl,operador,"
                    + "hr_alter,operIncl,dt_incl,dt_alter,dt_inicio, dt_termino) VALUES "
                    + "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato.getContrato());
            consulta.setBigDecimal(contrato.getCodFil());
            consulta.setString(contrato.getDescricao());
            consulta.setString(contrato.getTipo() + "");
            consulta.setString(contrato.getSituacao());
            consulta.setString(contrato.getIdentif());
            consulta.setString(contrato.getValidade() + "");
            consulta.setString(contrato.getCliFat());
            consulta.setString(contrato.getContratoCli());
            consulta.setString(contrato.getRefArq());
            consulta.setBigDecimal(contrato.getGrpReajuste());
            consulta.setBigDecimal(contrato.getGrpPagamento());
            consulta.setString(contrato.getOBS());
            consulta.setString(contrato.getAssinatura() + "");
            consulta.setString(contrato.getHr_Incl());
            consulta.setString(contrato.getOperador());
            consulta.setString(contrato.getHr_Alter());
            consulta.setString(contrato.getOperIncl());

            consulta.setString(contrato.getDt_Incl());
            consulta.setString(contrato.getDt_Alter());
            consulta.setString(contrato.getDt_Inicio());
            consulta.setString(contrato.getDt_Termino());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.inserirRegistros - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Contratos (contrato,codfil,descricao,tipo,"
                    + "situacao,identif,validade,clifat,contratocli,refarq,"
                    + "grpreajuste, grppagamento,obs,assinatura,hr_incl,operador,"
                    + "hr_alter,operIncl,dt_incl,dt_alter,dt_inicio, dt_termino) VALUES "
                    + "(" + contrato.getContrato() + "," + contrato.getCodFil() + "," + contrato.getDescricao() + "," + contrato.getTipo() + ","
                    + contrato.getSituacao() + "," + contrato.getIdentif() + "," + contrato.getValidade() + "," + contrato.getCliFat() + ","
                    + contrato.getContratoCli() + "," + contrato.getRefArq() + "," + contrato.getGrpReajuste() + "," + contrato.getGrpPagamento() + ","
                    + contrato.getOBS() + "," + contrato.getAssinatura() + "" + "," + contrato.getHr_Incl() + "," + contrato.getOperador() + ","
                    + contrato.getHr_Alter() + "," + contrato.getOperIncl() + ",CONVERT(DATE, getDate()),"
                    + "CONVERT(DATE, getDate()),CONVERT(DATE, getDate()),"
                    + "CONVERT(DATE, getDate()))");
        }
    }

    public void inserirRegistros(Contratos contrato, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO Contratos (contrato,codfil,descricao,tipo,"
                    + "situacao,identif,validade,clifat,contratocli,refarq,"
                    + "grpreajuste, grppagamento,obs,assinatura,hr_incl,operador,"
                    + "hr_alter,operIncl,dt_incl,dt_alter,dt_inicio, dt_termino)"
                    + " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato.getContrato());
            consulta.setBigDecimal(contrato.getCodFil());
            consulta.setString(contrato.getDescricao());
            consulta.setString(contrato.getTipo() + "");
            consulta.setString(contrato.getSituacao());
            consulta.setString(contrato.getIdentif());
            consulta.setString(contrato.getValidade() + "");
            consulta.setString(contrato.getCliFat());
            consulta.setString(contrato.getContratoCli());
            consulta.setString(contrato.getRefArq());
            consulta.setBigDecimal(contrato.getGrpReajuste());
            consulta.setBigDecimal(contrato.getGrpPagamento());
            consulta.setString(contrato.getOBS());
            consulta.setString(contrato.getAssinatura() + "");
            consulta.setString(contrato.getHr_Incl());
            consulta.setString(contrato.getOperador());
            consulta.setString(contrato.getHr_Alter());
            consulta.setString(contrato.getOperIncl());
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.inserirRegistros - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Contratos (contrato,codfil,descricao,tipo,"
                    + "situacao,identif,validade,clifat,contratocli,refarq,"
                    + "grpreajuste, grppagamento,obs,assinatura,hr_incl,operador,"
                    + "hr_alter,operIncl,dt_incl,dt_alter,dt_inicio, dt_termino)"
                    + " VALUES (" + contrato.getContrato() + "," + contrato.getCodFil() + "," + contrato.getDescricao() + ","
                    + contrato.getTipo() + "" + "," + contrato.getSituacao() + "," + contrato.getIdentif() + "," + contrato.getValidade() + ","
                    + contrato.getCliFat() + "," + contrato.getContratoCli() + "," + contrato.getRefArq() + "," + contrato.getGrpReajuste() + ","
                    + contrato.getGrpPagamento() + "," + contrato.getOBS() + "," + contrato.getAssinatura() + "" + "," + contrato.getHr_Incl() + ","
                    + contrato.getOperador() + "," + contrato.getHr_Alter() + "," + contrato.getOperIncl() + "," + dataAtual + "," + dataAtual + ","
                    + dataAtual + "," + dataAtual + ")");
        }
    }

    public void editarContrato(Contratos contrato, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Contratos SET Descricao = ?, Tipo = ?, Situacao = ?, Identif = ?, Validade = ?, \n"
                    + " Dt_Inicio = ?, Dt_Termino = ?, CliFat = ?, ContratoCli = ?, RefArq = ?, GrpReajuste = ?, GrpPagamento = ?, \n"
                    + " OBS = ?, Assinatura = ?, Processo = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? \n"
                    + " WHERE Contrato = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato.getDescricao());
            consulta.setString(contrato.getTipo());
            consulta.setString(contrato.getSituacao());
            consulta.setString(contrato.getIdentif());
            consulta.setString(contrato.getValidade());
            consulta.setString(contrato.getDt_Inicio());
            consulta.setString(contrato.getDt_Termino());
            consulta.setString(contrato.getCliFat());
            consulta.setString(contrato.getContratoCli());
            consulta.setString(contrato.getRefArq());
            consulta.setString(contrato.getGrpReajuste());
            consulta.setString(contrato.getGrpPagamento());
            consulta.setString(contrato.getOBS());
            consulta.setString(contrato.getAssinatura());
            consulta.setString(contrato.getProcesso());
            consulta.setString(contrato.getOperador());
            consulta.setString(contrato.getDt_Alter());
            consulta.setString(contrato.getHr_Alter());
            consulta.setString(contrato.getContrato());
            consulta.setString(contrato.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.editarContrato - " + e.getMessage() + "\r\n"
                    + " UPDATE Contratos SET Descricao = " + contrato.getDescricao() + ", Tipo = " + contrato.getTipo() + ", Situacao = " + contrato.getSituacao() + ", \n"
                    + " Identif = " + contrato.getIdentif() + ", Validade = " + contrato.getValidade() + ", Dt_Inicio = " + contrato.getDt_Inicio() + ", \n"
                    + " Dt_Termino = " + contrato.getDt_Termino() + ", CliFat = " + contrato.getCliFat() + ", ContratoCli = " + contrato.getContratoCli() + ", \n"
                    + " RefArq = " + contrato.getRefArq() + ", GrpReajuste = " + contrato.getGrpReajuste() + ", GrpPagamento = " + contrato.getGrpPagamento() + ", \n"
                    + " OBS = " + contrato.getOBS() + ", Assinatura = " + contrato.getAssinatura() + ", Processo = " + contrato.getProcesso() + ", \n"
                    + " Operador = " + contrato.getOperador() + ", Dt_Alter = " + contrato.getDt_Alter() + ", Hr_Alter = " + contrato.getHr_Alter() + " \n"
                    + " WHERE Contrato = " + contrato.getContrato() + " AND CodFil = " + contrato.getCodFil() + ")");
        }
    }

    /**
     * Verifica a existencia do cliente com o contrato
     *
     * @param codCli codigo do cliente
     * @param codFil
     * @param persistencia conexão com o banco de dados
     * @return verifica existencia do contrato para o cliente
     * @throws Exception
     */
    public boolean existeClienteContrato(String codCli, String codFil, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM contratos WHERE clifat = ? AND codFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.setString(codFil);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.existeClienteContrato - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM contratos WHERE clifat = " + codCli + " AND codFil = " + codFil);
        }
        return existe;
    }

    /**
     * Obtem informações sobre o contrato do cliente caso exista
     *
     * @param codCli Codigo do cliente
     * @param codfil Codigo da filia
     * @param persistencia Conexão com o banco de dados
     * @return contrato
     * @throws Exception
     */
    public String obterContrato(String codCli, String codfil, Persistencia persistencia) throws Exception {
        String contrato = "";
        try {
            String sql = "SELECT contrato FROM contratos WHERE codfil = ? AND clifat = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(codCli);
            consulta.select();

            while (consulta.Proximo()) {
                contrato = consulta.getString("contrato");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.obterContrato - " + e.getMessage() + "\r\n"
                    + "SELECT contrato FROM contratos WHERE codfil = " + codfil + " AND clifat = " + codCli);
        }
        return contrato;
    }

    /**
     * Verifica a existencia do contrrato
     *
     * @param contrato Numero do contrato
     * @param persistencia conexão com o banco de dadoss
     * @return se existe
     * @throws Exception
     */
    public boolean existeContrato(String contrato, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM contratos WHERE contrato = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contrato);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContratosDAO.existeContrato - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM contratos WHERE contrato = " + contrato);
        }
        return existe;
    }

    public List<Contratos> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Contratos> retorno = new ArrayList<>();
            String sql = " SELECT * FROM(\n"
                    + " SELECT ROW_NUMBER() OVER ( ORDER BY Contratos.Contrato ) AS RowNum,\n"
                    + "    Contratos.*,\n"
                    + "    CASE WHEN Contratos.Situacao = 'A' THEN 'Ativo' ELSE 'Cancelado' END SituacaoDesc,\n"
                    + "    Clientes.NRed, Clientes.Nome, Clientes.Banco, Clientes.Ende Endereco, Clientes.Cidade, Clientes.Estado,\n"
                    + "    Clientes.CEP, Clientes.Fone1, Clientes.Email, Clientes.Contato,Clientes.CGC CNPJ \n"
                    + " FROM Contratos\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Contratos.CliFat\n"
                    + "                  AND Clientes.CodFil = Contratos.CodFil\n"
                    + "WHERE Contratos.CodFil IS NOT NULL\n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql += ") RowConstraint\n"
                    + "WHERE RowNum >= ?\n"
                    + " AND RowNum < ? ";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            Contratos contrato;
            while (consulta.Proximo()) {
                contrato = new Contratos();
                contrato.setCodFil(consulta.getString("codFil").replace(".0", ""));
                contrato.setGrpReajuste(consulta.getString("grpReajuste").replace(".0", ""));
                contrato.setGrpPagamento(consulta.getString("grpPagamento").replace(".0", ""));
                contrato.setTipo(consulta.getString("tipo"));
                contrato.setValidade(consulta.getString("validade"));
                contrato.setAssinatura(consulta.getString("assinatura"));
                contrato.setDescricao(consulta.getString("descricao"));
                contrato.setContrato(consulta.getString("contrato"));
                contrato.setSituacao(consulta.getString("situacao"));
                contrato.setSituacaoDesc(consulta.getString("SituacaoDesc"));
                contrato.setIdentif(consulta.getString("identif"));
                contrato.setDt_Inicio(consulta.getString("Dt_Inicio"));
                contrato.setDt_Termino(consulta.getString("Dt_Termino"));
                contrato.setCliFat(consulta.getString("cliFat"));
                contrato.setContratoCli(consulta.getString("contratoCli"));
                contrato.setRefArq(consulta.getString("refArq"));
                contrato.setOBS(consulta.getString("obs"));
                contrato.setProcesso(consulta.getString("processo"));
                contrato.setOperIncl(consulta.getString("operIncl"));
                contrato.setDt_Incl(consulta.getString("dt_Incl"));
                contrato.setHr_Incl(consulta.getString("hr_Incl"));
                contrato.setOperador(consulta.getString("operador"));
                contrato.setDt_Alter(consulta.getString("dt_alter"));
                contrato.setHr_Alter(consulta.getString("hr_alter"));

                contrato.setNRed(consulta.getString("NRed"));
                contrato.setNome(consulta.getString("Nome"));
                contrato.setBanco(consulta.getString("Banco"));
                contrato.setEndereco(consulta.getString("Endereco"));
                contrato.setCidade(consulta.getString("Cidade"));
                contrato.setEstado(consulta.getString("Estado"));
                contrato.setCEP(consulta.getString("CEP"));
                contrato.setFone1(consulta.getString("Fone1"));
                contrato.setEmail(consulta.getString("Email"));
                contrato.setContato(consulta.getString("Contato"));
                contrato.setCNPJ(consulta.getString("CNPJ"));

                retorno.add(contrato);
            }
            consulta.close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("ContratosDAO.listaPaginada - " + e.getMessage());
        }
    }

    public Integer totalContratos(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) Total \n"
                    + " FROM Contratos \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Contratos.CliFat\n"
                    + "                  AND Clientes.CodFil = Contratos.CodFil\n"
                    + " WHERE Contratos.CodFil IS NOT NULL\n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContratosDao.totalContratos - " + e.getMessage());
        }
    }
}
