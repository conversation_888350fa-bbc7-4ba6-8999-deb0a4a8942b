/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.TbVal;
import SasDaos.TbValDao;

/**
 *
 * <AUTHOR>
 */
public class Motivos {

    public static void InsereMotivos(String sMatr, String Motivo, String Operador, String Senha, Persistencia persistencia) throws Exception {
        Integer seq = null;
        String data_atual = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL");
        String hora = br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA");

        TbVal oTbVal = new TbVal();

        TbValDao oTbValDao = new TbValDao();
        //valida login e senha do usuário
//        List<LoginRota> list_lrota;
//        try {
//            list_lrota = LoginDao.LoginRota(sMatr, <PERSON><PERSON>, persistencia);
//
//        } catch (Exception e) {
//            throw new Exception("Falha no login - " + e.getMessage());
//        }
//        for (LoginRota list_lrota1 : list_lrota) {
//            if (!list_lrota1.getPessoa().getPWWeb().equals(Senha)) {
//                seq = null;
//            } else {
        boolean repete = false;
        int cont = 1;

        oTbVal.setTabela(311);
        oTbVal.setDescricao(Motivo);
        oTbVal.setOperador(Operador);
        oTbVal.setDt_Alter(data_atual);
        oTbVal.setHr_Alter(hora);

        while (!repete) {
            //Seleciona sequencia incrementada
            seq = oTbValDao.getCodTBVal(311, persistencia);
            oTbVal.setCodigo(seq);
            repete = oTbValDao.gravaTbVal(oTbVal, persistencia);
            if (cont == 10) {
                repete = true;
            }
            cont++;
        }
//            }
//        }
    }

    public static void InsereMotivos(String sMatr, String Motivo, String Operador, String Senha, String data_atual, String hora, Persistencia persistencia) throws Exception {
        Integer seq = null;

        TbVal oTbVal = new TbVal();

        TbValDao oTbValDao = new TbValDao();
        boolean repete = false;
        int cont = 1;

        oTbVal.setTabela(311);
        oTbVal.setDescricao(Motivo);
        oTbVal.setOperador(Operador);
        oTbVal.setDt_Alter(data_atual);
        oTbVal.setHr_Alter(hora);

        while (!repete) {
            //Seleciona sequencia incrementada
            seq = oTbValDao.getCodTBVal(311, persistencia);
            oTbVal.setCodigo(seq);
            repete = oTbValDao.gravaTbVal(oTbVal, persistencia);
            if (cont == 10) {
                repete = true;
            }
            cont++;
        }
    }
}
