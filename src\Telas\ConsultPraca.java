package Telas;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import java.awt.Toolkit;
import java.awt.event.KeyEvent;
import javax.swing.table.DefaultTableModel;
import pacotesuteis.Main;

/**
 *
 * <AUTHOR>
 */
public class ConsultPraca extends javax.swing.JFrame {

    /**
     * Creates new form ConsultFilial
     */
    private Persistencia_OLD persistpraca;
    private String param;

    public ConsultPraca() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        jScrollPane1 = new javax.swing.JScrollPane();
        ConsultFilial = new javax.swing.JTable();
        nomePesq = new javax.swing.JTextField();
        TitPesq = new javax.swing.JLabel();
        jButton1 = new javax.swing.JButton();
        Consulta1 = new javax.swing.JRadioButton();
        Consulta2 = new javax.swing.JRadioButton();
        Tconsult = new javax.swing.JLabel();
        Consulta3 = new javax.swing.JRadioButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Funcionários");
        setAlwaysOnTop(true);
        setBackground(new java.awt.Color(20, 91, 155));
        setResizable(false);
        addWindowListener(new java.awt.event.WindowAdapter() {
            public void windowClosing(java.awt.event.WindowEvent evt) {
                formWindowClosing(evt);
            }
            public void windowOpened(java.awt.event.WindowEvent evt) {
                formWindowOpened(evt);
            }
        });

        ConsultFilial.setAutoCreateRowSorter(true);
        ConsultFilial.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        ConsultFilial.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {

            },
            new String [] {
                "Código", "Nred", "Razão Social", "Endereco", "Cidade", "UF", "CEP", "CNPJ", "Insc_Est", "Insc_Munic", "NFSerie", "NFSeq", "NFSeleFiscal", "CFOP", "CFOPDescr", "IRRF", "ISS", "INSS", "Operador", "Dt_Alter", "Hr_Alter"
            }
        ));
        ConsultFilial.setAutoResizeMode(javax.swing.JTable.AUTO_RESIZE_OFF);
        ConsultFilial.setRowHeight(30);
        ConsultFilial.setShowVerticalLines(false);
        ConsultFilial.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultFilialKeyPressed(evt);
            }
        });
        jScrollPane1.setViewportView(ConsultFilial);

        nomePesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        nomePesq.setFocusable(false);
        nomePesq.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                nomePesqMouseClicked(evt);
            }
        });
        nomePesq.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusGained(java.awt.event.FocusEvent evt) {
                nomePesqFocusGained(evt);
            }
        });
        nomePesq.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                nomePesqKeyPressed(evt);
            }
        });

        TitPesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TitPesq.setText("NRed:");

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/Figuras/icone_pesquisar.png"))); // NOI18N
        jButton1.setToolTipText("Toque para pesquisar");
        jButton1.setBorderPainted(false);
        jButton1.setContentAreaFilled(false);
        jButton1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jButton1MouseClicked(evt);
            }
        });
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        buttonGroup1.add(Consulta1);
        Consulta1.setSelected(true);
        Consulta1.setText("Nome Reduzido");
        Consulta1.setFocusable(false);
        Consulta1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                Consulta1MouseClicked(evt);
            }
        });

        buttonGroup1.add(Consulta2);
        Consulta2.setText("Código");
        Consulta2.setFocusable(false);
        Consulta2.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                Consulta2MouseClicked(evt);
            }
        });
        Consulta2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                Consulta2ActionPerformed(evt);
            }
        });
        Consulta2.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                Consulta2KeyPressed(evt);
            }
        });

        buttonGroup1.add(Consulta3);
        Consulta3.setText("Razão Social");
        Consulta3.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                Consulta3KeyPressed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(10, 10, 10)
                .addComponent(Tconsult)
                .addGap(6, 6, 6)
                .addComponent(Consulta1)
                .addGap(4, 4, 4)
                .addComponent(Consulta2)
                .addGap(2, 2, 2)
                .addComponent(Consulta3))
            .addGroup(layout.createSequentialGroup()
                .addGap(16, 16, 16)
                .addComponent(TitPesq, javax.swing.GroupLayout.PREFERRED_SIZE, 99, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(4, 4, 4)
                .addComponent(nomePesq, javax.swing.GroupLayout.PREFERRED_SIZE, 327, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(18, 18, 18)
                .addComponent(jButton1, javax.swing.GroupLayout.PREFERRED_SIZE, 52, javax.swing.GroupLayout.PREFERRED_SIZE))
            .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 665, javax.swing.GroupLayout.PREFERRED_SIZE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addGap(11, 11, 11)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(Tconsult)
                    .addComponent(Consulta1)
                    .addComponent(Consulta2)
                    .addComponent(Consulta3))
                .addGap(2, 2, 2)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(TitPesq, javax.swing.GroupLayout.PREFERRED_SIZE, 65, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(nomePesq, javax.swing.GroupLayout.PREFERRED_SIZE, 65, javax.swing.GroupLayout.PREFERRED_SIZE)
                    .addComponent(jButton1))
                .addGap(6, 6, 6)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 558, javax.swing.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void formWindowOpened(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowOpened
        ConsultFilial.requestFocus();
        DefaultTableModel modelo = (DefaultTableModel) ConsultFilial.getModel();
        setIconImage(Toolkit.getDefaultToolkit().getImage(getClass().getResource("/Figuras/icone_satellite.png")));
        try {
            persistpraca = new Persistencia_OLD(param, "/Dados/mapconect.txt", "DESK");
            Consulta consult = new Consulta("select top 20 "
                    + " Codigo,NRed,RazaoSocial,Endereco,Cidade,UF,CEP,"
                    + " CNPJ,Insc_Est,Insc_Munic,NFSerie,NFSeq,NFSeloFiscal,"
                    + " CFOP,CFOPDescr,IRRF,ISS,INSS,"
                    + " Operador,Dt_alter,Hr_alter"
                    + " from Pracas ", persistpraca);
            consult.select();
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4),
                    consult.getString(5),
                    consult.getString(6),
                    consult.getString(7),
                    consult.getString(8),
                    consult.getString(9),
                    consult.getString(10),
                    consult.getString(11),
                    consult.getString(12).substring(0, consult.getString(12).indexOf(".")),
                    consult.getString(13).substring(0, consult.getString(13).indexOf(".")),
                    consult.getString(14),
                    consult.getString(15),
                    consult.getString(16),
                    consult.getString(17),
                    consult.getString(18),
                    consult.getString(19),
                    consult.getString(20),
                    consult.getString(21),});
            }
            consult.Close();
            for (int i = 0; i < 21; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFilial, i);
            }
            ConsultFilial.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowOpened

    private void jButton1MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_jButton1MouseClicked
        Consulta();
    }//GEN-LAST:event_jButton1MouseClicked

    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_jButton1ActionPerformed

    private void nomePesqKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_nomePesqKeyPressed
        if (evt.getKeyCode() == KeyEvent.VK_ENTER) {
            Consulta();
        } else if (evt.getKeyCode() == KeyEvent.VK_F2) {
            if (Consulta1.isSelected()) {
                Consulta2.setSelected(true);
                TitPesq.setText("Código:");
                nomePesq.requestFocus();
            } else if (Consulta2.isSelected()) {
                Consulta3.setSelected(true);
                TitPesq.setText("Razão Social:");
                nomePesq.requestFocus();
            } else if (Consulta3.isSelected()) {
                Consulta1.setSelected(true);
                TitPesq.setText("NRed:");
                nomePesq.requestFocus();
            }
        }
    }//GEN-LAST:event_nomePesqKeyPressed

    private void ConsultFilialKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultFilialKeyPressed
        if (evt.getKeyCode() == 27) {
            String codfil;
            try {
                codfil = (String) ConsultFilial.getValueAt(ConsultFilial.getSelectedRow(), 0);
            } catch (Exception e) {
                codfil = "0";
            }
            Main.DadosConsultas.setCodfil(codfil);
            this.dispose();
        } else if (evt.getKeyCode() == KeyEvent.VK_P) {
            nomePesq.setFocusable(true);
            nomePesq.setText("");
            nomePesq.requestFocus();
        }
    }//GEN-LAST:event_ConsultFilialKeyPressed

    private void Consulta1MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_Consulta1MouseClicked
        Consulta1.setSelected(true);
        TitPesq.setText("Nred:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");

    }//GEN-LAST:event_Consulta1MouseClicked

    private void Consulta2KeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_Consulta2KeyPressed

    }//GEN-LAST:event_Consulta2KeyPressed

    private void Consulta2MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_Consulta2MouseClicked
        Consulta2.setSelected(true);
        TitPesq.setText("Código:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_Consulta2MouseClicked

    private void nomePesqFocusGained(java.awt.event.FocusEvent evt) {//GEN-FIRST:event_nomePesqFocusGained
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_nomePesqFocusGained

    private void nomePesqMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_nomePesqMouseClicked
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
    }//GEN-LAST:event_nomePesqMouseClicked

    private void formWindowClosing(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowClosing
        try {
            persistpraca.FechaConexao();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowClosing

    private void Consulta2ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_Consulta2ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_Consulta2ActionPerformed

    private void Consulta3KeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_Consulta3KeyPressed
        Consulta3.setSelected(true);
        TitPesq.setText("Razão Social:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_Consulta3KeyPressed

    private void Consulta() {
        DefaultTableModel modelo = (DefaultTableModel) ConsultFilial.getModel();
        try {
            Consulta consult;
            if (nomePesq.getText().equals("")) {
                consult = new Consulta("select "
                        + " Codigo,NRed,RazaoSocial,Endereco,Cidade,UF,CEP,"
                        + " CNPJ,Insc_Est,Insc_Munic,NFSerie,NFSeq,NFSeloFiscal,"
                        + " CFOP,CFOPDescr,IRRF,ISS,INSS,"
                        + " Operador,Dt_alter,Hr_alter"
                        + " from Pracas ", persistpraca);
            } else {
                if (Consulta1.isSelected()) {
                    consult = new Consulta("select top 20 "
                            + " Codigo,NRed,RazaoSocial,Endereco,Cidade,UF,CEP,"
                            + " CNPJ,Insc_Est,Insc_Munic,NFSerie,NFSeq,NFSeloFiscal,"
                            + " CFOP,CFOPDescr,IRRF,ISS,INSS,"
                            + " Operador,Dt_alter,Hr_alter"
                            + " from Pracas "
                            + " where Nred like ?", persistpraca);
                    consult.setString("%" + nomePesq.getText() + "%");
                } else if (Consulta2.isSelected()) {
                    consult = new Consulta("select top 20 "
                            + " Codigo,NRed,RazaoSocial,Endereco,Cidade,UF,CEP,"
                            + " CNPJ,Insc_Est,Insc_Munic,NFSerie,NFSeq,NFSeloFiscal,"
                            + " CFOP,CFOPDescr,IRRF,ISS,INSS,"
                            + " Operador,Dt_alter,Hr_alter"
                            + " from Pracas "
                            + " where Codigo = ?", persistpraca);
                    consult.setString(nomePesq.getText());
                } else {
                    consult = new Consulta("select top 20 "
                            + " Codigo,NRed,RazaoSocial,Endereco,Cidade,UF,CEP,"
                            + " CNPJ,Insc_Est,Insc_Munic,NFSerie,NFSeq,NFSeloFiscal,"
                            + " CFOP,CFOPDescr,IRRF,ISS,INSS,"
                            + " Operador,Dt_alter,Hr_alter"
                            + " from Pracas "
                            + " where RazaoSocial like ?", persistpraca);
                    consult.setString("%" + nomePesq.getText() + "%");
                }
            }
            consult.select();
            modelo.setNumRows(0);
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4),
                    consult.getString(5),
                    consult.getString(6),
                    consult.getString(7),
                    consult.getString(8),
                    consult.getString(9),
                    consult.getString(10),
                    consult.getString(11),
                    consult.getString(12).substring(0, consult.getString(12).indexOf(".")),
                    consult.getString(13).substring(0, consult.getString(13).indexOf(".")),
                    consult.getString(14),
                    consult.getString(15),
                    consult.getString(16),
                    consult.getString(17),
                    consult.getString(18),
                    consult.getString(19),
                    consult.getString(20),
                    consult.getString(21),});
            }
            consult.Close();
            for (int i = 0; i < 21; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFilial, i);
            }
            ConsultFilial.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }

    public void setParam(String value) {
        param = value;
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(ConsultPraca.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(ConsultPraca.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(ConsultPraca.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(ConsultPraca.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new ConsultPraca().setVisible(true);
            }
        });
    }
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTable ConsultFilial;
    private javax.swing.JRadioButton Consulta1;
    private javax.swing.JRadioButton Consulta2;
    private javax.swing.JRadioButton Consulta3;
    private javax.swing.JLabel Tconsult;
    private javax.swing.JLabel TitPesq;
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.JButton jButton1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTextField nomePesq;
    // End of variables declaration//GEN-END:variables
}
