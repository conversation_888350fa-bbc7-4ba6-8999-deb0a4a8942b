<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.8" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.ButtonGroup" name="buttonGroup1">
    </Component>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="2"/>
    <Property name="title" type="java.lang.String" value="Filial"/>
    <Property name="alwaysOnTop" type="boolean" value="true"/>
    <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
      <Color blue="9b" green="5b" red="14" type="rgb"/>
    </Property>
    <Property name="resizable" type="boolean" value="false"/>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <Events>
    <EventHandler event="windowOpened" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowOpened"/>
    <EventHandler event="windowClosing" listener="java.awt.event.WindowListener" parameters="java.awt.event.WindowEvent" handler="formWindowClosing"/>
  </Events>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jScrollPane1" alignment="0" max="32767" attributes="0"/>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <Component id="Tconsult" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="ConsultaDesc" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="ConsultaCod" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" attributes="0">
                      <Component id="TitPesq" min="-2" max="-2" attributes="0"/>
                      <EmptySpace type="unrelated" max="-2" attributes="0"/>
                      <Component id="nomePesq" min="-2" pref="323" max="-2" attributes="0"/>
                      <EmptySpace type="unrelated" max="-2" attributes="0"/>
                      <Component id="jButton1" min="-2" pref="52" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace pref="218" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="32767" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="ConsultaDesc" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="ConsultaCod" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="Tconsult" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" max="-2" attributes="0">
                  <Component id="jButton1" alignment="0" max="32767" attributes="0"/>
                  <Component id="nomePesq" max="32767" attributes="0"/>
                  <Component id="TitPesq" alignment="0" max="32767" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane1" min="-2" pref="558" max="-2" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="ConsultFilial">
          <Properties>
            <Property name="autoCreateRowSorter" type="boolean" value="true"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Tahoma" size="14" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="21" rowCount="0">
                <Column editable="true" title="CodFil" type="java.lang.Object"/>
                <Column editable="true" title="Descri&#xe7;&#xe3;o" type="java.lang.Object"/>
                <Column editable="true" title="Raz&#xe3;o Social" type="java.lang.Object"/>
                <Column editable="true" title="Endereco" type="java.lang.Object"/>
                <Column editable="true" title="Bairro" type="java.lang.Object"/>
                <Column editable="true" title="Cidade" type="java.lang.Object"/>
                <Column editable="true" title="UF" type="java.lang.Object"/>
                <Column editable="true" title="CEP" type="java.lang.Object"/>
                <Column editable="true" title="Fone" type="java.lang.Object"/>
                <Column editable="true" title="Fone 2" type="java.lang.Object"/>
                <Column editable="true" title="Fax" type="java.lang.Object"/>
                <Column editable="true" title="Contato" type="java.lang.Object"/>
                <Column editable="true" title="Email" type="java.lang.Object"/>
                <Column editable="true" title="CNPJ" type="java.lang.Object"/>
                <Column editable="true" title="InscEst" type="java.lang.Object"/>
                <Column editable="true" title="InscMunic" type="java.lang.Object"/>
                <Column editable="true" title="ISS" type="java.lang.Object"/>
                <Column editable="true" title="Pra&#xe7;a" type="java.lang.Object"/>
                <Column editable="true" title="Operador" type="java.lang.Object"/>
                <Column editable="true" title="Dt_Alter" type="java.lang.Object"/>
                <Column editable="true" title="Hr_ALter" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="autoResizeMode" type="int" value="0"/>
            <Property name="rowHeight" type="int" value="30"/>
            <Property name="showVerticalLines" type="boolean" value="false"/>
          </Properties>
          <Events>
            <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="ConsultFilialKeyPressed"/>
          </Events>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JTextField" name="nomePesq">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="focusable" type="boolean" value="false"/>
      </Properties>
      <Events>
        <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="nomePesqMouseClicked"/>
        <EventHandler event="focusGained" listener="java.awt.event.FocusListener" parameters="java.awt.event.FocusEvent" handler="nomePesqFocusGained"/>
        <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="nomePesqKeyPressed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="TitPesq">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Tahoma" size="14" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Descr.:"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="jButton1">
      <Properties>
        <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
          <Image iconType="3" name="/Figuras/icone_pesquisar.png"/>
        </Property>
        <Property name="toolTipText" type="java.lang.String" value="Toque para pesquisar"/>
        <Property name="borderPainted" type="boolean" value="false"/>
        <Property name="contentAreaFilled" type="boolean" value="false"/>
      </Properties>
      <Events>
        <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="jButton1MouseClicked"/>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="jButton1ActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JRadioButton" name="ConsultaDesc">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="selected" type="boolean" value="true"/>
        <Property name="text" type="java.lang.String" value="Descri&#xe7;&#xe3;o"/>
        <Property name="focusable" type="boolean" value="false"/>
      </Properties>
      <Events>
        <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="ConsultaDescMouseClicked"/>
      </Events>
    </Component>
    <Component class="javax.swing.JRadioButton" name="ConsultaCod">
      <Properties>
        <Property name="buttonGroup" type="javax.swing.ButtonGroup" editor="org.netbeans.modules.form.RADComponent$ButtonGroupPropertyEditor">
          <ComponentRef name="buttonGroup1"/>
        </Property>
        <Property name="text" type="java.lang.String" value="C&#xf3;digo"/>
        <Property name="focusable" type="boolean" value="false"/>
      </Properties>
      <Events>
        <EventHandler event="mouseClicked" listener="java.awt.event.MouseListener" parameters="java.awt.event.MouseEvent" handler="ConsultaCodMouseClicked"/>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="ConsultaCodActionPerformed"/>
        <EventHandler event="keyPressed" listener="java.awt.event.KeyListener" parameters="java.awt.event.KeyEvent" handler="ConsultaCodKeyPressed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="Tconsult">
    </Component>
  </SubComponents>
</Form>
