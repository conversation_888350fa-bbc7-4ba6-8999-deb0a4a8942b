package SasLibrary;

import Arquivo.ArquivoLog;
import Auxiliares.PessoasClientesPW;
import BeansEspeciais.GuiaE;
import Dados.Persistencia;
import SasBeans.CarregaRotaVol;
import SasBeans.ClientesPessoas;
import SasBeans.CodClienteAut;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.GuiasList;
import SasBeans.Pessoa;
import SasBeansCompostas.CarregaParadaRota;
import SasBeansCompostas.CarregaRota;
import SasBeansCompostas.LoginRota;
import SasDaos.CarregaRotaDao;
import SasDaos.ClientesPessoasDao;
import SasDaos.CodClienteAuthDao;
import SasDaos.CxForteDao;
import SasDaos.EscalaDao;
import SasDaos.LoginDao;
import SasDaos.PessoaDao;
import SasDaos.PstServDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.FormasPgto;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.Remessas;
import br.com.sasw.pacotesuteis.sasdaos.FormasPgtoDao;
import br.com.sasw.pacotesuteis.sasdaos.compostas.TesSaidaRtGuiasCxfGuiaDao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.RemessasDao;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.ServletContext;

/**
 *
 * <AUTHOR>
 */
public class Rotas {

    private static CodClienteAuthDao codClienteAuthDAO = null;

    /**
     * Retorna uma lista de serviços a serem feitos pela rota do chefe de equipe
     * que foi digitado a matricula e senha
     *
     * @param param
     * @param matricula
     * @param persistencia - classe de conexão
     * @param codfil
     * @param persistCentral
     * @return - XML contendo a lista de serviços Legenda:
     * <nr> numero total de registros na lista
     * <f>Fulano, Fim de Rota
     * <e> erro ou logou
     * @throws Exception - pode gerar exception
     */
    public static String CarregaRota(String param, String matricula, String codfil, Persistencia persistencia, Persistencia persistCentral) throws Exception {
        try {
            //carrega a rota
            List<CarregaRota> list_rota;
            try {
                list_rota = CarregaRotaDao.getRotas(param, matricula, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota - " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                throw new Exception("Fim de Rota");
            }
            List<PessoasClientesPW> lpw = new ArrayList();
            //busca todas as pessoas que respondem por cada empresa
            ClientesPessoasDao CPD = new ClientesPessoasDao();
            codClienteAuthDAO = new CodClienteAuthDao();

            List<ClientesPessoas> lCPD = CPD.getPessoas(list_rota, persistencia);
            if (lCPD.size() > 0) {
                //busca senha de cada pessoa responsável
                PessoaDao pd = new PessoaDao();
                List<Pessoa> lpessoa = pd.getPWs(lCPD, persistCentral);
                if (lpessoa.size() > 0) {
                    //associa senha que vem do banco central com os dados do banco do cliente código do cliente, na ordem da rota
                    for (int k = 0; k < list_rota.size(); k++) {
                        for (int i = 0; i < lCPD.size(); i++) {
                            if (list_rota.get(k).getCliOri().getCodCli().equals(lCPD.get(i).getCodCli())) {
                                for (int j = 0; j < lpessoa.size(); j++) {
                                    if (lCPD.get(i).getCodPessoaWEB().toString().equals(lpessoa.get(j).getCodigo().toString())) {
                                        PessoasClientesPW pcpw = new PessoasClientesPW();
                                        pcpw.setCodcli(lCPD.get(i).getCodCli());
                                        pcpw.setNomepessoa(lpessoa.get(j).getNome());
                                        pcpw.setPw(lpessoa.get(j).getPW());
                                        lpw.add(pcpw);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //busca código de caixa forte
            CxForteDao cxfdao = new CxForteDao();
            CxForte cxf = cxfdao.getCxForte(list_rota.get(0).getRota().getCodFil(), persistencia);
            String xml = "<nreg>" + list_rota.size() + "</nreg>";
            int codcxf = 0;
            String pessoapw;
            int i = 0;
            boolean continua = true;
            for (CarregaRota list_rota1 : list_rota) {
                pessoapw = "";
                if (cxf.getCodCli().equals(list_rota1.getCliOri().getCodigo())) {
                    codcxf = 1;
                } else {
                    codcxf = 0;
                }
                while (continua && i < lpw.size()) {
                    if (list_rota1.getCliOri().getCodCli().equals(lpw.get(i).getCodcli())) {
                        pessoapw = lpw.get(i).getPw() + ";" + lpw.get(i).getNomepessoa() + ";";
                        i++;
                    } else {
                        continua = false;
                    }
                }
                if (!"".equals(pessoapw)) {
                    pessoapw = Xmls.tag("senharesp", pessoapw);
                }
                TesSaidaRtGuiasCxfGuiaDao oTesSaidaRtGuiasCxfGuiaDao = new TesSaidaRtGuiasCxfGuiaDao();
                List<GuiasList> lCxfGRtGTesG = new ArrayList();
                if (("E".equals(list_rota1.getRt_perc().getER())) && (!"A".equals(list_rota1.getRt_perc().getTipoSrv()))) {
                    lCxfGRtGTesG = oTesSaidaRtGuiasCxfGuiaDao.getTesSaidasRt_GuiasCxfGuiasEntrega(
                            list_rota1.getRota().getCodFil().toPlainString(), list_rota1.getRt_perc().getHora1(),
                            list_rota1.getRt_perc().getSequencia().toPlainString(),
                            String.valueOf(list_rota1.getRt_perc().getParada()), persistencia);
                }
                String eguia = "";
                String eserie = "";
                String evalor = "";
                String eseqrota = "";
                String eparada = "";
                String eqtlacres = "";
                String elacres = "";
                String os = "";
                continua = true;
                xml += "<servico>";
                xml += Xmls.tag("rota", list_rota1.getRota().getRota());
                xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
                xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
                xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
                xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
                xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
                xml += Xmls.tag("cxforte", String.valueOf(codcxf));
                xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
                xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
                xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
                xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
                xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
                xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
                xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
                xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
                xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
                xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
                xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
                xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
                xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
                xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
                xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
                xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
                xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
                xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
                xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
                xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
                xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
                xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
                xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
                xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
                xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
                xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
                xml += Xmls.tag("os", list_rota1.getOS().getOS());
                xml += Xmls.tag("origempedido", list_rota1.getOS().getCliente());
                xml += Xmls.tag("nredorigempedido", list_rota1.getOS().getNRed());
                xml += Xmls.tag("dstpedido", list_rota1.getOS().getCliDst());
                xml += Xmls.tag("nreddstpedido", list_rota1.getOS().getNRedDst());

                for (GuiasList gl : lCxfGRtGTesG) {
                    eguia += ";" + gl.getGuia();
                    eserie += ";" + gl.getSerie();
                    evalor += ";" + gl.getValor();
                    eseqrota += ";" + gl.getSequenciaOri();
                    eparada += ";" + gl.getParadaOri();
                    eqtlacres += ";" + gl.getVolumes().size();
                    os += ";" + gl.getOS();

                    for (CxFGuiasVol cxfv : gl.getVolumes()) {
                        elacres += ";" + cxfv.getLacre();
                    }
                }
                eguia = eguia.replaceFirst(";", "");
                eserie = eserie.replaceFirst(";", "");
                evalor = evalor.replaceFirst(";", "");
                eseqrota = eseqrota.replaceFirst(";", "");
                eparada = eparada.replaceFirst(";", "");
                eqtlacres = eqtlacres.replaceFirst(";", "");
                elacres = elacres.replaceFirst(";", "");

                xml += Xmls.tag("eguia", eguia);
                xml += Xmls.tag("eserie", eserie);
                xml += Xmls.tag("evalor", evalor);
                xml += Xmls.tag("eseqrota", eseqrota);
                xml += Xmls.tag("eparada", eparada);
                xml += Xmls.tag("eqtlacres", eqtlacres);
                xml += Xmls.tag("elacres", elacres);
                xml += Xmls.tag("eos", os);
                //xml += "<senharesp>" +lpessoa.get(0).getPW()+";"+lpessoa.get(0).getNome()+ "</senharesp>";
                xml += pessoapw;
                xml += senhasClientes(list_rota1.getCliOri().getCodCli(), codfil, persistencia);
                xml += "</servico>";
            }
            return xml;
        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    public static String CarregaRota(String param, String matricula, String codfil, String dataAtual, Persistencia persistencia, Persistencia persistCentral) throws Exception {
        try {
            //carrega a rota
            List<CarregaRota> list_rota;
            try {
                list_rota = CarregaRotaDao.getRotas(param, matricula, dataAtual, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota - " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                throw new Exception("Fim de Rota");
            }
            List<PessoasClientesPW> lpw = new ArrayList();
            //busca todas as pessoas que respondem por cada empresa
            ClientesPessoasDao CPD = new ClientesPessoasDao();
            codClienteAuthDAO = new CodClienteAuthDao();

            List<ClientesPessoas> lCPD = CPD.getPessoas(list_rota, persistencia);
            if (lCPD.size() > 0) {
                //busca senha de cada pessoa responsável
                PessoaDao pd = new PessoaDao();
                List<Pessoa> lpessoa = pd.getPWs(lCPD, persistCentral);
                if (lpessoa.size() > 0) {
                    //associa senha que vem do banco central com os dados do banco do cliente código do cliente, na ordem da rota
                    for (int k = 0; k < list_rota.size(); k++) {
                        for (int i = 0; i < lCPD.size(); i++) {
                            if (list_rota.get(k).getCliOri().getCodCli().equals(lCPD.get(i).getCodCli())) {
                                for (int j = 0; j < lpessoa.size(); j++) {
                                    if (lCPD.get(i).getCodPessoaWEB().toString().equals(lpessoa.get(j).getCodigo().toString())) {
                                        PessoasClientesPW pcpw = new PessoasClientesPW();
                                        pcpw.setCodcli(lCPD.get(i).getCodCli());
                                        pcpw.setNomepessoa(lpessoa.get(j).getNome());
                                        pcpw.setPw(lpessoa.get(j).getPW());
                                        lpw.add(pcpw);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //busca código de caixa forte
            CxForteDao cxfdao = new CxForteDao();
            CxForte cxf = cxfdao.getCxForte(list_rota.get(0).getRota().getCodFil(), persistencia);
            String xml = "<nreg>" + list_rota.size() + "</nreg>";
            int codcxf = 0;
            String pessoapw;
            int i = 0;
            boolean continua = true;
            for (CarregaRota list_rota1 : list_rota) {
                pessoapw = "";
                if (cxf.getCodCli().equals(list_rota1.getCliOri().getCodigo())) {
                    codcxf = 1;
                } else {
                    codcxf = 0;
                }
                while (continua && i < lpw.size()) {
                    if (list_rota1.getCliOri().getCodCli().equals(lpw.get(i).getCodcli())) {
                        pessoapw = lpw.get(i).getPw() + ";" + lpw.get(i).getNomepessoa() + ";";
                        i++;
                    } else {
                        continua = false;
                    }
                }
                if (!"".equals(pessoapw)) {
                    pessoapw = Xmls.tag("senharesp", pessoapw);
                }
                TesSaidaRtGuiasCxfGuiaDao oTesSaidaRtGuiasCxfGuiaDao = new TesSaidaRtGuiasCxfGuiaDao();
                List<GuiasList> lCxfGRtGTesG = new ArrayList();
                if (("E".equals(list_rota1.getRt_perc().getER())) && (!"A".equals(list_rota1.getRt_perc().getTipoSrv()))) {
                    lCxfGRtGTesG = oTesSaidaRtGuiasCxfGuiaDao.getTesSaidasRt_GuiasCxfGuiasEntrega(
                            list_rota1.getRota().getCodFil().toPlainString(), list_rota1.getRt_perc().getHora1(),
                            list_rota1.getRt_perc().getSequencia().toPlainString(),
                            String.valueOf(list_rota1.getRt_perc().getParada()), persistencia);
                }
                String eguia = "";
                String eserie = "";
                String evalor = "";
                String eseqrota = "";
                String eparada = "";
                String eqtlacres = "";
                String elacres = "";
                String os = "";
                continua = true;
                xml += "<servico>";
                xml += Xmls.tag("rota", list_rota1.getRota().getRota());
                xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
                xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
                xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
                xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
                xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
                xml += Xmls.tag("cxforte", String.valueOf(codcxf));
                xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
                xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
                xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
                xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
                xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
                xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
                xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
                xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
                xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
                xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
                xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
                xml += Xmls.tag("origem", list_rota1.getCliOri().getNRed());
                xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
                xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
                xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
                xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
                xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
                xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
                xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
                xml += Xmls.tag("hrchegvei", list_rota1.getRt_percsla().getHrChegVei());
                xml += Xmls.tag("hrsaidavei", list_rota1.getRt_percsla().getHrSaidaVei());
                xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
                xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
                xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
                xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
                xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
                xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
                xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
                xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
                xml += Xmls.tag("os", list_rota1.getOS().getOS());
                xml += Xmls.tag("origempedido", list_rota1.getOS().getCliente());
                xml += Xmls.tag("nredorigempedido", list_rota1.getOS().getNRed());
                xml += Xmls.tag("dstpedido", list_rota1.getOS().getCliDst());
                xml += Xmls.tag("nreddstpedido", list_rota1.getOS().getNRedDst());

                for (GuiasList gl : lCxfGRtGTesG) {
                    eguia += ";" + gl.getGuia();
                    eserie += ";" + gl.getSerie();
                    evalor += ";" + gl.getValor();
                    eseqrota += ";" + gl.getSequenciaOri();
                    eparada += ";" + gl.getParadaOri();
                    eqtlacres += ";" + gl.getVolumes().size();
                    os += ";" + gl.getOS();

                    for (CxFGuiasVol cxfv : gl.getVolumes()) {
                        elacres += ";" + cxfv.getLacre();
                    }
                }
                eguia = eguia.replaceFirst(";", "");
                eserie = eserie.replaceFirst(";", "");
                evalor = evalor.replaceFirst(";", "");
                eseqrota = eseqrota.replaceFirst(";", "");
                eparada = eparada.replaceFirst(";", "");
                eqtlacres = eqtlacres.replaceFirst(";", "");
                elacres = elacres.replaceFirst(";", "");

                xml += Xmls.tag("eguia", eguia);
                xml += Xmls.tag("eserie", eserie);
                xml += Xmls.tag("evalor", evalor);
                xml += Xmls.tag("eseqrota", eseqrota);
                xml += Xmls.tag("eparada", eparada);
                xml += Xmls.tag("eqtlacres", eqtlacres);
                xml += Xmls.tag("elacres", elacres);
                xml += Xmls.tag("eos", os);
                //xml += "<senharesp>" +lpessoa.get(0).getPW()+";"+lpessoa.get(0).getNome()+ "</senharesp>";
                xml += pessoapw;
                xml += senhasClientes(list_rota1.getCliOri().getCodCli(), codfil, persistencia);
                xml += "</servico>";
            }
            return xml;
        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    public static String remessas(String matricula, String codFil, String dataAtual, Persistencia persistencia) throws Exception {
        String seqRota = EscalaDao.obterSequenciaRota(codFil, dataAtual, matricula, persistencia);
        RemessasDao remessasDao = new RemessasDao();
        List<Remessas> remessas = remessasDao.obterRemessas(seqRota, codFil, persistencia);
        StringBuilder retorno = new StringBuilder(), aux;
        for(Remessas remessa : remessas){
            aux = new StringBuilder();
            aux.append(Xmls.tag("CodCli", remessa.getCodCli()));
            aux.append(Xmls.tag("Nome", remessa.getNome()));
            aux.append(Xmls.tag("Endereco", remessa.getEndereco()));
            aux.append(Xmls.tag("Rota", remessa.getRota()));
            aux.append(Xmls.tag("Sequencia", remessa.getSequencia()));
            aux.append(Xmls.tag("NroChave", remessa.getNroChave()));
            aux.append(Xmls.tag("Data", remessa.getData()));
            aux.append(Xmls.tag("ER", remessa.getER()));
            aux.append(Xmls.tag("CodCli1", remessa.getCodCli1()));
            aux.append(Xmls.tag("Hora1", remessa.getHora1()));
            aux.append(Xmls.tag("Parada", remessa.getParada()));
            aux.append(Xmls.tag("Dpar", remessa.getDpar()));
            aux.append(Xmls.tag("CodCli2", remessa.getCodCli2()));
            aux.append(Xmls.tag("Hora1D", remessa.getHora1D()));
            aux.append(Xmls.tag("TipoSrv", remessa.getTipoSrv()));
            aux.append(Xmls.tag("Chave", remessa.getChave()));
            aux.append(Xmls.tag("Regiao", remessa.getRegiao()));
            aux.append(Xmls.tag("Observ", remessa.getObserv()));
            aux.append(Xmls.tag("Moeda", remessa.getMoeda()));
            aux.append(Xmls.tag("Valor", remessa.getValor()));
            aux.append(Xmls.tag("Guia", remessa.getGuia().replace(".0","")));
            aux.append(Xmls.tag("Serie", remessa.getSerie().replace(".0","")));
            aux.append(Xmls.tag("MatrChe", remessa.getMatrChe().replace(".0","")));
            aux.append(Xmls.tag("NomeChe", remessa.getNomeChe()));
            aux.append(Xmls.tag("Veiculo", remessa.getVeiculo().replace(".0","")));
            aux.append(Xmls.tag("CodRemessa", remessa.getCodRemessa().replace(".0","")));
            aux.append(Xmls.tag("Lacre", remessa.getLacre().replace(".0","")));
            aux.append(Xmls.tag("Qtde", remessa.getQtde().replace(".0","")));
            aux.append(Xmls.tag("Remessa", remessa.getRemessa().replace(".0","")));
            aux.append(Xmls.tag("Oper_Prep", remessa.getOper_Prep().replace(".0","")));
            retorno.append(Xmls.tag("Remessas", aux));
        }
        return retorno.toString();
    }
    
    public static String CarregaRotaVol(String param, String matricula, String codfil, String dataAtual,
            ServletContext context, String servletName, String sCodPessoa, ArquivoLog logerro,
            Persistencia persistencia) throws Exception {
        String vRunTrace = "";
        try {
            //carrega a rota
            List<CarregaRotaVol> list_rota;
            try {                
                String seqRota = EscalaDao.obterSequenciaRota(codfil, dataAtual, matricula, persistencia);                
                vRunTrace = vRunTrace+dataAtual+";"+"Carregando rota " + seqRota+";"+sCodPessoa;                         
                //Trace.gerarTrace(context, servletName, "Carregando rota " + seqRota, sCodPessoa, persistencia.getEmpresa(), logerro);
                list_rota = CarregaRotaDao.getRotasVol(seqRota, persistencia);
                vRunTrace = vRunTrace+";"+dataAtual+";"+"Rota carregada " + seqRota+";"+sCodPessoa;
                //Trace.gerarTrace(context, servletName, "Rota carregada: " + seqRota, sCodPessoa, persistencia.getEmpresa(), logerro);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota - " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                throw new Exception("Fim de Rota");
            }

            String xml = "<nreg>" + list_rota.size() + "</nreg>";

            String eguia;
            String eserie;
            String evalor;
            String emoeda;
            String eseqrota;
            String eparada;
            String eqtlacres;
            String elacres;
            String elacresvalor;
            String elacresqtde;
            String econtainers;
            String os;
            
            vRunTrace = vRunTrace+";"+dataAtual+";"+"Inicio carga guias ";
            //Trace.gerarTrace(context, servletName, "Início do loop - informações guias", sCodPessoa, persistencia.getEmpresa(), logerro);
            for (CarregaRotaVol list_rota1 : list_rota) {
                eguia = "";
                eserie = "";
                evalor = "";
                emoeda = "";
                eseqrota = "";
                eparada = "";
                eqtlacres = "";
                elacres = "";
                elacresvalor = "";
                elacresqtde = "";
                econtainers = "";
                os = "";
                
                xml += "<servico>";
                xml += Xmls.tag("rota", list_rota1.getRota().getRota());
                xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
                xml += Xmls.tag("horario1", list_rota1.getOS().getHorario1());
                xml += Xmls.tag("horario2", list_rota1.getOS().getHorario2());
                xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
                xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
                xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
                xml += Xmls.tag("valor", list_rota1.getRota().getValor());
                xml += Xmls.tag("moeda", list_rota1.getRt_perc().getMoeda());
                xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
                xml += Xmls.tag("cxforte", list_rota1.getCxforte().equals("S") ? "1" : "0");
                xml += Xmls.tag("PreOrder", list_rota1.getPreOrder().replace(".0", ""));
                xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
                xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
                xml += Xmls.tag("codext", list_rota1.getCliOri().getCodExt().replace(".0", ""));
                xml += Xmls.tag("codptocli", list_rota1.getCliOri().getCodPtoCli().replace(".0", ""));
                xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
                xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
                xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
                xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
                xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
                xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
                xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
                xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
                xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
                xml += Xmls.tag("origem", list_rota1.getCliOri().getNRed());
                xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
                xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
                xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
                xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
                xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
                xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
                xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
                xml += Xmls.tag("hrchegvei", list_rota1.getRt_percsla().getHrChegVei());
                xml += Xmls.tag("hrsaidavei", list_rota1.getRt_percsla().getHrSaidaVei());
                xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
                xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
                xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
                xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
                xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
                xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
                xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
                xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
                xml += Xmls.tag("os", list_rota1.getOS().getOS());
                xml += Xmls.tag("origempedido", list_rota1.getOS().getCliente());
                xml += Xmls.tag("nredorigempedido", list_rota1.getOS().getNRed());
                xml += Xmls.tag("dstpedido", list_rota1.getOS().getCliDst());
                xml += Xmls.tag("nreddstpedido", list_rota1.getOS().getNRedDst());
                xml += Xmls.tag("Observ", list_rota1.getRt_perc().getObserv());
                xml += Xmls.tag("Contato", list_rota1.getCliOri().getContato());
                xml += Xmls.tag("Fone1", list_rota1.getCliOri().getFone1());

                for (GuiasList gl : list_rota1.getGuias()) {
                    eguia += ";" + gl.getGuia();
                    eserie += ";" + gl.getSerie();
                    evalor += ";" + gl.getValor();
                    emoeda += ";" + gl.getMoeda();
                    eseqrota += ";" + gl.getSequenciaOri();
                    eparada += ";" + gl.getParadaOri();
                    eqtlacres += ";" + gl.getVolumes().size();
                    os += ";" + gl.getOS();

                    for (CxFGuiasVol cxfv : gl.getVolumes()) {
                        elacres += ";" + cxfv.getLacre();
                        elacresvalor += ";" + cxfv.getValor().toPlainString();
                        elacresqtde += ";" + cxfv.getQtde();
                    }
                }

                for (CxFGuiasVol cxfv : list_rota1.getContainers()) {
                    econtainers += ";" + cxfv.getLacre();
                }

                eguia = eguia.replaceFirst(";", "");
                eserie = eserie.replaceFirst(";", "");
                evalor = evalor.replaceFirst(";", "");
                emoeda = emoeda.replaceFirst(";", "");
                eseqrota = eseqrota.replaceFirst(";", "");
                eparada = eparada.replaceFirst(";", "");
                eqtlacres = eqtlacres.replaceFirst(";", "");
                elacres = elacres.replaceFirst(";", "");
                elacresvalor = elacresvalor.replaceFirst(";", "");
                elacresqtde = elacresqtde.replaceFirst(";", "");
                econtainers = econtainers.replaceFirst(";", "");

                xml += Xmls.tag("eguia", eguia);
                xml += Xmls.tag("eserie", eserie);
                xml += Xmls.tag("evalor", evalor);
                xml += Xmls.tag("emoeda", emoeda);
                xml += Xmls.tag("eseqrota", eseqrota);
                xml += Xmls.tag("eparada", eparada);
                xml += Xmls.tag("eqtlacres", eqtlacres);
                xml += Xmls.tag("elacres", elacres);
                xml += Xmls.tag("elacresvalor", elacresvalor);
                xml += Xmls.tag("elacresqtde", elacresqtde);
                xml += Xmls.tag("econtainers", econtainers);
                xml += Xmls.tag("eos", os);
                xml += "</servico>";
            }

            vRunTrace = vRunTrace+";"+dataAtual+";"+"Fim carga guias ";
            Trace.gerarTrace(context, servletName, vRunTrace, sCodPessoa, persistencia.getEmpresa(), logerro);
            //Trace.gerarTrace(context, servletName, "Fim do loop - informações guias", sCodPessoa, persistencia.getEmpresa(), logerro);
            return xml;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    public static String CarregaRotaData(String param, String matricula, String codfil, String dataAtual, String data,
            ServletContext context, String servletName, String sCodPessoa, ArquivoLog logerro,
            Persistencia persistencia) throws Exception {
        try {
            //carrega a rota
            List<CarregaRotaVol> list_rota;
            try {
                String seqRota = EscalaDao.obterSequenciaRota(codfil, data, matricula, persistencia);
                Trace.gerarTrace(context, servletName, "Carregando rota " + seqRota, sCodPessoa, persistencia.getEmpresa(), logerro);
                list_rota = CarregaRotaDao.getRotasVolData(seqRota, persistencia);
                Trace.gerarTrace(context, servletName, "Rota carregada: " + seqRota, sCodPessoa, persistencia.getEmpresa(), logerro);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota - " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                throw new Exception("Fim de Rota");
            }

            String xml = "<nreg>" + list_rota.size() + "</nreg>";

            String eguia;
            String eserie;
            String evalor;
            String emoeda;
            String eseqrota;
            String eparada;
            String eqtlacres;
            String elacres;
            String elacresvalor;
            String elacresqtde;
            String econtainers;
            String os;

            Trace.gerarTrace(context, servletName, "Início do loop - informações guias", sCodPessoa, persistencia.getEmpresa(), logerro);
            for (CarregaRotaVol list_rota1 : list_rota) {
                eguia = "";
                eserie = "";
                evalor = "";
                emoeda = "";
                eseqrota = "";
                eparada = "";
                eqtlacres = "";
                elacres = "";
                elacresvalor = "";
                elacresqtde = "";
                econtainers = "";
                os = "";
                
                xml += "<servico>";
                xml += Xmls.tag("rota", list_rota1.getRota().getRota());
                xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
                xml += Xmls.tag("horario1", list_rota1.getOS().getHorario1());
                xml += Xmls.tag("horario2", list_rota1.getOS().getHorario2());
                xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
                xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
                xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
                xml += Xmls.tag("valor", list_rota1.getRota().getValor());
                xml += Xmls.tag("moeda", list_rota1.getRt_perc().getMoeda());
                xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
                xml += Xmls.tag("cxforte", list_rota1.getCxforte().equals("S") ? "1" : "0");
                xml += Xmls.tag("PreOrder", list_rota1.getPreOrder().replace(".0", ""));
                xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
                xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
                xml += Xmls.tag("codext", list_rota1.getCliOri().getCodExt().replace(".0", ""));
                xml += Xmls.tag("codptocli", list_rota1.getCliOri().getCodPtoCli().replace(".0", ""));
                xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
                xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
                xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
                xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
                xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
                xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
                xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
                xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
                xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
                xml += Xmls.tag("origem", list_rota1.getCliOri().getNRed());
                xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
                xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
                xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
                xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
                xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
                xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
                xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
                xml += Xmls.tag("hrchegvei", list_rota1.getRt_percsla().getHrChegVei());
                xml += Xmls.tag("hrsaidavei", list_rota1.getRt_percsla().getHrSaidaVei());
                xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
                xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
                xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
                xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
                xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
                xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
                xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
                xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
                xml += Xmls.tag("os", list_rota1.getOS().getOS());
                xml += Xmls.tag("origempedido", list_rota1.getOS().getCliente());
                xml += Xmls.tag("nredorigempedido", list_rota1.getOS().getNRed());
                xml += Xmls.tag("dstpedido", list_rota1.getOS().getCliDst());
                xml += Xmls.tag("nreddstpedido", list_rota1.getOS().getNRedDst());
                xml += Xmls.tag("Observ", list_rota1.getRt_perc().getObserv());
                xml += Xmls.tag("Contato", list_rota1.getCliOri().getContato());
                xml += Xmls.tag("Fone1", list_rota1.getCliOri().getFone1());

                for (GuiasList gl : list_rota1.getGuias()) {
                    eguia += ";" + gl.getGuia();
                    eserie += ";" + gl.getSerie();
                    evalor += ";" + gl.getValor();
                    emoeda += ";" + gl.getMoeda();
                    eseqrota += ";" + gl.getSequenciaOri();
                    eparada += ";" + gl.getParadaOri();
                    eqtlacres += ";" + gl.getVolumes().size();
                    os += ";" + gl.getOS();

                    for (CxFGuiasVol cxfv : gl.getVolumes()) {
                        elacres += ";" + cxfv.getLacre();
                        elacresvalor += ";" + cxfv.getValor().toPlainString();
                        elacresqtde += ";" + cxfv.getQtde();
                    }
                }

                for (CxFGuiasVol cxfv : list_rota1.getContainers()) {
                    econtainers += ";" + cxfv.getLacre();
                }

                eguia = eguia.replaceFirst(";", "");
                eserie = eserie.replaceFirst(";", "");
                evalor = evalor.replaceFirst(";", "");
                emoeda = emoeda.replaceFirst(";", "");
                eseqrota = eseqrota.replaceFirst(";", "");
                eparada = eparada.replaceFirst(";", "");
                eqtlacres = eqtlacres.replaceFirst(";", "");
                elacres = elacres.replaceFirst(";", "");
                elacresvalor = elacresvalor.replaceFirst(";", "");
                elacresqtde = elacresqtde.replaceFirst(";", "");
                econtainers = econtainers.replaceFirst(";", "");

                xml += Xmls.tag("eguia", eguia);
                xml += Xmls.tag("eserie", eserie);
                xml += Xmls.tag("evalor", evalor);
                xml += Xmls.tag("emoeda", emoeda);
                xml += Xmls.tag("eseqrota", eseqrota);
                xml += Xmls.tag("eparada", eparada);
                xml += Xmls.tag("eqtlacres", eqtlacres);
                xml += Xmls.tag("elacres", elacres);
                xml += Xmls.tag("elacresvalor", elacresvalor);
                xml += Xmls.tag("elacresqtde", elacresqtde);
                xml += Xmls.tag("econtainers", econtainers);
                xml += Xmls.tag("eos", os);
                xml += "</servico>";
            }

            Trace.gerarTrace(context, servletName, "Fim do loop - informações guias", sCodPessoa, persistencia.getEmpresa(), logerro);
            return xml;
        } catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    public static String ultimoServico(String matricula, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            //carrega a rota
            CarregaRota list_rota1;
            try {
                list_rota1 = CarregaRotaDao.getUltimaParada(matricula, dataAtual, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota - " + e.getMessage());
            }
            String xml = "";
            xml += "<ultimo>";
            xml += Xmls.tag("rota", list_rota1.getRota().getRota());
            xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
            xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
            xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
            xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
            xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
            xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
            xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
            xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
            xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
            xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
            xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
            xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
            xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
            xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
            xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
            xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
            xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
            xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
            xml += Xmls.tag("origem", list_rota1.getCliOri().getNRed());
            xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
            xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
            xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
            xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
            xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
            xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
            xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
            xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
            xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
            xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
            xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
            xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
            xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
            xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
            xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
            xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
            xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
            xml += Xmls.tag("os", list_rota1.getOS().getOS());
            xml += Xmls.tag("origempedido", list_rota1.getOS().getCliente());
            xml += Xmls.tag("nredorigempedido", list_rota1.getOS().getNRed());
            xml += Xmls.tag("dstpedido", list_rota1.getOS().getCliDst());
            xml += Xmls.tag("nreddstpedido", list_rota1.getOS().getNRedDst());
            xml += "</ultimo>";

            return xml;
        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    public static String formasDePagamento(Persistencia persistencia) throws Exception {
        try {
            //carrega a rota
            List<FormasPgto> formasPagamento;
            try {
                formasPagamento = FormasPgtoDao.listarFormasPagamento(persistencia);
            } catch (Exception e) {
                formasPagamento = new ArrayList<>();
            }
            String xml = "";
            for (FormasPgto formaPgto : formasPagamento) {
                xml += Xmls.tag("formapgto", Xmls.tag("Codigo", formaPgto.getCodigo())
                        + Xmls.tag("Descricao", formaPgto.getDescricao())
                        + Xmls.tag("Operador", formaPgto.getOperador())
                        + Xmls.tag("Dt_Alter", formaPgto.getDt_Alter())
                        + Xmls.tag("Hr_Alter", formaPgto.getHr_Alter()));
            }
            xml += "";

            return xml;
        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    /**
     * Retorna um serviço a ser feitos pela rota do chefe de equipe que foi
     * digitado a matricula e senha
     *
     * @param parada - numero da parada
     * @param param
     * @param matricula
     * @param persistencia - classe de conexão
     * @param codfil
     * @param persistCentral
     * @return - XML contendo a lista de serviços Legenda:
     * <nr> numero total de registros na lista
     * <f>Fulano, Fim de Rota
     * <e> erro ou logou
     * @throws Exception - pode gerar exception
     */
    public static String CarregaRotaPorParada(String parada, String param, String matricula, String codfil,
            Persistencia persistencia, Persistencia persistCentral) throws Exception {
        try {
            //carrega a rota
            List<CarregaRota> list_rota = new ArrayList<>();
            try {
                list_rota.add(CarregaRotaDao.getRotaPorParada(parada, matricula, persistencia));
            } catch (Exception e) {
                throw new Exception("Falha ao carregar parada - " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                throw new Exception("Fim de Rota");
            }
            List<PessoasClientesPW> lpw = new ArrayList();
            //busca todas as pessoas que respondem por cada empresa
            ClientesPessoasDao CPD = new ClientesPessoasDao();
            codClienteAuthDAO = new CodClienteAuthDao();

            List<ClientesPessoas> lCPD = CPD.getPessoas(list_rota, persistencia);
            if (lCPD.size() > 0) {
                //busca senha de cada pessoa responsável
                PessoaDao pd = new PessoaDao();
                List<Pessoa> lpessoa = pd.getPWs(lCPD, persistCentral);
                if (lpessoa.size() > 0) {
                    //associa senha que vem do banco central com os dados do banco do cliente código do cliente, na ordem da rota
                    for (int k = 0; k < list_rota.size(); k++) {
                        for (int i = 0; i < lCPD.size(); i++) {
                            if (list_rota.get(k).getCliOri().getCodCli().equals(lCPD.get(i).getCodCli())) {
                                for (int j = 0; j < lpessoa.size(); j++) {
                                    if (lCPD.get(i).getCodPessoaWEB().toString().equals(lpessoa.get(j).getCodigo().toString())) {
                                        PessoasClientesPW pcpw = new PessoasClientesPW();
                                        pcpw.setCodcli(lCPD.get(i).getCodCli());
                                        pcpw.setNomepessoa(lpessoa.get(j).getNome());
                                        pcpw.setPw(lpessoa.get(j).getPW());
                                        lpw.add(pcpw);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //busca código de caixa forte
            CxForteDao cxfdao = new CxForteDao();
            CxForte cxf = cxfdao.getCxForte(list_rota.get(0).getRota().getCodFil(), persistencia);
            String xml = "";
            int codcxf = 0;
            String pessoapw;
            int i = 0;
            boolean continua = true;
            for (CarregaRota list_rota1 : list_rota) {
                pessoapw = "";
                if (cxf.getCodCli().equals(list_rota1.getCliOri().getCodigo())) {
                    codcxf = 1;
                } else {
                    codcxf = 0;
                }
                while (continua && i < lpw.size()) {
                    if (list_rota1.getCliOri().getCodCli().equals(lpw.get(i).getCodcli())) {
                        pessoapw = lpw.get(i).getPw() + ";" + lpw.get(i).getNomepessoa() + ";";
                        i++;
                    } else {
                        continua = false;
                    }
                }
                if (!"".equals(pessoapw)) {
                    pessoapw = Xmls.tag("senharesp", pessoapw);
                }
                TesSaidaRtGuiasCxfGuiaDao oTesSaidaRtGuiasCxfGuiaDao = new TesSaidaRtGuiasCxfGuiaDao();
                List<GuiasList> lCxfGRtGTesG = new ArrayList();
                if (("E".equals(list_rota1.getRt_perc().getER())) && (!"A".equals(list_rota1.getRt_perc().getTipoSrv()))) {
                    lCxfGRtGTesG = oTesSaidaRtGuiasCxfGuiaDao.getTesSaidasRt_GuiasCxfGuiasEntrega(
                            list_rota1.getRota().getCodFil().toPlainString(), list_rota1.getRt_perc().getHora1(),
                            list_rota1.getRt_perc().getSequencia().toPlainString(),
                            String.valueOf(list_rota1.getRt_perc().getParada()), persistencia);
                }
                String eguia = "";
                String eserie = "";
                String evalor = "";
                String eseqrota = "";
                String eparada = "";
                String eqtlacres = "";
                String elacres = "";
                String os = "";
                continua = true;
                xml += "<servico>";
                xml += Xmls.tag("rota", list_rota1.getRota().getRota());
                xml += Xmls.tag("horaprev", list_rota1.getRt_perc().getHora1());
                xml += Xmls.tag("nred", list_rota1.getRt_perc().getNRed());
                xml += Xmls.tag("sequencia", list_rota1.getRt_perc().getSequencia().toString());
                xml += Xmls.tag("parada", String.valueOf(list_rota1.getRt_perc().getParada()));
                xml += Xmls.tag("codcli", list_rota1.getRt_perc().getCodCli1());
                xml += Xmls.tag("cxforte", String.valueOf(codcxf));
                xml += Xmls.tag("er", list_rota1.getRt_perc().getER());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("h1d", list_rota1.getRt_perc().getHora1D());
                xml += Xmls.tag("veiculo", list_rota1.getEscala().getVeiculo().toString());
                xml += Xmls.tag("endeori", list_rota1.getCliOri().getEnde());
                xml += Xmls.tag("bairroori", list_rota1.getCliOri().getBairro());
                xml += Xmls.tag("cidadeori", list_rota1.getCliOri().getCidade());
                xml += Xmls.tag("estadoori", list_rota1.getCliOri().getEstado());
                xml += Xmls.tag("chaveori", list_rota1.getCliOri().getNroChave().toPlainString());
                xml += Xmls.tag("latitude", list_rota1.getCliOri().getLatitude());
                xml += Xmls.tag("longitude", list_rota1.getCliOri().getLongitude());
                xml += Xmls.tag("codclidst", list_rota1.getRt_perc().getCodCli2());
                xml += Xmls.tag("destino", list_rota1.getCliDst().getNRed());
                xml += Xmls.tag("endedst", list_rota1.getCliDst().getEnde());
                xml += Xmls.tag("bairrodst", list_rota1.getCliDst().getBairro());
                xml += Xmls.tag("cidadedst", list_rota1.getCliDst().getCidade());
                xml += Xmls.tag("estadodst", list_rota1.getCliDst().getEstado());
                xml += Xmls.tag("hrcheg", list_rota1.getRt_perc().getHrCheg());
                xml += Xmls.tag("hrsaida", list_rota1.getRt_perc().getHrSaida());
                xml += Xmls.tag("hrbaixa", list_rota1.getRt_perc().getHrBaixa());
                xml += Xmls.tag("latitudedst", list_rota1.getCliDst().getLatitude());
                xml += Xmls.tag("longitudedst", list_rota1.getCliDst().getLongitude());
                xml += Xmls.tag("tiposrv", list_rota1.getRt_perc().getTipoSrv());
                xml += Xmls.tag("clifat", list_rota1.getCliFat().getNRed());
                xml += Xmls.tag("endfat", list_rota1.getCliFat().getEnde());
                xml += Xmls.tag("bairrofat", list_rota1.getCliFat().getBairro());
                xml += Xmls.tag("cidadefat", list_rota1.getCliFat().getCidade());
                xml += Xmls.tag("estadofat", list_rota1.getCliFat().getEstado());
                xml += Xmls.tag("cnpjfat", list_rota1.getCliFat().getCGC());
                xml += Xmls.tag("iefat", list_rota1.getCliFat().getIE());
                xml += Xmls.tag("os", list_rota1.getOS().getOS());
                for (GuiasList gl : lCxfGRtGTesG) {
                    eguia += ";" + gl.getGuia();
                    eserie += ";" + gl.getSerie();
                    evalor += ";" + gl.getValor();
                    eseqrota += ";" + gl.getSequenciaOri();
                    eparada += ";" + gl.getParadaOri();
                    eqtlacres += ";" + gl.getVolumes().size();
                    os += ";" + gl.getOS();

                    for (CxFGuiasVol cxfv : gl.getVolumes()) {
                        elacres += ";" + cxfv.getLacre();
                    }
                }
                eguia = eguia.replaceFirst(";", "");
                eserie = eserie.replaceFirst(";", "");
                evalor = evalor.replaceFirst(";", "");
                eseqrota = eseqrota.replaceFirst(";", "");
                eparada = eparada.replaceFirst(";", "");
                eqtlacres = eqtlacres.replaceFirst(";", "");
                elacres = elacres.replaceFirst(";", "");

                xml += Xmls.tag("eguia", eguia);
                xml += Xmls.tag("eserie", eserie);
                xml += Xmls.tag("evalor", evalor);
                xml += Xmls.tag("eseqrota", eseqrota);
                xml += Xmls.tag("eparada", eparada);
                xml += Xmls.tag("eqtlacres", eqtlacres);
                xml += Xmls.tag("elacres", elacres);
                xml += Xmls.tag("eos", os);
                //xml += "<senharesp>" +lpessoa.get(0).getPW()+";"+lpessoa.get(0).getNome()+ "</senharesp>";
                xml += pessoapw;
                xml += senhasClientes(list_rota1.getCliOri().getCodCli(), codfil, persistencia);
                xml += "</servico>";
            }
            return xml;
        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new Exception("Falha ao carregar rota - " + e.getMessage());
        }
    }

    private static String senhasClientes(String cliOri, String codFil, Persistencia persistencia) throws Exception {
        String resposta = "<senhas>";
        try {
            List<CodClienteAut> autorizacoes = codClienteAuthDAO.recuperarInformacoes(cliOri, codFil, persistencia);
            for (CodClienteAut autorizacao : autorizacoes) {
                resposta += autorizacao.getCodigo() + "," + autorizacao.getPwWeb() + ";";
            }
            if (resposta.contains(",")) {
                resposta = resposta.substring(0, resposta.length() - 1);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao carregar senha clientes - " + e.getMessage());
        }
        resposta += "</senhas>";
        return resposta;
    }

    public static boolean LogaRotaSupervisao(String Matricula, String Senha,
            Persistencia persistencia) throws Exception {

        boolean ret = false;

        //valida login e senha do usuário
        List<LoginRota> list_lrota;
        try {
            list_lrota = LoginDao.LoginRota(Matricula, Senha, persistencia);

        } catch (Exception e) {
            throw new Exception("Falha no login de Rota - " + e.getMessage());
        }
        for (LoginRota list_lrota1 : list_lrota) {
            ret = list_lrota1.getPessoa().getPWWeb().equals(Senha);
        }

        return ret;
    }

    public static boolean ConfirmaRotaSupervisao(String aparencia, String atribuicoes, String proativo, String materiais,
            String organizacao, String obs, String latitude, String longitude, String sequencia, String sCodPessoa, String FMatricula, String Senha, Persistencia persistencia) throws Exception {

        boolean ret = false;

        //valida login e senha do usuário
        List<LoginRota> list_lrota;
        try {
            list_lrota = LoginDao.LoginRota(sCodPessoa, Senha, persistencia);

        } catch (Exception e) {
            throw new Exception("Falha no login de Rota - " + e.getMessage());
        }
        for (LoginRota list_lrota1 : list_lrota) {
            if (!list_lrota1.getPessoa().getPWWeb().equals(Senha)) {
                ret = false;
            } else {
                CarregaRotaDao.ConfirmaSupervisaoRota(aparencia, atribuicoes, proativo, materiais,
                        organizacao, obs, latitude, longitude, sequencia, sCodPessoa, FMatricula, persistencia);
                ret = true;
            }
        }

        return ret;
    }

    public static String CarregaRotaSupervisao(String turno, String sCodPessoa, String Senha, String CodFil, String dataAtual, Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {
            //carrega a rota
            List<CarregaRota> list_rota;
            try {
                list_rota = CarregaRotaDao.getRotaSuperv(sCodPessoa, dataAtual, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar rota para supervisao- " + e.getMessage());
            }
            if (list_rota.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>rotas_2</resp>";
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_rota.size() + "</nreg>";

            for (int i = 0; i < list_rota.size(); i++) {
                xml += "<servico>";
                xml += "<rota>" + list_rota.get(i).getRota().getRota() + "</rota>";
                xml += "<sequencia>" + list_rota.get(i).getRt_perc().getSequencia().toString() + "</sequencia>";
                xml += "<parada>" + list_rota.get(i).getRt_perc().getParada() + "</parada>";
                xml += "<nred>" + list_rota.get(i).getRt_perc().getNRed() + "</nred>";
                xml += "<codcli1>" + list_rota.get(i).getRt_perc().getCodCli1() + "</codcli1>";
                xml += "<codfilcli>" + list_rota.get(i).getCliOri().getCodFil().toString() + "</codfilcli>";
                xml += "<descricao>" + list_rota.get(i).getFiliais().getDescricao() + "</descricao>";
                xml += "<veiculo>" + list_rota.get(i).getEscala().getVeiculo().toString() + "</veiculo>";
                xml += "<endeori>" + list_rota.get(i).getCliOri().getEnde() + "</endeori>";
                xml += "<horaprev>" + list_rota.get(i).getRt_perc().getHora1() + "</horaprev>";
                xml += "<laticli>" + list_rota.get(i).getCliOri().getLatitude() + "</laticli>";
                xml += "<longcli>" + list_rota.get(i).getCliOri().getLongitude() + "</longcli>";
                xml += "<horachegada>" + list_rota.get(i).getRt_perc().getHrCheg() + "</horachegada>";

                //Verificar se tem secao
                PstServDao oSecao = new PstServDao();
                String secao = oSecao.getSecao(persistencia, list_rota.get(i).getCliOri().getCodFil().toString(), list_rota.get(i).getRt_perc().getCodCli1());
                if (secao != null) {
                    //se tem secao verificar se funcinario ja foram supervisionado
                    PstServDao oPstServ = new PstServDao();
                    Boolean funcP = oPstServ.getFuncSuperv(turno, secao, list_rota.get(i).getCliOri().getCodFil().toString(), dataAtual, persistencia);
                    if (funcP) {
                        xml += "<Pendencia>0</Pendencia>";
                    } else {
                        xml += "<Pendencia>1</Pendencia>";
                    }
                } else {
                    if (list_rota.get(i).getRt_perc().getHrSaida() != null) {
                        xml += "<Pendencia>0</Pendencia>";
                    } else {
                        xml += "<Pendencia>1</Pendencia>";
                    }
                }
                xml += "</servico>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public static String CarregaParada(BigDecimal Sequencia, int Parada, Persistencia persistencia) throws Exception {
        try {
            List<CarregaParadaRota> list_parada;
            List<GuiaE> list_guia = null;
            list_parada = CarregaRotaDao.CarregaDadosParada(Sequencia.toString(), Parada, persistencia);
            if (list_parada.get(0).getRt_perc().getER().contains("E") && !list_parada.get(0).getRt_perc().getTipoSrv().contains("A")) {
                list_guia = GuiasMobile.getGuiasE(Long.parseLong("0"), list_parada.get(0).getRt_perc().getSequencia().toString(), String.valueOf(list_parada.get(0).getRt_perc().getParada()), list_parada.get(0).getRt_perc().getHora1(), list_parada.get(0).getRt_perc().getCodFil().toString(), persistencia);
            }
            String xml = "<seq>" + list_parada.get(0).getRt_perc().getSequencia().toString() + "</seq>"
                    + "<par>" + list_parada.get(0).getRt_perc().getParada() + "</par>"
                    + "<er>" + list_parada.get(0).getRt_perc().getER() + "</er>"
                    + "<nredori>" + list_parada.get(0).getCliorigem().getNRed() + "</nredori>"
                    + "<endori>" + list_parada.get(0).getCliorigem().getEnde() + "</endori>"
                    + "<bairroori>" + list_parada.get(0).getCliorigem().getBairro() + "</bairroori>"
                    + "<cidori>" + list_parada.get(0).getCliorigem().getCidade() + "</cidori>"
                    + "<chori>" + list_parada.get(0).getCliorigem().getNroChave().toString() + "</chori>";
            //caso seja uma parada de ENTREGA, nao existe cliente de destino
            if (!list_parada.get(0).getRt_perc().getER().equals("E")) {
                xml += "<nreddst>" + list_parada.get(0).getClidestino().getNRed() + "</nreddst>"
                        + "<enddst>" + list_parada.get(0).getClidestino().getEnde() + "</enddst>"
                        + "<bairrodst>" + list_parada.get(0).getClidestino().getBairro() + "</bairrodst>"
                        + "<ciddst>" + list_parada.get(0).getClidestino().getCidade() + "</ciddst>"
                        + "<chdst>" + list_parada.get(0).getClidestino().getNroChave().toString() + "</chdst>";
            }
            String xmlGuia = "";
            if (list_guia != null) {
                for (int i = 0; i < list_guia.size(); i++) {
                    xmlGuia = "<qtguias>" + list_guia.size() + "</qtguias>"
                            + "<guia" + i + ">" + list_guia.get(i).getGuia() + "</guia" + i + ">"
                            + "<serie" + i + ">" + list_guia.get(i).getSerie() + "</serie" + i + ">"
                            + "<valor" + i + ">" + list_guia.get(i).getValor() + "</valor" + i + ">";
                }
                xml += xmlGuia;
            }
            if (list_guia == null || list_guia.isEmpty()) {
                //caso a parada nao tenha guia, qtguias(quantidade de guias = 0)
                xmlGuia = "<qtguias>0</qtguias>";
                xml += xmlGuia;
            }
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

}
