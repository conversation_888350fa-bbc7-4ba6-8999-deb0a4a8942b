/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Controller.ESocial;

import Dados.Persistencia;
import SasBeans.ESocial.R1000;
import SasBeans.ESocial.R1070;
import SasBeans.ESocial.R2020;
import SasBeans.ESocial.R2020.InfoProcRetAd;
import SasBeans.ESocial.R2020.InfoProcRetPr;
import SasBeans.ESocial.R2020.InfoTpServ;
import SasBeans.ESocial.R2020.Nfs;
import SasBeans.ESocial.R2098;
import SasBeans.ESocial.R2099;
import SasBeans.ESocial.S1000;
import SasBeans.ESocial.S1005;
import SasBeans.ESocial.S1010;
import SasBeans.ESocial.S1020;
import SasBeans.ESocial.S1030;
import SasBeans.ESocial.S1040;
import SasBeans.ESocial.S1050;
import SasBeans.ESocial.S1200;
import SasBeans.ESocial.S1210;
import SasBeans.ESocial.S1210.DetRubrFer;
import SasBeans.ESocial.S1210.InfoPgto;
import SasBeans.ESocial.S1210.PenAlim;
import SasBeans.ESocial.S1210.RetPgtoTot;
import SasBeans.ESocial.S1280;
import SasBeans.ESocial.S1295;
import SasBeans.ESocial.S1298;
import SasBeans.ESocial.S1299;
import SasBeans.ESocial.S2190;
import SasBeans.ESocial.S2200;
import SasBeans.ESocial.S2205;
import SasBeans.ESocial.S2206;
import SasBeans.ESocial.S2210;
import SasBeans.ESocial.S2220;
import SasBeans.ESocial.S2230;
import SasBeans.ESocial.S2240;
import SasBeans.ESocial.S2250;
import SasBeans.ESocial.S2298;
import SasBeans.ESocial.S2299;
import SasBeans.ESocial.S2300;
import SasBeans.ESocial.S2306;
import SasBeans.ESocial.S2399;
import SasBeans.ESocial.S3000;
import SasBeans.XMLeSocial;
import SasDaos.FiliaisDao;
import SasDaos.XMLeSocialDao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.R1000Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.R1070Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.R2020Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.R2098Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.R2099Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1000Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1005Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1010Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1020Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1030Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1040Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1050Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1200Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1210Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1280Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1295Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1298Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S1299Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2190Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2200Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2205Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2206Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2210Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2220Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2230Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2240Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2250Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2298Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2299Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2300Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2306Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S2399Dao;
import br.com.sasw.pacotesuteis.sasdaos.esocial.S3000Dao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarStringMoeda;
import br.com.sasw.pacotesuteis.utilidades.LogradouroESocial;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ESocialSatWeb {

    public List<String> envioXML(String codFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filiaisDao = new FiliaisDao();
            return filiaisDao.infoEnvioESocial(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1000> getS1000(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            S1000Dao s1000dao = new S1000Dao();
            return s1000dao.get(codFil, compet, ambiente, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1000XML(String xmlPadrao, S1000 s1000) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtInfoEmpregador_Id", "ID1" + s1000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_tpAmb", s1000.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1000.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1000.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1000.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1000.getIdeEmpregador_nrInsc())
                    .replace("idePeriodo_iniValid", s1000.getIdePeriodo_iniValid())
                    .replace("infoCadastro_nmRazao", s1000.getInfoCadastro_nmRazao())
                    .replace("infoCadastro_classTrib", "99") //99 - Pessoas Juridicas em Geral.
                    .replace("infoCadastro_natJurid", s1000.getInfoCadastro_natJurid())
                    .replace("infoCadastro_indCoop", s1000.getInfoCadastro_indCoop())
                    .replace("infoCadastro_indConstr", s1000.getInfoCadastro_indConstr())
                    .replace("infoCadastro_indDesFolha", s1000.getInfoCadastro_indDesFolha())
                    .replace("<indPorte>infoCadastro_indPorte</indPorte>", "")
                    .replace("infoCadastro_indOptRegEletron", s1000.getInfoCadastro_indOptRegEletron())
                    .replace("infoCadastro_indEntEd", s1000.getInfoCadastro_indEntEd())
                    .replace("infoCadastro_indEtt", s1000.getInfoCadastro_indEtt())
                    .replace("contato_nmCtt", s1000.getContato_nmCtt())
                    .replace("contato_cpfCtt", s1000.getContato_cpfCtt())
                    .replace("contato_foneFixo", s1000.getContato_foneFixo())
                    .replace("contato_email", s1000.getContato_email())
                    .replace("softwareHouse_cnpjSoftHouse", s1000.getSoftwareHouse_cnpjSoftHouse())
                    .replace("softwareHouse_nmRazao", s1000.getSoftwareHouse_nmRazao())
                    .replace("softwareHouse_nmCont", s1000.getSoftwareHouse_nmCont())
                    .replace("softwareHouse_telefone", s1000.getSoftwareHouse_telefone())
                    .replace("softwareHouse_email", s1000.getSoftwareHouse_email())
                    .replace("situacaoPJ_indSitPJ", s1000.getSituacaoPJ_indSitPJ());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1005> getS1005(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1005Dao s1005dao = new S1005Dao();
            List<S1005> listaS1005 = s1005dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1005 s1005 : listaS1005) {
                idPadrao = s1005.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1005.setEvtTabEstab_Id("ID1" + id2);
                s1005.setIdeEvento_tpAmb(tipoAmbiente);
                s1005.setIdeEmpregador_tpInsc(s1005.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1005.setIdeEmpregador_nrInsc(s1005.getIdeEmpregador_nrInsc().substring(0, 8));
                s1005.setIdeEstab_tpInsc(s1005.getIdeEstab_tpInsc().equals("J") ? "1" : "2");
                s1005.setIdeEstab_iniValid(validade);
                s1005.setAliqGilrat_aliqRat(s1005.getAliqGilrat_aliqRat().replace(".0", ""));
                s1005.setAliqRatAjust_aliqRatAjust(new BigDecimal(new BigDecimal(s1005.getAliqGilrat_aliqRat())
                        .multiply(new BigDecimal(s1005.getAliqGilrat_fap())).toPlainString()).setScale(4).toPlainString());
            }
            return listaS1005;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1005XML(String xmlPadrao, S1005 s1005) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabEstab_Id", s1005.getEvtTabEstab_Id())
                    .replace("ideEvento_procEmi", s1005.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1005.getIdeEvento_verProc())
                    .replace("ideEvento_tpAmb", s1005.getIdeEvento_tpAmb())
                    .replace("ideEmpregador_tpInsc", s1005.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1005.getIdeEmpregador_nrInsc())
                    .replace("ideEstab_tpInsc", s1005.getIdeEstab_tpInsc())
                    .replace("ideEstab_nrInsc", s1005.getIdeEstab_nrInsc())
                    .replace("ideEstab_iniValid", s1005.getIdeEstab_iniValid())
                    .replace("dadosEstab_cnaePrep", s1005.getDadosEstab_cnaePrep())
                    .replace("aliqGilrat_aliqRat", s1005.getAliqGilrat_aliqRat())
                    .replace("aliqGilrat_fap", s1005.getAliqGilrat_fap())
                    .replace("aliqRatAjust_aliqRatAjust", s1005.getAliqRatAjust_aliqRatAjust())
                    .replace("regPt_regPt", s1005.getRegPt_regPt())
                    .replace("contApr_contApr", s1005.getContApr_contApr());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1010> getS1010(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1010Dao s1010dao = new S1010Dao();
            List<S1010> listaS1010 = s1010dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1010 s1010 : listaS1010) {
                idPadrao = s1010.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1010.setEvtTabRubrica_Id("ID1" + id2);
                s1010.setIdeEmpregador_tpInsc(s1010.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1010.setIdeEmpregador_nrInsc(s1010.getIdeEmpregador_nrInsc().substring(0, 8));
                s1010.setIdeEvento_tpAmb(tipoAmbiente);
                s1010.setIdeRubrica_iniValid(validade);
            }
            return listaS1010;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1010XML(String xmlPadrao, S1010 s1010, String tipo) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabRubrica_Id", s1010.getEvtTabRubrica_Id())
                    .replace("ideEvento_tpAmb", s1010.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1010.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1010.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1010.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1010.getIdeEmpregador_nrInsc())
                    .replace("ideRubrica_codRubr", s1010.getIdeRubrica_codRubr())
                    .replace("ideRubrica_ideTabRubr", s1010.getIdeRubrica_ideTabRubr())
                    .replace("ideRubrica_iniValid", s1010.getIdeRubrica_iniValid())
                    .replace("dadosRubrica_dscRubr", s1010.getDadosRubrica_dscRubr())
                    .replace("dadosRubrica_natRubr", s1010.getDadosRubrica_natRubr())
                    .replace("dadosRubrica_tpRubr", s1010.getDadosRubrica_tpRubr())
                    .replace("dadosRubrica_codIncCP", s1010.getDadosRubrica_codIncCP())
                    .replace("dadosRubrica_codIncIRRF", s1010.getDadosRubrica_codIncIRRF())
                    .replace("dadosRubrica_codIncFGTS", s1010.getDadosRubrica_codIncFGTS())
                    .replace("dadosRubrica_codIncSIND", s1010.getDadosRubrica_codIncSIND());
            if (tipo.equals("ALTERACAO")) {
                xml = xml.replace("inclusao", "alteracao");
            }
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1020> getS1020(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1020Dao s1020dao = new S1020Dao();
            List<S1020> listaS1020;
            if (persistencia.getEmpresa().contains("LOYAL")) {
                listaS1020 = s1020dao.get2(codFil, validade, tipoAmbiente, persistencia);
            } else {
                listaS1020 = s1020dao.get(codFil, validade, tipoAmbiente, persistencia);
            }
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1020 s1020 : listaS1020) {
                idPadrao = s1020.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1020.setEvtTabLotacao_Id("ID1" + id2);
                s1020.setIdeEvento_tpAmb(tipoAmbiente);
//                s1020.setIdeEmpregador_tpInsc(s1020.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1020.setIdeEmpregador_nrInsc(s1020.getIdeEmpregador_nrInsc().substring(0, 8));
                s1020.setIdeLotacao_codLotacao(s1020.getIdeLotacao_codLotacao().replace(".0", ""));
                s1020.setIdeLotacao_iniValid(validade);
            }
            return listaS1020;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1020XML(String xmlPadrao, S1020 s1020) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabLotacao_Id", s1020.getEvtTabLotacao_Id())
                    .replace("ideEvento_tpAmb", s1020.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1020.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1020.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1020.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1020.getIdeEmpregador_nrInsc())
                    .replace("ideLotacao_codLotacao", s1020.getIdeLotacao_codLotacao())
                    .replace("ideLotacao_iniValid", s1020.getIdeLotacao_iniValid())
                    .replace("dadosLotacao_tpLotacao", s1020.getDadosLotacao_tpLotacao())
                    .replace("dadosLotacao_tpInsc", s1020.getDadosLotacao_tpInsc())
                    .replace("dadosLotacao_nrInsc", s1020.getDadosLotacao_nrInsc())
                    .replace("fpasLotacao_fpas", s1020.getFpasLotacao_fpas())
                    .replace("fpasLotacao_codTercs", s1020.getFpasLotacao_codTercs());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1030> getS1030(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1030Dao s1030dao = new S1030Dao();
            List<S1030> listaS1030 = s1030dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1030 s1030 : listaS1030) {
                idPadrao = s1030.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1030.setEvtTabCargo_Id("ID1" + id2);
                s1030.setIdeEvento_tpAmb(tipoAmbiente);
                s1030.setIdeEvento_procEmi(s1030.getIdeEvento_procEmi());
                s1030.setIdeEvento_verProc(s1030.getIdeEvento_verProc());
                s1030.setIdeEmpregador_tpInsc(s1030.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1030.setIdeEmpregador_nrInsc(s1030.getIdeEmpregador_nrInsc().substring(0, 8));
                s1030.setIdeCargo_codCargo(s1030.getIdeCargo_codCargo().replace(".0", ""));
                s1030.setIdeCargo_iniValid(validade);
                s1030.setDadosCargo_nmCargo(s1030.getDadosCargo_nmCargo());
                s1030.setDadosCargo_codCBO(s1030.getDadosCargo_codCBO());
            }
            return listaS1030;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1030XML(String xmlPadrao, S1030 s1030) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabCargo_Id", s1030.getEvtTabCargo_Id())
                    .replace("ideEvento_tpAmb", s1030.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1030.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1030.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1030.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1030.getIdeEmpregador_nrInsc())
                    .replace("ideCargo_codCargo", s1030.getIdeCargo_codCargo())
                    .replace("ideCargo_iniValid", s1030.getIdeCargo_iniValid())
                    .replace("dadosCargo_nmCargo", s1030.getDadosCargo_nmCargo())
                    .replace("dadosCargo_codCBO", s1030.getDadosCargo_codCBO());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1040> getS1040(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1040Dao s1040dao = new S1040Dao();
            List<S1040> listaS1040 = s1040dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1040 s1040 : listaS1040) {
                idPadrao = s1040.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1040.setEvtTabFuncao_Id("ID1" + id2);
                s1040.setIdeEvento_tpAmb(tipoAmbiente);
                s1040.setIdeEmpregador_tpInsc(s1040.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1040.setIdeEmpregador_nrInsc(s1040.getIdeEmpregador_nrInsc().substring(0, 8));
                s1040.setIdeFuncao_codFuncao(s1040.getIdeFuncao_codFuncao().replace(".0", ""));
                s1040.setIdeFuncao_iniValid(validade);
            }
            return listaS1040;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1040XML(String xmlPadrao, S1040 s1040) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabFuncao_Id", s1040.getEvtTabFuncao_Id())
                    .replace("ideEvento_tpAmb", s1040.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1040.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1040.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1040.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1040.getIdeEmpregador_nrInsc())
                    .replace("ideFuncao_codFuncao", s1040.getIdeFuncao_codFuncao())
                    .replace("ideFuncao_iniValid", s1040.getIdeFuncao_iniValid())
                    .replace("dadosFuncao_dscFuncao", s1040.getDadosFuncao_dscFuncao())
                    .replace("dadosFuncao_codCBO", s1040.getDadosFuncao_codCBO());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1050> getS1050(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1050Dao s1050dao = new S1050Dao();
            List<S1050> listaS1050 = s1050dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1050 s1050 : listaS1050) {
                idPadrao = s1050.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1050.setEvtTabHorTur_Id("ID1" + id2);
                s1050.setIdeEvento_tpAmb(tipoAmbiente);
                s1050.setIdeEmpregador_tpInsc(s1050.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1050.setIdeEmpregador_nrInsc(s1050.getIdeEmpregador_nrInsc().substring(0, 8));
                s1050.setIdeHorContratual_iniValid(validade);
                s1050.setDadosHorContratual_hrEntr(s1050.getDadosHorContratual_hrEntr().replace(":", ""));
                s1050.setDadosHorContratual_hrSaida(s1050.getDadosHorContratual_hrSaida().replace(":", ""));
                s1050.setDadosHorContratual_durJornada(s1050.getDadosHorContratual_durJornada().replace(".0", ""));
                s1050.setHorarioIntervalo_durInterv(s1050.getHorarioIntervalo_durInterv().replace(".0", ""));
            }
            return listaS1050;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1050XML(String xmlPadrao, S1050 s1050, String tipo) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtTabHorTur_Id", s1050.getEvtTabHorTur_Id())
                    .replace("ideEvento_tpAmb", s1050.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1050.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1050.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1050.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1050.getIdeEmpregador_nrInsc())
                    .replace("ideHorContratual_codHorContrat", s1050.getIdeHorContratual_codHorContrat())
                    .replace("ideHorContratual_iniValid", s1050.getIdeHorContratual_iniValid())
                    .replace("dadosHorContratual_hrEntr", s1050.getDadosHorContratual_hrEntr().replace(":", ""))
                    .replace("dadosHorContratual_hrSaida", s1050.getDadosHorContratual_hrSaida().replace(":", ""))
                    .replace("dadosHorContratual_durJornada", s1050.getDadosHorContratual_durJornada().replace(".0", ""))
                    .replace("dadosHorContratual_perHorFlexivel", s1050.getDadosHorContratual_perHorFlexivel())
                    .replace("horarioIntervalo_tpInterv", s1050.getHorarioIntervalo_tpInterv())
                    .replace("horarioIntervalo_durInterv", s1050.getHorarioIntervalo_durInterv().replace(".0", ""));
            if (tipo.equals("ALTERACAO")) {
                xml = xml.replace("inclusao", "alteracao");
            }
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1200> getS1200(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1200Dao s1200dao = new S1200Dao();
            List<S1200> listaS1200 = s1200dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1200 s1200 : listaS1200) {
                idPadrao = s1200.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1200.setEvtRemun_Id("ID1" + id2);
                s1200.setIdeEvento_tpAmb(tipoAmbiente);
                s1200.setIdeEmpregador_tpInsc(s1200.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1200.setIdeEmpregador_nrInsc(s1200.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS1200;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1200XML(String xmlPadrao, S1200 s1200) throws Exception {
        try {
            boolean existe = false;

            existe = !s1200.getIdeEvento_indRetif().equals("")
                    || !s1200.getIdeEvento_nrRecibo().equals("")
                    || !s1200.getIdeEvento_indApuracao().equals("")
                    || !s1200.getIdeEvento_perApur().equals("")
                    || !s1200.getIdeEvento_tpAmb().equals("")
                    || !s1200.getIdeEvento_procEmi().equals("")
                    || !s1200.getIdeEvento_verProc().equals("");
            if (existe) {
                xmlPadrao = xmlPadrao.replace("<indRetif>ideEvento_indRetif</indRetif>",
                        s1200.getIdeEvento_indRetif().equals("") ? "" : "<indRetif>" + s1200.getIdeEvento_indRetif() + "</indRetif>")
                        .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>",
                                s1200.getIdeEvento_nrRecibo().equals("") ? "" : "<nrRecibo>" + s1200.getIdeEvento_nrRecibo() + "</nrRecibo>")
                        .replace("<indApuracao>ideEvento_indApuracao</indApuracao>",
                                s1200.getIdeEvento_indApuracao().equals("") ? "" : "<indApuracao>" + s1200.getIdeEvento_indApuracao() + "</indApuracao>")
                        .replace("<perApur>ideEvento_perApur</perApur>",
                                s1200.getIdeEvento_perApur().equals("") ? "" : "<perApur>" + s1200.getIdeEvento_perApur() + "</perApur>")
                        .replace("<tpAmb>ideEvento_tpAmb</tpAmb>",
                                s1200.getIdeEvento_tpAmb().equals("") ? "" : "<tpAmb>" + s1200.getIdeEvento_tpAmb() + "</tpAmb>")
                        .replace("<procEmi>ideEvento_procEmi</procEmi>",
                                s1200.getIdeEvento_procEmi().equals("") ? "" : "<procEmi>" + s1200.getIdeEvento_procEmi() + "</procEmi>")
                        .replace("<verProc>ideEvento_verProc</verProc>",
                                s1200.getIdeEvento_verProc().equals("") ? "" : "<verProc>" + s1200.getIdeEvento_verProc() + "</verProc>");
            } else {
                xmlPadrao = xmlPadrao.replace("<ideEvento>", "")
                        .replace("<indRetif>ideEvento_indRetif</indRetif>", "")
                        .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "")
                        .replace("<indApuracao>ideEvento_indApuracao</indApuracao>", "")
                        .replace("<perApur>ideEvento_perApur</perApur>", "")
                        .replace("<tpAmb>ideEvento_tpAmb</tpAmb>", "")
                        .replace("<procEmi>ideEvento_procEmi</procEmi>", "")
                        .replace("<verProc>ideEvento_verProc</verProc>", "")
                        .replace("</ideEvento>", "");
            }

            existe = !s1200.getIdeEmpregador_tpInsc().equals("") || !s1200.getIdeEmpregador_nrInsc().equals("");
            if (existe) {
                xmlPadrao = xmlPadrao.replace("<tpInsc>ideEmpregador_tpInsc</tpInsc>",
                        s1200.getIdeEmpregador_tpInsc().equals("") ? "" : "<tpInsc>" + s1200.getIdeEmpregador_tpInsc() + "</tpInsc>")
                        .replace("<nrInsc>ideEmpregador_nrInsc</nrInsc>",
                                s1200.getIdeEmpregador_nrInsc().equals("") ? "" : "<nrInsc>" + s1200.getIdeEmpregador_nrInsc() + "</nrInsc>");
            } else {
                xmlPadrao = xmlPadrao.replace("<ideEmpregador>", "")
                        .replace("<tpInsc>ideEmpregador_tpInsc</tpInsc>", "")
                        .replace("<nrInsc>ideEmpregador_nrInsc</nrInsc>", "")
                        .replace("</ideEmpregador>", "");
            }

            existe = !s1200.getIdeTrabalhador_cpfTrab().equals("")
                    || !s1200.getIdeTrabalhador_nisTrab().equals("")
                    || !s1200.getInfoMV_indMV().equals("")
                    //                    || !s1200.getRemunOutrEmpr_tpInsc().equals("")
                    //                    || !s1200.getRemunOutrEmpr_nrInsc().equals("")
                    //                    || !s1200.getRemunOutrEmpr_codCateg().equals("")
                    //                    || !s1200.getRemunOutrEmpr_vlrRemunOE().equals("")
                    || !s1200.getInfoComplem_nmTrab().equals("")
                    || !s1200.getInfoComplem_dtNascto().equals("")
                    || !s1200.getSucessaoVinc_cnpjEmpregAnt().equals("")
                    || !s1200.getSucessaoVinc_matricAnt().equals("")
                    || !s1200.getSucessaoVinc_dtAdm().equals("")
                    || !s1200.getSucessaoVinc_observacao().equals("")
                    || !s1200.getProcJudTrab_tpTrib().equals("")
                    || !s1200.getProcJudTrab_nrProcJud().equals("")
                    || !s1200.getProcJudTrab_codSusp().equals("")
                    || !s1200.getInfoInterm_qtdDiasInterm().equals("0.0")
                    || !s1200.getInfoInterm_qtdDiasInterm().equals("0");
            if (existe) {
                xmlPadrao = xmlPadrao
                        .replace("<cpfTrab>ideTrabalhador_cpfTrab</cpfTrab>",
                                s1200.getIdeTrabalhador_cpfTrab().equals("") ? "" : "<cpfTrab>" + s1200.getIdeTrabalhador_cpfTrab() + "</cpfTrab>")
                        .replace("<nisTrab>ideTrabalhador_nisTrab</nisTrab>",
                                s1200.getIdeTrabalhador_nisTrab().equals("") ? "" : "<nisTrab>" + s1200.getIdeTrabalhador_nisTrab() + "</nisTrab>");

//                existe = !s1200.getInfoMV_indMV().equals("")
//                        || !s1200.getRemunOutrEmpr_tpInsc().equals("")
//                        || !s1200.getRemunOutrEmpr_nrInsc().equals("")
//                        || !s1200.getRemunOutrEmpr_codCateg().equals("")
//                        || !s1200.getRemunOutrEmpr_vlrRemunOE().equals("");
                existe = !s1200.getRemunOutrEmpr().isEmpty();
                StringBuilder xmlRemunOutrEmpr = new StringBuilder();
                String remunOutrEmprXml = "    <remunOutrEmpr>\n"
                        + "                    <tpInsc>remunOutrEmpr_tpInsc</tpInsc>\n"
                        + "                    <nrInsc>remunOutrEmpr_nrInsc</nrInsc>\n"
                        + "                    <codCateg>remunOutrEmpr_codCateg</codCateg>\n"
                        + "                    <vlrRemunOE>remunOutrEmpr_vlrRemunOE</vlrRemunOE>\n"
                        + "                </remunOutrEmpr>";

                if (existe) {
                    xmlPadrao = xmlPadrao
                            .replace("infoMV_indMV", "2");

                    for (S1200.RemunOutrEmpr remunOutrEmpr : s1200.getRemunOutrEmpr()) {
                        xmlRemunOutrEmpr.append(
                                remunOutrEmprXml
                                        .replace("<tpInsc>remunOutrEmpr_tpInsc</tpInsc>",
                                                remunOutrEmpr.getTpInsc().equals("") ? "" : "<tpInsc>" + remunOutrEmpr.getTpInsc() + "</tpInsc>")
                                        .replace("<nrInsc>remunOutrEmpr_nrInsc</nrInsc>",
                                                remunOutrEmpr.getNrInsc().equals("") ? "" : "<nrInsc>" + remunOutrEmpr.getNrInsc() + "</nrInsc>")
                                        .replace("<codCateg>remunOutrEmpr_codCateg</codCateg>",
                                                remunOutrEmpr.getCodCateg().equals("") ? "" : "<codCateg>" + remunOutrEmpr.getCodCateg() + "</codCateg>")
                                        .replace("<vlrRemunOE>remunOutrEmpr_vlrRemunOE</vlrRemunOE>",
                                                remunOutrEmpr.getVlrRemunOE().equals("") ? "" : "<vlrRemunOE>" + remunOutrEmpr.getVlrRemunOE() + "</vlrRemunOE>")
                        );
                    }
                    xmlPadrao = xmlPadrao.replace("remunOutEmpr_info", xmlRemunOutrEmpr.toString());
                } else {
                    remunOutrEmprXml = "";
                    xmlPadrao = xmlPadrao
                            .replace("<infoMV>", "")
                            .replace("<indMV>infoMV_indMV</indMV>", "")
                            .replace("remunOutEmpr_info", "")
                            .replace("</infoMV>", "");
                }

                existe = !s1200.getInfoComplem_nmTrab().equals("")
                        || !s1200.getInfoComplem_dtNascto().equals("")
                        || !s1200.getSucessaoVinc_cnpjEmpregAnt().equals("")
                        || !s1200.getSucessaoVinc_matricAnt().equals("")
                        || !s1200.getSucessaoVinc_dtAdm().equals("")
                        || !s1200.getSucessaoVinc_observacao().equals("");
                if (existe) {
                    xmlPadrao = xmlPadrao.replace("<nmTrab>infoComplem_nmTrab</nmTrab>",
                                    s1200.getInfoComplem_nmTrab().equals("") ? "" : "<nmTrab>"+s1200.getInfoComplem_nmTrab()+"</nmTrab>")
                            .replace("<dtNascto>infoComplem_dtNascto</dtNascto>",
                                    s1200.getInfoComplem_dtNascto().equals("") ? "" : "<dtNascto>"+s1200.getInfoComplem_dtNascto()+"</dtNascto>");

                    existe = !s1200.getSucessaoVinc_cnpjEmpregAnt().equals("")
                            || !s1200.getSucessaoVinc_matricAnt().equals("")
                            || !s1200.getSucessaoVinc_dtAdm().equals("")
                            || !s1200.getSucessaoVinc_observacao().equals("");
                    if (existe) {
                        xmlPadrao = xmlPadrao
                                .replace("<cnpjEmpregAnt>sucessaoVinc_cnpjEmpregAnt</cnpjEmpregAnt>",
                                        s1200.getSucessaoVinc_cnpjEmpregAnt().equals("") ? "" : "<cnpjEmpregAnt>" + s1200.getSucessaoVinc_cnpjEmpregAnt() + "</cnpjEmpregAnt>")
                                .replace("<matricAnt>sucessaoVinc_matricAnt</matricAnt>",
                                        s1200.getSucessaoVinc_matricAnt().equals("") ? "" : "<matricAnt>" + s1200.getSucessaoVinc_matricAnt() + "</matricAnt>")
                                .replace("<dtAdm>sucessaoVinc_dtAdm</dtAdm>",
                                        s1200.getSucessaoVinc_dtAdm().equals("") ? "" : "<dtAdm>" + s1200.getSucessaoVinc_dtAdm() + "</dtAdm>")
                                .replace("<observacao>sucessaoVinc_observacao</observacao>",
                                        s1200.getSucessaoVinc_observacao().equals("") ? "" : "<observacao>" + s1200.getSucessaoVinc_observacao() + "</observacao>");
                    } else {
                        xmlPadrao = xmlPadrao
                                .replace("<sucessaoVinc>", "")
                                .replace("<cnpjEmpregAnt>sucessaoVinc_cnpjEmpregAnt</cnpjEmpregAnt>", "")
                                .replace("<matricAnt>sucessaoVinc_matricAnt</matricAnt>", "")
                                .replace("<dtAdm>sucessaoVinc_dtAdm</dtAdm>", "")
                                .replace("<observacao>sucessaoVinc_observacao</observacao>", "")
                                .replace("</sucessaoVinc>", "");
                    }

                } else {
                    xmlPadrao = xmlPadrao
                            .replace("<infoComplem>", "")
                            .replace("<nmTrab>infoComplem_nmTrab</nmTrab>", "")
                            .replace("<dtNascto>infoComplem_dtNascto</dtNascto>", "")
                            .replace("<sucessaoVinc>", "")
                            .replace("<cnpjEmpregAnt>sucessaoVinc_cnpjEmpregAnt</cnpjEmpregAnt>", "")
                            .replace("<matricAnt>sucessaoVinc_matricAnt</matricAnt>", "")
                            .replace("<dtAdm>sucessaoVinc_dtAdm</dtAdm>", "")
                            .replace("<observacao>sucessaoVinc_observacao</observacao>", "")
                            .replace("</sucessaoVinc>", "")
                            .replace("</infoComplem>", "");
                }

                existe = !s1200.getProcJudTrab_tpTrib().equals("")
                        || !s1200.getProcJudTrab_nrProcJud().equals("")
                        || !s1200.getProcJudTrab_codSusp().equals("");
                if (existe) {
                    xmlPadrao = xmlPadrao
                            .replace("<tpTrib>procJudTrab_tpTrib</tpTrib>",
                                    s1200.getProcJudTrab_tpTrib().equals("") ? "" : "<tpTrib>" + s1200.getProcJudTrab_tpTrib() + "</tpTrib>")
                            .replace("<nrProcJud>procJudTrab_nrProcJud</nrProcJud>",
                                    s1200.getProcJudTrab_nrProcJud().equals("") ? "" : "<nrProcJud>" + s1200.getProcJudTrab_nrProcJud() + "</nrProcJud>")
                            .replace("<codSusp>procJudTrab_codSusp</codSusp>",
                                    s1200.getProcJudTrab_codSusp().equals("") ? "" : "<codSusp>" + s1200.getProcJudTrab_codSusp() + "</codSusp>");
                } else {
                    xmlPadrao = xmlPadrao
                            .replace("<procJudTrab>", "")
                            .replace("<tpTrib>procJudTrab_tpTrib</tpTrib>", "")
                            .replace("<nrProcJud>procJudTrab_nrProcJud</nrProcJud>", "")
                            .replace("<codSusp>procJudTrab_codSusp</codSusp>", "")
                            .replace("</procJudTrab>", "");
                }

                //existe = !s1200.getInfoInterm_qtdDiasInterm().equals("0.0") && !s1200.getInfoInterm_qtdDiasInterm().equals("0");
                existe = s1200.getIdeTrabalhador_codCateg().equals("111");
                if (existe) {
                    xmlPadrao = xmlPadrao
                            .replace("<dia>infoInterm_qtdDiasInterm</dia>",
                                    s1200.getInfoInterm_qtdDiasInterm().equals("") ? "" : "<dia>" + s1200.getInfoInterm_qtdDiasInterm().replace(".0", "") + "</dia>");
                } else {
                    xmlPadrao = xmlPadrao
                            .replace("<infoInterm>", "")
                            .replace("<dia>infoInterm_qtdDiasInterm</dia>", "")
                            .replace("</infoInterm>", "");
                }

            } else {
                xmlPadrao = xmlPadrao
                        .replace("<ideTrabalhador>", "")
                        .replace("<cpfTrab>ideTrabalhador_cpfTrab</cpfTrab>", "")
                        .replace("<nisTrab>ideTrabalhador_nisTrab</nisTrab>", "")
                        .replace("<infoMV>", "")
                        .replace("<indMV>infoMV_indMV</indMV>", "")
                        .replace("<remunOutrEmpr>", "")
                        .replace("<tpInsc>remunOutrEmpr_tpInsc</tpInsc>", "")
                        .replace("<nrInsc>remunOutrEmpr_nrInsc</nrInsc>", "")
                        .replace("<codCateg>remunOutrEmpr_codCateg</codCateg>", "")
                        .replace("<vlrRemunOE>remunOutrEmpr_vlrRemunOE</vlrRemunOE>", "")
                        .replace("</remunOutrEmpr>", "")
                        .replace("</infoMV>", "")
                        .replace("<infoComplem>", "")
                        .replace("<nmTrab>infoComplem_nmTrab</nmTrab>", "")
                        .replace("<dtNascto>infoComplem_dtNascto</dtNascto>", "")
                        .replace("<sucessaoVinc>", "")
                        .replace("<cnpjEmpregAnt>sucessaoVinc_cnpjEmpregAnt</cnpjEmpregAnt>", "")
                        .replace("<matricAnt>sucessaoVinc_matricAnt</matricAnt>", "")
                        .replace("<dtAdm>sucessaoVinc_dtAdm</dtAdm>", "")
                        .replace("<observacao>sucessaoVinc_observacao</observacao>", "")
                        .replace("</sucessaoVinc>", "")
                        .replace("</infoComplem>", "")
                        .replace("<procJudTrab>", "")
                        .replace("<tpTrib>procJudTrab_tpTrib</tpTrib>", "")
                        .replace("<nrProcJud>procJudTrab_nrProcJud</nrProcJud>", "")
                        .replace("<codSusp>procJudTrab_codSusp</codSusp>", "")
                        .replace("</procJudTrab>", "")
                        .replace("<infoInterm>", "")
                        .replace("<dia>infoInterm_qtdDiasInterm</dia>", "")
                        .replace("</infoInterm>", "")
                        .replace("</ideTrabalhador> ", "");
            }

            StringBuilder xmlDmDev = new StringBuilder(),
                    xmlItensRemun = new StringBuilder(),
                    xmlDetPlano = new StringBuilder();
            String vInfoComplementar = "";
            if (!s1200.getCBO().equals("")) {
                vInfoComplementar = "<infoComplCont>\n"
                        + "<codCBO>" + s1200.getCBO() + "</codCBO>\n"
                        + "</infoComplCont>\n";
            }
            String dmDev = "        <dmDev>\n"
                    + "            <ideDmDev>dmDev_ideDmDev</ideDmDev>\n"
                    + "            <codCateg>dmDev_codCateg</codCateg>\n"
                    + "            <infoPerApur>\n"
                    + "        </dmDev>",
                    infoPerApur = "            <infoPerApur>\n"
                    + "                <ideEstabLot>\n"
                    + "                    <tpInsc>infoPerApur_ideEstabLot_tpInsc</tpInsc>\n"
                    + "                    <nrInsc>infoPerApur_ideEstabLot_nrInsc</nrInsc>\n"
                    + "                    <codLotacao>infoPerApur_ideEstabLot_codLotacao</codLotacao>\n"
                    + "                    <qtdDiasAv>ideEstabLot_qtdDiasAv</qtdDiasAv>\n"
                    + "                    <remunPerApur>\n"
                    + "                        <matricula>remunPerApur_matricula</matricula>\n"
                    + "                        <indSimples>remunPerApur_indSimples</indSimples>\n"
                    + "                        itensRemun_itensRemun\n"
                    //+ "                        infoSaudeColet_infoSaudeColet\n"
                    + "                        <infoAgNocivo>\n"
                    + "                            <grauExp>remunPerApur_infoAgNocivo_grauExp</grauExp>\n"
                    + "                        </infoAgNocivo>\n"
                    + "                        <infoTrabInterm>\n"
                    + "                            <codConv>remunPerApur_infoTrabInterm_codConv</codConv>\n"
                    + "                        </infoTrabInterm>\n"
                    + "                    </remunPerApur>\n"
                    + "                </ideEstabLot>\n"
                    + "            </infoPerApur>\n"
                    + vInfoComplementar,
                    itensRemun = "                        <itensRemun>\n"
                    + "                            <codRubr>remunPerApur_itensRemun_codRubr</codRubr>\n"
                    + "                            <ideTabRubr>remunPerApur_itensRemun_ideTabRubr</ideTabRubr>\n"
                    + "                            <qtdRubr>remunPerApur_itensRemun_qtdRubr</qtdRubr>\n"
                    + "                            <fatorRubr>remunPerApur_itensRemun_fatorRubr</fatorRubr>\n"
                    + "                            <vrUnit>remunPerApur_itensRemun_vrUnit</vrUnit>\n"
                    + "                            <vrRubr>remunPerApur_itensRemun_vrRubr</vrRubr>\n"
                    + "                        </itensRemun>\n",
                    detOper = "                            <detOper>\n"
                    + "                                <cnpjOper>detOper_cnpjOper</cnpjOper>\n"
                    + "                                <regANS>detOper_regANS</regANS>\n"
                    + "                                <vrPgTit>detOper_vrPgTit</vrPgTit>\n"
                    + "                                <detPlano>\n"
                    + "                            </detOper>\n",
                    detPlano = "                                <detPlano>\n"
                    + "                                    <tpDep>detPlano_tpDep</tpDep>\n"
                    + "                                    <cpfDep>detPlano_cpfDep</cpfDep>\n"
                    + "                                    <nmDep>detPlano_nmDep</nmDep>\n"
                    + "                                    <dtNascto>detPlano_dtNascto</dtNascto>\n"
                    + "                                    <vlrPgDep>detPlano_vlrPgDep</vlrPgDep>\n"
                    + "                                </detPlano>\n",
                    aux;

            for (S1200.DmDev dm : s1200.getDmDev()) {
                aux = "";

                aux = dmDev
                        .replace("<ideDmDev>dmDev_ideDmDev</ideDmDev>",
                                dm.getDmDev_ideDmDev().equals("") ? "" : "<ideDmDev>" + dm.getDmDev_ideDmDev() + "</ideDmDev>")
                        .replace("<codCateg>dmDev_codCateg</codCateg>",
                                dm.getDmDev_codCateg().equals("") ? "" : "<codCateg>" + dm.getDmDev_codCateg() + "</codCateg>");

                existe = !dm.getInfoPerApur_ideEstabLot_tpInsc().equals("")
                        || !dm.getInfoPerApur_ideEstabLot_nrInsc().equals("")
                        || !dm.getInfoPerApur_ideEstabLot_codLotacao().equals("")
                        || !dm.getIdeEstabLot_qtdDiasAv().equals("")
                        || !dm.getRemunPerApur_matricula().equals("")
                        || !dm.getRemunPerApur_indSimples().equals("")
                        || !dm.getRemunPerApur_itensRemun().isEmpty()
                        || !dm.getRemunPerApur_infoSaudeColet().getDetOper().isEmpty()
                        || !dm.getRemunPerApur_infoAgNocivo_grauExp().equals("")
                        || !dm.getRemunPerApur_infoTrabInterm_codConv().equals("");
                if (existe) {
                    aux = dmDev.replace("<infoPerApur>",
                            infoPerApur
                                    .replace("<tpInsc>infoPerApur_ideEstabLot_tpInsc</tpInsc>",
                                            dm.getInfoPerApur_ideEstabLot_tpInsc().equals("") ? ""
                                            : "<tpInsc>" + (dm.getInfoPerApur_ideEstabLot_tpInsc().equals("J") ? "1" : "2") + "</tpInsc>")
                                    .replace("<nrInsc>infoPerApur_ideEstabLot_nrInsc</nrInsc>",
                                            dm.getInfoPerApur_ideEstabLot_nrInsc().equals("") ? "" : "<nrInsc>" + dm.getInfoPerApur_ideEstabLot_nrInsc() + "</nrInsc>")
                                    .replace("<codLotacao>infoPerApur_ideEstabLot_codLotacao</codLotacao>",
                                            dm.getInfoPerApur_ideEstabLot_codLotacao().equals("") ? "" : "<codLotacao>" + dm.getInfoPerApur_ideEstabLot_codLotacao().replace(".0", "") + "</codLotacao>")
                                    .replace("<qtdDiasAv>ideEstabLot_qtdDiasAv</qtdDiasAv>",
                                            (dm.getIdeEstabLot_qtdDiasAv().equals("")) ? "" : "<qtdDiasAv>" + dm.getIdeEstabLot_qtdDiasAv() + "</qtdDiasAv>")
                                    .replace("<matricula>remunPerApur_matricula</matricula>",
                                            ((dm.getRemunPerApur_matricula().equals(""))
                                            || (dm.getDmDev_codCateg().equals("701"))
                                            || (dm.getDmDev_codCateg().equals("721"))
                                            || (dm.getDmDev_codCateg().equals("722"))
                                            || (dm.getDmDev_codCateg().equals("723"))
                                            || (dm.getDmDev_codCateg().equals("901"))) ? "" : "<matricula>" + dm.getRemunPerApur_matricula().replace(".0", "") + "</matricula>")
                                    .replace("<indSimples>remunPerApur_indSimples</indSimples>",
                                            dm.getRemunPerApur_indSimples().equals("") ? "" : "<indSimples>" + dm.getRemunPerApur_indSimples() + "</indSimples>"));

                    xmlItensRemun = new StringBuilder();
                    DecimalFormat decimal = new DecimalFormat("0.00");
                    String vrRubr;
                    double ValorCalc;
                    for (S1200.ItensRemun itens : dm.getRemunPerApur_itensRemun()) {
                        vrRubr = "0.0";
                        ValorCalc = 0.00;
                        ValorCalc = Double.valueOf(itens.getItensRemun_vrRubr()).doubleValue();;
                        vrRubr = decimal.format(ValorCalc).replace(",", ".");
                        xmlItensRemun.append(itensRemun
                                .replace("<codRubr>remunPerApur_itensRemun_codRubr</codRubr>",
                                        itens.getItensRemun_codRubr().equals("") ? "" : "<codRubr>" + itens.getItensRemun_codRubr() + "</codRubr>")
                                .replace("<ideTabRubr>remunPerApur_itensRemun_ideTabRubr</ideTabRubr>",
                                        itens.getItensRemun_ideTabRubr().equals("") ? "" : "<ideTabRubr>" + itens.getItensRemun_ideTabRubr() + "</ideTabRubr>")
                                .replace("<qtdRubr>remunPerApur_itensRemun_qtdRubr</qtdRubr>",
                                        itens.getItensRemun_qtdRubr().equals("") ? "" : "<qtdRubr>" + itens.getItensRemun_qtdRubr() + "</qtdRubr>")
                                .replace("<fatorRubr>remunPerApur_itensRemun_fatorRubr</fatorRubr>",
                                        itens.getItensRemun_fatorRubr().equals("") ? "" : "<fatorRubr>" + itens.getItensRemun_fatorRubr() + "</fatorRubr>")
                                .replace("<vrUnit>remunPerApur_itensRemun_vrUnit</vrUnit>",
                                        itens.getItensRemun_vrUnit().equals("") ? "" : "<vrUnit>" + itens.getItensRemun_vrUnit() + "</vrUnit>")
                                .replace("<vrRubr>remunPerApur_itensRemun_vrRubr</vrRubr>",
                                        itens.getItensRemun_vrRubr().equals("") ? "" : "<vrRubr>" + vrRubr + "</vrRubr><indApurIR>0</indApurIR>"));
                    }

                    aux = aux.replace("itensRemun_itensRemun", xmlItensRemun.toString());

                    xmlItensRemun = new StringBuilder();
                    /* existe = !dm.getRemunPerApur_infoSaudeColet().getDetOper().isEmpty();
                    if (existe) {
                        xmlItensRemun.append("<infoSaudeColet>");
                        for (S1200.DetOper det : dm.getRemunPerApur_infoSaudeColet().getDetOper()) {
                            xmlDetPlano = new StringBuilder();
                            for (S1200.DetPlano plano : det.getDetPlano()) {
                                xmlDetPlano.append(detPlano
                                        .replace("<tpDep>detPlano_tpDep</tpDep>",
                                                plano.getDetPlano_tpDep().equals("") ? "" : "<tpDep>" + plano.getDetPlano_tpDep() + "</tpDep>")
                                        .replace("<cpfDep>detPlano_cpfDep</cpfDep>",
                                                plano.getDetPlano_cpfDep().equals("") ? "" : "<cpfDep>" + plano.getDetPlano_cpfDep() + "</cpfDep>")
                                        .replace("<nmDep>detPlano_nmDep</nmDep>",
                                                plano.getDetPlano_nmDep().equals("") ? "" : "<nmDep>" + plano.getDetPlano_nmDep() + "</nmDep>")
                                        .replace("<dtNascto>detPlano_dtNascto</dtNascto>",
                                                plano.getDetPlano_dtNascto().equals("") ? "" : "<dtNascto>" + plano.getDetPlano_tpDep() + "</dtNascto>"));
                            }

                            xmlItensRemun.append(detOper
                                    .replace("<cnpjOper>detOper_cnpjOper</cnpjOper>",
                                            det.getDetOper_cnpjOper().equals("") ? "" : "<cnpjOper>" + det.getDetOper_cnpjOper() + "</cnpjOper>")
                                    .replace("<regANS>detOper_regANS</regANS>",
                                            det.getDetOper_regANS().equals("") ? "" : "<regANS>" + det.getDetOper_regANS() + "</regANS>")
                                    .replace("<vrPgTit>detOper_vrPgTit</vrPgTit>",
                                            det.getDetOper_vrPgTit().equals("") ? "" : "<vrPgTit>" + det.getDetOper_vrPgTit() + "</vrPgTit>")
                                    .replace("<detPlano>", xmlDetPlano.toString()));
                        }
                        xmlItensRemun.append("</infoSaudeColet>");

                    }

                    aux = aux.replace("infoSaudeColet_infoSaudeColet", xmlItensRemun.toString());
                     */
                    existe = (!dm.getRemunPerApur_infoAgNocivo_grauExp().equals("")
                            & !dm.getDmDev_codCateg().equals("701")
                            & !dm.getDmDev_codCateg().equals("721")
                            & !dm.getDmDev_codCateg().equals("722")
                            & !dm.getDmDev_codCateg().equals("723")
                            & !dm.getDmDev_codCateg().equals("901"));
                    if (existe) {
                        aux = aux.replace("remunPerApur_infoAgNocivo_grauExp", dm.getRemunPerApur_infoAgNocivo_grauExp());
                    } else {
                        aux = aux.replace("<infoAgNocivo>", "")
                                .replace("<grauExp>remunPerApur_infoAgNocivo_grauExp</grauExp>", "")
                                .replace("</infoAgNocivo>", "");
                    }

                    existe = !dm.getRemunPerAnt_infoTrabInterm_codConv().equals("");
                    if (existe) {
                        aux = aux.replace("remunPerApur_infoTrabInterm_codConv", dm.getRemunPerAnt_infoTrabInterm_codConv());
                    } else {
                        aux = aux.replace("<infoTrabInterm>", "")
                                .replace("<codConv>remunPerApur_infoTrabInterm_codConv</codConv>", "")
                                .replace("</infoTrabInterm>", "");
                    }
                } else {
                    aux = aux.replace("<infoPerApur>", "");
                }

                xmlDmDev.append(aux
                        .replace("<ideDmDev>dmDev_ideDmDev</ideDmDev>",
                                dm.getDmDev_ideDmDev().equals("") ? "" : "<ideDmDev>" + dm.getDmDev_ideDmDev() + "</ideDmDev>")
                        .replace("<codCateg>dmDev_codCateg</codCateg>",
                                dm.getDmDev_codCateg().equals("") ? "" : "<codCateg>" + dm.getDmDev_codCateg() + "</codCateg>"));
            }

            return xmlPadrao
                    .replace("<dmDev>", xmlDmDev.toString())
                    .replace("evtRemun_Id", s1200.getEvtRemun_Id());
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1210> getS1210(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1210Dao s1210dao = new S1210Dao();
            List<S1210> listaS1210 = s1210dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1210 s1210 : listaS1210) {
                idPadrao = s1210.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1210.setEvtPgtos_Id("ID1" + id2);
                s1210.setIdeEvento_tpAmb(tipoAmbiente);
                s1210.setIdeEmpregador_tpInsc(s1210.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1210.setIdeEmpregador_nrInsc(s1210.getIdeEmpregador_nrInsc().substring(0, 8));
                // Data de pagamento agora vem automaticamente da consulta SQL (FpMensalDet, FPRescisoes ou FPFerias)
            }
            return listaS1210;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1210XML(String xmlPadrao, S1210 s1210) throws Exception {
        try {
            boolean existe;
            StringBuilder xmlAux, xmlAux2 = new StringBuilder();
            String xmlInfoPgto = "            <infoPgto>\n"
                    + "                <dtPgto>infoPgto_dtPgto</dtPgto>\n"
                    + "                <tpPgto>infoPgto_tpPgto</tpPgto>\n"
                    //+ "                <indResBr>infoPgto_indResBr</indResBr>\n"
                    //                    + "                <detPgtoFl>\n"
                    + "                    <perRef>detPgtoFl_perRef</perRef>\n"
                    + "                    <ideDmDev>detPgtoFl_ideDmDev</ideDmDev>\n"
                    //                    + "                    <indPgtoTt>detPgtoFl_indPgtoTt</indPgtoTt>\n"
                    + "                    <vrLiq>detPgtoFl_vrLiq</vrLiq>\n"
                    //                    + "                    <nrRecArq>detPgtoFl_nrRecArq</nrRecArq>\n"
                    //                    + "                    <retPgtoTot>\n"
                    //                    + "                </detPgtoFl>\n"
                    //                    + "                <detPgtoFer>\n"
                    //                    + "                    <codCateg>detPgtoFer_codCateg</codCateg>\n"
                    //                    + "                    <matricula>detPgtoFer_matricula</matricula>\n"
                    //                    + "                    <dtIniGoz>detPgtoFer_dtIniGoz</dtIniGoz>\n"
                    //                    + "                    <qtDias>detPgtoFer_qtDias</qtDias>\n"
                    //                    + "                    <vrLiq>detPgtoFer_vrLiq</vrLiq>\n"
                    //                    + "                    <detRubrFer>\n"
                    //                    + "                </detPgtoFer>\n"
                    + "            </infoPgto>\n", xmlInfoPgtoAux;

            xmlPadrao = xmlPadrao.replace("ideEvento_indRetif", s1210.getIdeEvento_indRetif())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>",
                            s1210.getIdeEvento_nrRecibo().equals("") ? "" : "<nrRecibo>" + s1210.getIdeEvento_nrRecibo() + "</nrRecibo>")
                    .replace("ideEvento_indApuracao", s1210.getIdeEvento_indApuracao())
                    .replace("ideEvento_perApur", s1210.getIdeEvento_perApur())
                    .replace("<ideDmDev>detPgtoFl_ideDmDev</ideDmDev>", "")
                    .replace("ideEvento_tpAmb", s1210.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1210.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1210.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1210.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1210.getIdeEmpregador_nrInsc())
                    .replace("ideBenef_cpfBenef", s1210.getIdeBenef_cpfBenef());

            existe = !s1210.getDeps_vrDedDep().equals("0");
            if (existe) {
                xmlPadrao = xmlPadrao.replace("deps_vrDedDep", s1210.getDeps_vrDedDep());
            } else {
                xmlPadrao = xmlPadrao.replace("<deps>", "")
                        .replace("<vrDedDep>deps_vrDedDep</vrDedDep>", "")
                        .replace("</deps>", "");
            }

            for (InfoPgto infoPgto : s1210.getIdeBenef_infoPgto()) {
                if (!infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_ideDmDev().equals("")) {
                    // Data de pagamento já vem preenchida da consulta SQL

                    // Validação para garantir que a data não esteja vazia
                    if (!infoPgto.getInfoPgto_dtPgto().equals("") && infoPgto.getInfoPgto_dtPgto().length() >= 7) {
                        xmlPadrao = xmlPadrao.replace("ideEvento_perApur", infoPgto.getInfoPgto_dtPgto().substring(0, 7));
                    }

                    if (infoPgto.getInfoPgto_tpPgto().equals("7")) {
                        xmlInfoPgto = xmlInfoPgto.replace("<detPgtoFl>", "")
                                .replace("<ideDmDev>detPgtoFl_ideDmDev</ideDmDev>", "")
                                .replace("</detPgtoFl>", "");
                    }
                    xmlInfoPgtoAux = xmlInfoPgto.replace("infoPgto_dtPgto", infoPgto.getInfoPgto_dtPgto())
                            .replace("infoPgto_tpPgto", infoPgto.getInfoPgto_tpPgto())
                            .replace("infoPgto_indResBr", infoPgto.getInfoPgto_indResBr());

                    existe = !infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_ideDmDev().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_indPgtoTt().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_vrLiq().equals("");

                    if ((existe) || (!existe)) {
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("<perRef>detPgtoFl_perRef</perRef>",
                                (infoPgto.getInfoPgto_tpPgto().equals("1") || infoPgto.getInfoPgto_tpPgto().equals("5") || infoPgto.getInfoPgto_tpPgto().equals("2")) ? "<perRef>" + infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_perRef() + "</perRef>" : "")
                                .replace("<ideDmDev>detPgtoFl_ideDmDev</ideDmDev>",
                                        infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_ideDmDev().equals("") ? "" : "<ideDmDev>" + infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_ideDmDev() + "</ideDmDev>")
                                .replace("<indPgtoTt>detPgtoFl_indPgtoTt</indPgtoTt>",
                                        infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_indPgtoTt().equals("") ? "" : "<indPgtoTt>" + infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_indPgtoTt() + "</indPgtoTt>")
                                .replace("<vrLiq>detPgtoFl_vrLiq</vrLiq>",
                                        infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_vrLiq().equals("0") && infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_indPgtoTt().equals("") ? "" : "<vrLiq>" + infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_vrLiq() + "</vrLiq>")
                                .replace("<nrRecArq>detPgtoFl_nrRecArq</nrRecArq>",
                                        infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_nrRecArq().equals("") ? "" : "<nrRecArq>" + infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_nrRecArq() + "</nrRecArq>");

                        xmlAux = new StringBuilder();
                        for (RetPgtoTot retPgtoTot : infoPgto.getInfoPgto_detPgtoFl().getDetPgtoFl_retPgtoTot()) {
                            xmlAux.append("                    <retPgtoTot>\n")
                                    .append("                        <codRubr>").append(retPgtoTot.getRetPgtoTot_codRubr()).append("</codRubr>\n")
                                    .append("                        <ideTabRubr>").append(retPgtoTot.getRetPgtoTot_ideTabRubr()).append("</ideTabRubr>\n")
                                    .append(retPgtoTot.getRetPgtoTot_qtdRubr().equals("")
                                            ? "" : "                        <qtdRubr>" + retPgtoTot.getRetPgtoTot_qtdRubr() + "</qtdRubr>\n")
                                    .append(retPgtoTot.getRetPgtoTot_fatorRubr().equals("")
                                            ? "" : "                        <fatorRubr>" + retPgtoTot.getRetPgtoTot_fatorRubr() + "</fatorRubr>\n")
                                    .append(retPgtoTot.getRetPgtoTot_vrUnit().equals("")
                                            ? "" : "                        <vrUnit>" + retPgtoTot.getRetPgtoTot_vrUnit() + "</vrUnit>\n")
                                    .append("                        <vrRubr>").append(retPgtoTot.getRetPgtoTot_vrRubr()).append("</vrRubr>\n")
                                    .append("                        <indApurIR>0").append("</indApurIR>\n");

                            for (PenAlim penAlim : retPgtoTot.getRetPgtoTot_penAlim()) {
                                xmlAux.append("                        <penAlim>\n")
                                        .append("                            <cpfBenef>").append(penAlim.getPenAlim_cpfBenef()).append("</cpfBenef>\n")
                                        .append(penAlim.getPenAlim_dtNasctoBenef().equals("")
                                                ? "" : "                            <dtNasctoBenef>" + penAlim.getPenAlim_dtNasctoBenef() + "</dtNasctoBenef>\n")
                                        .append("                            <nmBenefic>").append(penAlim.getPenAlim_nmBenefic()).append("</nmBenefic>\n")
                                        .append("                            <vlrPensao>").append(penAlim.getPenAlim_vlrPensao()).append("</vlrPensao>\n")
                                        .append("                        </penAlim>\n");
                            }
                            xmlAux.append("                    </retPgtoTot>\n");
                        }
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("                    <retPgtoTot>", xmlAux.toString());
                    } else {
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("<detPgtoFl>", "")
                                //.replace("<perRef>detPgtoFl_perRef</perRef>", "")
                                .replace("<ideDmDev>detPgtoFl_ideDmDev</ideDmDev>", "")
                                .replace("<indPgtoTt>detPgtoFl_indPgtoTt</indPgtoTt>", "")
                                .replace("<vrLiq>detPgtoFl_vrLiq</vrLiq>", "")
                                .replace("<nrRecArq>detPgtoFl_nrRecArq</nrRecArq>", "")
                                .replace("<retPgtoTot>", "")
                                .replace("</detPgtoFl>", "");
                    }

                    existe = !infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_codCateg().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_matricula().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_dtIniGoz().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_qtDias().equals("")
                            || !infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_vrLiq().equals("");
                    if (existe) {
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("detPgtoFer_codCateg", infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_codCateg())
                                .replace("detPgtoFer_matricula", infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_matricula().replace(".0", ""))
                                .replace("detPgtoFer_dtIniGoz", infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_dtIniGoz())
                                .replace("detPgtoFer_qtDias", infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_qtDias().replace(".0", ""))
                                .replace("detPgtoFer_vrLiq", infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_vrLiq());

                        xmlAux = new StringBuilder();
                        for (DetRubrFer detRubrFer : infoPgto.getInfoPgto_detPgtoFer().getDetPgtoFer_detRubrFer()) {
                            xmlAux.append("                    <detRubrFer>\n")
                                    .append("                        <codRubr>").append(detRubrFer.getDetRubrFer_codRubr()).append("</codRubr>\n")
                                    .append("                        <ideTabRubr>").append(detRubrFer.getDetRubrFer_ideTabRubr()).append("</ideTabRubr>\n")
                                    .append(detRubrFer.getDetRubrFer_qtdRubr().equals("")
                                            ? "" : "                        <qtdRubr>" + detRubrFer.getDetRubrFer_qtdRubr() + "</qtdRubr>\n")
                                    .append(detRubrFer.getDetRubrFer_fatorRubr().equals("")
                                            ? "" : "                        <fatorRubr>" + detRubrFer.getDetRubrFer_fatorRubr() + "</fatorRubr>\n")
                                    .append(detRubrFer.getDetRubrFer_vrUnit().equals("")
                                            ? "" : "                        <vrUnit>" + detRubrFer.getDetRubrFer_vrUnit() + "</vrUnit>\n")
                                    .append("                        <vrRubr>").append(detRubrFer.getDetRubrFer_vrRubr()).append("</vrRubr>\n")
                                    .append("                        <indApurIR>0").append("</indApurIR>\n");

                            for (PenAlim penAlim : detRubrFer.getDetRubrFer_penAlim()) {
                                xmlAux.append("                        <penAlim>\n")
                                        .append("                            <cpfBenef>").append(penAlim.getPenAlim_cpfBenef()).append("</cpfBenef>\n")
                                        .append(penAlim.getPenAlim_dtNasctoBenef().equals("")
                                                ? "" : "                            <dtNasctoBenef>" + penAlim.getPenAlim_dtNasctoBenef() + "</dtNasctoBenef>\n")
                                        .append("                            <nmBenefic>").append(penAlim.getPenAlim_nmBenefic()).append("</nmBenefic>\n")
                                        .append("                            <vlrPensao>").append(penAlim.getPenAlim_vlrPensao()).append("</vlrPensao>\n")
                                        .append("                        </penAlim>\n");
                            }
                            xmlAux.append("                    </detRubrFer>\n");
                        }
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("                    <detRubrFer>", xmlAux.toString());
                    } else {
                        xmlInfoPgtoAux = xmlInfoPgtoAux.replace("<detPgtoFer>", "")
                                .replace("<codCateg>detPgtoFer_codCateg</codCateg>", "")
                                .replace("<matricula>detPgtoFer_matricula</matricula>", "")
                                .replace("<dtIniGoz>detPgtoFer_dtIniGoz</dtIniGoz>", "")
                                .replace("<qtDias>detPgtoFer_qtDias</qtDias>", "")
                                .replace("<vrLiq>detPgtoFer_vrLiq</vrLiq>", "")
                                .replace("<detRubrFer>", "")
                                .replace("</detPgtoFer>", "");
                    }

                    xmlAux2.append(xmlInfoPgtoAux);
                }
            }

            StringBuilder xmlInfoIRComplem = new StringBuilder();
            if (s1210.getPlanSaude() != null) {
                xmlInfoIRComplem.
                        append("<infoIRComplem>\n").
                        append("<planSaude>\n").
                        append("<cnpjOper>").
                        append(s1210.getPlanSaude().getPlanSaude_cnpjOper()).
                        append("</cnpjOper>\n").
                        append("<vlrSaudeTit>").
                        append(s1210.getPlanSaude().getPlanSaude_vlrSaudeTit()).
                        append("</vlrSaudeTit>\n").
                        append("</planSaude>\n").
                        append("</infoIRComplem>\n");
            }                   
            
            return xmlPadrao.replace("evtPgtos_Id", s1210.getEvtPgtos_Id())
                    .replace("<infoPgto>", xmlAux2.toString())
                    .replace("<infoIRComplem>", xmlInfoIRComplem.toString());
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1280> getS1280(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1280Dao s1280dao = new S1280Dao();
            List<S1280> listaS1280 = s1280dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1280 s1280 : listaS1280) {
                idPadrao = s1280.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s1280.setEvtInfoComplPer_id("ID1" + id2);
                s1280.setIdeEvento_tpAmb(tipoAmbiente);
                s1280.setIdeEmpregador_tpInsc(s1280.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1280.setIdeEmpregador_nrInsc(s1280.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS1280;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1280XML(String xmlPadrao, S1280 s1280) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtInfoComplPer_id", s1280.getEvtInfoComplPer_id())
                    .replace("ideEvento_indRetif", s1280.getIdeEvento_indRetif())
                    .replace("ideEvento_nrRecibo", s1280.getIdeEvento_nrRecibo())
                    .replace("ideEvento_indApuracao", s1280.getIdeEvento_indApuracao())
                    .replace("ideEvento_perApur", s1280.getIdeEvento_perApur())
                    .replace("ideEvento_indGuia", s1280.getIdeEvento_indGuia())
                    .replace("ideEvento_tpAmb", s1280.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1280.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1280.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1280.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1280.getIdeEmpregador_nrInsc())
                    .replace("infoSubstPatr_indSubstPatr", s1280.getInfoSubstPatr_indSubstPatr())
                    .replace("infoSubstPatr_percRedContrib", s1280.getInfoSubstPatr_percRedContrib())
                    .replace("infoSubstPatrOpPort_codLotacao", s1280.getInfoSubstPatrOpPort_codLotacao())
                    .replace("infoAtivConcom_fatorMes", s1280.getInfoAtivConcom_fatorMes())
                    .replace("infoAtivConcom_fator13", s1280.getInfoAtivConcom_fator13());

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1295> getS1295(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1295Dao s1295dao = new S1295Dao();
            List<S1295> listaS1295 = s1295dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1295 s1295 : listaS1295) {
                idPadrao = s1295.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s1295.setEvtTotConting_Id("ID1" + id2);
                s1295.setIdeEvento_tpAmb(tipoAmbiente);
                s1295.setIdeEmpregador_tpInsc(s1295.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1295.setIdeEmpregador_nrInsc(s1295.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS1295;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1295XML(String xmlPadrao, S1295 s1295) throws Exception {
        try {
            String xml = xmlPadrao.replace("evtTotConting_Id", s1295.getEvtTotConting_Id())
                    .replace("ideEvento_indApuracao", s1295.getIdeEvento_indApuracao())
                    .replace("ideEvento_perApur", s1295.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", s1295.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1295.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1295.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1295.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1295.getIdeEmpregador_nrInsc());

            boolean existe = !s1295.getIdeRespInf_nmResp().equals("")
                    || !s1295.getIdeRespInf_cpfResp().equals("")
                    || !s1295.getIdeRespInf_telefone().equals("")
                    || !s1295.getIdeRespInf_email().equals("");
            if (existe) {
                xml = xml.replace("ideRespInf_nmResp", s1295.getIdeRespInf_nmResp())
                        .replace("ideRespInf_cpfResp", s1295.getIdeRespInf_cpfResp())
                        .replace("ideRespInf_telefone", s1295.getIdeRespInf_telefone())
                        .replace("<email>ideRespInf_email</email>",
                                s1295.getIdeRespInf_email().equals("") ? "" : "<email>" + s1295.getIdeRespInf_email() + "</email>");
            } else {
                xml = xml.replace("<ideRespInf>", "")
                        .replace("<nmResp>ideRespInf_nmResp</nmResp>", "")
                        .replace("<cpfResp>ideRespInf_cpfResp</cpfResp>", "")
                        .replace("<telefone>ideRespInf_telefone</telefone>", "")
                        .replace("<email>ideRespInf_email</email>", "")
                        .replace("</ideRespInf>", "");
            }

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1298> getS1298(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1298Dao s1298dao = new S1298Dao();
            List<S1298> listaS1298 = s1298dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1298 s1298 : listaS1298) {
                idPadrao = s1298.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s1298.setEvtReabreEvPer_Id("ID1" + id2);
                s1298.setIdeEvento_tpAmb(tipoAmbiente);
                s1298.setIdeEmpregador_tpInsc(s1298.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1298.setIdeEmpregador_nrInsc(s1298.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS1298;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1298XML(String xmlPadrao, S1298 s1298) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtReabreEvPer_Id", s1298.getEvtReabreEvPer_Id())
                    .replace("ideEvento_indApuracao", s1298.getIdeEvento_indApuracao())
                    .replace("ideEvento_perApur", s1298.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", s1298.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1298.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1298.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1298.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1298.getIdeEmpregador_nrInsc());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S1299> getS1299(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S1299Dao s1299dao = new S1299Dao();
            List<S1299> listaS1299 = s1299dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S1299 s1299 : listaS1299) {
                idPadrao = s1299.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s1299.setEvtFechaEvPer_Id("ID1" + id2);
                s1299.setIdeEvento_tpAmb(tipoAmbiente);
                s1299.setIdeEmpregador_tpInsc(s1299.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s1299.setIdeEmpregador_nrInsc(s1299.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS1299;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S1299XML(String xmlPadrao, S1299 s1299) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtFechaEvPer_Id", s1299.getEvtFechaEvPer_Id())
                    .replace("ideEvento_indApuracao", s1299.getIdeEvento_indApuracao())
                    .replace("ideEvento_perApur", s1299.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", s1299.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s1299.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s1299.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s1299.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s1299.getIdeEmpregador_nrInsc())
                    .replace("ideRespInf_nmResp", s1299.getIdeRespInf_nmResp())
                    .replace("ideRespInf_cpfResp", s1299.getIdeRespInf_cpfResp())
                    .replace("ideRespInf_telefone", s1299.getIdeRespInf_telefone())
                    .replace("ideRespInf_email", s1299.getIdeRespInf_email())
                    .replace("infoFech_evtRemun", s1299.getInfoFech_evtRemun())
                    .replace("infoFech_evtPgtos", s1299.getInfoFech_evtPgtos())
                    .replace("infoFech_evtAqProd", s1299.getInfoFech_evtAqProd())
                    .replace("infoFech_evtComProd", s1299.getInfoFech_evtComProd())
                    .replace("infoFech_evtContratAvNP", s1299.getInfoFech_evtContratAvNP())
                    .replace("infoFech_evtInfoComplPer", s1299.getInfoFech_evtInfoComplPer())
                    .replace("<compSemMovto>infoFech_compSemMovto</compSemMovto>", s1299.getInfoFech_compSemMovto().equals("")
                            ? "" : "<compSemMovto>" + s1299.getInfoFech_compSemMovto() + "</compSemMovto>");
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2190> getS2190(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2190Dao s2190dao = new S2190Dao();
            List<S2190> listaS2190 = s2190dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2190 s2190 : listaS2190) {
                idPadrao = s2190.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2190.setEvtAdmPrelim_Id("ID1" + id2);
                s2190.setIdeEvento_tpAmb(tipoAmbiente);
                s2190.setIdeEmpregador_tpInsc(s2190.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2190.setIdeEmpregador_nrInsc(s2190.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS2190;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2190XML(String xmlPadrao, S2190 s2190) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("ideEvento_indRetif", "1")
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "")
                    .replace("evtAdmPrelim_Id", s2190.getEvtAdmPrelim_Id())
                    .replace("ideEvento_tpAmb", s2190.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s2190.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s2190.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s2190.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s2190.getIdeEmpregador_nrInsc())
                    .replace("infoRegPrelim_cpfTrab", s2190.getInfoRegPrelim_cpfTrab())
                    .replace("infoRegPrelim_dtNascto", s2190.getInfoRegPrelim_dtNascto())
                    .replace("infoRegPrelim_dtAdm", s2190.getInfoRegPrelim_dtAdm());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2200> getS2200Simplificada(String codFil, String compet, String tipoAmbiente, String cadIni, Persistencia persistencia) throws Exception {
        try {
            S2200Dao s2200dao = new S2200Dao();
            List<S2200> retorno = s2200dao.getSimples(codFil, compet, tipoAmbiente, cadIni, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2200 s2200 : retorno) {
                idPadrao = s2200.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s2200.setVinculo_matricula(s2200.getVinculo_matricula().replace(".0", ""));
                s2200.setEvtAdmissao_Id("ID1" + id2);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2200> getS2200Completa(List<S2200> funcions, String codFil, String compet,
            String tipoAmbiente, String cadIni, String tipo, Persistencia persistencia, Persistencia persistenciaAgil2) throws Exception {
        try {
            S2200Dao s2200dao = new S2200Dao();
            List<S2200> listaS2200;
//            if(persistencia.getEmpresa().contains("LOYAL")) listaS2200 = s2200dao.getCompleta2(funcions, codFil, compet, tipoAmbiente, persistencia);
//            else listaS2200 = s2200dao.getCompleta(funcions, codFil, compet, tipoAmbiente, persistencia);
            listaS2200 = s2200dao.getCompleta(funcions, codFil, compet, tipoAmbiente, tipo, persistencia);
            listaS2200 = s2200dao.getDependentes(listaS2200, codFil, persistencia);
            //if (persistenciaAgil2 != null) {
            listaS2200 = s2200dao.getTransferencias(listaS2200, codFil, compet, persistencia, persistenciaAgil2);
            //}
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2200 s2200 : listaS2200) {
                idPadrao = s2200.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2200.setEvtAdmissao_Id("ID1" + id2);

                for (S2200.Dependentes d : s2200.getDependentes()) {
                    switch (d.getDependente_tpDep()) {
                        case "C":
                            d.setDependente_tpDep("01");
                            break;
                        case "E":
                        case "F":
                            d.setDependente_tpDep("03");
                            break;
                        case "M":
                        case "P":
                            d.setDependente_tpDep("09");
                            break;
                        case "S":
                            d.setDependente_tpDep("12");
                            break;
                        case "O":
                            d.setDependente_tpDep("99");
                            break;
                    }
                }
                s2200.setIdeEvento_tpAmb(tipoAmbiente);
                s2200.setIdeEmpregador_tpInsc(s2200.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2200.setIdeEmpregador_nrInsc(s2200.getIdeEmpregador_nrInsc().substring(0, 8));
                s2200.setTrabalhador_racaCor(
                        (s2200.getTrabalhador_racaCor().equals("2") ? "1"
                        : (s2200.getTrabalhador_racaCor().equals("4") ? "2"
                        : (s2200.getTrabalhador_racaCor().equals("8") ? "3"
                        : (s2200.getTrabalhador_racaCor().equals("6") ? "4"
                        : (s2200.getTrabalhador_racaCor().equals("0") ? "5" : ""))))));
                s2200.setTrabalhador_estCiv(
                        (s2200.getTrabalhador_estCiv().equals("S") ? "1"
                        : (s2200.getTrabalhador_estCiv().equals("C") ? "2"
                        : (s2200.getTrabalhador_estCiv().equals("Q") ? "4"
                        : (s2200.getTrabalhador_estCiv().equals("D") ? "3"
                        : (s2200.getTrabalhador_estCiv().equals("V") ? "5"
                        : (s2200.getTrabalhador_estCiv().equals("M") ? "1" : "")))))));
                s2200.setTrabalhador_grauInstr(
                        (s2200.getTrabalhador_grauInstr().equals("10") ? "01"
                        : (s2200.getTrabalhador_grauInstr().equals("20") ? "02"
                        : (s2200.getTrabalhador_grauInstr().equals("25") ? "03"
                        : (s2200.getTrabalhador_grauInstr().equals("30") ? "04"
                        : (s2200.getTrabalhador_grauInstr().equals("35") ? "05"
                        : (s2200.getTrabalhador_grauInstr().equals("40") ? "06"
                        : (s2200.getTrabalhador_grauInstr().equals("45") ? "07"
                        : (s2200.getTrabalhador_grauInstr().equals("50") ? "08"
                        : (s2200.getTrabalhador_grauInstr().equals("55") ? "09"
                        : (s2200.getTrabalhador_grauInstr().equals("65") ? "11"
                        : (s2200.getTrabalhador_grauInstr().equals("75") ? "12" : ""))))))))))));
                s2200.setTrabalhador_indPriEmpr((s2200.getTrabalhador_indPriEmpr().equals("10") ? "S" : "N"));
                s2200.setNascimento_codMunic(s2200.getNascimento_codMunic().replace(".0", ""));
                s2200.setBrasil_tpLograd(LogradouroESocial.getTipo(s2200.getBrasil_dscLograd()));
                s2200.setBrasil_codMunic(s2200.getBrasil_codMunic().replace(".0", ""));
                s2200.setVinculo_matricula(s2200.getVinculo_matricula().replace(".0", ""));
                s2200.setVinculo_cadIni(cadIni);

                if (cadIni.equals("S")) {
                    s2200.setTrabalhador_indPriEmpr("");
                }

                s2200.setRemuneracao_undSalFixo(
                        (s2200.getRemuneracao_undSalFixo().equals("H") ? "1"
                        : (s2200.getRemuneracao_undSalFixo().equals("D") ? "2"
                        : (s2200.getRemuneracao_undSalFixo().equals("M") ? "5" : ""))));
                s2200.setHorContratual_qtdHrsSem(s2200.getHorContratual_qtdHrsSem().replace(".0", ""));
            }
            return listaS2200;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2200> getS2200(String codFil, String compet, String tipoAmbiente, String cadIni, Persistencia persistencia) throws Exception {
        try {
            S2200Dao s2200dao = new S2200Dao();
            List<S2200> listaS2200 = s2200dao.get(codFil, compet, tipoAmbiente, persistencia);
            listaS2200 = s2200dao.getDependentes(listaS2200, codFil, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2200 s2200 : listaS2200) {
                for (S2200.Dependentes d : s2200.getDependentes()) {
                    switch (d.getDependente_tpDep()) {
                        case "C":
                            d.setDependente_tpDep("01");
                            break;
                        case "E":
                        case "F":
                            d.setDependente_tpDep("03");
                            break;
                        case "M":
                        case "P":
                            d.setDependente_tpDep("09");
                            break;
                        case "S":
                            d.setDependente_tpDep("12");
                            break;
                        case "O":
                            d.setDependente_tpDep("99");
                            break;
                    }
                }

                idPadrao = s2200.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s2200.setEvtAdmissao_Id("ID1" + id2);
                s2200.setIdeEvento_tpAmb(tipoAmbiente);
                s2200.setIdeEmpregador_tpInsc(s2200.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2200.setIdeEmpregador_nrInsc(s2200.getIdeEmpregador_nrInsc().substring(0, 8));
                s2200.setTrabalhador_racaCor(
                        (s2200.getTrabalhador_racaCor().equals("2") ? "1"
                        : (s2200.getTrabalhador_racaCor().equals("4") ? "2"
                        : (s2200.getTrabalhador_racaCor().equals("8") ? "3"
                        : (s2200.getTrabalhador_racaCor().equals("6") ? "4"
                        : (s2200.getTrabalhador_racaCor().equals("0") ? "5" : ""))))));
                s2200.setTrabalhador_estCiv(
                        (s2200.getTrabalhador_estCiv().equals("S") ? "1"
                        : (s2200.getTrabalhador_estCiv().equals("C") ? "2"
                        : (s2200.getTrabalhador_estCiv().equals("Q") ? "4"
                        : (s2200.getTrabalhador_estCiv().equals("D") ? "3"
                        : (s2200.getTrabalhador_estCiv().equals("V") ? "5"
                        : (s2200.getTrabalhador_estCiv().equals("M") ? "1" : "")))))));
                s2200.setTrabalhador_grauInstr(
                        (s2200.getTrabalhador_grauInstr().equals("10") ? "01"
                        : (s2200.getTrabalhador_grauInstr().equals("20") ? "02"
                        : (s2200.getTrabalhador_grauInstr().equals("25") ? "03"
                        : (s2200.getTrabalhador_grauInstr().equals("30") ? "04"
                        : (s2200.getTrabalhador_grauInstr().equals("35") ? "05"
                        : (s2200.getTrabalhador_grauInstr().equals("40") ? "06"
                        : (s2200.getTrabalhador_grauInstr().equals("45") ? "07"
                        : (s2200.getTrabalhador_grauInstr().equals("50") ? "08"
                        : (s2200.getTrabalhador_grauInstr().equals("55") ? "09"
                        : (s2200.getTrabalhador_grauInstr().equals("65") ? "11"
                        : (s2200.getTrabalhador_grauInstr().equals("75") ? "12" : ""))))))))))));
                s2200.setTrabalhador_indPriEmpr((s2200.getTrabalhador_indPriEmpr().equals("10") ? "S" : "N"));
                s2200.setNascimento_codMunic(s2200.getNascimento_codMunic().replace(".0", ""));
                s2200.setBrasil_tpLograd(LogradouroESocial.getTipo(s2200.getBrasil_dscLograd()));
                s2200.setBrasil_codMunic(s2200.getBrasil_codMunic().replace(".0", ""));
                s2200.setVinculo_matricula(s2200.getVinculo_matricula().replace(".0", ""));
                s2200.setVinculo_cadIni(cadIni);
                if (cadIni.equals("S")) {
                    s2200.setTrabalhador_indPriEmpr("");
                }
                s2200.setRemuneracao_undSalFixo(
                        (s2200.getRemuneracao_undSalFixo().equals("H") ? "1"
                        : (s2200.getRemuneracao_undSalFixo().equals("D") ? "2"
                        : (s2200.getRemuneracao_undSalFixo().equals("M") ? "5" : ""))));
                s2200.setHorContratual_qtdHrsSem(s2200.getHorContratual_qtdHrsSem().replace(".0", ""));
            }
            return listaS2200;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2200XML(String xmlPadrao, S2200 s2200) throws Exception {
        try {
            String xml, dependentes = "", paisNasc = "";
            for (S2200.Dependentes d : s2200.getDependentes()) {
                dependentes
                        += "            <dependente>\r\n"
                        + "                <tpDep>" + d.getDependente_tpDep() + "</tpDep> \r\n"
                        + "                <nmDep>" + d.getDependente_nmDep() + "</nmDep>\r\n"
                        + "                <dtNascto>" + d.getDependente_dtNascto() + "</dtNascto>\r\n"
                        + (d.getDependente_cpfDep().equals("") ? ""
                        : ("                <cpfDep>" + d.getDependente_cpfDep() + "</cpfDep>\r\n"))
                        + "                <depIRRF>" + d.getDependente_depIRRF() + "</depIRRF>\r\n"
                        + "                <depSF>" + d.getDependente_depSF() + "</depSF>\r\n"
                        + "                <incTrab>" + d.getDependente_incTrab() + "</incTrab>\r\n"
                        + "            </dependente>\r\n";
            }
//            horario = "";
//            for (S2200.Horario h : s2200.getHorarios()) {
//                horario += h.getHorario_dia() + "\r\n";
//                //horario += "                    <horario>\r\n"
//                //        + "                        <dia>" + h.getHorario_dia() + "</dia>\r\n"
//                //        + "                        <codHorContrat>" + h.getHorario_codHorContrat() + "</codHorContrat>\r\n"
//                //        + "                    </horario>\r\n";
//            }
            xml = xmlPadrao
                    .replace("evtAdmissao_Id", s2200.getEvtAdmissao_Id())
                    .replace("ideEvento_indRetif", s2200.getIdeEvento_indRetif())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>",
                            s2200.getIdeEvento_nrRecibo().equals("") ? "" : "<nrRecibo>" + s2200.getIdeEvento_nrRecibo() + "</nrRecibo>")
                    .replace("ideEvento_tpAmb", s2200.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s2200.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s2200.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s2200.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s2200.getIdeEmpregador_nrInsc())
                    .replace("trabalhador_cpfTrab", s2200.getTrabalhador_cpfTrab())
                    .replace("trabalhador_nisTrab", s2200.getTrabalhador_nisTrab())
                    .replace("trabalhador_nmTrab", s2200.getTrabalhador_nmTrab())
                    .replace("trabalhador_sexo", s2200.getTrabalhador_sexo())
                    .replace("trabalhador_racaCor", s2200.getTrabalhador_racaCor())
                    .replace("trabalhador_estCiv", s2200.getTrabalhador_estCiv())
                    .replace("trabalhador_grauInstr", s2200.getTrabalhador_grauInstr())
                    .replace("trabalhador_indPriEmpr", s2200.getTrabalhador_indPriEmpr())
                    .replace("nascimento_dtNascto", s2200.getNascimento_dtNascto())
                    .replace("<indPriEmpr></indPriEmpr>", "");
            if (s2200.getNascimento_paisNascto().equals("10")) {
                xml = xml
                        .replace("nascimento_codMunic", s2200.getNascimento_codMunic())
                        .replace("nascimento_uf", s2200.getNascimento_uf())
                        .replace("nascimento_paisNascto", "105")
                        .replace("nascimento_paisNac", "105")
                        .replace("<trabEstrangeiro>", "")
                        .replace("<dtChegada>trabEstrangeiro_dtChegada</dtChegada>", "")
                        .replace("<classTrabEstrang>trabEstrangeiro_classTrabEstrang</classTrabEstrang>", "")
                        .replace("<casadoBr>trabEstrangeiro_casadoBr</casadoBr>", "")
                        .replace("<filhosBr>trabEstrangeiro_filhosBr</filhosBr>", "")
                        .replace("</trabEstrangeiro>", "");

            } else {
                //Tabela paises
                switch (s2200.getNascimento_paisNascto()) {
                    case "40":
                        paisNasc = "341"; //Haiti
                        break;
                    case "21":
                        paisNasc = "063"; //Argentina
                        break;
                    case "22":
                        paisNasc = "097"; //Bolivia
                        break;
                    case "38":
                        paisNasc = "767"; //Suiça
                        break;
                    case "39":
                        paisNasc = "386"; //Italia
                        break;
                    case "41":
                        paisNasc = "399"; //Japão
                        break;
                    case "23":
                        paisNasc = "158"; //Chile
                        break;
                    case "42":
                        paisNasc = "160"; //China
                        break;
                    case "24":
                        paisNasc = "586"; //Paraguai
                        break;
                    case "43":
                        paisNasc = "190"; //Coreia do Sul
                        break;
                    case "25":
                        paisNasc = "845"; //Uruguai
                        break;
                    case "26":
                        paisNasc = "850"; //Venezuela
                        break;
                    case "27":
                        paisNasc = "169"; //Colombia
                        break;
                    case "28":
                        paisNasc = "589"; //Peru
                        break;
                    case "29":
                        paisNasc = "239"; //Equador
                        break;
                    default:
                        paisNasc = "105";
                        break;
                }

                xml = xml
                        .replace("<codMunic>nascimento_codMunic</codMunic>", "")
                        .replace("<uf>nascimento_uf</uf>", "")
                        .replace("nascimento_paisNascto", paisNasc)
                        .replace("nascimento_paisNac", paisNasc)
                        .replace("trabEstrangeiro_dtChegada", s2200.getTrabEstrangeiro_dtChegada())
                        .replace("trabEstrangeiro_classTrabEstrang", s2200.getTrabEstrangeiro_classTrabEstrang())
                        .replace("trabEstrangeiro_casadoBr", s2200.getTrabEstrangeiro_casadoBr())
                        .replace("trabEstrangeiro_filhosBr", s2200.getTrabEstrangeiro_filhosBr());
            }

            xml = xml
                    .replace("nascimento_nmMae", s2200.getNascimento_nmMae())
                    .replace("nascimento_nmPai", s2200.getNascimento_nmPai())
                    .replace("CTPS_nrCtps", s2200.getCTPS_nrCtps())
                    .replace("CTPS_serieCtps", s2200.getCTPS_serieCtps())
                    .replace("CTPS_ufCtps", s2200.getCTPS_ufCtps())
                    .replace("brasil_tpLograd", LogradouroESocial.getTipo(s2200.getBrasil_dscLograd()))
                    .replace("brasil_dscLograd", s2200.getBrasil_dscLograd())
                    .replace("brasil_nrLograd", s2200.getBrasil_nrLograd())
                    .replace("<complemento>brasil_complemento</complemento>",
                            s2200.getBrasil_complemento().equals("") ? "" : "<complemento>" + s2200.getBrasil_complemento() + "</complemento>")
                    .replace("<bairro>brasil_bairro</bairro>",
                            s2200.getBrasil_bairro().equals("") ? "" : "<bairro>" + s2200.getBrasil_bairro() + "</bairro>")
                    .replace("brasil_cep", s2200.getBrasil_cep())
                    .replace("brasil_codMunic", s2200.getBrasil_codMunic().replace(".0", ""))
                    .replace("brasil_uf", s2200.getBrasil_uf())
                    //.replace("<infoDeficiencia>", "")
                    .replace("infoDeficiencia_defFisica", s2200.getInfoDeficiencia_defFisica())
                    .replace("infoDeficiencia_defVisual", s2200.getInfoDeficiencia_defVisual())
                    .replace("infoDeficiencia_defAuditiva", s2200.getInfoDeficiencia_defAuditiva())
                    .replace("infoDeficiencia_defMental", s2200.getInfoDeficiencia_defMental())
                    .replace("infoDeficiencia_defIntelectual", s2200.getInfoDeficiencia_defIntelectual())
                    .replace("infoDeficiencia_reabReadap", s2200.getInfoDeficiencia_reabReadap())
                    .replace("infoDeficiencia_infoCota", s2200.getInfoDeficiencia_infoCota())
                    .replace("infoDeficiencia_observacao", s2200.getInfoDeficiencia_observacao())
                    //.replace("</infoDeficiencia>", "")
                    .replace("dependente_dependentes", dependentes)
                    .replace("<contato>", (!s2200.getContato_fonePrinc().equals("") || !s2200.getContato_foneAlternat().equals("") || !s2200.getContato_emailPrinc().equals("")) ? "<contato>" : "")
                    .replace("<fonePrinc>contato_fonePrinc</fonePrinc>", s2200.getContato_fonePrinc().equals("")
                            ? "" : "<fonePrinc>" + s2200.getContato_fonePrinc() + "</fonePrinc>")
                    .replace("<foneAlternat>contato_foneAlternat</foneAlternat>", s2200.getContato_foneAlternat().equals("")
                            ? "" : "<foneAlternat>" + s2200.getContato_foneAlternat() + "</foneAlternat>")
                    .replace("<emailPrinc>contato_emailPrinc</emailPrinc>", s2200.getContato_emailPrinc().equals("")
                            ? "" : "<emailPrinc>" + s2200.getContato_emailPrinc() + "</emailPrinc>")
                    .replace("</contato>", (!s2200.getContato_fonePrinc().equals("") || !s2200.getContato_foneAlternat().equals("") || !s2200.getContato_emailPrinc().equals("")) ? "</contato>" : "")
                    .replace("vinculo_matricula", s2200.getVinculo_matricula().replace(".0", ""))
                    .replace("vinculo_tpRegTrab", s2200.getVinculo_tpRegTrab())
                    .replace("vinculo_tpRegPrev", s2200.getVinculo_tpRegPrev())
                    .replace("vinculo_cadIni", s2200.getVinculo_cadIni())
                    .replace("<nrRecInfPrelim>vinculo_nrRecInfPrelim</nrRecInfPrelim>",
                            (null == s2200.getVinculo_nrRecInfPrelim() || s2200.getVinculo_nrRecInfPrelim().equals(""))
                            ? "" : "<nrRecInfPrelim>" + s2200.getVinculo_nrRecInfPrelim().replace("<nrRecibo>", "") + "</nrRecInfPrelim>")
                    .replace("infoCeletista_dtAdm", s2200.getInfoCeletista_dtAdm())
                    .replace("infoCeletista_tpAdmissao", s2200.getInfoCeletista_tpAdmissao())
                    .replace("infoCeletista_indAdmissao", s2200.getInfoCeletista_indAdmissao())
                    .replace("infoCeletista_tpRegJor", s2200.getInfoCeletista_tpRegJor())
                    .replace("infoCeletista_natAtividade", s2200.getInfoCeletista_natAtividade())
                    .replace("infoCeletista_cnpjSindCategProf", s2200.getInfoCeletista_cnpjSindCategProf())
                    .replace("FGTS_opcFGTS", s2200.getFGTS_opcFGTS())
                    .replace("FGTS_dtOpcFGTS", s2200.getFGTS_dtOpcFGTS())
                    .replace("infoContrato_nmCargo", s2200.getInfoContrato_nmCargo())
                    .replace("infoContrato_CBO", s2200.getCBO())
                    .replace("infoContrato_codCateg", s2200.getInfoContrato_codCateg())
                    .replace("remuneracao_vrSalFx", s2200.getRemuneracao_vrSalFx())
                    .replace("remuneracao_undSalFixo", s2200.getRemuneracao_undSalFixo())
                    .replace("duracao_tpContr", s2200.getDuracao_tpContr())
                    .replace("duracao_dtTerm", s2200.getDuracao_dtTerm())
                    .replace("localTrabGeral_tpInsc", s2200.getLocalTrabGeral_tpInsc())
                    .replace("localTrabGeral_nrInsc", s2200.getLocalTrabGeral_nrInsc())
                    .replace("horContratual_qtdHrsSem", s2200.getHorContratual_qtdHrsSem().replace(".0", ""))
                    .replace("horContratual_tpJornada", s2200.getHorContratual_tpJornada())
                    .replace("horContratual_tmpParc", s2200.getHorContratual_tmpParc())
                    //.replace("horario_horario", horario) //Alterado Carlos 13/06/2022
                    .replace("<horNoturno>N</horNoturno>", "<horNoturno>N</horNoturno>")
                    .replace("horario_horario", "<dscJorn>" + s2200.getHorContratual_dscJorn() + "</dscJorn>")
                    //.replace("filiacaoSindical_filiacaoSindical", s2200.getFiliacaoSindical_cnpjSindTrab().equals("N") ? ""
                    //        : "                <filiacaoSindical>\n"
                    //        + "                    <cnpjSindTrab>" + s2200.getInfoCeletista_cnpjSindCategProf() + "</cnpjSindTrab>\n"
                    //        + "                </filiacaoSindical>\n")
                    //Demonstrar apenas se situacao = D
                    .replace("desligamento_dtDesligamento", (s2200.getDesligamento_dtDesligamento().equals("") ? ""
                            : "            <desligamento>\r\n"
                            + "                <dtDeslig>" + s2200.getDesligamento_dtDesligamento() + "</dtDeslig>\r\n"
                            + "            </desligamento>\r\n"));

            if (!"".equals(s2200.getInfoCeletista_dtBase()) 
                    && !"0".equals(s2200.getInfoCeletista_dtBase())) {
                xml = xml.replace("infoCeletista_dtBase", 
                            s2200.getInfoCeletista_dtBase());                
            } else {
                xml = xml.replace("<dtBase>infoCeletista_dtBase</dtBase>", "");

            }       
            
            boolean existe = !s2200.getCNH_nrRegCnh().equals("")
                    && !s2200.getCNH_ufCnh().equals("")
                    && !s2200.getCNH_dtValid().equals("")
                    && !s2200.getCNH_categoriaCnh().equals("");

            if (existe) {
                xml = xml.replace("<nrRegCnh>CNH_nrRegCnh</nrRegCnh>", s2200.getCNH_nrRegCnh().equals("")
                        ? "" : "<nrRegCnh>" + s2200.getCNH_nrRegCnh() + "</nrRegCnh>")
                        .replace("<ufCnh>CNH_ufCnh</ufCnh>", s2200.getCNH_ufCnh().equals("")
                                ? "" : "<ufCnh>" + s2200.getCNH_ufCnh() + "</ufCnh>")
                        .replace("<dtValid>CNH_dtValid</dtValid>", s2200.getCNH_dtValid().equals("")
                                ? "" : "<dtValid>" + s2200.getCNH_dtValid() + "</dtValid>")
                        .replace("<categoriaCnh>CNH_categoriaCnh</categoriaCnh>", s2200.getCNH_categoriaCnh().equals("")
                                ? "" : "<categoriaCnh>" + s2200.getCNH_categoriaCnh() + "</categoriaCnh>");
            } else {
                xml = xml.replace("<CNH>", "")
                        .replace("<nrRegCnh>CNH_nrRegCnh</nrRegCnh>", "")
                        .replace("<ufCnh>CNH_ufCnh</ufCnh>", "")
                        .replace("<dtValid>CNH_dtValid</dtValid>", "")
                        .replace("<categoriaCnh>CNH_categoriaCnh</categoriaCnh>", "")
                        .replace("</CNH>", "");
            }

            existe = !s2200.getSucessaoVinc_cnpjEmpregAnt().equals("")
                    || !s2200.getSucessaoVinc_matricAnt().equals("")
                    || !s2200.getSucessaoVinc_dtTransf().equals("");

//            if (s2200.getVinculo_cadIni().equals("S")) {
//                existe = false;
//            }
            xml = xml.replace("sucessaoVinc_sucessaoVinc",
                    existe ? "                <sucessaoVinc>\n"
                            + "                    <tpInsc>" + (((s2200.getSucessaoVinc_cnpjEmpregAnt().length()) != 11) ? "1" : "2") + "</tpInsc>\n"
                            + "                    <nrInsc>" + s2200.getSucessaoVinc_cnpjEmpregAnt() + "</nrInsc>\n"
                            + "                    <matricAnt>" + s2200.getSucessaoVinc_matricAnt() + "</matricAnt>\n"
                            + "                    <dtTransf>" + s2200.getSucessaoVinc_dtTransf() + "</dtTransf>\n"
                            + "                </sucessaoVinc>\n"
                            : "");
            xml = xml.replace("duracao_dtTerm", s2200.getDuracao_dtTerm());
            xml = xml.replace("RG_RG",
                    !s2200.getRG_nrRg().equals("") && !s2200.getRG_orgaoEmissor().equals("") ? "                <RG>\n"
                    + "                    <nrRg>" + s2200.getRG_nrRg() + "</nrRg>\n"
                    + "                    <orgaoEmissor>" + s2200.getRG_orgaoEmissor() + "</orgaoEmissor>\n"
                    + (s2200.getRG_dtExped().equals("") ? "" : "<dtExped>" + s2200.getRG_dtExped() + "</dtExped>\n")
                    + "                </RG>\n" : "");
            xml = xml.replace("infoContrato_nmCargo", s2200.
                    getInfoContrato_nmCargo());

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2205> getS2205(String codFil, String compet, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2205Dao s2205dao = new S2205Dao();
            List<S2205> listaS2205 = s2205dao.get(codFil, compet, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2205 s2205 : listaS2205) {

                idPadrao = s2205.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s2205.setEvtAltCadastral_Id("ID1" + id2);
                s2205.setIdeTrabalhador_cpfTrab(s2205.getIdeTrabalhador_cpfTrab().replace(".0", ""));

                for (S2205.Dependentes d : s2205.getDependentes()) {
                    switch (d.getDependente_tpDep()) {
                        case "C":
                            d.setDependente_tpDep("01");
                            break;
                        case "E":
                        case "F":
                            d.setDependente_tpDep("03");
                            break;
                        case "M":
                        case "P":
                            d.setDependente_tpDep("09");
                            break;
                        case "S":
                            d.setDependente_tpDep("12");
                            break;
                        case "O":
                            d.setDependente_tpDep("99");
                            break;
                    }
                }
                s2205.setIdeEmpregador_tpInsc(s2205.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2205.setIdeEmpregador_nrInsc(s2205.getIdeEmpregador_nrInsc().substring(0, 8));
                s2205.setDadosTrabalhador_racaCor(
                        (s2205.getDadosTrabalhador_racaCor().equals("2") ? "1"
                        : (s2205.getDadosTrabalhador_racaCor().equals("4") ? "2"
                        : (s2205.getDadosTrabalhador_racaCor().equals("8") ? "3"
                        : (s2205.getDadosTrabalhador_racaCor().equals("6") ? "4"
                        : (s2205.getDadosTrabalhador_racaCor().equals("0") ? "5" : ""))))));
                s2205.setDadosTrabalhador_estCiv(
                        (s2205.getDadosTrabalhador_estCiv().equals("S") ? "1"
                        : (s2205.getDadosTrabalhador_estCiv().equals("C") ? "2"
                        : (s2205.getDadosTrabalhador_estCiv().equals("Q") ? "4"
                        : (s2205.getDadosTrabalhador_estCiv().equals("D") ? "3"
                        : (s2205.getDadosTrabalhador_estCiv().equals("V") ? "5"
                        : (s2205.getDadosTrabalhador_estCiv().equals("M") ? "1" : "")))))));
                s2205.setDadosTrabalhador_grauInstr(
                        (s2205.getDadosTrabalhador_grauInstr().equals("10") ? "01"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("20") ? "02"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("25") ? "03"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("30") ? "04"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("35") ? "05"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("40") ? "06"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("45") ? "07"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("50") ? "08"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("55") ? "09"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("65") ? "11"
                        : (s2205.getDadosTrabalhador_grauInstr().equals("75") ? "12" : ""))))))))))));
            }
            return listaS2205;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2205XML(String xmlPadrao, S2205 s2205) throws Exception {
        String xml = xmlPadrao, dependentes = "";
        boolean existe;

        for (S2205.Dependentes d : s2205.getDependentes()) {
            dependentes
                    += "            <dependente>\r\n"
                    + "                <tpDep>" + d.getDependente_tpDep() + "</tpDep> \r\n"
                    + "                <nmDep>" + d.getDependente_nmDep() + "</nmDep>\r\n"
                    + "                <dtNascto>" + d.getDependente_dtNascto() + "</dtNascto>\r\n"
                    + (d.getDependente_cpfDep().equals("") ? ""
                    : ("                <cpfDep>" + d.getDependente_cpfDep() + "</cpfDep>\r\n"))
                    + "                <sexoDep>" + d.getDependente_sexoDep() + "</sexoDep>\r\n"
                    + "                <depIRRF>" + d.getDependente_depIRRF() + "</depIRRF>\r\n"
                    + "                <depSF>" + d.getDependente_depSF() + "</depSF>\r\n"
                    + "                <incTrab>" + d.getDependente_incTrab() + "</incTrab>\r\n"
                    + "            </dependente>\r\n";
        }
        xml = xml.replace("dependentes", dependentes);

        xml = xml.replace("evtAltCadastral_Id", s2205.getEvtAltCadastral_Id())
                .replace("ideEvento_indRetif", "1")
                .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "")
                .replace("ideEvento_tpAmb", s2205.getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2205.getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2205.getIdeEvento_verProc())
                .replace("ideEmpregador_tpInsc", s2205.getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2205.getIdeEmpregador_nrInsc())
                .replace("ideTrabalhador_cpfTrab", s2205.getIdeTrabalhador_cpfTrab())
                .replace("alteracao_dtAlteracao", s2205.getAlteracao_dtAlteracao())
                .replace("dadosTrabalhador_nisTrab", s2205.getDadosTrabalhador_nisTrab())
                .replace("dadosTrabalhador_nmTrab", s2205.getDadosTrabalhador_nmTrab())
                .replace("dadosTrabalhador_sexo", s2205.getDadosTrabalhador_sexo())
                .replace("dadosTrabalhador_racaCor", s2205.getDadosTrabalhador_racaCor())
                .replace("dadosTrabalhador_estCiv", s2205.getDadosTrabalhador_estCiv())
                .replace("dadosTrabalhador_grauInstr", s2205.getDadosTrabalhador_grauInstr())
                .replace("nascimento_dtNascto", s2205.getNascimento_dtNascto())
                .replace("nascimento_codMunic", s2205.getNascimento_codMunic().replace(".0", ""))
                .replace("nascimento_dtNascto", s2205.getNascimento_dtNascto())
                //            .replace("nascimento_codMunic", s2205.getNascimento_codMunic().replace(".0", ""))
                .replace("nascimento_uf", s2205.getNascimento_uf())
                .replace("nascimento_paisNascto", s2205.getNascimento_paisNascto())
                .replace("nascimento_paisNac", s2205.getNascimento_paisNac())
                .replace("nascimento_nmMae", s2205.getNascimento_nmMae());

        if (!s2205.getNascimento_nmPai().equals("")) {
            xml = xml.replace("nascimento_nmPai", s2205.getNascimento_nmPai());
        } else {
            xml = xml.replace("<nmPai>nascimento_nmPai</nmPai>", "");
        }

        existe = !s2205.getCTPS_nrCtps().equals("");
        if (existe) {
            xml = xml.replace("CTPS_nrCtps", s2205.getCTPS_nrCtps())
                    .replace("nrCtps_serieCtps", s2205.getNrCtps_serieCtps())
                    .replace("nrCtps_ufCtps", s2205.getNrCtps_ufCtps());
        } else {
            xml = xml.replace("<nrCtps>CTPS_nrCtps</nrCtps>", "")
                    .replace("<serieCtps>nrCtps_serieCtps</serieCtps>", "")
                    .replace("<ufCtps>nrCtps_ufCtps</ufCtps>", "");
        }

        xml = xml.replace("RG_nrRg", s2205.getRG_nrRg())
                .replace("RG_orgaoEmissor", s2205.getRG_orgaoEmissor())
                .replace("RG_dtExped", s2205.getRG_dtExped());

        existe = null != s2205.getCNH_nrRegCnh()
                && !s2205.getCNH_nrRegCnh().equals("")
                && null != s2205.getCNH_ufCnh()
                && !s2205.getCNH_ufCnh().equals("")
                && null != s2205.getCNH_dtValid()
                && !s2205.getCNH_dtValid().equals("")
                && null != s2205.getCNH_categCnh()
                && !s2205.getCNH_categCnh().equals("");

        if (existe) {
            xml = xml.replace("<nrRegCnh>CNH_nrRegCnh</nrRegCnh>", s2205.getCNH_nrRegCnh().equals("")
                    ? "" : "<nrRegCnh>" + s2205.getCNH_nrRegCnh() + "</nrRegCnh>")
                    .replace("<ufCnh>CNH_ufCnh</ufCnh>", s2205.getCNH_ufCnh().equals("")
                            ? "" : "<ufCnh>" + s2205.getCNH_ufCnh() + "</ufCnh>")
                    .replace("<dtValid>CNH_dtValid</dtValid>", s2205.getCNH_dtValid().equals("")
                            ? "" : "<dtValid>" + s2205.getCNH_dtValid() + "</dtValid>")
                    .replace("<categoriaCnh>CNH_categoriaCnh</categoriaCnh>", s2205.getCNH_categCnh().equals("")
                            ? "" : "<categoriaCnh>" + s2205.getCNH_categCnh() + "</categoriaCnh>");
        } else {
            xml = xml.replace("<CNH>", "")
                    .replace("<nrRegCnh>CNH_nrRegCnh</nrRegCnh>", "")
                    .replace("<ufCnh>CNH_ufCnh</ufCnh>", "")
                    .replace("<dtValid>CNH_dtValid</dtValid>", "")
                    .replace("<categCnh>CNH_categCnh</categCnh>", "")
                    .replace("</CNH>", "");
        }

        xml = xml.replace("brasil_tpLograd", LogradouroESocial.getTipo(s2205.getBrasil_dscLograd()))
                .replace("brasil_dscLograd", s2205.getBrasil_dscLograd())
                .replace("brasil_nrLograd", s2205.getBrasil_nrLograd());

        if (!s2205.getBrasil_complemento().equals("")) {
            xml = xml.replace("brasil_complemento", s2205.getBrasil_complemento());
        } else {
            xml = xml.replace("<complemento>brasil_complemento</complemento>", "");
        }

        xml = xml.replace("brasil_bairro", s2205.getBrasil_bairro())
                .replace("brasil_cep", s2205.getBrasil_cep())
                .replace("brasil_codMunic", s2205.getBrasil_codMunic().replace(".0", ""))
                .replace("brasil_uf", s2205.getBrasil_uf())
                .replace("infoDeficiencia_defFisica", s2205.getInfoDeficiencia_defFisica())
                .replace("infoDeficiencia_defVisual", s2205.getInfoDeficiencia_defVisual())
                .replace("infoDeficiencia_defAuditiva", s2205.getInfoDeficiencia_defAuditiva())
                .replace("infoDeficiencia_defMental", s2205.getInfoDeficiencia_defMental())
                .replace("infoDeficiencia_defIntelectual", s2205.getInfoDeficiencia_defIntelectual())
                .replace("infoDeficiencia_reabReadap", s2205.getInfoDeficiencia_reabReadap())
                .replace("infoDeficiencia_infoCota", s2205.getInfoDeficiencia_infoCota())
                .replace("infoDeficiencia_observacao", s2205.getInfoDeficiencia_observacao())
                /*
                .replace("<infoDeficiencia>", "")
                .replace("<defFisica>infoDeficiencia_defFisica</defFisica>", "")
                .replace("<defvisual>infoDeficiencia_defVisual</defvisual>", "")
                .replace("<defAuditiva>infoDeficiencia_defAuditiva</defAuditiva>", "")
                .replace("<defMental>infoDeficiencia_defMental</defMental>", "")
                .replace("<defIntelectual>infoDeficiencia_defIntelectual</defIntelectual>", "")
                .replace("<reabReadap>infoDeficiencia_reabReadap</reabReadap>", "")
                .replace("<infoCota>infoDeficiencia_infoCota</infoCota>", "")
                .replace("<observacao>infoDeficiencia_observacao</observacao>", "")
                .replace("</infoDeficiencia>", "")
                 */
                .replace("dependente", "");

        if (!s2205.getContato_fonePrinc().equals("")) {
            xml = xml.replace("contato_fonePrinc", s2205.getContato_fonePrinc());
        } else {
            xml = xml.replace("<fonePrinc>contato_fonePrinc</fonePrinc>", "");
        }

        if (!s2205.getContato_foneAlternat().equals("")) {
            xml = xml.replace("contato_foneAlternat", s2205.getContato_foneAlternat());
        } else {
            xml = xml.replace("<foneAlternat>contato_foneAlternat</foneAlternat>", "");
        }

        if (!s2205.getContato_emailPrinc().equals("")) {
            xml = xml.replace("contato_emailPrinc", s2205.getContato_emailPrinc());
        } else {
            xml = xml.replace("<emailPrinc>contato_emailPrinc</emailPrinc>", "");
        }

        if (!s2205.getContato_emailAlternat().equals("")) {
            xml = xml.replace("contato_emailAlternat", s2205.getContato_emailAlternat());
        } else {
            xml = xml.replace("<emailAlternat>contato_emailAlternat</emailAlternat>", "");
        }

        return xml;
    }

    public List<S2206> getS2206(String codFil, String compet, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2206Dao s2206dao = new S2206Dao();
            List<S2206> listaS2206 = s2206dao.get(codFil, compet, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2206 s2206 : listaS2206) {

                idPadrao = s2206.getIdeEmpregador().getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s2206.setEvtAltContratual_Id("ID1" + id2);
                s2206.getIdeVinculo().setIdeVinculo_matricula(s2206.getIdeVinculo().getIdeVinculo_matricula().replace(".0", ""));
            }
            return listaS2206;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2206XML(String xmlPadrao, S2206 s2206) throws Exception {
        String xml = xmlPadrao;
        boolean existe;


        xml = xml.replace("evtAltContratual_Id", s2206.getEvtAltContratual_Id());

        xml = xml.replace("ideEvento_indRetif", s2206.getIdeEvento().getIdeEvento_indRetif())
                .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", s2206.getIdeEvento().getIdeEvento_indRetif().equals("2")
                        ? "<nrRecibo>" + s2206.getIdeEvento().getIdeEvento_nrRecibo() + "</nrRecibo>"
                        : "")
                .replace("ideEvento_tpAmb", s2206.getIdeEvento().getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2206.getIdeEvento().getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2206.getIdeEvento().getIdeEvento_verProc());

        xml = xml.replace("ideEmpregador_tpInsc", s2206.getIdeEmpregador().getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2206.getIdeEmpregador().getIdeEmpregador_nrInsc());

        xml = xml.replace("ideVinculo_cpfTrab", s2206.getIdeVinculo().getIdeVinculo_cpfTrab())
                .replace("ideVinculo_nisTrab", s2206.getIdeVinculo().getIdeVinculo_nisTrab())
                .replace("ideVinculo_matricula", s2206.getIdeVinculo().getIdeVinculo_matricula());

        xml = xml.replace("altContratual_dtAlteracao", s2206.getAltContratual().getAltContratual_dtAlteracao())
                .replace("<dtEf>altContratual_dtEf</dtEf>",
                        null == s2206.getAltContratual().getAltContratual_dtEf() || s2206.getAltContratual().getAltContratual_dtEf().equals("")
                        ? ""
                        : "<dtEf>" + s2206.getAltContratual().getAltContratual_dtEf() + "</dtEf>")
                .replace("<dscAlt>altContratual_dscAlt</dscAlt>", ((null == s2206.getAltContratual().getAltContratual_dscAlt()) && (s2206.getAltContratual().getAltContratual_dscAlt().equals("")))
                        ? ""
                        : "<dscAlt>" + s2206.getAltContratual().getAltContratual_dscAlt() + "</dscAlt>");

        xml = xml.replace("vinculo_tpRegPrev", s2206.getAltContratual().getAltContratual_vinculo().getVinculo_tpRegPrev());

        // info celetista
        existe = s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_tpRegJor() != null
                && s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_natAtividade() != null
                && s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_cnpjSindCategProf() != null;
        if (existe) {
            xml = xml.replace("infoCeletista_tpRegJor", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_tpRegJor())
                    .replace("infoCeletista_natAtividade", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_natAtividade())
                    .replace("<dtBase>infoCeletista_dtBase</dtBase>", "")
                    //.replace("<dtBase>infoCeletista_dtBase</dtBase>\n", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_dtBase() == null
                    //        ? ""
                    //        : "<dtBase>" + s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_dtBase() + "</dtBase>\n")
                    .replace("infoCeletista_cnpjSindCategProf", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_cnpjSindCategProf());

            existe = s2206.getAltContratual().getAltContratual_infoRegimeTrab()
                    .getInfoRegimeTrab_infoCeletista().getInfoCeletista_trabTempo().getTrabTemp_justProrr() != null;
            if (existe) {
                xml = xml.replace("trabTemp_justProrr", s2206.getAltContratual().getAltContratual_infoRegimeTrab()
                        .getInfoRegimeTrab_infoCeletista().getInfoCeletista_trabTempo().getTrabTemp_justProrr());
            } else {
                xml = xml.replace("<trabTemporario>", "")
                        .replace("<justProrr>trabTemp_justProrr</justProrr>", "")
                        .replace("</trabTemporario>", "");
            }

            existe = s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_aprend().getAprend_tpInsc() != null
                    && s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_aprend().getAprend_nrInsc() != null;
            if (existe) {
                xml = xml.replace("aprend_tpInsc", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_aprend().getAprend_tpInsc())
                        .replace("aprend_nrInsc", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_aprend().getAprend_nrInsc());
            } else {
                xml = xml.replace("<aprend>", "")
                        .replace("<tpInsc>aprend_tpInsc</tpInsc>", "")
                        .replace("<nrInsc>aprend_nrInsc</nrInsc>", "")
                        .replace("</aprend>", "");
            }
        } else {
            xml = xml.replace("                <infoCeletista>\n"
                    + "                    <tpRegJor>infoCeletista_tpRegJor</tpRegJor>\n"
                    + "                    <natAtividade>infoCeletista_natAtividade</natAtividade>\n"
                    + "                    <dtBase>infoCeletista_dtBase</dtBase>\n"
                    + "                    <cnpjSindCategProf>infoCeletista_cnpjSindCategProf</cnpjSindCategProf>\n"
                    + "                    <trabTemp>\n"
                    + "                        <justProrr>trabTemp_justProrr</justProrr>\n"
                    + "                    </trabTemp>\n"
                    + "                    <aprend>\n"
                    + "                        <tpInsc>aprend_tpInsc</tpInsc>\n"
                    + "                        <nrInsc>aprend_nrInsc</nrInsc>\n"
                    + "                    </aprend>\n"
                    + "                </infoCeletista>\n", "");
        }

        existe = s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoEstatuario().getInfoEstatutario_tpPlanRP() != null;
        if (existe) {
            xml = xml.replace("infoEstatutario_tpPlanRP", s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoEstatuario().getInfoEstatutario_tpPlanRP());
        } else {
            xml = xml.replace("<infoEstatutario>", "")
                    .replace("<tpPlanRP>infoEstatutario_tpPlanRP</tpPlanRP>", "")
                    .replace("</infoEstatutario>", "");
        }

        xml = xml
                .replace("<nmCargo>infoContrato_nmCargo</nmCargo>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_nmCargo() == null
                        ? ""
                        : "<nmCargo>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_nmCargo() + "</nmCargo>")
                .replace("<CBOCargo>infoContrato_CBO</CBOCargo>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_CBO() == null
                        ? ""
                        : "<CBOCargo>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_CBO() + "</CBOCargo>")
                .replace("<codFuncao>infoContrato_codFuncao</codFuncao>\n", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codFuncao() == null
                        ? ""
                        : "<codFuncao>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codFuncao() + "</codFuncao>\n")
                .replace("infoContrato_codCateg", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codCateg())
                .replace("<codCarreira>infoContrato_codCarreira</codCarreira>\n", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codCarreira() == null
                        ? ""
                        : "<codCarreira>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codCarreira() + "</codCarreira>\n")
                .replace("<dtIngrCarr>infoContrato_dtIngrCarr</dtIngrCarr>\n", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_dtIngrCarr() == null
                        ? ""
                        : "<dtIngrCarr>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_dtIngrCarr() + "</dtIngrCarr>\n");

        xml = xml.replace("remuneracao_vrSalFx", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao().getRemuneracao_vrSalFx())
                .replace("remuneracao_undSalFixo", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao()
                        .getRemuneracao_undSalFixo().equals("H") ? "1"
                        : (s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao()
                                .getRemuneracao_undSalFixo().equals("D") ? "2"
                        : (s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao()
                                .getRemuneracao_undSalFixo().equals("M") ? "5" : "")))
                .replace("<dscSalVar>remuneracao_dscSalVar</dscSalVar>",
                        s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao().getRemuneracao_undSalFixo().equals("6")
                        || s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao().getRemuneracao_undSalFixo().equals("7")
                        ? "<dscSalVar>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_remuneracao().getRemuneracao_dscSalVar() + "</dscSalVar>"
                        : "");
                    
        xml = xml.replace("duracao_tpContr", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_duracao().getDuracao_tpContr())
                .replace("<dtTerm>duracao_dtTerm</dtTerm>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_duracao().getDuracao_tpContr().equals("2")
                        ? "<dtTerm>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_duracao().getDuracao_dtTerm() + "</dtTerm>"
                        : "");

        existe = s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_tpInsc() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_nrInsc() != null;
        if (existe) {
            xml = xml.replace("localTrabGeral_tpInsc", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_tpInsc())
                    .replace("localTrabGeral_nrInsc", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_nrInsc())
                    .replace("<descComp>localTrabGeral_descComp</descComp>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_descComp() == null
                            ? ""
                            : "<descComp>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabGeral().getLocalTrabGeral_descComp() + "</descComp>");
        } else {
            xml = xml.replace("                    <localTrabGeral>\n"
                    + "                        <tpInsc>localTrabGeral_tpInsc</tpInsc>\n"
                    + "                        <nrInsc>localTrabGeral_nrInsc</nrInsc>\n"
                    + "                        <descComp>localTrabGeral_descComp</descComp>\n"
                    + "                    </localTrabGeral>\n", "");
        }

        existe = s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_tpLograd() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_dscLograd() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_nrLograd() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_cep() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_codMunic() != null
                && s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_uf() != null;
        if (existe) {
            xml = xml.replace("localTrabDom_tpLograd", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_tpLograd())
                    .replace("localTrabDom_dscLograd", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_dscLograd())
                    .replace("localTrabDom_nrLograd", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_nrLograd())
                    .replace("<complemento>localTrabDom_complemento</complemento>\n", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_complemento() == null
                            ? ""
                            : "<complemento>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_complemento() + "</complemento>\n")
                    .replace("<bairro>localTrabDom_bairro</bairro>\n", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_bairro() == null
                            ? ""
                            : "<bairro>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_bairro() + "</bairro>\n")
                    .replace("localTrabDom_cep", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_cep())
                    .replace("localTrabDom_codMunic", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_codMunic())
                    .replace("localTrabDom_uf", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_localTrabalho().getLocalTrabalho_localTrabDom().getLocalTrabDom_uf());
        } else {
            xml = xml.replace("<localTrabDom>", "")
                    .replace("<localTempDom>", "")
                    .replace("<tpLograd>localTrabDom_tpLograd</tpLograd>", "")
                    .replace("<dscLograd>localTrabDom_dscLograd</dscLograd>", "")
                    .replace("<nrLograd>localTrabDom_nrLograd</nrLograd>", "")
                    .replace("<complemento>localTrabDom_complemento</complemento>", "")
                    .replace("<bairro>localTrabDom_bairro</bairro>", "")
                    .replace("<cep>localTrabDom_cep</cep>", "")
                    .replace("<codMunic>localTrabDom_codMunic</codMunic>", "")
                    .replace("<uf>localTrabDom_uf</uf>", "")
                    .replace("</localTempDom>", "")
                    .replace("</localTrabDom>", "");
        }

        StringBuilder aux;
        existe = s2206.getAltContratual().getAltContratual_infoRegimeTrab().getInfoRegimeTrab_infoCeletista().getInfoCeletista_tpRegJor().equals("1");
        if (existe) {
            String vNoturno = "";
            if (s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_dscTpJorn().contains("NOT")) {
                vNoturno = "S";
            } else {
                vNoturno = "N";
            }

//            horario = "";
//            for (S2206.AltContratual.InfoContrato.HorContratual.Horario h : 
//                    s2206.getAltContratual().getAltContratual_infoContrato().
//                            getInfoContrato_horContratual().getHorContratual_horario()) {
//                horario += h.getHorario_dia() + "\r\n";
//            }
//;
            xml = xml
                    .replace("<qtdHrsSem>horContratual_qtdHrsSem</qtdHrsSem>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_codCateg().equals("111")
                            ? ""
                            : "<qtdHrsSem>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_qtdHrsSem().replace(".0", "") + "</qtdHrsSem>")
                    .replace("horContratual_tpJornada", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_tpJornada())
                    .replace("<dscTpJorn>horContratual_dscTpJorn</dscTpJorn>", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_tpJornada().equals("9")
                            ? "<dscTpJorn>" + s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_dscTpJorn() + "</dscTpJorn>"
                            : "")
                    .replace("horContratual_tmpParc", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_tmpParc())
                    .replace("horContratual_horNoturno", vNoturno)
                    .replace("horContratual_horNoturno", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_tpJornada())
                    .replace("horario_codHorContrat", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_horContratual().getHorContratual_dscJorn());

        } else {
            xml = xml.replace("                <horContratual>\n"
                    + "                    <qtdHrsSem>horContratual_qtdHrsSem</qtdHrsSem>\n"
                    + "                    <tpJornada>horContratual_tpJornada</tpJornada>\n"
                    + "                    <dscTpJorn>horContratual_dscTpJorn</dscTpJorn>\n"
                    + "                    <tmpParc>horContratual_tmpParc</tmpParc>\n"
                    + "                    <horario>\n"
                    + "                        <dia>horario_dia</dia>\n"
                    + "                        <codHorContrat>horario_codHorContrat</codHorContrat>\n"
                    + "                    </horario>\n"
                    + "                </horContratual>\n", "");
        }

        existe = s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_filiacaoSindical().getFiliacaoSindical_cnpjSindTrab() != null;
        if (existe) {
            xml = xml.replace("filiacaoSindical_cnpjSindTrab", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_filiacaoSindical().getFiliacaoSindical_cnpjSindTrab());
        } else {
            xml = xml.replace("                <filiacaoSindical>\n"
                    + "                    <cnpjSindTrab>filiacaoSindical_cnpjSindTrab</cnpjSindTrab>\n"
                    + "                </filiacaoSindical>\n", "");
        }

        existe = s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_alvaraJudicial().getAlvaraJudicial_nrProcJud() != null;
        if (existe) {
            xml = xml.replace("alvaraJudicial_nrProcJud", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_alvaraJudicial().getAlvaraJudicial_nrProcJud());
        } else {
            xml = xml.replace("<alvaraJudicial>", "")
                    .replace("<nrProcJud>alvaraJudicial_nrProcJud</nrProcJud>", "")
                    .replace("</alvaraJudicial>", "");
        }

        existe = !s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_observacoes().isEmpty();
        if (existe) {
            aux = new StringBuilder(); 
            for (S2206.AltContratual.InfoContrato.Observacoes observacoes : s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_observacoes()) {
                aux = aux.append("                <observacoes>\n")
                        .append("                    <observacao>").append(observacoes.getObservacoes_observacao()).append("</observacao>\n")
                        .append("                </observacoes>\n");
            }
        } else {
            xml = xml.replace("<observacoes>", "")
                    .replace("<observacao>observacoes_observacao</observacao>", "")
                    .replace("</observacoes>", "");
        }

        existe = s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_servPubl().getServPubl_mtvAlter() != null;
        if (existe) {
            xml = xml.replace("servPubl_mtvAlter", s2206.getAltContratual().getAltContratual_infoContrato().getInfoContrato_servPubl().getServPubl_mtvAlter());
        } else {
            xml = xml.replace("                <servPubl>\n"
                    + "                    <mtvAlter>servPubl_mtvAlter</mtvAlter>\n"
                    + "                </servPubl>\n", "");
        }

        return xml;
    }

    public List<S2210> getS2210(List<S2210> funcions, String codFil, String compet,
            String tipoAmbiente, String cadIni, String tipo, Persistencia persistencia) throws Exception {
        try {
            S2210Dao s2210dao = new S2210Dao();
            List<S2210> listaS2210;

            listaS2210 = s2210dao.get(funcions, codFil, compet, tipoAmbiente, tipo, persistencia);
            listaS2210 = s2210dao.get(funcions, codFil, compet, tipoAmbiente, tipo, persistencia);

            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2210 s2210 : listaS2210) {
                idPadrao = s2210.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2210.setEvtCAT_Id("ID1" + id2);
                s2210.setIdeEvento_tpAmb(tipoAmbiente);
                s2210.setIdeEmpregador_tpInsc(s2210.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2210.setIdeEmpregador_nrInsc(s2210.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS2210;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2210XML(String xmlPadrao, S2210 s2210) {
        String xml = xmlPadrao
                .replace("<indRetif>ideEvento_indRetif</indRetif>", "")
                .replace("evtCAT_Id", s2210.getEvtCAT_Id())
                .replace("evtInfoEmpregador_Id", "ID1" + s2210.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                .replace("ideEvento_tpAmb", s2210.getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2210.getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2210.getIdeEvento_verProc())
                .replace("ideEmpregador_tpInsc", s2210.getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2210.getIdeEmpregador_nrInsc())
                .replace("ideVinculo_cpfTrab", s2210.getIdeVinculo_cpfTrab())
                .replace("ideVinculo_matricula", s2210.getIdeVinculo_matricula())
                .replace("ideVinculo_codCateg", s2210.getIdeVinculo_codCateg())
                .replace("cat_dtAcid", s2210.getCat_dtAcid())
                .replace("cat_tpAcid", Integer.toString(s2210.getCat_tpAcid()))
                .replace("cat_hrAcid", s2210.getCat_hrAcid())
                .replace("cat_hrsTrabAntesAcid", s2210.getCat_hrsTrabAntesAcid())
                .replace("cat_tpCat", Integer.toString(s2210.getCat_tpCat()))
                .replace("cat_indCatObito", s2210.getCat_indCatObito())
                //.replace("cat_dtObito", s2210.getCat_dtObito())
                .replace("<dtObito>cat_dtObito</dtObito>", "")
                .replace("cat_indComunPolicia", s2210.getCat_indComunPolicia())
                .replace("cat_codSitGeradora", Integer.toString(s2210.getCat_codSitGeradora()))
                .replace("cat_iniciatCAT", Integer.toString(s2210.getCat_iniciatCAT()))                
                //.replace("cat_obsCAT", s2210.getCat_obsCAT())
                .replace("<obsCAT>cat_obsCAT</obsCAT>", "")
                .replace("cat_ultDiaTrab", s2210.getCat_ultDiaTrab())
                .replace("cat_houveAfast", s2210.getCat_houveAfast())                                               
                .replace("localAcidente_tpLocal", Integer.toString(s2210.getLocalAcidente_tpLocal()))
                //.replace("localAcidente_dscLocal", s2210.getLocalAcidente_dscLocal())
                .replace("localAcidente_dscLocal", "PUBLICO")
                .replace("localAcidente_tpLograd", s2210.getLocalAcidente_tpLograd())
                .replace("AVENIDA", "AV")
                .replace("localAcidente_dscLograd", s2210.getLocalAcidente_dscLograd())
                .replace("localAcidente_nrLograd", s2210.getLocalAcidente_nrLograd())
                .replace("localAcidente_complemento", s2210.getLocalAcidente_complemento())
                .replace("localAcidente_bairro", s2210.getLocalAcidente_bairro())
                .replace("localAcidente_cep", s2210.getLocalAcidente_cep())
                .replace("localAcidente_codMunic", Integer.toString(s2210.getLocalAcidente_codMunic()))
                .replace("localAcidente_uf", s2210.getLocalAcidente_uf())
                .replace("localAcidente_pais", s2210.getLocalAcidente_pais())
                .replace("localAcidente_codPostal", s2210.getLocalAcidente_codPostal())
                .replace("ideLocalAcid_tpInsc", Integer.toString(s2210.getIdeLocalAcid_tpInsc()))
                .replace("ideLocalAcid_nrInsc", s2210.getIdeLocalAcid_nrInsc())
                .replace("parteAtingida_codParteAting", Integer.toString(s2210.getParteAtingida_codParteAting()))
                .replace("parteAtingida_lateralidade", Integer.toString(s2210.getParteAtingida_lateralidade()))
                .replace("agenteCausador_codAgntCausador", Integer.toString(s2210.getAgenteCausador_codAgntCausador()))
                .replace("atestado_dtAtendimento", s2210.getAtestado_dtAtendimento())
                .replace("atestado_hrAtendimento", s2210.getAtestado_hrAtendimento())
                .replace("atestado_indInternacao", s2210.getAtestado_indInternacao())
                .replace("atestado_durTrat", Integer.toString(s2210.getAtestado_durTrat()))
                .replace("atestado_indAfast", s2210.getAtestado_indAfast())
                .replace("atestado_dscLesao", Integer.toString(s2210.getAtestado_dscLesao()))
                .replace("atestado_dscCompLesao", s2210.getAtestado_dscCompLesao())
                .replace("atestado_diagProvavel", s2210.getAtestado_diagProvavel())
                .replace("atestado_codCID", s2210.getAtestado_codCID())
                .replace("atestado_observacao", s2210.getAtestado_observacao())
                .replace("emitente_nmEmit", s2210.getEmitente_nmEmit())
                .replace("emitente_ideOC", Integer.toString(s2210.getEmitente_ideOC()))
                .replace("emitente_nrOC", s2210.getEmitente_nrOC())
                .replace("emitente_ufOC", s2210.getEmitente_ufOC());
        String retorno = xml;

        try {
            return retorno;
        } catch (Exception ex) {
            return "";
        }
    }

    // Evento Novo 2220 - Monitoramento de Saude Carlos 23/12/2022
    public List<S2220> getS2220(List<S2220> funcions, String codFil, String compet,
            String tipoAmbiente, String cadIni, String tipo, Persistencia persistencia) throws Exception {
        try {
            S2220Dao s2220dao = new S2220Dao();
            List<S2220> listaS2220;

            listaS2220 = s2220dao.get(funcions, codFil, compet, tipoAmbiente, tipo, persistencia);

            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2220 s2220 : listaS2220) {
                idPadrao = s2220.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2220.setevtMonit_Id("ID1" + id2);
                s2220.setIdeEvento_tpAmb(tipoAmbiente);
                s2220.setIdeEmpregador_tpInsc(s2220.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2220.setIdeEmpregador_nrInsc(s2220.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS2220;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2220XML(String xmlPadrao, S2220 s2220) {
        String xml = xmlPadrao
                .replace("<indRetif>ideEvento_indRetif</indRetif>", "<indRetif>1</indRetif>")
                .replace("evtMonit_Id", s2220.getevtMonit_Id());
        if (s2220.getIdeEvento_nrRecibo().equals("")) {
            xml = xml.replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "");
        } else {
            xml = xml.replace("ideEvento_nrRecibo", s2220.getIdeEvento_nrRecibo());
        }
        xml = xml.replace("evtInfoEmpregador_Id", "ID1" + s2220.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                .replace("ideEvento_tpAmb", s2220.getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2220.getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2220.getIdeEvento_verProc())
                .replace("ideEmpregador_tpInsc", s2220.getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2220.getIdeEmpregador_nrInsc())
                .replace("ideVinculo_cpfTrab", s2220.getIdeVinculo_cpfTrab())
                .replace("ideVinculo_matricula", s2220.getIdeVinculo_matricula())
                //.replace("ideVinculo_codCateg", s2220.getIdeVinculo_codCateg())                 <codCateg>ideVinculo_codCateg</codCateg>

                .replace("exMedOcup_tpExameOcup", Integer.toString(s2220.getexMedOcup_tpExameOcup()))
                .replace("exMedOcup_dtAso", s2220.getexMedOcup_dtAso())
                .replace("exMedOcup_resAso", Integer.toString(s2220.getexMedOcup_resAso()))
                .replace("exMedOcup_dtExm", s2220.getExMedOcup_dtExm())
                .replace("exMedOcup_procRealizado", FuncoesString.PreencheEsquerda(Integer.toString(s2220.getExMedOcup_procRealizado()), 4, "0"))
                .replace("exMedOcup_obsProc", s2220.getExMedOcup_obsProc())
                .replace("exMedOcup_ordExame", Integer.toString(s2220.getExMedOcup_ordExame()))
                .replace("exMedOcup_indResult", Integer.toString(s2220.getExMedOcup_indResult()))
                .replace("exMedOcup_nmMed", s2220.getExMedOcup_nmMed())
                .replace("exMedOcup_nrCRM", s2220.getExMedOcup_nrCRM())
                .replace("exMedOcup_ufCRM", s2220.getExMedOcup_ufCRM())
                .replace("respMonit_cpfResp", s2220.getRespMonit_cpfResp())
                .replace("respMonit_nmResp", s2220.getRespMonit_nmResp())
                .replace("respMonit_nrCRM", s2220.getRespMonit_nrCRM())
                .replace("respMonit_ufCRM", s2220.getRespMonit_ufCRM());
        String retorno = xml;

        try {
            return retorno;
        } catch (Exception ex) {
            return "";
        }
    }

    public List<S2230> getS2230(String codFil, String validade, String tipoAmbiente, boolean inicio, Persistencia persistencia) throws Exception {
        try {
            S2230Dao s2230dao = new S2230Dao();
            List<S2230> listaS2230 = s2230dao.get(codFil, validade, tipoAmbiente, inicio, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2230 s2230 : listaS2230) {
                idPadrao = s2230.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2230.setEvtAfastTemp_Id("ID1" + id2);
                s2230.setIdeEvento_tpAmb(tipoAmbiente);
                s2230.setIdeEmpregador_nrInsc(s2230.getIdeEmpregador_nrInsc().substring(0, 8));
                s2230.setIdeVinculo_matricula(s2230.getIdeVinculo_matricula().replace(".0", ""));
                s2230.setCodPonto(s2230.getCodPonto().replace(".0", ""));
            }
            return listaS2230;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2230XML(String xmlPadrao, boolean inicio, S2230 s2230) throws Exception {
        try {
            String xml = xmlPadrao;
            boolean existe;

            xml = xml.replace("evtAfastTemp_Id", s2230.getEvtAfastTemp_Id());

            // ideEvento
            if (s2230.getIdeEvento_nrRecibo().equals("")) {
                xml = xml.replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "");
            } else {
                xml = xml.replace("ideEvento_nrRecibo", s2230.getIdeEvento_nrRecibo());
            }
            xml = xml.replace("ideEvento_indRetif", s2230.getIdeEvento_indRetif())
                    .replace("ideEvento_tpAmb", s2230.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s2230.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s2230.getIdeEvento_verProc());

            xml = xml.replace("ideEmpregador_tpInsc", s2230.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s2230.getIdeEmpregador_nrInsc());

            // ideVinculo
            if (s2230.getIdeVinculo_nisTrab().equals("")) {
                xml = xml.replace("<nisTrab>ideVinculo_nisTrab</nisTrab>", "");
            } else {
                xml = xml.replace("ideVinculo_nisTrab", s2230.getIdeVinculo_nisTrab());
            }
            if (s2230.getCodPonto().equals("")) {
                xml = xml.replace("<matricula>ideVinculo_matricula</matricula>", "");
            } else {
                xml = xml.replace("ideVinculo_matricula", s2230.getCodPonto());
            }
            if (s2230.getIdeVinculo_codCateg().equals("")) {
                xml = xml.replace("<codCateg>ideVinculo_codCateg</codCateg>", "");
            } else {
                xml = xml.replace("ideVinculo_codCateg", s2230.getIdeVinculo_codCateg());
            }

            xml = xml.replace("ideVinculo_cpfTrab", s2230.getIdeVinculo_cpfTrab());

            // Se nao for ferias tira perAquis - Carlos 12/07/2022
            // iniAfastamento                        
            if (inicio) {
                xml = xml.replace("iniAfastamento_dtIniAfast", s2230.getIniAfastamento_dtIniAfast())
                        .replace("iniAfastamento_codMotAfast", s2230.getIniAfastamento_codMotAfast());

                if (s2230.getIniAfastamento_infoMesmoMtv().equals("")) {
                    xml = xml.replace("<infoMesmoMtv>iniAfastamento_infoMesmoMtv</infoMesmoMtv>", "");
                } else {
                    xml = xml.replace("iniAfastamento_infoMesmoMtv", s2230.getIniAfastamento_infoMesmoMtv());
                }

                if (s2230.getIniAfastamento_tpAcidTransito().equals("")) {
                    xml = xml.replace("<tpAcidTransito>iniAfastamento_tpAcidTransito</tpAcidTransito>", "");
                } else {
                    xml = xml.replace("iniAfastamento_tpAcidTransito", s2230.getIniAfastamento_tpAcidTransito());
                }

                if (s2230.getIniAfastamento_observacao().equals("")) {
                    xml = xml.replace("<observacao>iniAfastamento_observacao</observacao>", "");
                } else {
                    xml = xml.replace("iniAfastamento_observacao", s2230.getIniAfastamento_observacao());
                }

                if (!s2230.getIniAfastamento_codMotAfast().equals("15") || s2230.getIniAfastamento_codMotAfast().equals(null)) {
                    xml = xml.replace("<dados_peraquis>", "");
                } else {
                    xml = xml.replace("<dados_peraquis>", "<perAquis>"
                            + "<dtInicio>" + s2230.getIniAfastamento_dtInicio() + "</dtInicio>"
                            + "<dtFim>" + s2230.getIniAfastamento_dtFim() + "</dtFim>"
                            + "</perAquis>");
                }

                // infoAtestado
                existe = false;
                if (s2230.getIniAfastamento_codMotAfast().equals("01") || s2230.getIniAfastamento_codMotAfast().equals("03")) {
                    existe = true;
                }
//                if(!s2230.getInfoAtestado_codCID().equals("")) existe = true;
//                if(!s2230.getInfoAtestado_qtdDiasAfast().equals("")) existe = true;

                if (existe) {
                    //if (s2230.getInfoAtestado_codCID().equals("") || s2230.getIniAfastamento_codMotAfast().equals("03")) {
                    if (s2230.getInfoAtestado_codCID().equals("")) {
                        xml = xml.replace("<codCID>infoAtestado_codCID</codCID>", "");
                    } else {
                        xml = xml.replace("infoAtestado_codCID", s2230.getInfoAtestado_codCID());
                    }

                    //if(s2230.getInfoAtestado_codCID().equals("") || !s2230.getIniAfastamento_codMotAfast().equals("03")){
                    //if(s2230.getInfoAtestado_codCID().equals("")){
                    //    //xml = xml.replace("<infoAtestado>", "");
                    //    xml = xml.replace("<qtdDiasAfast>infoAtestado_qtdDiasAfast</qtdDiasAfast>", "");
                    //    //xml = xml.replace("</infoAtestado>", "");
                    //} else 
                    xml = xml.replace("infoAtestado_qtdDiasAfast", s2230.getInfoAtestado_qtdDiasAfast());
                    if (!s2230.getIniAfastamento_codMotAfast().equals("15") || s2230.getIniAfastamento_codMotAfast().equals(null)) {
                        xml = xml.replace("<dados_peraquis>", "");
                    } else {
                        xml = xml.replace("<dados_peraquis>", "<perAquis>"
                                + "<dtInicio>" + s2230.getIniAfastamento_dtInicio() + "</dtInicio>"
                                + "<dtFim>" + s2230.getIniAfastamento_dtFim() + "</dtFim>"
                                + "</perAquis>");
                    }
                    // emitente
                    existe = false;
                    if ((!s2230.getEmitente_nmEmit().equals(""))
                            && (!s2230.getEmitente_ideOC().equals(""))
                            && (!s2230.getEmitente_nrOc().equals(""))
                            && (!s2230.getEmitente_ufOC().equals(""))) {
                        existe = true;
                    }

                    if (existe) {
                        if (s2230.getEmitente_nmEmit().equals("")) {
                            xml = xml.replace("<nmEmit>emitente_nmEmit</nmEmit>", "");
                        } else {
                            xml = xml.replace("emitente_nmEmit", s2230.getEmitente_nmEmit());
                        }

                        if (s2230.getEmitente_ideOC().equals("")) {
                            xml = xml.replace("<ideOC>emitente_ideOC</ideOC>", "");
                        } else {
                            xml = xml.replace("emitente_ideOC", s2230.getEmitente_ideOC());
                        }

                        if (s2230.getEmitente_nrOc().equals("")) {
                            xml = xml.replace("<nrOc>emitente_nrOc</nrOc>", "");
                        } else {
                            xml = xml.replace("emitente_nrOc", s2230.getEmitente_nrOc());
                        }

                        if (s2230.getEmitente_ufOC().equals("")) {
                            xml = xml.replace("<ufOC>emitente_ufOC</ufOC>", "");
                        } else {
                            xml = xml.replace("emitente_ufOC", s2230.getEmitente_ufOC());
                        }
                    } else {
                        xml = xml.replace("<emitente>", "")
                                .replace("<nmEmit>emitente_nmEmit</nmEmit>", "")
                                .replace("<ideOC>emitente_ideOC</ideOC>", "")
                                .replace("<nrOc>emitente_nrOc</nrOc>", "")
                                .replace("<ufOC>emitente_ufOC</ufOC>", "")
                                .replace("</emitente>", "");
                    }

                } else {
                    xml = xml.replace("<infoAtestado>", "")
                            .replace("<codCID>infoAtestado_codCID</codCID>", "")
                            .replace("<qtdDiasAfast>infoAtestado_qtdDiasAfast</qtdDiasAfast>", "")
                            .replace("<emitente>", "")
                            .replace("<nmEmit>emitente_nmEmit</nmEmit>", "")
                            .replace("<ideOC>emitente_ideOC</ideOC>", "")
                            .replace("<nrOc>emitente_nrOc</nrOc>", "")
                            .replace("<ufOC>emitente_ufOC</ufOC>", "")
                            .replace("</emitente>", "")
                            .replace("</infoAtestado>", "");
                }

                // infoCessao
                existe = false;
//                if(!s2230.getInfoCessao_cnpjCess().equals("")) existe = true;
                if (!s2230.getInfoCessao_infOnus().equals("")) {
                    existe = true;
                }

                if (existe) {
                    xml = xml.replace("infoCessao_cnpjCess", s2230.getInfoMandSind_cnpjSind());
                    xml = xml.replace("infoMandSind_infOnusRemun", s2230.getInfoMandSind_infOnusRemun());
                } else {
                    xml = xml.replace("<infoCessao>", "")
                            .replace("<cnpjCess>infoCessao_cnpjCess</cnpjCess>", "")
                            .replace("<infOnus>infoCessao_infOnus</infOnus>", "")
                            .replace("</infoCessao>", "");
                }

                // infoMandSind
                if (s2230.getIniAfastamento_codMotAfast().equals("24")) {
                    xml = xml.replace("infoMandSind_cnpjSind", s2230.getInfoMandSind_cnpjSind());
                    xml = xml.replace("infoMandSind_infOnusRemun", s2230.getInfoMandSind_infOnusRemun());
                } else {
                    xml = xml.replace("<infoMandSind>", "")
                            .replace("<cnpjSind>infoMandSind_cnpjSind</cnpjSind>", "")
                            .replace("<infOnusRemun>infoMandSind_infOnusRemun</infOnusRemun>", "")
                            .replace("</infoMandSind>", "");
                }
            } else {

                xml = xml.replace("<iniAfastamento>", "")
                        .replace("<dtIniAfast>iniAfastamento_dtIniAfast</dtIniAfast>", "")
                        .replace("<codMotAfast>iniAfastamento_codMotAfast</codMotAfast>", "")
                        .replace("<infoMesmoMtv>iniAfastamento_infoMesmoMtv</infoMesmoMtv>", "")
                        .replace("<tpAcidTransito>iniAfastamento_tpAcidTransito</tpAcidTransito>", "")
                        .replace("<observacao>iniAfastamento_observacao</observacao>", "")
                        .replace("<infoAtestado>", "")
                        .replace("<codCID>infoAtestado_codCID</codCID>", "")
                        .replace("<qtdDiasAfast>infoAtestado_qtdDiasAfast</qtdDiasAfast>", "")
                        .replace("<emitente>", "")
                        .replace("<nmEmit>emitente_nmEmit</nmEmit>", "")
                        .replace("<ideOC>emitente_ideOC</ideOC>", "")
                        .replace("<nrOc>emitente_nrOc</nrOc>", "")
                        .replace("<ufOC>emitente_ufOC</ufOC>", "")
                        .replace("</emitente>", "")
                        .replace("</infoAtestado>", "")
                        .replace("<dadosperaquis>", "")
                        .replace("<dados_peraquis>", "")
                        .replace("<infoCessao>", "")
                        .replace("<cnpjCess>infoCessao_cnpjCess</cnpjCess>", "")
                        .replace("<infOnus>infoCessao_infOnus</infOnus>", "")
                        .replace("</infoCessao>", "")
                        .replace("<infoMandSind>", "")
                        .replace("<cnpjSind>infoMandSind_cnpjSind</cnpjSind>", "")
                        .replace("<infOnusRemun>infoMandSind_infOnusRemun</infOnusRemun>", "")
                        .replace("</infoMandSind>", "")
                        .replace("</iniAfastamento>", "");
            }

            // infoRetif
            existe = false;
            if (!s2230.getInfoRetif_origRetif().equals("")) {
                existe = true;
            }
            if (!s2230.getInfoRetif_tpProc().equals("")) {
                existe = true;
            }
            if (!s2230.getInfoRetif_nrProc().equals("")) {
                existe = true;
            }

            if (existe) {
                xml = xml.replace("infoRetif_origRetif", s2230.getInfoRetif_origRetif());

                // tpProc
                if (s2230.getInfoRetif_tpProc().equals("")) {
                    xml = xml.replace("<tpProc>infoRetif_tpProc</tpProc>", "");
                } else {
                    xml = xml.replace("infoRetif_tpProc", s2230.getInfoRetif_tpProc());
                }

                // nrProc
                if (s2230.getInfoRetif_nrProc().equals("")) {
                    xml = xml.replace("<nrProc>infoRetif_nrProc</nrProc>", "");
                } else {
                    xml = xml.replace("infoRetif_nrProc", s2230.getInfoRetif_nrProc());
                }
            } else {
                xml = xml.replace("<infoRetif>", "")
                        .replace("<origRetif>infoRetif_origRetif</origRetif>", "")
                        .replace("<tpProc>infoRetif_tpProc</tpProc>", "")
                        .replace("<nrProc>infoRetif_nrProc</nrProc>", "")
                        .replace("</infoRetif>", "");
            }

            // fimAfastamento
            if (!inicio || s2230.getIniAfastamento_codMotAfast().equals("15")) {
                xml = xml.replace("fimAfastamento_dtTermAfast", s2230.getFimAfastamento_dtTermAfast());
            } else {
                xml = xml
                        .replace("<fimAfastamento>", "")
                        .replace("<dtTermAfast>fimAfastamento_dtTermAfast</dtTermAfast>", "")
                        .replace("</fimAfastamento>", "");
            }

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    // Evento Novo 2240 - Condições Ambientais do Trabalho - Agentes Nocivos 27/12/2022
    public List<S2240> getS2240(List<S2240> funcions, String codFil, String compet,
            String tipoAmbiente, String cadIni, String tipo, Persistencia persistencia) throws Exception {
        try {
            S2240Dao s2240dao = new S2240Dao();
            List<S2240> listaS2240;

            listaS2240 = s2240dao.get(funcions, codFil, compet, tipoAmbiente, tipo, persistencia);

            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2240 s2240 : listaS2240) {
                idPadrao = s2240.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2240.setEvtExpRisco_Id("ID1" + id2);
                s2240.setIdeEvento_tpAmb(tipoAmbiente);
                s2240.setIdeEmpregador_tpInsc(s2240.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2");
                s2240.setIdeEmpregador_nrInsc(s2240.getIdeEmpregador_nrInsc().substring(0, 8));
            }
            return listaS2240;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2240XML(String xmlPadrao, S2240 s2240) {
        String xml = xmlPadrao
                .replace("<indRetif>ideEvento_indRetif</indRetif>", "<indRetif>1</indRetif>")
                .replace("evtExpRisco_Id", s2240.getEvtExpRisco_Id());
        if (s2240.getIdeEvento_nrRecibo().equals("")) {
            xml = xml.replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "");
        } else {
            xml = xml.replace("ideEvento_nrRecibo", s2240.getIdeEvento_nrRecibo());
        }

        if (s2240.getInfoExpRisco_codAgNoc().equals("09.01.001")) {
            xml = xml.replace("<dscAgNoc>infoExpRisco_dscAgNoc</dscAgNoc>", "")
                    .replace("<tpAval>infoExpRisco_tpAval</tpAval>", "")
                    .replace("<tpAval>infoExpRisco_tpAval</tpAval>", "")
                    .replace("<intConc>infoExpRisco_intConc</intConc>", "")
                    .replace("<unMed>infoExpRisco_unMed</unMed>", "")
                    .replace("<tecMedicao>infoExpRisco_tecMedicao</tecMedicao>", "")                    
                    .replace("<epcEpi>","")
                    .replace("<utilizEPC>infoExpRisco_utilizEPC</utilizEPC>", "")
                    .replace("<utilizEPI>infoExpRisco_utilizEPI</utilizEPI>", "")
                    .replace("<eficEpi>infoExpRisco_eficEpi</eficEpi>", "")
                    .replace("<epi>", "")
                    .replace("<docAval>infoExpRisco_docAval</docAval>", "")
                    .replace("</epi>", "")
                    .replace("<epiCompl>", "")
                    .replace("<medProtecao>infoExpRisco_medProtecao</medProtecao>", "")
                    .replace("<condFuncto>infoExpRisco_condFuncto</condFuncto>", "")
                    .replace("<usoInint>infoExpRisco_usoInint</usoInint>", "")
                    .replace("<przValid>infoExpRisco_przValid</przValid>", "")
                    .replace("<periodicTroca>infoExpRisco_periodicTroca</periodicTroca>", "")
                    .replace("<higienizacao>infoExpRisco_higienizacao</higienizacao>", "")
                    .replace("</epiCompl>", "")
                    .replace("</epcEpi>", "");
            
        }

        if (Integer.toString(s2240.getInfoExpRisco_intConc()).equals("1")) {
            xml = xml.replace("<intConc>infoExpRisco_intConc</intConc>", "")
                    .replace("<unMed>infoExpRisco_unMed</unMed>", "")
                    .replace("<tecMedicao>infoExpRisco_tecMedicao</tecMedicao>", "");
        }
        

        xml = xml.replace("evtInfoEmpregador_Id", "ID1" + s2240.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                .replace("ideEvento_tpAmb", s2240.getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2240.getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2240.getIdeEvento_verProc())
                .replace("ideEmpregador_tpInsc", s2240.getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2240.getIdeEmpregador_nrInsc())
                .replace("ideVinculo_cpfTrab", s2240.getIdeVinculo_cpfTrab())
                .replace("ideVinculo_matricula", s2240.getIdeVinculo_matricula())
                //.replace("ideVinculo_codCateg", s2240.getIdeVinculo_codCateg())       <codCateg>ideVinculo_codCateg</codCateg>

                .replace("infoExpRisco_nrInsc", s2240.getInfoExpRisco_nrInsc())
                .replace("infoExpRisco_dtIniCondicao", s2240.getInfoExpRisco_dtIniCondicao())
                .replace("infoExpRisco_localAmb", Integer.toString(s2240.getInfoExpRisco_localAmb()))
                .replace("infoExpRisco_dscSetor", s2240.getInfoExpRisco_dscSetor())
                .replace("infoExpRisco_dscAtivDes", s2240.getInfoExpRisco_dscAtivDes())
                .replace("infoExpRisco_codAgNoc", s2240.getInfoExpRisco_codAgNoc())
                .replace("infoExpRisco_dscAgNoc", s2240.getInfoExpRisco_dscAgNoc())
                .replace("infoExpRisco_tpAval", Integer.toString(s2240.getInfoExpRisco_tpAval()))
                .replace("infoExpRisco_intConc", Integer.toString(s2240.getInfoExpRisco_intConc()))
                .replace("infoExpRisco_unMed", Integer.toString(s2240.getInfoExpRisco_unMed()))
                .replace("infoExpRisco_tecMedicao", s2240.getInfoExpRisco_tecMedicao())
                .replace("infoExpRisco_utilizEPC", Integer.toString(s2240.getInfoExpRisco_utilizEPC()))
                .replace("infoExpRisco_utilizEPI", Integer.toString(s2240.getInfoExpRisco_utilizEPI()))
                .replace("infoExpRisco_eficEpi", s2240.getInfoExpRisco_eficEpi())
                .replace("infoExpRisco_docAval", s2240.getInfoExpRisco_docAval())
                .replace("infoExpRisco_medProtecao", s2240.getInfoExpRisco_medProtecao())
                .replace("infoExpRisco_condFuncto", s2240.getInfoExpRisco_condFuncto())
                .replace("infoExpRisco_usoInint", s2240.getInfoExpRisco_usoInint())
                .replace("infoExpRisco_przValid", s2240.getInfoExpRisco_przValid())
                .replace("infoExpRisco_periodicTroca", s2240.getInfoExpRisco_periodicTroca())
                .replace("infoExpRisco_higienizacao", s2240.getInfoExpRisco_higienizacao())
                .replace("infoExpRisco_cpfResp", s2240.getInfoExpRisco_cpfResp())
                .replace("infoExpRisco_ideOC", Integer.toString(s2240.getInfoExpRisco_ideOC()))
                .replace("infoExpRisco_nrOC", s2240.getInfoExpRisco_nrOC())
                .replace("infoExpRisco_ufOC", s2240.getInfoExpRisco_ufOC());
        String retorno = xml;

        try {
            return retorno;
        } catch (Exception ex) {
            return "";
        }
    }

    public List<S2250> getS2250(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            S2250Dao S2250dao = new S2250Dao();
            return S2250dao.get(codFil, compet, ambiente, true, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2250XML(String xmlPadrao, S2250 S2250) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtAvPrevio_Id", "ID1" + S2250.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_tpAmb", S2250.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", S2250.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", S2250.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", S2250.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", S2250.getIdeEmpregador_nrInsc())
                    .replace("ideVinculo_cpfTrab", S2250.getIdeVinculo_cpfTrab())
                    .replace("ideVinculo_nisTrab", S2250.getIdeVinculo_nisTrab())
                    .replace("ideVinculo_matricula", S2250.getIdeVinculo_matricula())
                    .replace("ideVinculo_matricula", S2250.getIdeVinculo_matricula())
                    .replace("detAvPrevio_dtAvPrevio", S2250.getDetAvPrevio_dtAvPrevio())
                    .replace("detAvPrevio_dtPrevDeslig", S2250.getDetAvPrevio_dtPrevDeslig())
                    .replace("detAvPrevio_tpAvPrevio", S2250.getDetAvPrevio_tpAvPrevio())
                    .replace("detAvPrevio_observacao", S2250.getDetAvPrevio_observacao())
                    .replace("ideEvento_indRetif", "1")
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "");
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2300> getS2300(String codFil, String compet, String ambiente, String cadIni, String tipo, Persistencia persistencia) throws Exception {
        try {
            S2300Dao S2300dao = new S2300Dao();
            return S2300dao.get(codFil, compet, ambiente, cadIni, tipo, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2300XML(String xmlPadrao, S2300 S2300) throws Exception {
        try {
            String CTPS, CNH, FGTS, InfoEstagiario;
            String xml = xmlPadrao
                    .replace("evtTSVInicio_Id", "ID1" + S2300.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_indRetif", S2300.getIdeEvento_indRetif())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>",
                            S2300.getIdeEvento_indRetif().equals("1") ? "" : "<nrRecibo>" + S2300.getIdeEvento_nrRecibo() + "</nrRecibo>")
                    .replace("ideEvento_tpAmb", S2300.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", S2300.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", S2300.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", S2300.getIdeEmpregador_tpInsc().equals("J") ? "1" : "2")
                    .replace("ideEmpregador_nrInsc", S2300.getIdeEmpregador_nrInsc().substring(0, 8))
                    .replace("trabalhador_cpfTrab", S2300.getTrabalhador_cpfTrab())
                    .replace("trabalhador_nisTrab", S2300.getTrabalhador_nisTrab())
                    .replace("trabalhador_nmTrab", FuncoesString.RecortaString(S2300.getTrabalhador_nmTrab(), 0, 70))
                    .replace("trabalhador_sexo", S2300.getTrabalhador_sexo())
                    .replace("trabalhador_racaCor", ((S2300.getTrabalhador_racaCor().equals("2") ? "1"
                            : (S2300.getTrabalhador_racaCor().equals("4") ? "2"
                            : (S2300.getTrabalhador_racaCor().equals("8") ? "3"
                            : (S2300.getTrabalhador_racaCor().equals("6") ? "4"
                            : (S2300.getTrabalhador_racaCor().equals("0") ? "5" : "")))))))
                    .replace("trabalhador_estCiv", ((S2300.getTrabalhador_estCiv().equals("S") ? "1"
                            : (S2300.getTrabalhador_estCiv().equals("C") ? "2"
                            : (S2300.getTrabalhador_estCiv().equals("Q") ? "4"
                            : (S2300.getTrabalhador_estCiv().equals("D") ? "3"
                            : (S2300.getTrabalhador_estCiv().equals("V") ? "5"
                            : (S2300.getTrabalhador_estCiv().equals("M") ? "1" : ""))))))))
                    .replace("trabalhador_grauInstr",
                            (S2300.getTrabalhador_grauInstr().equals("10") ? "01"
                            : (S2300.getTrabalhador_grauInstr().equals("20") ? "02"
                            : (S2300.getTrabalhador_grauInstr().equals("25") ? "03"
                            : (S2300.getTrabalhador_grauInstr().equals("30") ? "04"
                            : (S2300.getTrabalhador_grauInstr().equals("35") ? "05"
                            : (S2300.getTrabalhador_grauInstr().equals("40") ? "06"
                            : (S2300.getTrabalhador_grauInstr().equals("45") ? "07"
                            : (S2300.getTrabalhador_grauInstr().equals("50") ? "08"
                            : (S2300.getTrabalhador_grauInstr().equals("55") ? "09"
                            : (S2300.getTrabalhador_grauInstr().equals("65") ? "11"
                            : (S2300.getTrabalhador_grauInstr().equals("75") ? "12" : ""))))))))))))
                    .replace("<nmSoc>trabalhador_nmSoc</nmSoc>", "")
                    .replace("nascimento_dtNascto", S2300.getNascimento_dtNascto())
                    .replace("nascimento_codMunic", S2300.getNascimento_codMunic().replace(".0", ""))
                    .replace("nascimento_uf", S2300.getNascimento_uf())
                    .replace("nascimento_paisNascto", S2300.getNascimento_paisNascto())
                    .replace("nascimento_paisNac", S2300.getNascimento_paisNac())
                    .replace("nascimento_nmMae", S2300.getNascimento_nmMae())
                    .replace("nascimento_nmPai", S2300.getNascimento_nmPai())
                    .replace("infoTSVInicio_cadIni", S2300.getInfoTSVInicio_cadIni())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>",
                            S2300.getIdeEvento_nrRecibo().equals("") ? "" : "<nrRecibo>" + S2300.getIdeEvento_nrRecibo() + "</nrRecibo>");

            if (S2300.getCTPS_nrCtps().length() > 0) {
                CTPS = "<CTPS>\n"
                        + " <nrCtps>" + S2300.getCTPS_nrCtps() + "</nrCtps>\n"
                        + " <serieCtps>" + S2300.getCTPS_serieCtps() + "</serieCtps>\n"
                        + " <ufCtps>" + S2300.getCTPS_ufCtps() + "</ufCtps>\n"
                        + "</CTPS>";
            } else {
                CTPS = "";
            }

            if (S2300.getCNH_nrRegCnh().length() > 0) {
                CNH = " <CNH>\n"
                        + " <nrRegCnh>" + S2300.getCNH_nrRegCnh() + "</nrRegCnh>\n"
                        //                        + " <dtExped>" + S2300.getCNH_dtExped() + "</dtExped>\n"
                        + " <ufCnh>" + S2300.getCNH_ufCnh() + "</ufCnh>\n"
                        + " <dtValid>" + S2300.getCNH_dtValid() + "</dtValid>\n"
                        + " <categoriaCnh>" + S2300.getCNH_categoriaCnh() + "</categoriaCnh>"
                        + "</CNH >";
            } else {
                CNH = "";
            }

            //if ((!S2300.getInfoTSVInicio_codCateg().equals("723")) && (!S2300.getInfoTSVInicio_codCateg().equals("701"))) {
            if (S2300.getInfoTSVInicio_codCateg().equals("721")) {
                FGTS = "<fgts> "
                        //+ "<opcFGTS>" + S2300.getFgts_opcFGTS() + "</opcFGTS>"
                        + "<dtOpcFGTS>" + S2300.getFgts_dtOpcFGTS() + "</dtOpcFGTS>"
                        + "</fgts>";
            } else {
                FGTS = "";
            }
            if (S2300.getInfoTSVInicio_codCateg().equals("901")) {
                InfoEstagiario = " <infoEstagiario>\n"
                        + "                <natEstagio>" + S2300.getInfoEstagiario_natEstagio() + "</natEstagio>\n"
                        + "                    <nivEstagio>" + S2300.getInfoEstagiario_nivEstagio().replace(".0", "") + "</nivEstagio>\n"
                        //                                                + "                    <areaAtuacao>infoEstagiario_areaAtuacao</areaAtuacao>\n"
                        //                                                + "                    <nrApol>infoEstagiario_nrApol</nrApol>\n"
                        //+ "                    <vlrBolsa>" + S2300.getInfoEstagiario_vlrBolsa() + "</vlrBolsa>\n"
                        + "                    <dtPrevTerm>" + S2300.getInfoEstagiario_dtPrevTerm() + "</dtPrevTerm>\n"
                        + "                    <instEnsino>\n"
                        + "                        <cnpjInstEnsino>" + S2300.getInstEnsino_cnpjInstEnsino() + "</cnpjInstEnsino>\n"
                        + "                        <nmRazao>" + S2300.getInstEnsino_nmRazao() + "</nmRazao>\n"
                        + "                        <dscLograd>" + S2300.getInstEnsino_dscLograd() + "</dscLograd>\n"
                        //                        + "                        <nrLograd>instEnsino_nrLograd</nrLograd>\n"
                        + "                        <bairro>" + S2300.getInstEnsino_bairro() + "</bairro>\n"
                        + "                        <cep>" + S2300.getInstEnsino_cep() + "</cep>\n"
                        + "                        <codMunic>" + S2300.getInstEnsino_codMunic().replace(".0", "") + "</codMunic>\n"
                        + "                        <uf>" + S2300.getInstEnsino_uf() + "</uf>\n"
                        + "                    </instEnsino>\n"
                        + "                    <ageIntegracao>\n"
                        + "                        <cnpjAgntInteg>" + S2300.getAgeIntegracao_cnpjAgntInteg() + "</cnpjAgntInteg>\n"
                        //+ "                        <nmRazao>" + S2300.getAgeIntegracao_nmRazao() + "</nmRazao>\n"
                        //+ "                        <dscLograd>" + S2300.getAgeIntegracao_dscLograd() + "</dscLograd>\n"
                        // + "                        <nrLograd>" + S2300.getAgeIntegracao_nrLograd().replace(".0","") + "</nrLograd>\n"
                        // + "                        <nrLograd>0</nrLograd>\n"
                        // + "                        <bairro>" + S2300.getAgeIntegracao_bairro() + "</bairro>\n"
                        // + "                        <cep>" + S2300.getAgeIntegracao_cep() + "</cep>\n"
                        // + "                        <codMunic>" + S2300.getAgeIntegracao_codMunic().replace(".0", "") + "</codMunic>\n"
                        // + "                        <uf>" + S2300.getAgeIntegracao_uf() + "</uf>\n"
                        + "                    </ageIntegracao>\n"
                        + "                    <supervisorEstagio>\n"
                        + "                        <cpfSupervisor>" + S2300.getSupervisorEstagio_cpfSupervisor() + "</cpfSupervisor>\n"
                        // + "                        <nmSuperv>" + S2300.getSupervisorEstagio_nmSuperv() + "</nmSuperv>\n"
                        + "                    </supervisorEstagio>\n"
                        + "                </infoEstagiario>\n";

            } else {
                InfoEstagiario = "";
            }
            xml = xml
                    .replace("opcaoFgts", FGTS)
                    .replace("documentos_CTPS", CTPS)
                    .replace("<RIC>", "")
                    .replace("<nrRic>RIC_nrRic</nrRic>", "")
                    .replace("<orgaoEmissor>RIC_orgaoEmissor</orgaoEmissor>", "")
                    .replace("<dtExped>RIC_dtExped</dtExped>", "")
                    .replace("</RIC>", "")
                    .replace("RG_nrRg", S2300.getRG_nrRg())
                    .replace("RG_orgaoEmissor", S2300.getRG_orgaoEmissor())
                    .replace("RG_dtExped", S2300.getRG_dtExped())
                    .replace("<RNE>", "")
                    .replace("<nrRne>RNE_nrRne</nrRne>", "")
                    .replace("<orgaoEmissor>RNE_orgaoEmissor</orgaoEmissor>", "")
                    .replace("<dtExped>RNE_dtExped</dtExped>", "")
                    .replace("</RNE>", "")
                    .replace("<OC>", "")
                    .replace("<nrOc>OC_nrOc</nrOc>", "")
                    .replace("<orgaoEmissor>OC_orgaoEmissor</orgaoEmissor>", "")
                    .replace("<dtExped>OC_dtExped</dtExped>", "")
                    .replace("<dtValid>OC_dtValid</dtValid>", "")
                    .replace("</OC>", "")
                    .replace("documentos_CNH", CNH)
                    .replace("brasil_tpLograd", LogradouroESocial.getTipo(S2300.getBrasil_dscLograd()))
                    .replace("brasil_dscLograd", S2300.getBrasil_dscLograd())
                    .replace("brasil_nrLograd", S2300.getBrasil_nrLograd())
                    .replace("<complemento>brasil_complemento</complemento>", "")
                    .replace("brasil_bairro", S2300.getBrasil_bairro())
                    .replace("brasil_cep", S2300.getBrasil_cep())
                    .replace("brasil_codMunic", S2300.getBrasil_codMunic().replace(".0", ""))
                    .replace("brasil_uf", S2300.getBrasil_uf())
                    //                <exterior>
                    //                    .replace("exterior_paisResid", S2300.getexterior_paisResid())
                    //                    .replace("exterior_dscLograd", S2300.getexterior_dscLograd())
                    //                    .replace("exterior_nrLograd", S2300.getexterior_nrLograd())
                    //                    .replace("exterior_complemento", S2300.getexterior_complemento())
                    //                    .replace("exterior_bairro", S2300.getexterior_bairro())
                    //                    .replace("exterior_nmCid", S2300.getexterior_nmCid())
                    //                    .replace("exterior_codPostal", S2300.getexterior_codPostal())
                    //                </exterior>

                    //            <trabEstrangeiro>
                    //                .replace("trabEstrangeiro_dtChegada", S2300.gettrabEstrangeiro_dtChegada())
                    //                .replace("trabEstrangeiro_classTrabEstrang", S2300.gettrabEstrangeiro_classTrabEstrang())
                    //                .replace("trabEstrangeiro_casadoBr", S2300.gettrabEstrangeiro_casadoBr())
                    //                .replace("trabEstrangeiro_filhosBr", S2300.gettrabEstrangeiro_filhosBr())
                    //            </trabEstrangeiro>

                    //                    .replace("infoDeficiencia_defFisica", S2300.getInfoDeficiencia_defFisica())
                    //                    .replace("infoDeficiencia_defVisual", S2300.getInfoDeficiencia_defVisual())
                    //                    .replace("infoDeficiencia_defAuditiva", S2300.getInfoDeficiencia_defAuditiva())
                    //                    .replace("infoDeficiencia_defMental", S2300.getInfoDeficiencia_defMental())
                    //                    .replace("infoDeficiencia_defIntelectual", S2300.getInfoDeficiencia_defIntelectual())
                    //                    .replace("infoDeficiencia_reabReadap", S2300.getInfoDeficiencia_reabReadap())
                    //                    .replace("infoDeficiencia_observacao", S2300.getInfoDeficiencia_observacao())
                    //                    .replace("dependente_tpDep", S2300.getDependente_tpDep())
                    //                    .replace("dependente_nmDep", S2300.getDependente_nmDep())
                    //                    .replace("dependente_dtNascto", S2300.getDependente_dtNascto())
                    //                    .replace("dependente_cpfDep", S2300.getDependente_cpfDep())
                    //                    .replace("dependente_depIRRF", S2300.getDependente_depIRRF())
                    //                    .replace("dependente_depSF", S2300.getDependente_depSF())
                    //                    .replace("dependente_incTrab", S2300.getDependente_incTrab())
                    .replace("<fonePrinc>contato_fonePrinc</fonePrinc>", S2300.getContato_fonePrinc().equals("")
                            ? "" : "<fonePrinc>" + S2300.getContato_fonePrinc() + "</fonePrinc>")
                    .replace("<foneAlternat>contato_foneAlternat</foneAlternat>", S2300.getContato_foneAlternat().equals("")
                            ? "" : "<foneAlternat>" + S2300.getContato_foneAlternat() + "</foneAlternat>")
                    .replace("<emailPrinc>contato_emailPrinc</emailPrinc>", S2300.getContato_emailPrinc().equals("")
                            ? "" : "<emailPrinc>" + S2300.getContato_emailPrinc() + "</emailPrinc>")
                    .replace("<emailAlternat>contato_emailAlternat</emailAlternat>", "")
                    //                    .replace("infoTSVInicio_cadIni", S2300.getInfoTSVInicio_cadIni())
                    .replace("infoTSVInicio_codCateg", S2300.getInfoTSVInicio_codCateg())
                    .replace("infoTSVInicio_dtInicio", S2300.getInfoTSVInicio_dtInicio())
                    .replace("<natAtividade>infoTSVInicio_natAtividade</natAtividade>",
                            ((S2300.getInfoTSVInicio_codCateg().equals(""))
                            || (S2300.getInfoTSVInicio_codCateg().equals("701"))
                            || (S2300.getInfoTSVInicio_codCateg().equals("721"))
                            || (S2300.getInfoTSVInicio_codCateg().equals("722"))
                            || (S2300.getInfoTSVInicio_codCateg().equals("723"))
                            || (S2300.getInfoTSVInicio_codCateg().equals("901"))) ? "" : "<natAtividade>" + S2300.getInfoTSVInicio_natAtividade().replace(".0", "") + "</natAtividade>")
                    //                    .replace("infoTSVInicio_natAtividade", S2300.getInfoTSVInicio_natAtividade())
                    .replace("cargoFuncao_codCargo", S2300.getCargoFuncao_codCargo())
                    //                    .replace("cargoFuncao_codFuncao", S2300.getCargoFuncao_codFuncao())
                    .replace("remuneracao_vrSalFx", S2300.getRemuneracao_vrSalFx())
                    .replace("remuneracao_undSalFixo", S2300.getRemuneracao_undSalFixo().equals("H") ? "1"
                            : S2300.getRemuneracao_undSalFixo().equals("D") ? "2"
                            : S2300.getRemuneracao_undSalFixo().equals("M") ? "5" : "")
                    //                    .replace("remuneracao_dscSalVar", S2300.getRemuneracao_dscSalVar())
                    //                    .replace("infoDirigenteSindical_categOrig", S2300.getInfoDirigenteSindical_categOrig())
                    //                    .replace("infoDirigenteSindical_cnpjOrigem", S2300.getInfoDirigenteSindical_cnpjOrigem())
                    //                    .replace("infoDirigenteSindical_dtAdmOrig", S2300.getInfoDirigenteSindical_dtAdmOrig())
                    //                    .replace("infoDirigenteSindical_matricOrig", S2300.getInfoDirigenteSindical_matricOrig())
                    //                    .replace("infoTrabCedido_categOrig", S2300.getInfoTrabCedido_categOrig())
                    //                    .replace("infoTrabCedido_cnpjCednt", S2300.getInfoTrabCedido_cnpjCednt())
                    //                    .replace("infoTrabCedido_matricCed", S2300.getInfoTrabCedido_matricCed())
                    //                    .replace("infoTrabCedido_dtAdmCed", S2300.getInfoTrabCedido_dtAdmCed())
                    //                    .replace("infoTrabCedido_tpRegTrab", S2300.getInfoTrabCedido_tpRegTrab())
                    //                    .replace("infoTrabCedido_tpRegPrev", S2300.getInfoTrabCedido_tpRegPrev())
                    //                    .replace("infoTrabCedido_infOnus", S2300.getInfoTrabCedido_infOnus())
                    //                    .replace("infoEstagiario_natEstagio", S2300.getInfoEstagiario_natEstagio())
                    //                    .replace("infoEstagiario_nivEstagio", S2300.getInfoEstagiario_nivEstagio())
                    //                    .replace("infoEstagiario_areaAtuacao", S2300.getInfoEstagiario_areaAtuacao())
                    //                    .replace("infoEstagiario_nrApol", S2300.getInfoEstagiario_nrApol())
                    //                    .replace("infoEstagiario_vlrBolsa", S2300.getInfoEstagiario_vlrBolsa())
                    //                    .replace("infoEstagiario_dtPrevTerm", S2300.getInfoEstagiario_dtPrevTerm())
                    //                    .replace("instEnsino_cnpjInstEnsino", S2300.getInstEnsino_cnpjInstEnsino())
                    //                    .replace("instEnsino_nmRazao", S2300.getInstEnsino_nmRazao())
                    //                    .replace("instEnsino_dscLograd", S2300.getInstEnsino_dscLograd())
                    //                    .replace("instEnsino_nrLograd", S2300.getInstEnsino_nrLograd())
                    //                    .replace("instEnsino_bairro", S2300.getInstEnsino_bairro())
                    //                    .replace("instEnsino_cep", S2300.getInstEnsino_cep())
                    //                    .replace("instEnsino_codMunic", S2300.getInstEnsino_codMunic())
                    //                    .replace("instEnsino_uf", S2300.getInstEnsino_uf())
                    //                    .replace("ageIntegracao_cnpjAgntInteg", S2300.getAgeIntegracao_cnpjAgntInteg())
                    //                    .replace("ageIntegracao_nmRazao", S2300.getAgeIntegracao_nmRazao())
                    //                    .replace("ageIntegracao_dscLograd", S2300.getAgeIntegracao_dscLograd())
                    //                    .replace("ageIntegracao_nrLograd", S2300.getAgeIntegracao_nrLograd())
                    //                    .replace("ageIntegracao_bairro", S2300.getAgeIntegracao_bairro())
                    //                    .replace("ageIntegracao_cep", S2300.getAgeIntegracao_cep())
                    //                    .replace("ageIntegracao_codMunic", S2300.getAgeIntegracao_codMunic())
                    //                    .replace("ageIntegracao_uf", S2300.getAgeIntegracao_uf())
                    //                    .replace("supervisorEstagio_cpfSupervisor", S2300.getSupervisorEstagio_cpfSupervisor())
                    //                    .replace("supervisorEstagio_nmSuperv", S2300.getSupervisorEstagio_nmSuperv())
                    .replace("afastamento_dtIniAfast", S2300.getAfastamento_dtIniAfast())
                    .replace("afastamento_codMotAfast", S2300.getAfastamento_codMotAfast())
                    .replace("termino_dtTerm", S2300.getTermino_dtTerm());
            //        .replace("infoEstagiario", InfoEstagiario);
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2306> getS2306(String codFil, String compet, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2306Dao s2306dao = new S2306Dao();
            List<S2306> listaS2306 = s2306dao.get(codFil, compet, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2306 s2306 : listaS2306) {

                idPadrao = s2306.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }

                s2306.setEvtTSVAltContr_Id("ID1" + id2);
            }
            return listaS2306;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2306XML(String xmlPadrao, S2306 s2306) throws Exception {
        String xml = xmlPadrao;
        boolean existe;

        xml = xml.replace("evtTSVAltContr_Id", s2306.getEvtTSVAltContr_Id());

        xml = xml.replace("ideEvento_indRetif", s2306.getIdeEvento_indRetif())
                .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>\n", "")
                .replace("ideEvento_tpAmb", s2306.getIdeEvento_tpAmb())
                .replace("ideEvento_procEmi", s2306.getIdeEvento_procEmi())
                .replace("ideEvento_verProc", s2306.getIdeEvento_verProc());

        xml = xml.replace("ideEmpregador_tpInsc", s2306.getIdeEmpregador_tpInsc())
                .replace("ideEmpregador_nrInsc", s2306.getIdeEmpregador_nrInsc());

        xml = xml.replace("ideTrabSemVinculo_cpfTrab", s2306.getIdeTrabSemVinculo_cpfTrab())
                .replace("ideTrabSemVinculo_nisTrab", s2306.getIdeTrabSemVinculo_nisTrab())
                .replace("ideTrabSemVinculo_codCateg", s2306.getIdeTrabSemVinculo_codCateg());

        xml = xml.replace("infoTSVAlteracao_dtAlteracao", s2306.getInfoTSVAlteracao_dtAlteracao())
                .replace("infoTSVAlteracao_natAtividade", s2306.getInfoTSVAlteracao_natAtividade());

        xml = xml.replace("cargoFuncao_codCargo", s2306.getCargoFuncao_codCargo());
        xml = xml.replace("<codFuncao>cargoFuncao_codFuncao</codFuncao> ", "");

        xml = xml.replace("remuneracao_vrSalFx", s2306.getRemuneracao_vrSalFx())
                .replace("remuneracao_undSalFixo", s2306.getRemuneracao_undSalFixo().equals("H") ? "1"
                        : (s2306.getRemuneracao_undSalFixo().equals("D") ? "2"
                        : (s2306.getRemuneracao_undSalFixo().equals("M") ? "5" : "")))
                .replace("<dscSalVar>remuneracao_dscSalVar</dscSalVar>\n",
                        s2306.getRemuneracao_undSalFixo().equals("6")
                        || s2306.getRemuneracao_undSalFixo().equals("7")
                        ? "<dscSalVar>" + s2306.getRemuneracao_dscSalVar() + "</dscSalVar>\n"
                        : "");

        StringBuilder aux;
        existe = s2306.getIdeTrabSemVinculo_codCateg().equals("901");

        if (existe) {
            xml = xml.replace("infoEstagiario_natEstagio", s2306.getInfoEstagiario_natEstagio())
                    .replace("infoEstagiario_nivEstagio", s2306.getInfoEstagiario_nivEstagio())
                    .replace("<areaAtuacao>infoEstagiario_areaAtuacao</areaAtuacao>", "")
                    .replace("<nrApol>infoEstagiario_nrApol</nrApol>", "")
                    .replace("infoEstagiario_vlrBolsa", s2306.getInfoEstagiario_vlrBolsa())
                    .replace("infoEstagiario_dtPrevTerm", s2306.getInfoEstagiario_dtPrevTerm())
                    .replace("instEnsino_cnpjInstEnsino", s2306.getInstEnsino_cnpjInstEnsino())
                    .replace("instEnsino_nmRazao", s2306.getInstEnsino_nmRazao())
                    .replace("instEnsino_dscLograd", s2306.getInstEnsino_dscLograd())
                    .replace("<nrLograd>instEnsino_nrLograd</nrLograd>", "")
                    .replace("instEnsino_bairro", s2306.getInstEnsino_bairro())
                    .replace("instEnsino_cep", s2306.getInstEnsino_cep())
                    .replace("instEnsino_codMunic", s2306.getInstEnsino_codMunic().replace(".0", ""))
                    .replace("instEnsino_uf", s2306.getInstEnsino_uf())
                    .replace("ageIntegracao_cnpjAgntInteg", s2306.getAgeIntegracao_cnpjAgntInteg())
                    .replace("ageIntegracao_nmRazao", s2306.getAgeIntegracao_nmRazao())
                    .replace("ageIntegracao_dscLograd", s2306.getAgeIntegracao_dscLograd())
                    .replace("ageIntegracao_nrLograd", s2306.getAgeIntegracao_nrLograd())
                    .replace("ageIntegracao_bairro", s2306.getAgeIntegracao_bairro())
                    .replace("ageIntegracao_cep", s2306.getAgeIntegracao_cep())
                    .replace("ageIntegracao_codMunic", s2306.getAgeIntegracao_codMunic().replace(".0", ""))
                    .replace("ageIntegracao_uf", s2306.getAgeIntegracao_uf())
                    .replace("supervisorEstagio_cpfSupervisor", s2306.getSupervisorEstagio_cpfSupervisor())
                    .replace("supervisorEstagio_nmSuperv", s2306.getSupervisorEstagio_nmSuperv());

        } else {
            xml = xml.replace(
                    "                <infoEstagiario>\n"
                    + "                    <natEstagio>infoEstagiario_natEstagio</natEstagio>\n"
                    + "                    <nivEstagio>infoEstagiario_nivEstagio</nivEstagio>\n"
                    + "                    <areaAtuacao>infoEstagiario_areaAtuacao</areaAtuacao>                        \n"
                    + "                    <nrApol>infoEstagiario_nrApol</nrApol>\n"
                    + "                    <vlrBolsa>infoEstagiario_vlrBolsa</vlrBolsa>\n"
                    + "                    <dtPrevTerm>infoEstagiario_dtPrevTerm</dtPrevTerm>\n"
                    + "                    <instEnsino>\n"
                    + "                        <cnpjInstEnsino>instEnsino_cnpjInstEnsino</cnpjInstEnsino>\n"
                    + "                        <nmRazao>instEnsino_nmRazao</nmRazao>\n"
                    + "                        <dscLograd>instEnsino_dscLograd</dscLograd>\n"
                    + "                        <nrLograd>instEnsino_nrLograd</nrLograd>                            \n"
                    + "                        <bairro>instEnsino_bairro</bairro>\n"
                    + "                        <cep>instEnsino_cep</cep>\n"
                    + "                        <codMunic>instEnsino_codMunic</codMunic>\n"
                    + "                        <uf>instEnsino_uf</uf>\n"
                    + "                    </instEnsino>\n"
                    + "                    <ageIntegracao>\n"
                    + "                        <cnpjAgntInteg>ageIntegracao_cnpjAgntInteg</cnpjAgntInteg>\n"
                    + "                        <nmRazao>ageIntegracao_nmRazao</nmRazao>\n"
                    + "                        <dscLograd>ageIntegracao_dscLograd</dscLograd>                           \n"
                    + "                        <nrLograd>ageIntegracao_nrLograd</nrLograd>                           \n"
                    + "                        <bairro>ageIntegracao_bairro</bairro>                            \n"
                    + "                        <cep>ageIntegracao_cep</cep>\n"
                    + "                        <codMunic>ageIntegracao_codMunic</codMunic>\n"
                    + "                        <uf>ageIntegracao_uf</uf>\n"
                    + "                    </ageIntegracao>\n"
                    + "                    <supervisorEstagio>\n"
                    + "                        <cpfSupervisor>supervisorEstagio_cpfSupervisor</cpfSupervisor>\n"
                    + "                        <nmSuperv>supervisorEstagio_nmSuperv</nmSuperv>\n"
                    + "                    </supervisorEstagio>\n"
                    + "                </infoEstagiario>", "");
        }

        return xml;
    }

    public List<S2298> getS2298(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2298Dao S2298dao = new S2298Dao();
            List<S2298> listaS2298 = S2298dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2298 S2298 : listaS2298) {
                idPadrao = S2298.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                S2298.setEvtReintegr_Id("ID1" + id2);
                S2298.setIdeEvento_tpAmb(tipoAmbiente);
                S2298.setIdeVinculo_matricula(S2298.getIdeVinculo_matricula().replace(".0", ""));
            }
            return listaS2298;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2298XML(String xmlPadrao, S2298 S2298) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtReintegr_Id", "ID" + S2298.getIdeEmpregador_tpInsc() + S2298.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_indRetif", S2298.getIdeEvento_indRetif())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "")
                    .replace("ideEvento_tpAmb", S2298.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", S2298.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", S2298.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", S2298.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", S2298.getIdeEmpregador_nrInsc())
                    .replace("ideVinculo_cpfTrab", S2298.getIdeVinculo_cpfTrab())
                    .replace("ideVinculo_nisTrab", S2298.getIdeVinculo_nisTrab())
                    .replace("ideVinculo_matricula", S2298.getIdeVinculo_matricula())
                    .replace("infoReintegr_tpReint", S2298.getInfoReintegr_tpReint())
                    .replace("infoReintegr_dtEfetRetorno", S2298.getInfoReintegr_dtEfetRetorno())
                    .replace("infoReintegr_dtEfeito", S2298.getInfoReintegr_dtEfeito())
                    .replace("infoReintegr_indPagtoJuizo", S2298.getInfoReintegr_indPagtoJuizo());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2299> getS2299(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2299Dao s2299dao = new S2299Dao();
            List<S2299> listaS2299 = s2299dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2299 s2299 : listaS2299) {
                idPadrao = s2299.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2299.setEvtDeslig_Id("ID1" + id2);
                s2299.setIdeEvento_tpAmb(tipoAmbiente);
                s2299.setIdeVinculo_matricula(s2299.getIdeVinculo_matricula().replace(".0", ""));
                s2299.setInfoDeslig_dtDeslig(s2299.getInfoDeslig_dtDeslig().split(" ")[0]);
            }
            return listaS2299;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2299XML(String xmlPadrao, S2299 s2299) throws Exception {
        try {
            String xml = "", xmlDetPlano = "", xmlDetOper = "", xmlInfoSaudeColet, xmlDetVerbas = "";

            boolean envVerbas = true;

            //Empresas 2º Grupo
            if (s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("10602561")
                    || //Loyal
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("13396501")
                    || //Loyal
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("17498887")
                    || //Loyal
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("49520653")
                    || //Loyal
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("63006084")
                    || //Loyal
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("03159145")
                    || //Servite                                       
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("02103266")
                    || //TransExcel
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("06291321")
                    || //TransExcel
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("09494448")
                    || //TransExcel
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("06145774")
                    || //InvioSeg
                    s2299.getIdeEmpregador_nrInsc().substring(0, 8).equals("06267270")) {  //InvioSeg                 

                if ((s2299.getIdeEvento_compet().replace(".0", "").equals("1801"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1802"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1803"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1804"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1805"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1806"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1807"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1808"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1809"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1810"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1811"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1812"))) {
                    envVerbas = false;
                }
            } else { //Empresas 1º Grupo
                if ((s2299.getIdeEvento_compet().replace(".0", "").equals("1801"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1802"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1803"))
                        || (s2299.getIdeEvento_compet().replace(".0", "").equals("1804"))) {
                    envVerbas = false;
                }
            }
            if (envVerbas) {
                for (S2299.DetVerbas detVerbas : s2299.getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas()) {
                    String xmlDescFolha = "";
                    S2299.DescFolha descFolha =  detVerbas.getDescFolha();
                    if (descFolha != null) {
                        xmlDescFolha = "<descFolha>\n" 
                                + "  <tpDesc>1</tpDesc>\n" 
                                + "  <instFinanc>" + descFolha.getDescFolha_instFinanc()+ "</instFinanc>\n" 
                                + "  <nrDoc>" + descFolha.getDescFolha_nrDoc()+ "</nrDoc>\n" 
                                + "  <observacao>" + descFolha.getDescFolha_observacao() + "</observacao>\n" 
                                +"</descFolha>\n";
                    }
                    //if(detVerbas.getDetVerbas_codRubr().contains("9219")) natRubr9219 = true;                                                                   
                    xmlDetVerbas += "\n"
                            + "                            <detVerbas>\n"
                            + "                                <codRubr>" + detVerbas.getDetVerbas_codRubr() + "</codRubr>\n"
                            + "                                <ideTabRubr>" + detVerbas.getDetVerbas_ideTabRubr() + "</ideTabRubr>\n"
                            + //"                                <qtdRubr>"+detVerbas.getDetVerbas_qtdRubr()+"</qtdRubr>\n" +
                            //"                                <vrUnit>"+detVerbas.getDetVerbas_vrUnit()+"</vrUnit>\n" +
                            "                                <vrRubr>" + Math.round(Double.parseDouble(detVerbas.getDetVerbas_vrRubr())*100.0)/100.0 + "</vrRubr>\n"
                            //"                                <vrRubr>" + detVerbas.getDetVerbas_vrRubr() + "</vrRubr>\n"
                            + "                                <indApurIR>0</indApurIR>\n"
                            + xmlDescFolha
                            + "                            </detVerbas>\n";
                }
            }
            //Tratamento quando não existir verbas.
            if (s2299.getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas().isEmpty() || (!envVerbas)) {
                xmlPadrao = xmlPadrao.replace("<verbasResc>", "")
                        .replace("<dmDev>", "")
                        .replace("<ideDmDev>dmDev_ideDmDev</ideDmDev>", "")
                        .replace("<infoPerApur>", "")
                        .replace("<ideEstabLot>", "")
                        .replace("<tpInsc>ideEstabLot_tpInsc</tpInsc>", "")
                        .replace("<nrInsc>ideEstabLot_nrInsc</nrInsc>", "")
                        .replace("<codLotacao>ideEstabLot_codLotacao</codLotacao>", "")
                        .replace("detVerbas", "")
                        .replace("infoSaudeColet", "")
                        .replace("<infoAgNocivo>", "")
                        .replace("<grauExp>infoAgNocivo_grauExp</grauExp>", "")
                        .replace("</infoAgNocivo>", "")
                        .replace("</ideEstabLot>", "")
                        .replace("</infoPerApur>", "")
                        .replace("</dmDev>", "")
                        .replace("</verbasResc>", "")
                        .replace("detVerbas", "");
            }

            for (S2299.DetOper detOper : s2299.getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper()) {
                xmlDetOper
                        += "                                <detOper>\n"
                        + "                                    <cnpjOper>" + detOper.getDetOper_cnpjOper() + "</cnpjOper>\n"
                        + "                                    <regANS>" + detOper.getDetOper_regANS() + "</regANS>\n"
                        + "                                    <vrPgTit>" + detOper.getDetOper_vrPgTit() + "</vrPgTit>\n"
                        + "                                    @detPlano\n"
                        + "                                </detOper>\n";
                xmlDetPlano = "";
                for (S2299.DetPlano detPlano : detOper.getDetOper_detPlano()) {
                    xmlDetPlano
                            += "                                    <detPlano>\n"
                            + "                                        <tpDep>" + detPlano.getDetPlano_tpDep() + "</tpDep>\n"
                            + "                                        <cpfDep>" + detPlano.getDetPlano_cpfDep() + "</cpfDep>\n"
                            + "                                        <nmDep>" + detPlano.getDetPlano_nmDep() + "</nmDep>\n"
                            + "                                        <dtNascto>" + detPlano.getDetPlano_dtNascto() + "</dtNascto>\n"
                            + "                                        <vlrPgDep>" + detPlano.getDetPlano_vlrPgDep() + "</vlrPgDep>\n"
                            + "                                    </detPlano>\n";
                }
                xmlDetOper = xmlDetOper.replace("@detPlano", xmlDetPlano);
            }

            xmlInfoSaudeColet = xmlDetOper.equals("") ? ""
                    : ("\n"
                    + "                            <infoSaudeColet>\n"
                    + xmlDetOper
                    + "                            </infoSaudeColet>\n");

            xml = xmlPadrao
                    .replace("evtDeslig_Id", s2299.getEvtDeslig_Id())
                    .replace("ideEvento_indRetif", s2299.getIdeEvento_indRetif())
                    .replace("ideEvento_tpAmb", s2299.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s2299.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s2299.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s2299.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s2299.getIdeEmpregador_nrInsc().substring(0, 8))
                    .replace("ideVinculo_cpfTrab", s2299.getIdeVinculo_cpfTrab())
                    .replace("ideVinculo_nisTrab", s2299.getIdeVinculo_nisTrab())
                    .replace("ideVinculo_matricula", s2299.getIdeVinculo_matricula())
                    .replace("infoDeslig_mtvDeslig", s2299.getInfoDeslig_mtvDeslig())
                    .replace("infoDeslig_dtDeslig", s2299.getInfoDeslig_dtDeslig())
                    .replace("detAvPrevio_dtAvPrevio", s2299.getdetAvPrevio_dtAvPrevio())
                    .replace("infoDeslig_indPagtoAPI", s2299.getInfoDeslig_indPagtoAPI());
            if (s2299.getInfoDeslig_indPagtoAPI().equals("S")) {
                xml = xml.replace("infoDeslig_dtProjFimAPI", s2299.getInfoDeslig_dtProjFimAPI().split(" ")[0]);
            } else {
                xml = xml.replace("<dtProjFimAPI>infoDeslig_dtProjFimAPI</dtProjFimAPI>", "");
            }

            xml = xml.replace("infoDeslig_pensAlim", s2299.getInfoDeslig_pensAlim());
            if (s2299.getInfoDeslig_pensAlim().equals("0") || s2299.getInfoDeslig_pensAlim().equals("2")) {
                xml = xml.replace("<percAliment>infoDeslig_percAliment</percAliment>", "");
            } else {
                xml = xml.replace("infoDeslig_percAliment", s2299.getInfoDeslig_percAliment());
            }

            if (s2299.getInfoDeslig_pensAlim().equals("0") || s2299.getInfoDeslig_pensAlim().equals("1")) {
                xml = xml.replace("<vrAlim>infoDeslig_vrAlim</vrAlim>", "");
            } else {
                xml = xml.replace("infoDeslig_vrAlim", s2299.getInfoDeslig_vrAlim());
            }

            if (s2299.getInfoDeslig_mtvDeslig().equals("9") || s2299.getInfoDeslig_mtvDeslig().equals("10")) {
                xml = xml.replace("infoDeslig_nrCertObito", s2299.getInfoDeslig_nrCertObito());
            } else {
                xml = xml.replace("<nrCertObito>infoDeslig_nrCertObito</nrCertObito>", "");
            }

            if (s2299.getIdeVinculo_trabIntermitente().equals("S")) {
                xml = xml.replace("infoDeslig_qtdDiasInterm", s2299.getInfoDeslig_qtdDiasInterm());
            } else {
                xml = xml.replace("<infoInterm>", "")
                        .replace("<dia>infoDeslig_qtdDiasInterm</dia>", "")
                        .replace("</infoInterm>", "");
            }
            if ((s2299.getInfoDeslig_mtvDeslig().equals("11"))
                    && (s2299.getSucessaoVinc_cnpjSucessora() != null)
                    && (!s2299.getSucessaoVinc_cnpjSucessora().equals(""))) {
                xml = xml.replace("sucessaoVinc_cnpjSucessora", s2299.getSucessaoVinc_cnpjSucessora())
                        .replace("<tpInsc>9</tpInsc>", "<tpInsc>1</tpInsc>");
            } else {
                xml = xml.replace("<sucessaoVinc>", "")
                        .replace("<tpInsc>9</tpInsc>", "")
                        .replace("<nrInsc>sucessaoVinc_cnpjSucessora</nrInsc>", "")
                        .replace("</sucessaoVinc>", "");
            }

            xml = xml.replace("infoDeslig_indCumprParc", s2299.getInfoDeslig_indCumprParc())
                    .replace("dmDev_ideDmDev", s2299.getDmDev_ideDmDev())
                    .replace("ideEstabLot_tpInsc", s2299.getIdeEstabLot_tpInsc())
                    .replace("ideEstabLot_nrInsc", s2299.getIdeEstabLot_nrInsc())
                    .replace("ideEstabLot_codLotacao", s2299.getIdeEstabLot_codLotacao().replace(".0", ""))
                    .replace("infoAgNocivo_grauExp", "1")
                    .replace("infoSimples_indSimples", "2")
                    .replace("detVerbas", xmlDetVerbas)
                    .replace("infoSaudeColet", xmlInfoSaudeColet);

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S2399> getS2399(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            S2399Dao s2399dao = new S2399Dao();
            List<S2399> listaS2399 = s2399dao.get(codFil, validade, tipoAmbiente, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S2399 s2399 : listaS2399) {
                idPadrao = s2399.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s2399.setEvtTSVTermino_Id("ID1" + id2);
                s2399.setIdeEvento_tpAmb(tipoAmbiente);
                s2399.setIdeTrabSemVinculo_matricula(s2399.getIdeTrabSemVinculo_matricula().replace(".0", ""));
                s2399.setInfoTSVTermino_dtTerm(s2399.getInfoTSVTermino_dtTerm().split(" ")[0]);
            }
            return listaS2399;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String S2399XML(String xmlPadrao, S2399 s2399) throws Exception {
        try {
            String xml = "", xmlDetPlano = "", xmlDetOper = "", xmlInfoSaudeColet, xmlDetVerbas = "";

            boolean envVerbas = true;

            //Empresas 2º Grupo
            if (s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("10602561")
                    || //Loyal
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("13396501")
                    || //Loyal
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("17498887")
                    || //Loyal
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("49520653")
                    || //Loyal
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("63006084")
                    || //Loyal
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("03159145")
                    || //Servite                                       
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("02103266")
                    || //TransExcel
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("06291321")
                    || //TransExcel
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("09494448")
                    || //TransExcel
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("06145774")
                    || //InvioSeg
                    s2399.getIdeEmpregador_nrInsc().substring(0, 8).equals("06267270")) {  //InvioSeg                 

                if ((s2399.getIdeEvento_compet().replace(".0", "").equals("1801"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1802"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1803"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1804"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1805"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1806"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1807"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1808"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1809"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1810"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1811"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1812"))) {
                    envVerbas = false;
                }
            } else { //Empresas 1º Grupo
                if ((s2399.getIdeEvento_compet().replace(".0", "").equals("1801"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1802"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1803"))
                        || (s2399.getIdeEvento_compet().replace(".0", "").equals("1804"))) {
                    envVerbas = false;
                }
            }
            if (envVerbas) {
                for (S2399.DetVerbas detVerbas : s2399.getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas()) {
                    //if(detVerbas.getDetVerbas_codRubr().contains("9219")) natRubr9219 = true;                                                                   
                    xmlDetVerbas += "\n"
                            + "                            <detVerbas>\n"
                            + "                                <codRubr>" + detVerbas.getDetVerbas_codRubr() + "</codRubr>\n"
                            + "                                <ideTabRubr>" + detVerbas.getDetVerbas_ideTabRubr() + "</ideTabRubr>\n"
                            + //"                                <qtdRubr>"+detVerbas.getDetVerbas_qtdRubr()+"</qtdRubr>\n" +
                            //"                                <vrUnit>"+detVerbas.getDetVerbas_vrUnit()+"</vrUnit>\n" +
                            "                                <vrRubr>" + detVerbas.getDetVerbas_vrRubr() + "</vrRubr>\n"
                            + "                                <indApurIR>0</indApurIR>\n"
                            + "                            </detVerbas>\n";
                }
            }
            //Tratamento quando não existir verbas.
            if (s2399.getInfoPerApur_ideEstabLot().getIdeEstabLot_detVerbas().isEmpty() || (!envVerbas)) {
                xmlPadrao = xmlPadrao.replace("<verbasResc>", "")
                        .replace("<dmDev>", "")
                        .replace("<ideDmDev>dmDev_ideDmDev</ideDmDev>", "")
                        .replace("<infoPerApur>", "")
                        .replace("<ideEstabLot>", "")
                        .replace("<tpInsc>ideEstabLot_tpInsc</tpInsc>", "")
                        .replace("<nrInsc>ideEstabLot_nrInsc</nrInsc>", "")
                        .replace("<codLotacao>ideEstabLot_codLotacao</codLotacao>", "")
                        .replace("detVerbas", "")
                        .replace("infoSaudeColet", "")
                        .replace("<infoSimples>", "")
                        .replace("<indSimples>infoSimples_indSimples</indSimples>", "")
                        .replace("</infoSimples>", "")
                        .replace("<infoAgNocivo>", "")
                        .replace("<grauExp>infoAgNocivo_grauExp</grauExp>", "")
                        .replace("</infoAgNocivo>", "")
                        .replace("</ideEstabLot>", "")
                        .replace("</infoPerApur>", "")
                        .replace("</dmDev>", "")
                        .replace("</verbasResc>", "")
                        .replace("detVerbas", "");
            }

            for (S2399.DetOper detOper : s2399.getInfoPerApur_ideEstabLot().getInfoSaudeColet_detOper()) {
                xmlDetOper
                        += "                                <detOper>\n"
                        + "                                    <cnpjOper>" + detOper.getDetOper_cnpjOper() + "</cnpjOper>\n"
                        + "                                    <regANS>" + detOper.getDetOper_regANS() + "</regANS>\n"
                        + "                                    <vrPgTit>" + detOper.getDetOper_vrPgTit() + "</vrPgTit>\n"
                        + "                                    @detPlano\n"
                        + "                                </detOper>\n";
                xmlDetPlano = "";
                for (S2399.DetPlano detPlano : detOper.getDetOper_detPlano()) {
                    xmlDetPlano
                            += "                                    <detPlano>\n"
                            + "                                        <tpDep>" + detPlano.getDetPlano_tpDep() + "</tpDep>\n"
                            + "                                        <cpfDep>" + detPlano.getDetPlano_cpfDep() + "</cpfDep>\n"
                            + "                                        <nmDep>" + detPlano.getDetPlano_nmDep() + "</nmDep>\n"
                            + "                                        <dtNascto>" + detPlano.getDetPlano_dtNascto() + "</dtNascto>\n"
                            + "                                        <vlrPgDep>" + detPlano.getDetPlano_vlrPgDep() + "</vlrPgDep>\n"
                            + "                                    </detPlano>\n";
                }
                xmlDetOper = xmlDetOper.replace("@detPlano", xmlDetPlano);
            }

            xmlInfoSaudeColet = xmlDetOper.equals("") ? ""
                    : ("\n"
                    + "                            <infoSaudeColet>\n"
                    + xmlDetOper
                    + "                            </infoSaudeColet>\n");

            xml = xmlPadrao
                    .replace("evtTSVTermino_Id", s2399.getEvtTSVTermino_Id())
                    .replace("ideEvento_indRetif", s2399.getIdeEvento_indRetif())
                    .replace("<nrRecibo>ideEvento_nrRecibo</nrRecibo>", "")
                    .replace("ideEvento_tpAmb", s2399.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s2399.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s2399.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", s2399.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s2399.getIdeEmpregador_nrInsc().substring(0, 8))
                    .replace("ideTrabSemVinculo_cpfTrab", s2399.getIdeTrabSemVinculo_cpfTrab())
                    .replace("ideTrabSemVinculo_nisTrab", s2399.getIdeTrabSemVinculo_nisTrab())
                    .replace("ideTrabSemVinculo_matricula", s2399.getIdeTrabSemVinculo_matricula())
                    .replace("ideTrabSemVinculo_codCateg", s2399.getIdeTrabSemVinculo_codCateg())
                    .replace("infoTSVTermino_dtTerm", s2399.getInfoTSVTermino_dtTerm());

            if (s2399.getIdeTrabSemVinculo_codCateg().equals("721")) {
                xml = xml
                        .replace("infoTSVTermino_mtvDesligTSV", s2399.getInfoTSVTermino_mtvDesligTSV());
            } else {
                xml = xml
                        .replace("<mtvDesligTSV>infoTSVTermino_mtvDesligTSV</mtvDesligTSV>", "");
            }

            xml = xml.replace("dmDev_ideDmDev", s2399.getDmDev_ideDmDev())
                    .replace("ideEstabLot_tpInsc", s2399.getIdeEstabLot_tpInsc())
                    .replace("ideEstabLot_nrInsc", s2399.getIdeEstabLot_nrInsc())
                    .replace("ideEstabLot_codLotacao", s2399.getIdeEstabLot_codLotacao().replace(".0", ""))
                    .replace("infoAgNocivo_grauExp", "1")
                    .replace("infoSimples_indSimples", "2")
                    .replace("detVerbas", xmlDetVerbas)
                    .replace("infoSaudeColet", xmlInfoSaudeColet);

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<S3000> getS3000(String codFil, String validade, String tipoAmbiente, String evento, Persistencia persistencia) throws Exception {
        try {
            S3000Dao s3000dao = new S3000Dao();
            List<S3000> listaS3000 = s3000dao.get(codFil, validade, tipoAmbiente, evento, persistencia);
            String idPadrao, id1 = "", id2 = "";
            BigInteger seq = BigInteger.ONE;
            for (S3000 s3000 : listaS3000) {
                idPadrao = s3000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                        + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS");
                id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                if (id2.equals(id1)) {
                    seq = seq.add(BigInteger.ONE);
                    id2 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                    id1 = id2;
                } else {
                    seq = BigInteger.ONE;
                    id1 = idPadrao + FuncoesString.PreencheEsquerda(seq.toString(), 5, "0");
                }
                s3000.setId("ID1" + id2);
                s3000.setIdeEvento_tpAmb(tipoAmbiente);

            }
            return listaS3000;
        } catch (Exception e) {
            return new ArrayList<>();
//            throw new Exception(e.getMessage());
        }
    }

    public String S3000XML(String xmlPadrao, S3000 s3000) throws Exception {
        try {
            String xmlIdeTrabalhador = "", xmlIdeFolhaPagto = "";
            if (s3000.getInfoExclusao_tpEvento().equals("S-2190") || s3000.getInfoExclusao_tpEvento().equals("S-2200") || s3000.getInfoExclusao_tpEvento().equals("S-2205")
                    || s3000.getInfoExclusao_tpEvento().equals("S-2206") || s3000.getInfoExclusao_tpEvento().equals("S-2210") || s3000.getInfoExclusao_tpEvento().equals("S-2220")
                    || s3000.getInfoExclusao_tpEvento().equals("S-2230") || s3000.getInfoExclusao_tpEvento().equals("S-2240") || s3000.getInfoExclusao_tpEvento().equals("S-2241")
                    || s3000.getInfoExclusao_tpEvento().equals("S-2250") || s3000.getInfoExclusao_tpEvento().equals("S-2260") || s3000.getInfoExclusao_tpEvento().equals("S-2298")
                    || s3000.getInfoExclusao_tpEvento().equals("S-2300") || s3000.getInfoExclusao_tpEvento().equals("S-2306") || s3000.getInfoExclusao_tpEvento().equals("S-2306")
                    || s3000.getInfoExclusao_tpEvento().equals("S-2399") || s3000.getInfoExclusao_tpEvento().equals("S-2400") || s3000.getInfoExclusao_tpEvento().equals("S-1200")
                    || s3000.getInfoExclusao_tpEvento().equals("S-1202") || s3000.getInfoExclusao_tpEvento().equals("S-1210") || s3000.getInfoExclusao_tpEvento().equals("S-2299")) {
                xmlIdeTrabalhador = "            <ideTrabalhador>\r\n"
                        + "                 <cpfTrab>" + s3000.getIdeTrabalhador_cpfTrab() + "</cpfTrab>\r\n";
                /*
                if (!s3000.getInfoExclusao_tpEvento().equals("S-1210")) {
                    xmlIdeTrabalhador = xmlIdeTrabalhador + "                 <nisTrab>" + s3000.getIdeTrabalhador_nisTrab() + "</nisTrab>\r\n";
                }
                 */
                xmlIdeTrabalhador = xmlIdeTrabalhador + "            </ideTrabalhador>\r\n";
            } else {
                xmlIdeTrabalhador = "";
            }

            if (s3000.getInfoExclusao_tpEvento().equals("S-1200") || s3000.getInfoExclusao_tpEvento().equals("S-1202")
                    || s3000.getInfoExclusao_tpEvento().equals("S-1207")
                    || s3000.getInfoExclusao_tpEvento().equals("S-1280")) {
                xmlIdeFolhaPagto = " <ideFolhaPagto>\r\n"
                        + "    <indApuracao>" + s3000.getIdeFolhaPagto_indApuracao() + "</indApuracao> \r\n"
                        + "    <perApur>" + s3000.getIdeFolhaPagto_perApur() + "</perApur> \r\n"
                        + " </ideFolhaPagto>\r\n";
            } else if (s3000.getInfoExclusao_tpEvento().equals("S-1210")) {
                xmlIdeFolhaPagto = " <ideFolhaPagto>\r\n"
                        + "    <perApur>" + s3000.getIdeFolhaPagto_perApur() + "</perApur> \r\n"
                        + " </ideFolhaPagto>\r\n";
            } else {
                xmlIdeFolhaPagto = "";
            }

            String xml = xmlPadrao
                    .replace("evtExclusao_Id", s3000.getId())
                    .replace("ideEvento_tpAmb", s3000.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", s3000.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", s3000.getIdeEvento_verProc())
                    .replace("ideContri_tpInsc", s3000.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", s3000.getIdeEmpregador_nrInsc())
                    .replace("infoExclusao_tpEvento", s3000.getInfoExclusao_tpEvento())
                    .replace("infoExclusao_nrRecEvt", s3000.getInfoExclusao_nrRecEvt())
                    .replace("ideTrabalhador", xmlIdeTrabalhador)
                    .replace("ideFolhaPagto", xmlIdeFolhaPagto);

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<R1000> getR1000(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            R1000Dao R1000dao = new R1000Dao();
            List<R1000> r1000 = R1000dao.get(codFil, compet, ambiente, persistencia);
            return r1000;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String R1000XML(String xmlPadrao, R1000 r1000) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtInfoContri_Id", "ID" + r1000.getIdeEmpregador_tpInsc() + r1000.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_tpAmb", r1000.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", r1000.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", r1000.getIdeEvento_verProc())
                    .replace("ideEmpregador_tpInsc", r1000.getIdeEmpregador_tpInsc())
                    .replace("ideEmpregador_nrInsc", r1000.getIdeEmpregador_nrInsc())
                    .replace("idePeriodo_iniValid", r1000.getIdePeriodo_iniValid())
                    .replace("infoCadastro_nmRazao", r1000.getInfoCadastro_nmRazao())
                    .replace("infoCadastro_classTrib", r1000.getInfoCadastro_classTrib())
                    .replace("infoCadastro_indEscrituracao", r1000.getinfoCadastro_indEscrituracao())
                    .replace("infoCadastro_indDesoneracao", r1000.getInfoCadastro_indDesoneracao())
                    .replace("infoCadastro_ndAcordoIsenMulta", r1000.getInfoCadastro_ndAcordoIsenMulta())
                    .replace("infoCadastro_indSitPJ", r1000.getinfoCadastro_indSitPJ())
                    .replace("contato_foneCel", r1000.getContato_foneCel())
                    .replace("softHouse_cnpjSoftHouse", r1000.getSoftwareHouse_cnpjSoftHouse())
                    .replace("softHouse_nmRazao", r1000.getSoftwareHouse_nmRazao())
                    .replace("softHouse_nmCont", r1000.getSoftwareHouse_nmCont())
                    .replace("softHouse_telefone", r1000.getSoftwareHouse_telefone())
                    .replace("softHouse_email", r1000.getSoftwareHouse_email())
                    .replace("softHouse_email", r1000.getSoftwareHouse_email())
                    .replace("contato_nmCtt", r1000.getContato_nmCtt())
                    .replace("contato_cpfCtt", r1000.getContato_cpfCtt())
                    .replace("contato_foneFixo", r1000.getContato_foneFixo())
                    .replace("contato_email", r1000.getContato_email())
                    .replace("softwareHouse_cnpjSoftHouse", r1000.getSoftwareHouse_cnpjSoftHouse())
                    .replace("softwareHouse_nmRazao", r1000.getSoftwareHouse_nmRazao())
                    .replace("softwareHouse_nmCont", r1000.getSoftwareHouse_nmCont())
                    .replace("softwareHouse_telefone", r1000.getSoftwareHouse_telefone())
                    .replace("softwareHouse_email", r1000.getSoftwareHouse_email())
                    .replace("<fimValid>idePeriodo_fimValid</fimValid>", "");

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<R1070> getR1070(String codFil, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            R1070Dao R1070dao = new R1070Dao();
            List<R1070> r1070 = R1070dao.get(codFil, compet, ambiente, persistencia);
            return r1070;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String R1070XML(String xmlPadrao, R1070 r1070) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtInfoContri_Id", "ID" + r1070.getIdeEmpregador_tpInsc() + r1070.getIdeEmpregador_nrInsc().substring(0, 8) + "000000"
                            + DataAtual.getDataAtual("SQL") + DataAtual.getDataAtual("HHMMSS") + "00001")
                    .replace("ideEvento_tpAmb", r1070.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", r1070.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", r1070.getIdeEvento_verProc())
                    .replace("ideContri_tpInsc", r1070.getIdeEmpregador_tpInsc())
                    .replace("ideContri_nrInsc", r1070.getIdeEmpregador_nrInsc())
                    .replace("ideProcesso_tpProc", r1070.getIdeProcesso_tpProc())
                    .replace("ideProcesso_nrProc", r1070.getIdeProcesso_nrProc())
                    .replace("ideProcesso_iniValid", r1070.getIdeProcesso_iniValid())
                    .replace("ideProcesso_indAutoria", r1070.getIdeProcesso_indAutoria())
                    .replace("infoSusp_codSusp", r1070.getInfoSusp_codSusp())
                    .replace("infoSusp_indSusp", r1070.getInfoSusp_indSusp())
                    .replace("infoSusp_dtDecisao", r1070.getInfoSusp_dtDescisao())
                    .replace("infoSusp_indDeposito", r1070.getInfoSusp_indDeposito())
                    .replace("softHouse_cnpjSoftHouse", r1070.getDadosProcJud_ufVara())
                    .replace("softHouse_nmRazao", r1070.getDadosProcJud_codMunic())
                    .replace("softHouse_nmCont", r1070.getDadosProcJud_idVara());
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<R2020> getR2020(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            R2020Dao R2020dao = new R2020Dao();
            return R2020dao.get(codFil, validade, tipoAmbiente, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String R2020XML(String xmlPadrao, R2020 r2020) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtServPrest_Id", r2020.getEvtServPrest_Id())
                    .replace("ideEvento_indRetif", r2020.getIdeEvento_indRetif())
                    .replace("ideEvento_perApur", r2020.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", r2020.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", r2020.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", r2020.getIdeEvento_verProc())
                    .replace("ideContri_tpInsc", r2020.getIdeContri_tpInsc())
                    .replace("ideContri_nrInsc", r2020.getIdeContri_nrInsc().substring(0, 8))
                    .replace("ideEstabPrest_tpInscEstabPrest", r2020.getIdeEstabPrest_tpInscEstabPrest())
                    .replace("ideEstabPrest_nrInscEstabPrest", r2020.getIdeEstabPrest_nrInscEstabPrest())
                    .replace("ideTomador_tpInscTomador", r2020.getIdeTomador_tpInscTomador())
                    .replace("ideTomador_nrInscTomador", r2020.getIdeTomador_nrInscTomador())
                    .replace("ideTomador_indObra", r2020.getIdeTomador_indObra())
                    .replace("ideTomador_vlrTotalBruto", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBruto(), false).replace(".", ""))
                    .replace("ideTomador_vlrTotalBaseRet", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBaseRet(), false).replace(".", ""))
                    //.replace("ideTomador_vlrTotalRetPrinc", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBruto(),false).replace(".",""))
                    //.replace("ideTomador_vlrTotalRetAdic", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBruto(),false).replace(".",""))
                    //.replace("getIdeTomador_vlrTotalNRetPrinc", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBruto(),false).replace(".",""))
                    //.replace("ideTomador_vlrTotalNRetAdic", formatarStringMoeda(r2020.getIdeTomador_vlrTotalBruto(),false).replace(".",""));
                    .replace("<vlrTotalRetPrinc>ideTomador_vlrTotalRetPrinc</vlrTotalRetPrinc>",
                            r2020.getIdeTomador_vlrTotalRetPrinc().equals("") ? "" : "<vlrTotalRetPrinc>" + formatarStringMoeda(r2020.getIdeTomador_vlrTotalRetPrinc(), false).replace(".", "") + "</vlrTotalRetPrinc>") //
                    .replace("<vlrTotalRetAdic>ideTomador_vlrTotalRetAdic</vlrTotalRetAdic>",
                            r2020.getIdeTomador_vlrTotalRetAdic().equals("") ? "" : "<vlrTotalRetAdic>" + formatarStringMoeda(r2020.getIdeTomador_vlrTotalRetAdic(), false).replace(".", "") + "</vlrTotalRetAdic>") //
                    .replace("<vlrTotalNRetPrinc>ideTomador_vlrTotalNRetPrinc</vlrTotalNRetPrinc>",
                            r2020.getIdeTomador_vlrTotalNRetPrinc().equals("") ? "" : "<vlrTotalNRetPrinc>" + formatarStringMoeda(r2020.getIdeTomador_vlrTotalNRetPrinc(), false).replace(".", "") + "</vlrTotalNRetPrinc>")
                    .replace("<vlrTotalNRetAdic>ideTomador_vlrTotalNRetAdic</vlrTotalNRetAdic>",
                            r2020.getIdeTomador_vlrTotalNRetAdic().equals("") ? "" : "<vlrTotalNRetAdic>" + formatarStringMoeda(r2020.getIdeTomador_vlrTotalNRetAdic(), false).replace(".", "") + "</vlrTotalNRetAdic>");

            String nfsXml = "                    <nfs>\n"
                    + "                        <serie>nfs_serie</serie>\n"
                    + "                        <numDocto>nfs_numDocto</numDocto>\n"
                    + "                        <dtEmissaoNF>nfs_dtEmissaoNF</dtEmissaoNF>\n"
                    + "                        <vlrBruto>nfs_vlrBruto</vlrBruto>\n"
                    + "                        <obs>nfs_obs</obs>\n"
                    + "<infoTpServ>\n"
                    + "                    </nfs>\n",
                    infoTpServXml = "                        <infoTpServ>\n"
                    + "                            <tpServico>infoTpServ_tpServico</tpServico>\n"
                    + "                            <vlrBaseRet>infoTpServ_vlrBaseRet</vlrBaseRet>\n"
                    + "                            <vlrRetencao>infoTpServ_vlrRetencao</vlrRetencao>\n"
                    + "                            <vlrRetSub>infoTpServ_vlrRetSub</vlrRetSub>\n" //
                    + "                            <vlrNRetPrinc>infoTpServ_vlrNRetPrinc</vlrNRetPrinc>\n" //
                    + "                            <vlrServicos15>infoTpServ_vlrServicos15</vlrServicos15>\n" //
                    + "                            <vlrServicos20>infoTpServ_vlrServicos20</vlrServicos20>\n" //
                    + "                            <vlrServicos25>infoTpServ_vlrServicos25</vlrServicos25>\n" //
                    + "                            <vlrAdicional>infoTpServ_vlrAdicional</vlrAdicional>\n" //
                    + "                            <vlrNRetAdic>infoTpServ_vlrNRetAdic</vlrNRetAdic>\n" //
                    + "                        </infoTpServ>\n",
                    infoProcRetPrXml = "                    <infoProcRetPr>\n"
                    + "                        <tpProcRetPrinc>infoProcRetPr_tpProcRetPrinc</tpProcRetPrinc>\n"
                    + "                        <nrProcRetPrinc>infoProcRetPr_nrProcRetPrinc</nrProcRetPrinc>\n"
                    + "                        <codSuspPrinc>infoProcRetPr_codSuspPrinc</codSuspPrinc>\n"
                    + "                        <valorPrinc>infoProcRetPr_valorPrinc</valorPrinc>\n"
                    + "                    </infoProcRetPr>\n",
                    infoProcRetAdXml = "                    <infoProcRetAd>\n"
                    + "                        <tpProcRetAdic>infoProcRetAd_tpProcRetAdic</tpProcRetAdic>\n"
                    + "                        <nrProcRetAdic>infoProcRetAd_nrProcRetAdic</nrProcRetAdic>\n"
                    + "                        <codSuspAdic>infoProcRetAd_codSuspAdic</codSuspAdic>\n"
                    + "                        <valorAdic>infoProcRetAd_valorAdic</valorAdic>\n"
                    + "                    </infoProcRetAd>";

            StringBuilder aux, nfsAux;

            nfsAux = new StringBuilder();
            for (Nfs nfs : r2020.getIdeTomador_nfs()) {
                aux = new StringBuilder();
                for (InfoTpServ infoTpServ : nfs.getNfs_infoTpserv()) {
                    aux.append(infoTpServXml.replace("infoTpServ_tpServico", infoTpServ.getInfoTpServ_tpServico())
                            .replace("infoTpServ_vlrBaseRet", formatarStringMoeda(infoTpServ.getInfoTpServ_vlrBaseRet(), false).replace(".", ""))
                            .replace("infoTpServ_vlrRetencao", formatarStringMoeda(infoTpServ.getInfoTpServ_vlrRetencao(), false).replace(".", ""))
                            .replace("<vlrRetSub>infoTpServ_vlrRetSub</vlrRetSub>",
                                    infoTpServ.getInfoTpServ_vlrRetSub().equals("") ? "" : "<vlrRetSub>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrRetSub(), false).replace(".", "") + "</vlrRetSub>")
                            .replace("<vlrNRetPrinc>infoTpServ_vlrNRetPrinc</vlrNRetPrinc>",
                                    infoTpServ.getInfoTpServ_vlrNRetPrinc().equals("") ? "" : "<vlrNRetPrinc>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrNRetPrinc(), false).replace(".", "") + "</vlrNRetPrinc>")
                            .replace("<vlrServicos15>infoTpServ_vlrServicos15</vlrServicos15>",
                                    infoTpServ.getInfoTpServ_vlrServicos15().equals("") ? "" : "<vlrServicos15>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrServicos15(), false).replace(".", "") + "</vlrServicos15>")
                            .replace("<vlrServicos20>infoTpServ_vlrServicos20</vlrServicos20>",
                                    infoTpServ.getInfoTpServ_vlrServicos20().equals("") ? "" : "<vlrServicos20>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrServicos20(), false).replace(".", "") + "</vlrServicos20>")
                            .replace("<vlrServicos25>infoTpServ_vlrServicos25</vlrServicos25>",
                                    infoTpServ.getInfoTpServ_vlrServicos25().equals("") ? "" : "<vlrServicos25>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrServicos25(), false).replace(".", "") + "</vlrServicos25>")
                            .replace("<vlrAdicional>infoTpServ_vlrAdicional</vlrAdicional>",
                                    infoTpServ.getInfoTpServ_vlrAdicional().equals("") ? "" : "<vlrAdicional>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrAdicional(), false).replace(".", "") + "</vlrAdicional>")
                            .replace("<vlrNRetAdic>infoTpServ_vlrNRetAdic</vlrNRetAdic>",
                                    infoTpServ.getInfoTpServ_vlrNRetAdic().equals("") ? "" : "<vlrNRetAdic>" + formatarStringMoeda(infoTpServ.getInfoTpServ_vlrNRetAdic(), false).replace(".", "") + "</vlrNRetAdic>"));
                }

                nfsAux.append(nfsXml.replace("nfs_serie", nfs.getNfs_serie())
                        .replace("nfs_numDocto", nfs.getNfs_numDocto().replace(".0", ""))
                        .replace("nfs_dtEmissaoNF", nfs.getNfs_dtEmissaoNF())
                        .replace("nfs_vlrBruto", formatarStringMoeda(nfs.getNfs_vlrBruto(), false).replace(".", ""))
                        .replace("<obs>nfs_obs</obs>",
                                nfs.getNfs_obs().equals("") ? "" : nfs.getNfs_obs())
                        .replace("<infoTpServ>", aux.toString()));
            }
            xml = xml.replace("<nfs>", nfsAux.toString());

            aux = new StringBuilder();
            for (InfoProcRetPr infoProcRetPr : r2020.getIdeTomador_infoProcRetPr()) {
                aux.append(infoProcRetPrXml.replace("infoProcRetPr_tpProcRetPrinc", infoProcRetPr.getInfoProcRetPr_tpProcRetPrinc())
                        .replace("infoProcRetPr_nrProcRetPrinc", infoProcRetPr.getInfoProcRetPr_nrProcRetPrinc())
                        .replace("<codSuspPrinc>infoProcRetPr_codSuspPrinc</codSuspPrinc>",
                                infoProcRetPr.getInfoProcRetPr_codSuspPrinc().equals("") ? "" : infoProcRetPr.getInfoProcRetPr_codSuspPrinc())
                        .replace("infoProcRetPr_valorPrinc", infoProcRetPr.getInfoProcRetPr_valorPrinc()));
            }
            xml = xml.replace("<infoProcRetPr>", aux.toString());

            aux = new StringBuilder();
            for (InfoProcRetAd infoProcRetAd : r2020.getIdeTomador_infoProcRetAd()) {
                aux.append(infoProcRetAdXml.replace("infoProcRetAd_tpProcRetAdic", infoProcRetAd.getInfoProcRetAd_tpProcRetAdic())
                        .replace("infoProcRetAd_nrProcRetAdic", infoProcRetAd.getInfoProcRetAd_nrProcRetAdic())
                        .replace("<codSuspAdic>infoProcRetAd_codSuspAdic</codSuspAdic>",
                                infoProcRetAd.getInfoProcRetAd_codSuspAdic().equals("") ? "" : infoProcRetAd.getInfoProcRetAd_codSuspAdic())
                        .replace("infoProcRetAd_valorAdic", infoProcRetAd.getInfoProcRetAd_valorAdic()));
            }
            xml = xml.replace("<infoProcRetAd>", aux.toString());

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<R2098> getR2098(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            R2098Dao R2098dao = new R2098Dao();
            List<R2098> listaR2098 = R2098dao.get(codFil, validade, tipoAmbiente, persistencia);
            return listaR2098;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String R2098XML(String xmlPadrao, R2098 r2098) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtReabreEvPer_Id", r2098.getEvtReabreEvPer_Id())
                    .replace("ideEvento_perApur", r2098.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", r2098.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", r2098.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", r2098.getIdeEvento_verProc())
                    .replace("ideContri_tpInsc", r2098.getIdeContri_tpInsc())
                    .replace("ideContri_nrInsc", r2098.getIdeContri_nrInsc());

            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<R2099> getR2099(String codFil, String validade, String tipoAmbiente, Persistencia persistencia) throws Exception {
        try {
            R2099Dao R2099dao = new R2099Dao();
            return R2099dao.get(codFil, validade, tipoAmbiente, persistencia);
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public String R2099XML(String xmlPadrao, R2099 r2099) throws Exception {
        try {
            String xml = xmlPadrao
                    .replace("evtFechaEvPer_Id", r2099.getEvtFechaEvPer_Id())
                    .replace("ideEvento_perApur", r2099.getIdeEvento_perApur())
                    .replace("ideEvento_tpAmb", r2099.getIdeEvento_tpAmb())
                    .replace("ideEvento_procEmi", r2099.getIdeEvento_procEmi())
                    .replace("ideEvento_verProc", r2099.getIdeEvento_verProc())
                    .replace("ideContri_tpInsc", r2099.getIdeContri_tpInsc())
                    .replace("ideContri_nrInsc", r2099.getIdeContri_nrInsc())
                    .replace("ideRespInf_nmResp", r2099.getIdeRespInf_nmResp())
                    .replace("ideRespInf_cpfResp", r2099.getIdeRespInf_cpfResp())
                    .replace("ideRespInf_telefone", r2099.getIdeRespInf_telefone())
                    .replace("ideRespInf_email", r2099.getIdeRespInf_email())
                    .replace("infoFech_evtServTm", r2099.getInfoFech_evtServTm())
                    .replace("infoFech_evtServPr", r2099.getInfoFech_evtServPr())
                    .replace("infoFech_evtAssDespRec", r2099.getInfoFech_evtAssDespRec())
                    .replace("infoFech_evtAssDespRep", r2099.getInfoFech_evtAssDespRep())
                    .replace("infoFech_evtComProd", r2099.getInfoFech_evtComProd())
                    .replace("infoFech_evtCPRB", r2099.getInfoFech_evtCPRB())
                    .replace("infoFech_evtPgtos", r2099.getInfoFech_evtPgtos())
                    .replace("<compSemMovto>infoFech_compSemMovto</compSemMovto>", r2099.getInfoFech_compSemMovto().equals("")
                            ? "" : "<compSemMovto>" + r2099.getInfoFech_compSemMovto() + "</compSemMovto>");
            return xml;
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    public List<XMLeSocial> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            return xmleSocialDao.listaPaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            return xmleSocialDao.totalXML(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public boolean existeEvento(XMLeSocial xml, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            return xmleSocialDao.existeEvento(xml, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirEvento(XMLeSocial xml, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            xmleSocialDao.inserirNovoEvento(xml, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarEvento(XMLeSocial xml, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            xmleSocialDao.atualizarEvento(xml, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public void consultarEventoA3(XMLeSocial xml, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            xmleSocialDao.marcarAssinar(xml, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public List<XMLeSocial> getEventosPendentes(String filial, String evento, String compet, String ambiente, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            return xmleSocialDao.eventosPendentes(filial, evento.split(" - ")[0], compet, ambiente, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

    public List<XMLeSocial> getEventosVerificacao(XMLeSocial parametros, Persistencia persistencia) throws Exception {
        try {
            XMLeSocialDao xmleSocialDao = new XMLeSocialDao();
            return xmleSocialDao.eventosVerificar(parametros, persistencia);
        } catch (Exception e) {
            throw new Exception("XMLeSocial.falhageral<message>" + e.getMessage());
        }
    }

}
