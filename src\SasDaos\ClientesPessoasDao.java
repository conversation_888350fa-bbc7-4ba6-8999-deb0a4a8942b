package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ClientesPessoas;
import SasBeansCompostas.CarregaRota;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClientesPessoasDao {

    /**
     * Retorna todas as pessoas que podem responder pela empresa passada no
     * codfil passado
     *
     * @param cp - Objeto ClientesPessoas - informar codcli e codfil
     * @param persistencia - Conexão ao banco
     * @return - retorna lista das pessoas autorizadas para a empresa
     * @throws Exception - pode gerar exception
     */
    public List<ClientesPessoas> getPessoas(ClientesPessoas cp, Persistencia persistencia) throws Exception {
        try {
            List<ClientesPessoas> retorno = new ArrayList();
            String sql = "Select CodPessoa, CodPessoaWeb from ClientesPessoas where Codcli = ? and CodFil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cp.getCodCli());
            consult.setString(cp.getCodFil().toString());
            consult.select();
            while (consult.Proximo()) {
                ClientesPessoas cps = new ClientesPessoas();
                cps.setCodCli(cp.getCodCli());
                cps.setCodFil(cp.getCodFil().toString());
                cps.setCodPessoa(consult.getString("CodPessoa"));
                cps.setCodPessoaWEB(consult.getString("CodPessaoWeb"));
                retorno.add(cps);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesPessoasDao.getPessoas - " + e.getMessage() + "\r\n"
                    + "Select CodPessoa, CodPessoaWeb from ClientesPessoas where Codcli = " + cp.getCodCli() + " and CodFil = " + cp.getCodFil().toString());
        }
    }

    public List<ClientesPessoas> getPessoas(List<CarregaRota> lrota, Persistencia persistencia) throws Exception {
        String sql = "Select CodCli, CodPessoa, CodPessoaWeb from ClientesPessoas where codfil = ? and Codcli in (";
        try {
            List<ClientesPessoas> retorno = new ArrayList();

            for (CarregaRota cr : lrota) {
                sql += "'" + cr.getCliOri().getCodigo() + "',";
            }
            sql += ")";
            sql = sql.replace(",)", ")");
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(lrota.get(0).getRota().getCodFil().toString());
            consult.select();
            while (consult.Proximo()) {
                ClientesPessoas cp = new ClientesPessoas();
                cp.setCodCli(consult.getString("CodCli"));
                cp.setCodFil(lrota.get(0).getRota().getCodFil().toString());
                cp.setCodPessoa(consult.getString("CodPessoa"));
                cp.setCodPessoaWEB(consult.getString("CodPessoaWeb"));
                retorno.add(cp);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesPessoasDao.getPessoas - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
}
