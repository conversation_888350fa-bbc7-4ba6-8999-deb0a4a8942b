/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Contatos;
import SasBeans.ContatosIntegracao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContatosDao {

    /**
     * Inserir contato a partir de um cliente
     *
     * @param clientes
     * @param persistencia
     * @throws Exception
     */
    public void inserir(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            String sql = "Insert into Contatos (Codigo,CodFil, Nome, Fantasia, Endereco, Bairro,"
                    + " Cidade, CodCidade, CEP, UF, Fone1,<PERSON><PERSON><PERSON><PERSON>, <PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>_<PERSON><PERSON>,<PERSON><PERSON>_<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON>p<PERSON><PERSON>, CodOrigem)"
                    + " Values( (Select Max(Codigo)+1 from contatos),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(clientes.getCodFil());
            consulta.setString(clientes.getNome());
            consulta.setString(clientes.getNRed());
            consulta.setString(clientes.getEnde());
            consulta.setString(clientes.getBairro());
            consulta.setString(clientes.getCidade());
            consulta.setString(clientes.getCodCidade());
            consulta.setString(clientes.getCEP());
            consulta.setString(clientes.getEstado());
            consulta.setString(clientes.getFone1());
            consulta.setFloat(1);
            consulta.setString(clientes.getDtSituacao());
            consulta.setString(clientes.getOper_Alt());
            consulta.setDate(DataAtual.LC2Date(clientes.getDt_Alter()));
            consulta.setString(clientes.getHr_Alter());
            consulta.setString(clientes.getCodigo());
            consulta.setString("6");
            consulta.setInt(8);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContatosDao.inserir - " + e.getMessage() + "\r\n"
                    + "Insert into Contatos (Codigo,CodFil, Nome, Fantasia, Endereco, Bairro,"
                    + " Cidade, CodCidade, CEP, UF, Fone1,Situacao, DtSituacao,Operador,Dt_Alter,Hr_Alter,CodCli, TpCli, CodOrigem)"
                    + " Values( (Select Max(Codigo)+1 from contatos)," + clientes.getCodFil() + "," + clientes.getNome() + ","
                    + clientes.getNRed() + "," + clientes.getEnde() + "," + clientes.getBairro() + "," + clientes.getCidade() + ","
                    + clientes.getCodCidade() + "," + clientes.getCEP() + "," + clientes.getEstado() + "," + clientes.getFone1() + ",1,"
                    + clientes.getDtSituacao() + "," + clientes.getOper_Alt() + "," + clientes.getDt_Alter() + "," + clientes.getHr_Alter()
                    + "," + clientes.getCodigo() + ",6,8)");
        }
    }

    /**
     * Retorna a quantidade total de contatos
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int totalContatos(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select count(*) total from contatos "
                    + "where codigo is not null";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContatosDao.totalContatos - " + e.getMessage());
        }
    }

    /**
     * Lista de contatos
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Contatos> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Contatos> contatos = new ArrayList<>();
        try {
            String sql = "SELECT  *  FROM"
                    + " ( SELECT ROW_NUMBER() OVER ( ORDER BY nome ) AS RowNum,"
                    + "        * from Contatos "
                    + " where codigo is not null ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult"
                    + " WHERE RowNum >= ?"
                    + " AND RowNum < ?"
                    + " ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Contatos contato;
            while (consulta.Proximo()) {
                contato = new Contatos();
                contato.setCodFil(consulta.getBigDecimal("codfil"));
                contato.setCodigo(consulta.getBigDecimal("codigo"));
                contato.setNome(consulta.getString("nome"));
                contato.setFantasia(consulta.getString("fantasia"));
                contato.setEndereco(consulta.getString("endereco"));
                contato.setBairro(consulta.getString("bairro"));
                contato.setCidade(consulta.getString("cidade"));
                contato.setUF(consulta.getString("uf"));
                contato.setCEP(consulta.getString("cep"));
                contato.setFone1(consulta.getString("fone1"));
                contato.setFone2(consulta.getString("fone2"));
                contato.setCNPJ(consulta.getString("cnpj"));
                contato.setIE(consulta.getString("ie"));
                contato.setIM(consulta.getString("im"));
                contato.setCPF(consulta.getString("cpf"));
                contato.setRG(consulta.getString("rg"));
                contato.setOperador(consulta.getString("Operador"));
                contato.setDt_Alter(consulta.getString("dt_alter"));
                contato.setHr_Alter(consulta.getString("hr_alter"));
                contato.setDtSituacao(consulta.getString("dtsituacao"));
                contato.setEmail(consulta.getString("email"));
                contato.setObs(consulta.getString("obs"));
                contato.setTpCli(consulta.getString("tpcli"));
                contato.setSituacao(consulta.getBigDecimal("situacao"));
                contato.setCodOrigem(consulta.getInt("codorigem"));
                contato.setContato(consulta.getString("contato"));
                contatos.add(contato);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContatosDao.listaPaginada - " + e.getMessage());
        }
        return contatos;
    }

    /**
     * Cadastro de contatos
     *
     * @param contato
     * @param persistencia
     * @return o código do contato cadastrado.
     * @throws Exception
     */
    public String cadastrar(Contatos contato, Persistencia persistencia) throws Exception {
        try {
            String sqlCodigo = " (Select ISNULL(Max(Codigo),0) + 1 Codigo from contatos) ";
            String sql = "Insert into Contatos (Codigo,CodFil, Nome, Fantasia, Endereco, Bairro,"
                    + " Cidade, CEP, UF, Fone1,Situacao, DtSituacao,Operador,Dt_Alter,Hr_Alter, TpCli, CodOrigem,"
                    + " cnpj, cpf, im, ie, rg, email, fone2, obs, operincl, dt_incl, contato, latitude, longitude)"
                    + " Values( ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,"
                    + " ?,?,?,?,?,?,?,?,?,?,?,?,?) ";
            for (int i = 0; i < 20; i++) {
                Consulta consultaCodigo = new Consulta(sqlCodigo, persistencia);
                consultaCodigo.select();
                while (consultaCodigo.Proximo()) {
                    contato.setCodigo(new BigDecimal(consultaCodigo.getString("Codigo")));
                }
                consultaCodigo.close();
                Consulta consulta = new Consulta(sql, persistencia);
                consulta.setBigDecimal(contato.getCodigo());
                consulta.setBigDecimal(contato.getCodFil());
                consulta.setString(contato.getNome());
                consulta.setString(contato.getFantasia());
                consulta.setString(contato.getEndereco());
                consulta.setString(contato.getBairro());
                consulta.setString(contato.getCidade());
                consulta.setString(contato.getCEP());
                consulta.setString(contato.getUF());
                consulta.setString(contato.getFone1());
                consulta.setBigDecimal(contato.getSituacao());
                consulta.setString(contato.getDtSituacao());
                consulta.setString(contato.getOperador());
                consulta.setString(contato.getDt_Alter());
                consulta.setString(contato.getHr_Alter());
                consulta.setString(contato.getTpCli());
                consulta.setInt(contato.getCodOrigem());
                consulta.setString(contato.getCNPJ());
                consulta.setString(contato.getCPF());
                consulta.setString(contato.getIM());
                consulta.setString(contato.getIE());
                consulta.setString(contato.getRG());
                consulta.setString(contato.getEmail());
                consulta.setString(contato.getFone2());
                consulta.setString(contato.getObs());
                consulta.setString(contato.getOperIncl());
                consulta.setString(DataAtual.getDataAtual("SQL"));
                consulta.setString(contato.getContato());
                consulta.setString(contato.getLatitude());
                consulta.setString(contato.getLongitude());
                consulta.insert();
                consulta.close();
                return contato.getCodigo().toBigInteger().toString();
            }
            return null;
        } catch (Exception e) {
            throw new Exception("ContatosDao.cadastrar - " + e.getMessage() + "\r\n"
                    + "Insert into Contatos (Codigo,CodFil, Nome, Fantasia, Endereco, Bairro,"
                    + " Cidade, CEP, UF, Fone1,Situacao, DtSituacao,Operador,Dt_Alter,Hr_Alter, TpCli, CodOrigem,"
                    + " cnpj, cpf, im, ie, rg, email, fone2, obs, operincl, dt_incl, contato)"
                    + " Values( (Select Max(Codigo)+1 from contatos)" + contato.getCodFil() + "," + contato.getNome() + "," + contato.getFantasia() + ","
                    + contato.getEndereco() + "," + contato.getBairro() + "," + contato.getCidade() + "," + contato.getCEP() + "," + contato.getUF() + ","
                    + contato.getFone1() + "," + contato.getSituacao() + "," + contato.getDtSituacao() + "," + contato.getOperador() + ","
                    + contato.getDt_Alter() + "," + contato.getHr_Alter() + "," + contato.getTpCli() + "," + contato.getCodOrigem() + "," + contato.getCNPJ() + ",18,"
                    + contato.getCPF() + "," + contato.getIM() + "," + contato.getIE() + "," + contato.getRG() + "," + contato.getEmail() + "," + contato.getFone2() + ","
                    + contato.getObs() + "," + contato.getOperIncl() + "," + DataAtual.getDataAtual("SQL") + "," + contato.getContato() + ")");
        }
    }

    /**
     * Atualiza um contato
     *
     * @param contato
     * @param persistencia
     * @throws Exception
     */
    public void atualizar(Contatos contato, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE contatos set Nome = ?, Fantasia = ?, Endereco = ?, Bairro = ?, "
                    + " Cidade = ?, CEP = ?, UF = ?, Fone1 = ?, Situacao = ?, DtSituacao = ?, Operador = ?, "
                    + " Dt_Alter = ?, Hr_Alter = ?, TpCli = ?, CodOrigem = ?, "
                    + " cnpj = ?, cpf = ?, im = ?, ie = ?, rg = ?, email = ?, fone2 = ?, obs = ?, contato = ? "
                    + " where codigo = ? and codfil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contato.getNome());
            consulta.setString(contato.getFantasia());
            consulta.setString(contato.getEndereco());
            consulta.setString(contato.getBairro());
            consulta.setString(contato.getCidade());
            consulta.setString(contato.getCEP());
            consulta.setString(contato.getUF());
            consulta.setString(contato.getFone1());
            consulta.setBigDecimal(contato.getSituacao());
            consulta.setString(contato.getDtSituacao());
            consulta.setString(contato.getOperador());
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(contato.getTpCli());
            consulta.setInt(contato.getCodOrigem());
            consulta.setString(contato.getCNPJ());
            consulta.setString(contato.getCPF());
            consulta.setString(contato.getIM());
            consulta.setString(contato.getIE());
            consulta.setString(contato.getRG());
            consulta.setString(contato.getEmail());
            consulta.setString(contato.getFone2());
            consulta.setString(contato.getObs());
            consulta.setString(contato.getContato());
            consulta.setBigDecimal(contato.getCodigo());
            consulta.setBigDecimal(contato.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContatosDao.atualizar - " + e.getMessage() + "\r\n"
                    + " UPDATE contatos set Nome = " + contato.getNome() + ", Fantasia = " + contato.getFantasia() + ", Endereco = " + contato.getEndereco() + ", "
                    + " Bairro = " + contato.getBairro() + ", Cidade = " + contato.getCidade() + ", CEP = " + contato.getCEP() + ", UF = " + contato.getUF() + ", "
                    + " Fone1 = " + contato.getFone1() + ", Situacao = " + contato.getSituacao() + ", DtSituacao = " + contato.getDtSituacao() + ", "
                    + " Operador = " + contato.getOperador() + ", Dt_Alter = " + DataAtual.getDataAtual("SQL") + ", Hr_Alter = " + DataAtual.getDataAtual("HORA") + ", "
                    + " TpCli = " + contato.getTpCli() + ", CodOrigem = " + contato.getCodOrigem() + ", cnpj = " + contato.getCNPJ() + ", cpf = " + contato.getCPF() + ", "
                    + " im = " + contato.getIM() + ", ie = " + contato.getIE() + ", rg = " + contato.getRG() + ", email = " + contato.getEmail() + ", "
                    + " fone2 = " + contato.getFone2() + ", obs = " + contato.getObs() + ", contato = " + contato.getContato() + " "
                    + " where codigo = " + contato.getCodigo() + " and codfil = " + contato.getCodFil());
        }
    }

    /**
     * Busca contatos pelo nome ou nome fantasia
     *
     * @param query
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Contatos> buscarContatos(String query, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = " select top 20 * from contatos "
                    + " where "
                    + " codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = ? and "
                    + " paramet.path = '"
                    //                    + (persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : persistencia.getEmpresa()) + "')"
                    + (persistencia.getEmpresa()) + "')"
                    + " and (nome like ? or fantasia like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            List<Contatos> retorno = new ArrayList<>();
            Contatos contato;
            while (consult.Proximo()) {
                contato = new Contatos();
                contato.setNome(consult.getString("nome"));
                contato.setCodigo(consult.getBigDecimal("codigo"));
                contato.setEndereco(consult.getString("endereco"));
                contato.setBairro(consult.getString("bairro"));
                contato.setCidade(consult.getString("cidade"));
                contato.setUF(consult.getString("uf"));
                contato.setFantasia(consult.getString("fantasia"));
                contato.setFone1(consult.getString("fone1"));
                contato.setFone2(consult.getString("fone2"));
                contato.setContato(consult.getString("contato"));
                contato.setEmail(consult.getString("email"));
                retorno.add(contato);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContatosDao.buscarContatos - " + e.getMessage() + "\r\n"
                    + "select top 20 * from contatos "
                    + " where "
                    + " codfil in (select filiais.codfil "
                    + " from saspw"
                    + " inner join saspwfil on saspwfil.nome = saspw.nome"
                    + " inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + " inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + " where saspw.codpessoa = " + codPessoa + " and "
                    + " paramet.path = '"
                    //                    + (persistencia.getEmpresa().equals("SATCONFEDERALBSB") ? "SatCVB" : persistencia.getEmpresa()) + "')"
                    + (persistencia.getEmpresa()) + "')"
                    + " and (nome like " + query + " or fantasia like " + query + ")");
        }
    }

    /**
     * Busca um contato pelo código
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Contatos selecionarContato(BigDecimal codigo, Persistencia persistencia) throws Exception {
        Contatos contato = new Contatos();
        try {
            String sql = " select * from contatos where codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codigo);
            consult.select();
            while (consult.Proximo()) {
                contato.setNome(consult.getString("nome"));
                contato.setCodigo(consult.getBigDecimal("codigo"));
                contato.setEndereco(consult.getString("endereco"));
                contato.setBairro(consult.getString("bairro"));
                contato.setCidade(consult.getString("cidade"));
                contato.setUF(consult.getString("uf"));
                contato.setFantasia(consult.getString("fantasia"));
                contato.setFone1(consult.getString("fone1"));
                contato.setFone2(consult.getString("fone2"));
                contato.setContato(consult.getString("contato"));
                contato.setEmail(consult.getString("email"));
                contato.setCEP(consult.getString("cep"));
            }
            consult.Close();
            return contato;
        } catch (Exception e) {
            throw new Exception("ContatosDao.selecionarContato - " + e.getMessage() + "\r\n"
                    + "select * from contatos where codigo = " + codigo);
        }
    }

    public void inserirContatoIntegracaoBlip(ContatosIntegracao modelo, Persistencia persistencia) throws Exception {
        Contatos contato = new Contatos();
        String sql = "", Codigo = "";

        try {
            // Verifica se já existe Contato Cadastrado
            sql = " select * from contatos where Obs = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(modelo.getIdentity());
            consult.select();

            while (consult.Proximo()) {
                Codigo = consult.getString("Codigo");
            }
            consult.Close();

            // Tratar Dados
            if ((null == modelo.getTelefone() || modelo.getTelefone().equals(""))
                    && null != modelo.getPhoneNumber() && !modelo.getPhoneNumber().equals("")) {
                modelo.setTelefone(modelo.getPhoneNumber());
            }

            if ((null == modelo.getNomecompleto() || modelo.getNomecompleto().equals(""))
                    && null != modelo.getName() && !modelo.getName().equals("")) {
                modelo.setNomecompleto(modelo.getName());
            }

            if ((null == modelo.getNomecompleto() || modelo.getNomecompleto().equals(""))
                    && null != modelo.getNomedousuario() && !modelo.getNomedousuario().equals("")) {
                modelo.setNomecompleto(modelo.getNomedousuario());
            }

            // Insert ou Update
            sql = "";

            if (null == Codigo || Codigo.equals("")) {
                // Insert
                sql = "INSERT INTO contatos (Codigo, "
                        + "                  CodFil, "
                        + "                  Nome, "
                        + "                  Fantasia, "
                        + "                  Endereco, "
                        + "                  Bairro, "
                        + "                  Cidade, "
                        + "                  CEP, "
                        + "                  UF, "
                        + "                  Fone1, "
                        + "                  Email, "
                        + "                  CNPJ, "
                        + "                  CPF, "
                        + "                  Obs, "
                        + "                  DtSituacao, "
                        + "                  OperIncl, "
                        + "                  Dt_Incl, "
                        + "                  Operador, "
                        + "                  Dt_Alter, "
                        + "                  Hr_Alter) VALUES ( "
                        + " (SELECT (ISNULL(MAX(Codigo),0) + 1) FROM contatos), "
                        + " 1,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";

                consult = new Consulta(sql, persistencia);
                
                consult.setString(modelo.getNomecompleto());
                consult.setString(modelo.getNomecompleto());
                consult.setString(modelo.getEndereco());
                consult.setString(modelo.getEnderecoBairro());
                consult.setString(modelo.getEnderecoCidade());
                consult.setString(modelo.getEnderecoCEP());
                consult.setString(modelo.getEnderecoUF());

                consult.setString(modelo.getTelefone());
                consult.setString(modelo.getEmail());
                if (null == modelo.getCpfCnpj() || modelo.getCpfCnpj().equals("")) {
                    consult.setString(modelo.getCpfCnpj());
                    consult.setString(modelo.getCpfCnpj());
                } else {
                    if (modelo.getCpfCnpj().length() <= 14) {
                        consult.setString("");
                        consult.setString(modelo.getCpfCnpj());
                    } else {
                        consult.setString(modelo.getCpfCnpj());
                        consult.setString("");
                    }
                }
                consult.setString(modelo.getIdentity());
                consult.setString(modelo.getDt_Alter());
                consult.setString("SatServer");
                consult.setString(modelo.getDt_Alter());
                consult.setString("SatServer");
                consult.setString(modelo.getDt_Alter());
                consult.setString(modelo.getHr_Alter());
                
                consult.insert();
                consult.close();
            } else {
                // Update
                sql = "UPDATE contatos "
                        + " SET Nome       = ?, "
                        + "     Fantasia   = ?, "
                        + "     Endereco   = ?, "
                        + "     Bairro     = ?, "
                        + "     Cidade     = ?, "
                        + "     CEP        = ?, "
                        + "     UF         = ?, "
                        + "     Fone1      = ?, "
                        + "     Email      = ?, "
                        + "     CNPJ       = ?, "
                        + "     CPF        = ?, "
                        + "     DtSituacao = ?, "
                        + "     Operador   = ?, "
                        + "     Dt_Alter   = ?, "
                        + "     Hr_Alter   = ?"
                        + " WHERE Codigo = ?";

                consult = new Consulta(sql, persistencia);
                
                consult.setString(modelo.getNomecompleto());
                consult.setString(modelo.getNomecompleto());
                consult.setString(modelo.getEndereco());
                consult.setString(modelo.getEnderecoBairro());
                consult.setString(modelo.getEnderecoCidade());
                consult.setString(modelo.getEnderecoCEP());
                consult.setString(modelo.getEnderecoUF());

                consult.setString(modelo.getTelefone());
                consult.setString(modelo.getEmail());
                if (null == modelo.getCpfCnpj() || modelo.getCpfCnpj().equals("")) {
                    consult.setString("");
                    consult.setString("");
                } else {
                    if (modelo.getCpfCnpj().length() <= 14) {
                        consult.setString("");
                        consult.setString(modelo.getCpfCnpj());
                    } else {
                        consult.setString(modelo.getCpfCnpj());
                        consult.setString("");
                    }
                }
                consult.setString(modelo.getDt_Alter());
                consult.setString("SatServer");
                consult.setString(modelo.getDt_Alter());
                consult.setString(modelo.getHr_Alter());
                
                consult.setString(Codigo);
                
                consult.update();
                consult.close();
            }

        } catch (Exception e) {
            throw new Exception("ContatosDao.inserirContatoIntegracaoBlip - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
