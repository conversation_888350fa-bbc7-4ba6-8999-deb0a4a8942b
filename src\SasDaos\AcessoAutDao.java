/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.AcessoAut;
import SasBeans.AcessoAutArea;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AcessoAutDao {

    public List<AcessoAut> listaAcessos(String dataIni, String dataFim, String codFil, Boolean somentePendentes, String chavePesquisa, String valorPesquisa, String orderBy, Persistencia persistencia) throws Exception {
        String sql = "";
        List<AcessoAut> retorno = new ArrayList<>();

        try {
            sql = "Select \n"
                    + "AcessoAut.Sequencia,\n"
                    + "AcessoAut.CodFil,\n"
                    + "AcessoAut.Situacao,\n"
                    + "AcessoAut.DtInicio,\n"
                    + "AcessoAut.DtFinal,\n"
                    + "AcessoAut.SegSex,\n"
                    + "AcessoAut.Sab,\n"
                    + "AcessoAut.Dom,\n"
                    + "AcessoAut.Fer,\n"
                    + "Pessoa.Nome,\n"
                    + "Pessoa.Situacao PSituacao,\n"
                    + "Pessoa.RG,\n"
                    + "AcessoAut.Solicitante,\n"
                    + "Substring(AcessoAut.Obs,1,100) ObsRes, \n"
                    + "Pessoa.Codigo,\n"
                    + "AcessoAut.Destino,\n"
                    + "AcessoAut.Finalidade,\n"
                    + "AcessoAut.Operador,\n"
                    + "AcessoAut.Dt_Alter,\n"
                    + "AcessoAut.Hr_Alter,\n"
                    + "AcessoAut.OperAut,\n"
                    + "AcessoAut.Dt_Aut,\n"
                    + "AcessoAut.Hr_Aut\n"
                    + "from AcessoAut\n"
                    + "left join Pessoa on (Pessoa.Codigo = AcessoAut.CodPessoa)\n"
                    + "where Sequencia > 0\n"
                    + "and (DtInicio BETWEEN ? AND ?\n"
                    + "  or DtFinal  BETWEEN ? AND ?)\n";

            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                sql += "and AcessoAut.CodFil = ?\n";
            }

            if (somentePendentes) {
                sql += "and AcessoAut.Situacao = ?\n";
            }

            if (null != valorPesquisa && !valorPesquisa.equals("")) {
                chavePesquisa = chavePesquisa.replace("OBSERVACAO", "AcessoAut.Obs");
                chavePesquisa = chavePesquisa.replace("NOME", "Pessoa.Nome");

                sql += "and " + chavePesquisa + " LIKE '%" + valorPesquisa + "%'\n";
            }

            sql += "order by " + orderBy;

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataIni);
            consulta.setString(dataFim);
            consulta.setString(dataIni);
            consulta.setString(dataFim);

            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            if (somentePendentes) {
                consulta.setString("PD");
            }

            consulta.select();
            AcessoAut acessoAut;

            while (consulta.Proximo()) {
                acessoAut = new AcessoAut();

                acessoAut.setCodFil2(consulta.getBigDecimal("CodFil"));
                acessoAut.setCodPessoa(consulta.getString("Codigo"));
                acessoAut.setDestino(consulta.getString("Destino"));
                acessoAut.setDom(consulta.getString("Dom"));
                acessoAut.setDtFinal(consulta.getString("DtFinal"));
                acessoAut.setDtInicio(consulta.getString("DtInicio"));
                acessoAut.setDt_Alter(consulta.getString("Dt_Alter"));
                acessoAut.setDt_Aut(consulta.getString("Dt_Aut"));
                acessoAut.setFer(consulta.getString("Fer"));
                acessoAut.setHr_Alter(consulta.getString("Hr_Alter"));
                acessoAut.setHr_Aut(consulta.getString("Hr_Aut"));
                acessoAut.setObs(consulta.getString("ObsRes"));
                acessoAut.setOperAut(consulta.getString("OperAut"));
                acessoAut.setOperador(consulta.getString("Operador"));
                acessoAut.setSab(consulta.getString("Sab"));
                acessoAut.setSegSex(consulta.getString("SegSex"));
                acessoAut.setSequencia2(consulta.getBigDecimal("Sequencia"));
                acessoAut.setSituacao(consulta.getString("Situacao"));
                acessoAut.setSolicitante(consulta.getString("Solicitante"));
                acessoAut.setNome(consulta.getString("Nome"));
                acessoAut.setPSituacao(consulta.getString("PSituacao"));
                acessoAut.setRG(consulta.getString("RG"));
                acessoAut.setFinalidade(consulta.getString("Finalidade"));

                retorno.add(acessoAut);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessoAutDao.listaAcessos - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<AcessoAutArea> listaAcessosAreas(String sequencia, Persistencia persistencia) throws Exception {
        String sql = "";
        List<AcessoAutArea> retorno = new ArrayList<>();

        try {
            sql = "SELECT AcessoAutArea.*, SegAreas.Descricao\n"
                    + "FROM AcessoAutArea \n"
                    + "LEFT JOIN SegAreas\n"
                    + "  ON AcessoAutArea.CodArea = SegAreas.Codigo\n"
                    + " AND AcessoAutArea.CodFil = SegAreas.CodFil\n"
                    + "WHERE AcessoAutArea.Sequencia = ?\n"
                    + "ORDER BY SegAreas.Descricao";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(sequencia);
            consulta.select();
            AcessoAutArea acessoAutArea;

            while (consulta.Proximo()) {
                acessoAutArea = new AcessoAutArea();
                acessoAutArea.setCodArea(consulta.getBigDecimal("CodArea"));
                acessoAutArea.setCodFil(consulta.getString("CodFil"));
                acessoAutArea.setDescricao(consulta.getString("Descricao"));
                acessoAutArea.setDt_Alter(consulta.getString("Dt_Alter"));
                acessoAutArea.setHrEntrada(consulta.getString("HrEntrada"));
                acessoAutArea.setHrSaida(consulta.getString("HrSaida"));
                acessoAutArea.setHr_Alter(consulta.getString("Hr_Alter"));
                acessoAutArea.setOperador(consulta.getString("Operador"));
                acessoAutArea.setSequencia(consulta.getBigDecimal("Sequencia"));

                retorno.add(acessoAutArea);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessoAutDao.listaAcessosAreas - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<AcessoAutArea> listaCadastrosAreas(String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<AcessoAutArea> retorno = new ArrayList<>();

        try {
            sql = "SELECT * FROM SegAreas WHERE CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(codFil);
            consulta.select();
            AcessoAutArea acessoAutArea;

            while (consulta.Proximo()) {
                acessoAutArea = new AcessoAutArea();
                acessoAutArea.setCodArea(consulta.getBigDecimal("Codigo"));
                acessoAutArea.setCodFil(consulta.getString("CodFil"));
                acessoAutArea.setDescricao(consulta.getString("Descricao"));

                retorno.add(acessoAutArea);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessoAutDao.listaAcessosAreas - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void salvar(AcessoAut autorizacao, List<AcessoAutArea> areas, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            if (null == autorizacao.getSituacao() || autorizacao.getSituacao().equals("")) {
                autorizacao.setSituacao("PD");
            }

            if (null == autorizacao.getSequencia2() || autorizacao.getSequencia2().equals(BigDecimal.ZERO)) {
                sql = "INSERT INTO AcessoAut (Sequencia, CodFil, CodPessoa, DtInicio, DtFinal, SegSex, Sab, Dom, Fer, Solicitante, Destino, Finalidade, Obs, Situacao, Operador, Dt_alter, Hr_alter) VALUES((SELECT (ISNULL(MAX(Sequencia), 0) + 1) FROM AcessoAut),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
            } else {
                sql = "UPDATE AcessoAut"
                        + " SET CodFil      = ?,"
                        + "     CodPessoa   = ?,"
                        + "     DtInicio    = ?,"
                        + "     DtFinal     = ?,"
                        + "     SegSex      = ?,"
                        + "     Sab         = ?,"
                        + "     Dom         = ?,"
                        + "     Fer         = ?,"
                        + "     Solicitante = ?,"
                        + "     Destino     = ?,"
                        + "     Finalidade  = ?,"
                        + "     Obs         = ?,"
                        + "     Situacao    = ?,"
                        + "     Operador    = ?,"
                        + "     Dt_alter    = ?,"
                        + "     Hr_alter    = ?"
                        + " WHERE Sequencia = ?";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            String sequencia = "";

            if (null == autorizacao.getSequencia2() || autorizacao.getSequencia2().equals(BigDecimal.ZERO)) {
                // INSERT
                consulta.setString(autorizacao.getCodFil());
                consulta.setBigDecimal(autorizacao.getCodPessoa());
                consulta.setBigDecimal(autorizacao.getDtInicio());
                consulta.setBigDecimal(autorizacao.getDtFinal());
                consulta.setBigDecimal(autorizacao.getSegSex());
                consulta.setBigDecimal(autorizacao.getSab());
                consulta.setBigDecimal(autorizacao.getDom());
                consulta.setBigDecimal(autorizacao.getFer());
                consulta.setBigDecimal(autorizacao.getSolicitante());
                consulta.setBigDecimal(autorizacao.getDestino());
                consulta.setBigDecimal(autorizacao.getFinalidade());
                consulta.setBigDecimal(autorizacao.getObs());
                consulta.setBigDecimal(autorizacao.getSituacao());
                consulta.setBigDecimal(autorizacao.getOperador());
                consulta.setBigDecimal(autorizacao.getDt_Alter());
                consulta.setBigDecimal(autorizacao.getHr_Alter());

                consulta.insert();

                sql = "SELECT MAX(Sequencia) Sequencia FROM AcessoAut";

                consulta = new Consulta(sql, persistencia);
                consulta.select();

                while (consulta.Proximo()) {
                    sequencia = consulta.getString("Sequencia");
                }
            } else {
                // Update
                consulta.setBigDecimal(autorizacao.getCodFil2());
                consulta.setBigDecimal(autorizacao.getCodPessoa());
                consulta.setBigDecimal(autorizacao.getDtInicio());
                consulta.setBigDecimal(autorizacao.getDtFinal());
                consulta.setBigDecimal(autorizacao.getSegSex());
                consulta.setBigDecimal(autorizacao.getSab());
                consulta.setBigDecimal(autorizacao.getDom());
                consulta.setBigDecimal(autorizacao.getFer());
                consulta.setBigDecimal(autorizacao.getSolicitante());
                consulta.setBigDecimal(autorizacao.getDestino());
                consulta.setBigDecimal(autorizacao.getFinalidade());
                consulta.setBigDecimal(autorizacao.getObs());
                consulta.setBigDecimal(autorizacao.getSituacao());
                consulta.setBigDecimal(autorizacao.getOperador());
                consulta.setBigDecimal(autorizacao.getDt_Alter());
                consulta.setBigDecimal(autorizacao.getHr_Alter());
                consulta.setBigDecimal(autorizacao.getSequencia2());

                consulta.update();

                sequencia = autorizacao.getSequencia2().toPlainString().replace(".0", "");

            }

            sql = "DELETE FROM AcessoAutArea WHERE Sequencia = ? AND CodFil = ?";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(autorizacao.getCodFil());
            consulta.delete();
            sql = "";

            for (AcessoAutArea item : areas) {
                sql += "INSERT INTO AcessoAutArea(Sequencia, CodFil, CodArea, HrEntrada, HrSaida, Operador, Dt_alter, Hr_alter) VALUES(";
                sql += sequencia + ",";
                sql += autorizacao.getCodFil() + ",";
                sql += item.getCodArea().toPlainString().replace(".0", "") + ",";
                sql += "'" + item.getHrEntrada() + "',";
                sql += "'" + item.getHrSaida() + "',";
                sql += "'" + item.getOperador() + "',";
                sql += "'" + item.getDt_Alter() + "',";
                sql += "'" + item.getHr_Alter() + "');";
            }

            if (!sql.equals("")) {
                consulta = new Consulta(sql, persistencia);
                consulta.insert();
            }

            consulta.Close();

        } catch (Exception e) {
            throw new Exception("AcessoAutDao.salvar - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void excluir(String sequencia, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "DELETE FROM AcessoAutArea WHERE Sequencia = ?;\n"
                    + " DELETE FROM AcessoAut WHERE Sequencia = ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(sequencia);
            consulta.delete();
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("AcessoAutDao.excluir - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void autorizar(AcessoAut autorizacao, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "Update AcessoAut set\n"
                    + "Situacao = 'OK',\n"
                    + "OperAut  = ?,\n"
                    + "Dt_Aut   = ?,\n"
                    + "Hr_Aut   = ?\n"
                    + "where Sequencia = ? and (OperAut IS NULL OR OperAut = '')";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(autorizacao.getOperAut());
            consulta.setString(autorizacao.getDt_Aut());
            consulta.setString(autorizacao.getHr_Aut());
            consulta.setBigDecimal(autorizacao.getSequencia2());
            consulta.update();
            
            consulta.Close();

        } catch (Exception e) {
            throw new Exception("AcessoAutDao.autorizar - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
