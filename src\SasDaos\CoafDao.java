/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.CoafComunicacoes;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CoafDao {

    public List<CoafComunicacoes> listagemComunicacoes(String CodFil, String DataInicio, String DataFinal, String Corporativo, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "SELECT\n"
                    + " COAFComunic.*, (Select Count(*) from COAFOCor where Sequencia = COAFComunic.Sequencia) Qtde\n"
                    + " FROM COAFComunic\n"
                    + " WHERE DtInicio >= ?\n"
                    + " AND   DtFinal  <= ?\n";
            if (Corporativo.equals("N")) {
                sql += " AND ﻿COAFComunic.CodFil = ?\n";
            }
            sql += " ORDER BY COAFComunic.Sequencia";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(DataInicio);
            consulta.setString(DataFinal);
            if (Corporativo.equals("N")) {
                consulta.setBigDecimal(CodFil);
            }
            consulta.select();

            List<CoafComunicacoes> listaComunicacoes = new ArrayList<>();
            CoafComunicacoes comunicacao;

            while (consulta.Proximo()) {
                comunicacao = new CoafComunicacoes();

                comunicacao.setCodFil(consulta.getString("Codfil"));
                comunicacao.setDtAlter(consulta.getString("Dt_Alter"));
                comunicacao.setDtFinal(consulta.getString("DtFinal"));
                comunicacao.setDtInicio(consulta.getString("DtInicio"));
                comunicacao.setHrAlter(consulta.getString("Hr_Alter"));
                comunicacao.setOperador(consulta.getString("Operador"));
                comunicacao.setProtocolo(consulta.getString("Protocolo"));
                comunicacao.setQtde(consulta.getString("Qtde"));
                comunicacao.setSequencia(consulta.getString("Sequencia"));

                listaComunicacoes.add(comunicacao);
            }

            consulta.close();
            return listaComunicacoes;

        } catch (Exception e) {
            throw new Exception("CoafDao.listagemComunicacoes - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
