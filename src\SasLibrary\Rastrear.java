package SasLibrary;

import Dados.Consulta;
import Dados.Persistencia;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class Rastrear {

    /**
     * Grava posicao passada na tabela rastrear
     *
     * @param ID_Modulo - ID do modulo
     * @param Latitude - latitude em graus
     * @param Longitude - longitude em graus
     * @param Data - Data GPS
     * @param Hora - Hora GPS
     * @param DtTrans - Data servidor
     * @param HrTrans - Hora servidor
     * @param Satelite - Numero de satélites
     * @param Precisao - Raio de precisão em metros
     * @param Matricula - Matricula do operador
     * @param Batida - ID da Batia
     * @param persistencia - conexão com o banco
     * @throws Exception - pode gerar exceção
     */
    public static void Gravar_Rastrear(String ID_Modulo, String Latitude, String Longitude, String Data, String Hora,
            String DtTrans, String HrTrans, String Satelite, String Precisao, String Matricula,
            String Batida, Persistencia persistencia) throws Exception {
        String sql;
        BigDecimal sequencia = new BigDecimal("0");
        int tentativas = 1;
        try {
            Consulta consult = new Consulta("select MAX(codigo) Codigo from rastrear", persistencia);
            consult.select();
            while (consult.Proximo()) {
                if (consult.getString("Codigo") != null) {
                    sequencia = new BigDecimal(consult.getString(1));
                }
            }
            while (tentativas <= 10) {
                sequencia = sequencia.add(new BigDecimal(String.valueOf(tentativas)));
                sql = "insert into rastrear "
                        + "(Codigo,ID_Modulo,Latitude, Longitude, Data, Hora, DtTrans, HrTrans, Satelite,Precisao,Matr,Batida)"
                        + " values (" + sequencia.toString() + ",'" + ID_Modulo + "','" + Latitude + "','" + Longitude + "','" + Data + "','" + Hora + "','"
                        + DtTrans + "','" + HrTrans + "'," + "'" + Satelite + "','" + Precisao + "','" + Matricula + "','" + Batida + "')";
                try {
//                    persistencia.executaSql(sql);
                    tentativas = 11;
                } catch (Exception e) {
                    tentativas++;
                }
            }
        } catch (Exception e) {
            throw new Exception("Falha ao gravar posicao - " + e.getMessage());
        }
    }
}
