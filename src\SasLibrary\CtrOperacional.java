/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.CtrOperV;
import SasBeansCompostas.LoginRota;
import SasDaos.CtrOpervDao;
import SasDaos.LoginDao;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR> - (05/08/2015)
 */
public class CtrOperacional {

    public static boolean InsereCtrOperV(CtrOperV ctrOperv, String sCodPessoa, String Senha, Persistencia persistencia) throws Exception {
        boolean ret = false;

        CtrOpervDao oCtrOperVDao = new CtrOpervDao();
        //valida login e senha do usuário
        List<LoginRota> list_lrota;
        try {
            list_lrota = LoginDao.LoginRota(sCodPessoa, Senha, persistencia);

        } catch (Exception e) {
            throw new Exception("Falha no login - " + e.getMessage());
        }
        for (LoginRota list_lrota1 : list_lrota) {
            if (!list_lrota1.getPessoa().getPWWeb().equals(Senha)) {
                ret = false;
            } else {
                BigDecimal Numr = oCtrOperVDao.getNumeroCtrOperV(ctrOperv.getCodFil(), persistencia);
                oCtrOperVDao.adicionaCtrOperV(Numr, ctrOperv, persistencia);
                ret = true;
            }
        }

        return ret;
    }
}
