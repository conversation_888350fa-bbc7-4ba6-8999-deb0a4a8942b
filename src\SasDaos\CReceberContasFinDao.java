package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CReceber;
import SasBeans.ContasFin;
import SasBeansCompostas.CReceberContasFin;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CReceberContasFinDao {

    /**
     * Mostra titulos a receber e efetivamente recebidos
     *
     * @param inicio - início do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<CReceberContasFin> ReceberXRecebidos(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<CReceberContasFin> retorno = new ArrayList();
        try {
            String sql = "Select SubString(CReceber.DtPagto,1,6) CompetPg, Sum(Valor) Valor,"
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= ?" //'20160101'"
                    + " and DtPagto <= ?" //'20160630'"
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by SubString(CReceber.DtPagto,1,6)"
                    + " order by CompetPg";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;
            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                creceber.setValor(consult.getString("Valor"));
                creceber.setValorPago(consult.getString("ValorPago"));
                creceber.setDtPagto(consult.getString("CompetPg"));
                creceb.setCreceber(creceber);
                retorno.add(creceb);
            }

            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.ReceberXRecebidos - " + e.getMessage() + "\r\n"
                    + "Select SubString(CReceber.DtPagto,1,6) CompetPg, Sum(Valor) Valor,"
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= " + inicio //'20160101'"
                    + " and DtPagto <= " + fim //'20160630'"
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by SubString(CReceber.DtPagto,1,6)"
                    + " order by CompetPg");
        }
    }

    /**
     *
     * @param inicio
     * @param fim
     * @param persistencia
     * @return
     * @throws java.lang.Exception
     */
    public List<CReceberContasFin> RXRContaFinDatas(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<CReceberContasFin> retorno = new ArrayList();
        try {
            String sql = "Select SubString(CReceber.DtPagto,1,6) CompetPg, ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= ?" //'20160101'" 
                    + " and DtPagto <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by SubString(CReceber.DtPagto,1,6), ContasFin.Descricao"
                    + " order by CompetPg, ContasFin.Descricao";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;
            ContasFin contasfin;
            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                contasfin = new ContasFin();
                creceber.setValor(consult.getString("Valor"));
                creceber.setValorPago(consult.getString("ValorPago"));
                creceber.setDtPagto(consult.getString("CompetPg"));
                contasfin.setDescricao(consult.getString("Descricao"));
                creceb.setCreceber(creceber);
                creceb.setContasfin(contasfin);
                retorno.add(creceb);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.RXRContaFinDatas - " + e.getMessage() + "\r\n"
                    + "Select SubString(CReceber.DtPagto,1,6) CompetPg, ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= " + inicio //'20160101'" 
                    + " and DtPagto <= " + fim //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by SubString(CReceber.DtPagto,1,6), ContasFin.Descricao"
                    + " order by CompetPg, ContasFin.Descricao");
        }
    }

    public List<CReceberContasFin> RXRContaFin(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<CReceberContasFin> retorno = new ArrayList();
        try {
            String sql = "Select ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= ?" //'20160101'" 
                    + " and DtPagto <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by ContasFin.Descricao"
                    + " order by ContasFin.Descricao";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;
            ContasFin contasfin;
            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                contasfin = new ContasFin();
                creceber.setValor(consult.getString("Valor"));
                creceber.setValorPago(consult.getString("ValorPago"));
                contasfin.setDescricao(consult.getString("Descricao"));
                creceb.setCreceber(creceber);
                creceb.setContasfin(contasfin);
                creceb.setDescricao(consult.getString("Descricao"));
                creceb.setValor(FuncoesString.formatarStringMoeda(consult.getString("ValorPago"), false));
                retorno.add(creceb);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.RXRContaFin - " + e.getMessage() + "\r\n"
                    + "Select ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= " + inicio //'20160101'" 
                    + " and DtPagto <= " + fim //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')"
                    + " group by ContasFin.Descricao"
                    + " order by ContasFin.Descricao");
        }
    }

    /**
     * Total de recebimentos no período
     *
     * @param inicio
     * @param fim
     * @param persistencia
     * @return
     * @throws Exception
     */
    public CReceberContasFin TotRec(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        CReceberContasFin retorno = null;
        try {
            String sql = "Select  "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= ?" //'20160101'" 
                    + " and DtPagto <= ?" //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;
            ContasFin contasfin;
            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                contasfin = new ContasFin();
                creceber.setValorPago(consult.getString("ValorPago"));
                creceb.setCreceber(creceber);
                creceb.setContasfin(contasfin);
                retorno = creceb;
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.TotRec - " + e.getMessage() + "\r\n"
                    + "Select  "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto >= " + inicio //'20160101'" 
                    + " and DtPagto <= " + fim //'20160630'" 
                    + " and (Descricao like 'RECEITA%' or Descricao like '%Identificar')");
        }
    }

    /**
     * Mostra titulos a receber e efetivamente recebidos com descriçao não nula
     *
     * @param inicio - início do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<CReceberContasFin> ReceberXRecebidosDescNaoNula(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<CReceberContasFin> retorno = new ArrayList();
        try {

            String sql = " Select SubString(CReceber.DtPagto,1,6) CompetPg, Sum(Valor) Valor,"
                    + " Sum(ValorPago) ValorPago"
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto between ? " //'20160101' 
                    + " and  ?" //'20160630'
                    + " and Descricao IS NOT NULL"
                    + " group by SubString(CReceber.DtPagto,1,6)"
                    + " order by CompetPg";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;

            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                creceber.setValor(consult.getString("Valor"));
                creceber.setValorPago(consult.getString("ValorPago"));
                creceber.setCompetPg(consult.getString("CompetPg"));

                creceb.setCreceber(creceber);
                retorno.add(creceb);

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.ReceberXRecebidosDescNaoNula - " + e.getMessage() + "\r\n"
                    + "Select SubString(CReceber.DtPagto,1,6) CompetPg, Sum(Valor) Valor,"
                    + " Sum(ValorPago) ValorPago"
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto between " + inicio //'20160101' 
                    + " and " + fim //'20160630'
                    + " and Descricao IS NOT NULL"
                    + " group by SubString(CReceber.DtPagto,1,6)"
                    + " order by CompetPg");
        }
    }

    /**
     * Mostra titulos a receber, recebidos, e a descrição dos títulos.
     *
     * @param inicio - início do período
     * @param fim - fim do período
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<CReceberContasFin> RXRContaFinDescricao(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        List<CReceberContasFin> retorno = new ArrayList();
        try {
            String sql = "Select SubString(CReceber.DtPagto,1,6) CompetPg, ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto between ? and ?" //'20160101' and //'20160630'" 
                    + " and Descricao IS NOT NULL "
                    + " group by SubString(CReceber.DtPagto,1,6), ContasFin.Descricao"
                    + " order by CompetPg, ContasFin.Descricao";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CReceberContasFin creceb;
            CReceber creceber;
            ContasFin contasfin;

            while (consult.Proximo()) {
                creceb = new CReceberContasFin();
                creceber = new CReceber();
                contasfin = new ContasFin();
                creceber.setValor(consult.getString("Valor"));
                creceber.setValorPago(consult.getString("ValorPago"));
                creceber.setCompetPg(consult.getString("CompetPg"));
                contasfin.setDescricao(consult.getString("Descricao"));
                creceb.setCreceber(creceber);
                creceb.setContasfin(contasfin);
                retorno.add(creceb);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("CReceberContasFinDao.RXRContaFinDescricao - " + e.getMessage() + "\r\n"
                    + "Select SubString(CReceber.DtPagto,1,6) CompetPg, ContasFin.Descricao, Sum(Valor) Valor, "
                    + " Sum(ValorPago) ValorPago "
                    + " from CReceber"
                    + " left join ContasFin on ContasFin.Codigo = CReceber.ContaFin"
                    + " where dtPagto between " + inicio + " and " + fim //'20160101' and //'20160630'" 
                    + " and Descricao IS NOT NULL "
                    + " group by SubString(CReceber.DtPagto,1,6), ContasFin.Descricao"
                    + " order by CompetPg, ContasFin.Descricao");
        }
    }

}
