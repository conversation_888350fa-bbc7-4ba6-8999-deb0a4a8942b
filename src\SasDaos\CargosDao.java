/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cargos;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CargosDao {

    /**
     * Busca um cargo pelo código
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Cargos getCargo(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " select * from cargos where codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            Cargos cargo = new Cargos();
            while (consulta.Proximo()) {
                cargo.setCBO(consulta.getString("cbo"));
                cargo.setCargo(consulta.getString("cargo"));
                cargo.setCodigo(consulta.getString("codigo"));
                cargo.setDescricao(consulta.getString("descricao"));
                cargo.setDt_Alter(consulta.getString("dt_Alter"));
                cargo.setFuncao(consulta.getString("funcao"));
                cargo.setHr_Alter(consulta.getString("hr_alter"));
                cargo.setOperador(consulta.getString("operador"));
                cargo.setSituacao(consulta.getString("situacao"));
            }
            consulta.Close();
            return cargo;
        } catch (Exception e) {
            throw new Exception("CargosDao.getCargo - " + e.getMessage() + "\r\n"
                    + " select * from cargo where codigo = " + codigo);
        }
    }

    /**
     * Busca uma lista de cargos pela descrição do cargo
     *
     * @param descricao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Cargos> buscarCargos(String descricao, Persistencia persistencia) throws Exception {
        try {
            List<Cargos> retorno = new ArrayList<>();
            String sql = " select * from cargos ";
            if (!descricao.equals("")) {
                sql += " where descricao like ? and situacao = 'A'";
            } else {
                sql += " where situacao = 'A' ";
            }
            sql += " ORDER BY Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            if (!descricao.equals("")) {
                consulta.setString("%" + descricao + "%");
            }
            consulta.select();
            Cargos cargo;
            while (consulta.Proximo()) {
                cargo = new Cargos();
                cargo.setCBO(consulta.getString("cbo"));
                cargo.setCargo(consulta.getString("cargo"));
                cargo.setCodigo(consulta.getString("codigo"));
                cargo.setDescricao(consulta.getString("descricao"));
                cargo.setDt_Alter(consulta.getString("dt_Alter"));
                cargo.setFuncao(consulta.getString("funcao"));
                cargo.setHr_Alter(consulta.getString("hr_alter"));
                cargo.setOperador(consulta.getString("operador"));
                cargo.setSituacao(consulta.getString("situacao"));
                retorno.add(cargo);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CargosDao.buscarCargos - " + e.getMessage() + "\r\n"
                    + " select * from cargos "
                    + " where descricao like %" + descricao
                    + "% and situacao = 'A' ");
        }
    }

    /**
     * Busca a lista de cargos
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Cargos> listarCargos(Persistencia persistencia) throws Exception {
        try {
            List<Cargos> retorno = new ArrayList<>();
            String sql = " select * from cargos "
                    + " where  situacao = 'A' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Cargos cargo;
            while (consulta.Proximo()) {
                cargo = new Cargos();
                cargo.setCBO(consulta.getString("cbo"));
                cargo.setCargo(consulta.getString("cargo"));
                cargo.setCodigo(consulta.getString("codigo"));
                cargo.setDescricao(consulta.getString("descricao"));
                cargo.setDt_Alter(consulta.getString("dt_Alter"));
                cargo.setFuncao(consulta.getString("funcao"));
                cargo.setHr_Alter(consulta.getString("hr_alter"));
                cargo.setOperador(consulta.getString("operador"));
                cargo.setSituacao(consulta.getString("situacao"));
                retorno.add(cargo);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CargosDao.buscarCargos - " + e.getMessage() + "\r\n"
                    + " select * from cargos "
                    + " where situacao = 'A' ");
        }
    }
}
