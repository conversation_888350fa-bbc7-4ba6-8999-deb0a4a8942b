package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxForte;
import SasBeansCompostas.CxFEntradasDTO;
import SasBeansCompostas.CxFGuiasGTVDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CxForteDao {

    private Persistencia persistencia;

    public CxForteDao() {
    }

    public CxForteDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Verifica se uma parada é caixa forte
     *
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean paradaCxForte(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT CxForte.CodCli FROM CxForte \n"
                    + "LEFT JOIN Rt_Perc ON Rt_Perc.CodCli1 = CxForte.CodCli\n"
                    + "                    AND Rt_Perc.CodFil = CxForte.CodFil\n"
                    + "where Rt_Perc.sequencia = ? AND Rt_Perc.Parada = ?;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();
            boolean retorno = consulta.Proximo();
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxForteDao.paradaCxForte - " + e.getMessage() + "\r\n"
                    + "SELECT CxForte.CodCli FROM CxForte \n"
                    + "LEFT JOIN Rt_Perc ON Rt_Perc.CodCli1 = CxForte.CodCli\n"
                    + "                    AND Rt_Perc.CodFil = CxForte.CodFil\n"
                    + "where Rt_Perc.sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada + ";");
        }
    }

    public void entradaAutomatizadaCxForte(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            String sql = "--Declarando variaveis---------------------------\n"
                    + "Declare @seqRota Int;\n"
                    + "Declare @parada Int;\n"
                    + "Declare @data date;\n"
                    + "Declare @hora Varchar(05);\n"
                    + "Declare @cxforte varchar(07);\n"
                    + "Declare @cxforteNred Varchar(20);\n"
                    + "Declare @cxforteRegiao Varchar(03);\n"
                    + "Declare @serie Varchar(3);\n"
                    + "Declare @gtv float;\n"
                    + "Declare @operador Varchar(10);\n"
                    + "Declare @codCliParada varchar(07);\n"
                    + "-------------------------------------------------\n"
                    + "--Parametros-------------------------------------\n"
                    + "set @seqRota = ? ;\n"
                    + "set @parada = ? ;\n"
                    + "set @data = Convert(date,getdate());\n"
                    + "set @hora = Substring(Convert(Varchar,getdate(),114),1,5);\n"
                    + "-------------------------------------------------\n"
                    + "--Definindo codigo CXForte-----------------------\n"
                    + "Select top 1 \n"
                    + "@cxforte = Clientes.Codigo,\n"
                    + "@cxforteNred = Clientes.Nred,\n"
                    + "@cxforteRegiao = Clientes.Regiao\n"
                    + "from CxForte \n"
                    + "Left join Clientes  on Clientes.Codigo = CxForte.CodCli\n"
                    + "					and Clientes.CodFil = CxForte.CodFil\n"
                    + "where CxForte.CodFil = (Select CodFil from Rotas (Nolock) where Sequencia = @seqRota) \n"
                    + "Order By DtFecha Desc;\n"
                    + "-------------------------------------------------\n"
                    + "--Verificando cliente da parada enviada----------\n"
                    + "Select @codCliParada = CodCli1 from Rt_Perc (NoLock) where Sequencia = @seqRota and Parada = @parada;\n"
                    + "-------------------------------------------------\n"
                    + "--Caso parada seja CXF---------------------------\n"
                    + "if(@codCliParada = @cxforte) Begin\n"
                    + "	--Buscando guias pendentes de entrada------------\n"
                    + "	Select \n"
                    + "	Rotas.Sequencia, Rotas.Data, Rotas.Rota, Rotas.CodFil, Rt_Perc.CodCli1, Rt_Guias.Guia,\n"
                    + "	Rt_Guias.Serie, Rt_Guias.OS, Rt_Guias.Valor, Rt_Perc.Hora1, OS_Vig.CliDst, pCheEquip.Codigo CodPessoa\n"
                    + "	into #Guias\n"
                    + "	From Rotas (Nolock)\n"
                    + "	Left join Rt_Perc (Nolock)  on Rt_Perc.Sequencia = Rotas.Sequencia\n"
                    + "	Inner join Rt_Guias (Nolock)  on Rt_Guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "						 and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "	Left join Escala (Nolock)  on Escala.SeqRota = Rotas.Sequencia\n"
                    + "	Left join Pessoa pCheEquip  (Nolock) on pCheEquip.Matr = Escala.MatrChe\n"
                    + "	Left join OS_Vig (Nolock)  on OS_Vig.OS = Rt_Guias.OS\n"
                    + "							  and OS_Vig.Codfil = Rotas.CodFil\n"
                    + "	where Rotas.Sequencia = @seqRota\n"
                    + "	  and Rt_Perc.CodCli2 in (Select CodCli from CxForte (Nolock) where CxForte.CodFil = Rotas.CodFil)\n"
                    + "	  and Rt_Guias.Guia not in (Select Guia from CxfGuias (Nolock) Where CxfGuias.Serie = Rt_Guias.Serie);\n"
                    + "	-------------------------------------------------\n"
                    + "	--while de inserção------------------------------\n"
                    + "	WHILE (SELECT count(*) FROM #guias) > 0\n"
                    + "	BEGIN \n"
                    + "\n"
                    + "		Select top 01\n"
                    + "		@gtv = Guia,\n"
                    + "		@serie = Serie,\n"
                    + "		@operador = 'SPM-'+Replicate('0',6-len(Convert(Varchar,convert(BigInt,CodPessoa))))+Convert(Varchar,convert(BigInt,CodPessoa))\n"
                    + "		From #guias;\n"
                    + "\n"
                    + "		Insert into CxFGuias (CodFil, CodCli, Guia, Serie, CliOri, CliDst, Valor, Tipo, RotaEnt, OperEnt, DtEnt, HrEnt, SeqRota, OS, Hora1, Volumes,\n"
                    + "							  VolDh, ValorDh, VolCh, ValorCh, VolMd, ValorMd, VolMet, ValorMet, VolMEstr, ValorMEstr, VolOutr, ValorOutr )\n"
                    + "		Select CodFil,@cxforte, Guia, Serie, CodCli1, CliDst, Valor, 1, Replicate('0',3-len(Convert(Varchar,Convert(BigInt,Rota))))+Convert(Varchar,Convert(BigInt,Rota)),   \n"
                    + "		@operador, @data, @hora, Sequencia, OS, Hora1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\n"
                    + "		from #guias where Guia = @gtv and Serie = @serie;\n"
                    + "\n"
                    + "		--Verificando existencia volume\n"
                    + "		if((Select isnull(Count(*),0) from CxfGuiasVol where Guia = @gtv and Serie = @serie) = 0) begin\n"
                    + "			Insert into cxfGuiasVol (CodFil, CodCli, Guia, Serie, Ordem, Qtde, Lacre, Tipo, Valor, OBS)\n"
                    + "			Select CodFil, @cxforte, Guia, Serie, 1, 1, '', 1, Valor, '' from #guias where Guia = @gtv and Serie = @serie;\n"
                    + "		end\n"
                    + "\n"
                    + "		Delete from #guias where Guia = @gtv and Serie = @serie;\n"
                    + "	   IF (SELECT count(*) FROM #guias) = 0\n"
                    + "		  BREAK  \n"
                    + "	   ELSE  \n"
                    + "		  CONTINUE  \n"
                    + "	END  \n"
                    + "	Drop table #guias;\n"
                    + "END";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxForteDao.entradaAutomatizadaCxForte - " + e.getMessage());
        }
    }

    /**
     * Busca o código de caixa forte da filial
     *
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão ao banco de dados
     * @return - cxforte com apenas o codcli preenchido, esse codcli é o código
     * do caixa forte da filial
     * @throws Exception
     */
    public CxForte getCxForte(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        CxForte retorno = new CxForte();
        Consulta consult;
        String sql;
        sql = "select clientes.email, clientes.NRed, cxforte.* "
                + " from cxforte "
                + " left join clientes on clientes.codigo  = cxforte.codcli "
                + "                    and clientes.codfil = cxforte.codfil "
                + " where cxforte.codfil = ? ";
        try {
            consult = new Consulta(sql, persistencia);
            consult.setString(CodFil.toString());
            consult.select();
            if (consult.Proximo()) {
                retorno.setCodCli(consult.getString("codcli"));
                retorno.setEmail(consult.getString("email"));
                retorno.setNRed(consult.getString("NRed"));

                retorno.setCodFil(consult.getString("CodFil"));
                retorno.setSaldo(consult.getString("Saldo"));
                retorno.setSaldoReal(consult.getString("SaldoReal"));
                retorno.setRemPendQtde(consult.getString("RemPendQtde")); // Qtde. Remessas Custódia
                retorno.setRemPendValor(consult.getString("RemPendValor")); // Valor Remessas Custódia
                retorno.setRemPrepQtde(consult.getString("RemPrepQtde")); // Qtde. Remessas Preparadas
                retorno.setRemPrepValor(consult.getString("RemPrepValor")); // Qtde. Remessas Preparadas Valor
                retorno.setOperador(consult.getString("Operador"));
                retorno.setDt_Alter(consult.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consult.getString("Hr_Alter"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxForteDao.getCxForte  - " + e.getMessage() + "\r\n"
                    + "select cxforte.codcli, clientes.email "
                    + " from cxforte "
                    + " left join clientes on clientes.codigo  = cxforte.codcli "
                    + "                    and clientes.codfil = cxforte.codfil "
                    + " where cxforte.codfil = " + CodFil);
        }
    }

    /**
     * Busca lista de caixas forte da Filial
     *
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão ao banco de dados
     * @return - cxforte lista de caixas forte
     * @throws Exception
     */
    public List<CxForte> listarCaixasForte(String CodFil, Persistencia persistencia) throws Exception {
        List<CxForte> retorno = new ArrayList<>();
        CxForte cxForte;
        Consulta consult;
        String sql;
        sql = "select clientes.email, clientes.NRed, cxforte.*, filiais.descricao descrFilial, cxfSaidas.*, cxfEntradas.* "
                + " from cxforte "
                + " left join clientes on clientes.codigo  = cxforte.codcli "
                + "                    and clientes.codfil = cxforte.codfil "
                + " left join filiais on filiais.codfil = cxforte.codfil"
                + " left join (SELECT\n"
                + "            A.dt_saida,\n"
                + "            A.hr_saida,\n"
                + "            B.valor valorSaida,\n"
                + "            B.oper_saida,\n"
                + "            A.codfil codFilSaida,\n"
                + "            B.codRemessa\n"
                + "            FROM (SELECT\n"
                + "                 A.dt_saida,\n"
                + "                 A.codfil,\n"
                + "                 max(hr_saida) hr_saida\n"
                + "                 FROM(SELECT\n"
                + "                      MAX(dt_saida) dt_saida,\n"
                + "                      Codfil\n"
                + "                      FROM cxfSaidas\n"
                + "                      GROUP BY codfil) AS A\n"
                + "                 JOIN cxfSaidas        AS B\n"
                + "                   ON A.dt_saida = B.dt_saida\n"
                + "                  AND A.Codfil   = B.Codfil\n"
                + "                 GROUP BY A.codfil, A.dt_saida) AS A\n"
                + "            JOIN cxfSaidas AS B\n"
                + "              ON A.codfil   = B.codfil\n"
                + "             AND A.dt_saida = B.dt_saida\n"
                + "             AND A.hr_saida = B.hr_saida) cxfSaidas\n"
                + "  on cxfSaidas.codFilSaida  = cxforte.codfil\n"
                + " left join (SELECT\n"
                + "            A.dtEnt,\n"
                + "            A.hrEnt,\n"
                + "            B.valor valorEntrada,\n"
                + "            B.operEnt,\n"
                + "            A.codfil codFilEnt,\n"
                + "            B.guia\n"
                + "            FROM (SELECT\n"
                + "                 A.dtEnt,\n"
                + "                 A.codfil,\n"
                + "                 max(hrEnt) hrEnt\n"
                + "                 FROM(SELECT\n"
                + "                      MAX(dtEnt) dtEnt,\n"
                + "                      Codfil\n"
                + "                      FROM cxfGuias\n"
                + "                      GROUP BY codfil) AS A\n"
                + "                 JOIN cxfGuias         AS B\n"
                + "                   ON A.dtEnt = B.dtEnt\n"
                + "                  AND A.Codfil   = B.Codfil\n"
                + "                 GROUP BY A.codfil, A.dtEnt) AS A\n"
                + "            JOIN cxfGuias AS B\n"
                + "              ON A.codfil   = B.codfil\n"
                + "             AND A.dtEnt = B.dtEnt\n"
                + "             AND A.hrEnt = B.hrEnt) cxfEntradas\n"
                + "  on cxfEntradas.codFilEnt  = cxforte.codfil\n";

        if (null != CodFil && !CodFil.equals("0")) {
            sql += " where cxforte.codfil = ? ";
        }
        sql += " order by filiais.descricao, cxfEntradas.guia desc, cxfSaidas.codRemessa ";

        try {
            consult = new Consulta(sql, persistencia);
            if (null != CodFil && !CodFil.equals("0")) {
                consult.setString(CodFil);
            }
            consult.select();

            String CodFilCarregado = "";

            while (consult.Proximo()) {
                cxForte = new CxForte();

                if (!CodFilCarregado.equals(consult.getString("CodFil").replace(".0", ""))) {

                    cxForte.setCodCli(consult.getString("codcli"));
                    cxForte.setEmail(consult.getString("email"));
                    cxForte.setNRed(consult.getString("NRed"));

                    cxForte.setCodFil(consult.getString("CodFil").replace(".0", ""));
                    cxForte.setSaldo(consult.getString("Saldo"));
                    cxForte.setSaldoReal(consult.getString("SaldoReal"));
                    cxForte.setRemPendQtde(consult.getString("RemPendQtde")); // Qtde. Remessas Custódia
                    cxForte.setRemPendValor(consult.getString("RemPendValor")); // Valor Remessas Custódia
                    cxForte.setRemPrepQtde(consult.getString("RemPrepQtde")); // Qtde. Remessas Preparadas
                    cxForte.setRemPrepValor(consult.getString("RemPrepValor")); // Qtde. Remessas Preparadas Valor
                    cxForte.setOperador(consult.getString("Operador"));
                    cxForte.setDt_Alter(consult.getLocalDate("Dt_Alter"));
                    cxForte.setHr_Alter(consult.getString("Hr_Alter"));

                    cxForte.setFilial(consult.getString("descrFilial"));
                    cxForte.setUltMovDataEntrada(consult.getString("dtEnt"));
                    cxForte.setUltMovDataSaida(consult.getString("dt_saida"));
                    cxForte.setUltMovHoraEntrada(consult.getString("hrEnt"));
                    cxForte.setUltMovHoraSaida(consult.getString("hr_saida"));
                    cxForte.setUltMovOperadorEntrada(consult.getString("operEnt"));
                    cxForte.setUltMovOperadorSaida(consult.getString("oper_saida"));
                    cxForte.setUltMovValorEntrada(consult.getString("valorEntrada"));
                    cxForte.setUltMovValorSaida(consult.getString("valorSaida"));

                    retorno.add(cxForte);
                }
                
                CodFilCarregado = consult.getString("CodFil").replace(".0", "");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CxForteDao.listarCaixasForte  - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    /**
     * Lista de caixas fortes do cliente
     *
     * @param codFil codigo da filia
     * @param persistencia conexao com o banco de dados
     * @return lista de caixas fortes
     * @throws Exception
     */
    public List<CxForte> caixasFortes(BigDecimal codFil, Persistencia persistencia) throws Exception {
        List<CxForte> caixasForte = new ArrayList<>();
        try {
            String sql = "SELECT codcli FROM cxforte WHERE codfil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codFil);
            consulta.select();

            CxForte cxForte = null;
            while (consulta.Proximo()) {
                cxForte = new CxForte();
                cxForte.setCodCli(consulta.getString("codcli"));
                caixasForte.add(cxForte);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("CxForteDao.caixasFortes  - " + e.getMessage() + "\r\n"
                    + "SELECT codcli FROM cxforte WHERE codfil = " + codFil);
        }
        return caixasForte;
    }

    public List<CxFEntradasDTO> listaTodasEntradasCxForte(Map filters) throws Exception {
        String rota = (String) filters.get("rota");
        String sequencia = (String) filters.get("sequencia");
        String codFil = (String) filters.get("codFil");
        String data = (String) filters.get("data");
        boolean checked = (boolean) filters.get("checked");

        String sql = "SELECT\nRt_perc.Sequencia,\nRt_Perc.Parada,\nRt_Perc.CodCli1,\n"
                + "Rt_Perc.Hora1,\nRt_Perc.ER,\nRt_Perc.TipoSrv,\nRt_Perc.NRed,\n"
                + "Rt_Perc.Flag_Excl,\nRt_Perc.OperExcl,\nMAx(Rt_Perc.Observ) Observ,\n"
                + "MAX(CxFGuias.PedidoDst) PedidoDst,\nSUM(CXFguias.Valor) AS Valor,\n"
                + "MAX(CXFGuias.Guia) GuiaMax\n"
                + "FROM\nRt_Perc\nLEFT JOIN rotas ON\n"
                + "Rotas.Sequencia = Rt_Perc.Sequencia\n"
                + "Left JOIN CXFGuias ON\n"
                + "CXFGuias.SeqRota = Rt_Perc.Sequencia\nAND CXFGuias.Hora1 = Rt_Perc.Hora1\n"
                + "WHERE\nRotas.Rota = ? \n"
                + " AND Rotas.data = ? \n"
                + " AND Rotas.codfil = ? \n";
        sql += "AND (\n"
                + "    (Rt_Perc.CodCli1 = ? AND Rt_Perc.ER = 'E') "
                + "    OR (Rt_Perc.Flag_Excl <> '*' ";

        if (checked) {
            sql += "        AND Rt_Perc.CodCLi2 = ? \n";
        }

        sql += "))\nGROUP BY\nRt_perc.Sequencia,\nRt_Perc.CodCli1,\nRt_Perc.Hora1,\n"
                + "Rt_Perc.ER,\nRt_Perc.TipoSrv,\nRt_Perc.Parada,\nRt_Perc.NRed,\n"
                + "Rt_Perc.Flag_Excl,\nRt_Perc.OperExcl\n"
                + "ORDER BY Rt_Perc.Hora1, Rt_Perc.parada;";

        List<CxFEntradasDTO> caixasForte = new ArrayList<>();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rota);
            consulta.setString(data);
            consulta.setBigDecimal(codFil);
            consulta.setString(sequencia);
            if (checked) {
                consulta.setString(sequencia);
            }
            consulta.select();

            while (consulta.Proximo()) {
                CxFEntradasDTO cxForte = new CxFEntradasDTO();
                cxForte.setSequencia(consulta.getString("Sequencia"));
                cxForte.setCodCli1(consulta.getString("CodCli1"));
                cxForte.setHora1(consulta.getString("Hora1"));
                cxForte.setER(consulta.getString("ER"));
                cxForte.setTipoSrv(consulta.getString("TipoSrv"));
                cxForte.setParada(consulta.getString("Parada"));
                cxForte.setNRed(consulta.getString("NRed"));
                cxForte.setFlag_Excl(consulta.getString("Flag_Excl"));
                cxForte.setOperExcl(consulta.getString("OperExcl"));
                cxForte.setObserv(consulta.getString("Observ"));
                cxForte.setPedidoDst(consulta.getString("PedidoDst"));
                cxForte.setValor(consulta.getString("Valor"));

                caixasForte.add(cxForte);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return caixasForte;
    }

    public int contagemEntradasCxForte(Map filters) throws Exception {
        String rota = (String) filters.get("rota");
        String sequencia = (String) filters.get("sequencia");
        String codFil = (String) filters.get("codFil");
        String data = (String) filters.get("data");
        boolean checked = (boolean) filters.get("checked");

        String sql = "SELECT COUNT(*) AS total\n FROM (\n"
                + "    SELECT Rt_perc.Sequencia FROM\nRt_Perc\nLEFT JOIN rotas ON\n"
                + "    Rotas.Sequencia = Rt_Perc.Sequencia\n"
                + "    Left JOIN CXFGuias ON\n"
                + "    CXFGuias.SeqRota = Rt_Perc.Sequencia\nAND CXFGuias.Hora1 = Rt_Perc.Hora1\n"
                + "    WHERE\nRotas.Rota = ? \n"
                + "     AND Rotas.data = ? \n"
                + "     AND Rotas.codfil = ? \n";
        sql += "    AND (\n"
                + "        (Rt_Perc.CodCli1 = ? AND Rt_Perc.ER = 'E') "
                + "        OR (Rt_Perc.Flag_Excl <> '*' ";

        if (checked) {
            sql += "            AND Rt_Perc.CodCLi2 = ? \n";
        }

        sql += "    ))\nGROUP BY\nRt_perc.Sequencia,\nRt_Perc.CodCli1,\nRt_Perc.Hora1,\n"
                + "    Rt_Perc.ER,\nRt_Perc.TipoSrv,\nRt_Perc.Parada,\nRt_Perc.NRed,\n"
                + "     Rt_Perc.Flag_Excl,\nRt_Perc.OperExcl\n"
                + ") src;";

        int total = 0;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(rota);
            consulta.setString(data);
            consulta.setBigDecimal(codFil);
            consulta.setString(sequencia);
            if (checked) {
                consulta.setString(sequencia);
            }
            consulta.select();

            if (consulta.Proximo()) {
                total = consulta.getInt("total");
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return total;
    }

    public void saidaCxForte(String valor, String codFil, String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxForte set \n"
                    + " Saldo        = Saldo - ?, \n"
                    + " RemPrepQtde  = RemPrepQtde  + 1, \n"
                    + " RemPrepValor = RemPrepValor + ?, \n"
                    + " RemPendQtde  = RemPendQtde  - 1,\n"
                    + " RemPendValor = RemPendValor - ?\n"
                    + " where CodFil = ?\n"
                    + "   and CodCli = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(valor);
            consulta.setString(valor);
            consulta.setString(codFil);
            consulta.setString(codigo);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxForteDao.saidaCxForte - " + e.getMessage() + "\r\n"
                    + "Update CxForte set \n"
                    + " Saldo        = Saldo - " + valor + ", \n"
                    + " RemPrepQtde  = RemPrepQtde  + 1, \n"
                    + " RemPrepValor = RemPrepValor + " + valor + ", \n"
                    + " RemPendQtde  = RemPendQtde  - 1,\n"
                    + " RemPendValor = RemPendValor - " + valor + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and CodCli = " + codigo);
        }
    }

    public List<CxFGuiasGTVDTO> getCxForteEntBuscaGTV(String codFil, String guia, String serie, String sequencia) throws Exception {
        String sql = "SELECT Rotas.Rota, Rotas.Data, Rt_perc.Sequencia, Rt_Perc.CodCli1,\n"
                + "       Rt_Perc.NRed, Rt_Perc.Hora1, Rt_Perc.ER, Rt_Perc.Parada,\n"
                + "       OS_Vig.NRed, OS_Vig.CliDst, OS_Vig.NRedDst,\n"
                + "       GTV.Guia, GTV.Serie,\n"
                + "       Rt_Perc.Valor   TotalGeral,\n"
                + "       GTV.CodFil, GTV.OS,\n"
                + "       Rt_Perc.Operador,\n"
                + "       Rt_Guias.Valor  ValorGTV,\n"
                + "       Rt_Guias.Parada ParadaRtGuias\n"
                + "FROM GTV\n"
                + "         LEFT JOIN OS_Vig ON GTV.OS = OS_Vig.OS\n"
                + "    AND GTV.CodFil = OS_Vig.CodFil\n"
                + "         LEFT JOIN Rt_Perc ON OS_Vig.Cliente = Rt_Perc.CodCli1\n"
                + "    AND Rt_Perc.Flag_Excl <> '*'\n"
                + "    AND (Rt_Perc.ER = 'R'\n"
                + "        OR Rt_Perc.ER = 'T')\n"
                + "    AND Rt_Perc.Sequencia = ? \n"
                + "         LEFT JOIN Rt_Guias ON Rt_Guias.Sequencia = Rt_perc.Sequencia\n"
                + "    AND Rt_Guias.Guia = ? \n"
                + "    AND Rt_Guias.Serie = ? \n"
                + "         LEFT JOIN Rotas ON Rotas.Sequencia = Rt_perc.Sequencia\n"
                + "    AND Rotas.CodFil = GTV.CodFil\n"
                + "WHERE GTV.Guia = ? \n"
                + "  AND GTV.Serie = ? \n"
                + "  AND GTV.CodFil = ? ;";

        List<CxFGuiasGTVDTO> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                CxFGuiasGTVDTO item = new CxFGuiasGTVDTO();
                item.setRota(consulta.getString("Rota"));
                item.setData(consulta.getDate("Data"));
                item.setSequencia(consulta.getString("Sequencia"));
                item.setCodCli1(consulta.getString("CodCli1"));
                item.setHora1(consulta.getString("Hora1"));
                item.setER(consulta.getString("ER"));
                item.setParada(consulta.getInt("Parada"));
                item.setNRed(consulta.getString("NRed"));
                item.setCliDst(consulta.getString("CliDst"));
                item.setNRedDst(consulta.getString("NRedDst"));
                item.setGuia(consulta.getString("Guia"));
                item.setSerie(consulta.getString("Serie"));
                item.setTotalGeral(consulta.getBigDecimal("TotalGeral"));
                item.setCodFil(consulta.getString("CodFil"));
                item.setOS(consulta.getString("OS"));
                item.setOperador(consulta.getString("Operador"));
                item.setValorGTV(consulta.getBigDecimal("ValorGTV"));
                item.setParadaRtGuias(consulta.getString("ParadaRtGuias"));
//                item.setNRedOri(consulta.getString("NRedOri"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public List<CxFGuiasGTVDTO> getCxForteEntBuscaTesSaida(String codFil, String guia, String serie, String sequencia) throws Exception {
        String sql = "SELECT Rotas.Rota,\n"
                + "       Rotas.Data,\n"
                + "       Rt_perc.Sequencia,\n"
                + "       Rt_Perc.CodCli1,\n"
                + "       Rt_Perc.Hora1,\n"
                + "       Rt_Perc.ER,\n"
                + "       Rt_Perc.Parada,\n"
                + "       Rt_Perc.Nred,\n"
                + "       Clientes.NRed     NRedDst,\n"
                + "       CliOri.Nred       NRedOri,\n"
                + "       TesSaidas.Codcli1 CliOri,\n"
                + "       TesSaidas.CodCli2 CliDst,\n"
                + "       TesSaidas.Guia,\n"
                + "       TesSaidas.Serie,\n"
                + "       TesSaidas.CodFil,\n"
                + "       TesSaidas.TotalGeral,\n"
                + "       TesSaidas.ChequesValor,\n"
                + "       GTV.OS,\n"
                + "       Rt_Perc.Operador,\n"
                + "       Rt_Guias.Valor    ValorGTV,\n"
                + "       Rt_Guias.Parada   ParadaRtGuias\n"
                + "FROM TesSaidas\n"
                + "         LEFT JOIN Rt_Perc ON TesSaidas.Codcli1 = Rt_Perc.CodCli1\n"
                + "    AND Rt_Perc.flag_excl <> '*'\n"
                + "    AND Rt_Perc.ER = 'R'\n"
                + "    AND Rt_Perc.Sequencia = ? \n"
                + "         LEFT JOIN Rt_Guias ON Rt_Guias.Sequencia = Rt_perc.Sequencia\n"
                + "    AND Rt_Guias.Guia = ? \n"
                + "    AND Rt_Guias.Serie = ? \n"
                + "         LEFT JOIN Rotas ON Rotas.Sequencia = Rt_perc.Sequencia\n"
                + "    AND Rotas.CodFil = TesSaidas.CodFil\n"
                + "         LEFT JOIN Clientes ON Clientes.Codigo = TesSaidas.CodCli2\n"
                + "    AND Clientes.CodFil = TesSaidas.CodFil\n"
                + "         LEFT JOIN Clientes CliOri ON CliOri.Codigo = TesSaidas.CodCli1\n"
                + "    AND CliOri.CodFil = TesSaidas.CodFil\n"
                + "         LEFT JOIN GTV ON GTV.Guia = TesSaidas.Guia\n"
                + "    AND GTV.Serie = TesSaidas.Serie\n"
                + "WHERE TesSaidas.Guia = ? \n"
                + "  AND TesSaidas.Serie = ? \n"
                + "  AND TesSaidas.CodFil = ? ;";

        List<CxFGuiasGTVDTO> lista = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);

            consulta.select();
            while (consulta.Proximo()) {
                CxFGuiasGTVDTO item = new CxFGuiasGTVDTO();
                item.setRota(consulta.getString("Rota"));
                item.setData(consulta.getDate("Data"));
                item.setSequencia(consulta.getString("Sequencia"));
                item.setCodCli1(consulta.getString("CodCli1"));
                item.setHora1(consulta.getString("Hora1"));
                item.setER(consulta.getString("ER"));
                item.setParada(consulta.getInt("Parada"));
                item.setNRed(consulta.getString("NRed"));
                item.setCliDst(consulta.getString("CliDst"));
                item.setNRedDst(consulta.getString("NRedDst"));
                item.setGuia(consulta.getString("Guia"));
                item.setSerie(consulta.getString("Serie"));
                item.setTotalGeral(consulta.getBigDecimal("TotalGeral"));
                item.setCodFil(consulta.getString("CodFil"));
                item.setOS(consulta.getString("OS"));
                item.setOperador(consulta.getString("Operador"));
                item.setValorGTV(consulta.getBigDecimal("ValorGTV"));
                item.setParadaRtGuias(consulta.getString("ParadaRtGuias"));
                item.setCliOri(consulta.getString("CliOri"));
                item.setNRedOri(consulta.getString("NRedOri"));

                lista.add(item);
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return lista;
    }

    public void deletarGuiaSerie(
            String codFil,
            String codCXF,
            String guia,
            String serie,
            String operador
    ) throws Exception {
        String sql = "DECLARE @guia int;\n"
                + "DECLARE @serie varchar(03);\n"
                + "DECLARE @codfil float;\n"
                + "DECLARE @cxforte varchar(07);\n"
                + "DECLARE @operador varchar(10);\n"
                + "\n"
                + "SET @guia = ?;\n"
                + "SET @serie = ?;\n"
                + "SET @cxforte = ?;\n"
                + "SET @codfil = ?;\n"
                + "SET @operador = ?;\n"
                + "\n"
                + "BEGIN TRANSACTION\n"
                + "\n"
                + "    INSERT INTO SASLog(Sequencia, Tabela, CodFil, Comando, Historico, Operador, Data, Hora)\n"
                + "    VALUES ((SELECT isnull(Max(Sequencia), 0) + 1 FROM Saslog),\n"
                + "            'CxfGuias',\n"
                + "            @codfil,\n"
                + "            'Exclusão entrada CXforte',\n"
                + "            'Guia: ' + Convert(varchar, Convert(bigint, @guia)) + ' Serie: ' + @serie,\n"
                + "            @operador,\n"
                + "            Convert(varchar, getdate(), 112),\n"
                + "            Substring(Convert(varchar, getdate(), 114), 1, 5))\n"
                + "\n"
                + "    UPDATE CxForte\n"
                + "    SET RemPendQtde  = RemPendQtde - 1\n"
                // + "        , RemPendValor = RemPendValor - @valor,\n"
                // + "        , SaldoReal    = SaldoReal - @valor,\n"
                // + "        , Saldo        = Saldo - @valor\n"
                + "    WHERE CodFil = @codfil\n"
                + "      AND CodCli = @cxforte;\n"
                + "\n"
                + "    DELETE\n"
                + "    FROM CxfGuias\n"
                + "    WHERE Guia = @guia\n"
                + "      AND Serie = @serie;\n"
                + "\n"
                + "    DELETE\n"
                + "    FROM CxfGuiasVol\n"
                + "    WHERE Guia = @guia\n"
                + "      AND Serie = @serie\n"
                + "      AND Guia NOT IN (SELECT Guia\n"
                + "                       FROM Rt_Guias\n"
                + "                       WHERE Rt_Guias.Guia = CxfGuiasVol.Guia\n"
                + "                         AND Rt_Guias.Serie = CxfGuiasVol.Serie);\n"
                + "\n"
                + "COMMIT";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.setString(codCXF);
            consulta.setString(operador);

            consulta.delete();
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

}
