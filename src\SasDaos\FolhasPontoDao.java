/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * AND open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.FolhaPonto;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class FolhasPontoDao {

    private String sqlSelect, sqlTotalFuncionarios, sqlTabelas, sqlGrupo,
            sqlOrdem, sqlPaginacao, sqlTotalClose;

    public FolhasPontoDao() {
        FolhasPontoDao();
    }
    
    public FolhasPontoDao(String recebeOrderBy) {
        FolhasPontoDao();
        sqlOrdem = "ORDER BY " + recebeOrderBy + "\n";
    }
    
    private void FolhasPontoDao(){
                sqlSelect = "SELECT Funcion.CodFil, PstServ.Secao, PstServ.Local Posto,\n"
                + "STR(Funcion.Matr, 10) Matr, Funcion.Nome,\n"
                + "Funcion.CTPS_Nro, Funcion.CTPS_Serie, RhHorario.Descricao Horario, Cargos.Descricao, Clientes.Nome NomeCliente, Clientes.Nred\n";

        sqlTotalFuncionarios = "SELECT COUNT(*) total FROM (\n" + sqlSelect;

        sqlTabelas = "FROM RH_Horas_XPE (Nolock)\n"
                + "LEFT JOIN Funcion ON Funcion.Matr = RH_Horas_XPE.Matr\n"
                + "LEFT JOIN PstServ ON PstServ.Secao = Funcion.Secao\n"
                + "AND PstServ.CodFil = Funcion.CodFil\n"
                + "LEFT JOIN RhHorario ON RhHorario.Codigo = Funcion.Horario\n"
                + "AND RhHorario.CodFil = Funcion.CodFil\n"
                + "LEFT JOIN Cargos AS Cargos ON Cargos.Cargo = Funcion.Cargo\n"
                + "LEFT JOIN Clientes ON PstServ.CodCLi = Clientes.Codigo AND Funcion.CodFil = Clientes.CodFil\n"
                + "WHERE (RH_Horas_XPE.Data BETWEEN ? AND ?)\n";

        sqlGrupo = "GROUP BY PstServ.Local, Funcion.Matr, Funcion.Nome, Funcion.CodFil,"
                + "PstServ.Secao, Funcion.CTPS_Nro, Funcion.CTPS_Serie, RhHorario.Descricao, Cargos.Descricao, Clientes.Nome, Clientes.Nred\n";

        sqlOrdem = "ORDER BY PstServ.Local, Funcion.Nome\n";

        sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

        sqlTotalClose = ") total\n";

    }

    private void setConsultaFuncionarios(Consulta consulta, String codFil, String posto, String matr, String nome) throws Exception {
        try {
            if (!codFil.equals("")) {
                consulta.setString(codFil);
            }
            if (!posto.equals("")) {
                consulta.setString("%" + posto + "%");
            }
            if (!matr.equals("")) {
                consulta.setString("%" + matr + "%");
            }
            if (!nome.equals("")) {
                consulta.setString("%" + nome + "%");
            }
        } catch (Exception e) {
            throw new Exception("Filtros de pesquisa inválidos - " + e.getMessage());
        }
    }

    private String getClausulaWhere(String codFil, String posto, String matr, String nome) {
        String where = "";
        if (!codFil.equals("")) {
            where += "AND Funcion.CodFil = ? \n";
        }
        if (!posto.equals("")) {
            where += "AND PstServ.Local LIKE ? \n";
        }
        if (!matr.equals("")) {
            where += "AND STR(Funcion.Matr, 10, 10) LIKE ?\n";
        }
        if (!nome.equals("")) {
            where += "AND Funcion.Nome LIKE ? \n";
        }

        return where;
    }

    private List<FolhaPonto> getConsulta(Consulta consulta) throws Exception {
        List<FolhaPonto> pontos = new ArrayList();
        while (consulta.Proximo()) {
            FolhaPonto ponto = new FolhaPonto();

            ponto.setCodFil(consulta.getString("CodFil"));
            ponto.setMatr(consulta.getString("Matr"));
            ponto.setSecao(consulta.getString("Secao"));
            ponto.setPosto(consulta.getString("Posto"));
            ponto.setNome(consulta.getString("Nome"));
            ponto.setHorario(consulta.getString("Horario"));
            ponto.setCargo(consulta.getString("Descricao"));
            ponto.setCTPS_Nro(consulta.getString("CTPS_Nro"));
            ponto.setCTPS_Serie(consulta.getString("CTPS_Serie"));
            
            ponto.setCliente(consulta.getString("NomeCliente"));

            pontos.add(ponto);
        }

        return pontos;
    }

    private Integer getTotal(Consulta consulta) throws Exception {
        Integer retorno = 0;
        while (consulta.Proximo()) {
            retorno = consulta.getInt("total");
        }
        return retorno;
    }

    public List<FolhaPonto> getPaginadoSemFiltro(
            int primeiro,
            int linhas,
            Map filters,
            Persistencia persistencia
    ) throws Exception {
        try {
            String codFil = (String) filters.get("codFil");
            String sDt_Ini = (String) filters.get("dataInicio");
            String sDt_Fim = (String) filters.get("dataFim");
            String matricula = (String) filters.get("matricula");
            String nome = (String) filters.get("nome");
            String posto = (String) filters.get("posto");
            List<FolhaPonto> pontos;
            String where = getClausulaWhere(codFil, posto, matricula, nome);
            String sql = sqlSelect + sqlTabelas + where + sqlGrupo + sqlOrdem + sqlPaginacao;
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sDt_Ini);
            consulta.setString(sDt_Fim);
            setConsultaFuncionarios(consulta, codFil, posto, matricula, nome);
            consulta.setInt(primeiro);
            consulta.setInt(linhas);

            consulta.select();
            pontos = getConsulta(consulta);
            consulta.Close();
            return pontos;
        } catch (Exception e) {
            throw new Exception("Falha ao retornar dados da função - " + e.getMessage());
        }
    }

    public Integer getQuantidade(Map filters, Persistencia persistencia) throws Exception {
        try {
            String codFil = (String) filters.get("codFil");
            String sDt_Ini = (String) filters.get("dataInicio");
            String sDt_Fim = (String) filters.get("dataFim");
            String matricula = (String) filters.get("matricula");
            String nome = (String) filters.get("nome");
            String posto = (String) filters.get("posto");
            String where = getClausulaWhere(codFil, posto, matricula, nome);
            String sql = sqlTotalFuncionarios + sqlTabelas + where + sqlGrupo + sqlTotalClose;
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sDt_Ini);
            consulta.setString(sDt_Fim);
            setConsultaFuncionarios(consulta, codFil, posto, matricula, nome);

            consulta.select();
            Integer total = getTotal(consulta);
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("Falha ao retornar dados da função - " + e.getMessage());
        }
    }
}
