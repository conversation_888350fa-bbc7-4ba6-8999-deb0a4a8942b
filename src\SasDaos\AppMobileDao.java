package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.AppMobile;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AppMobileDao {

    /**
     * Busca apps da plataforma
     *
     * @param Plataforma - plataforma que deseja-se buscar apps
     * @param idioma
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<AppMobile> CarregaApps(String Plataforma, String idioma, Persistencia persistencia) throws Exception {
        try {
            List<AppMobile> retorno = new ArrayList();
            String sql = "select appmobile.sequencia, appmobiledesc.nome, appmobiledesc.descricao, "
                    + " appmobile.pacote, appmobile.plataforma, appmobile.parametro, "
                    + "       parametmobile.versao, parametmobile.versaoaceita "
                    + " from appmobile "
                    + " left join parametmobile on parametmobile.sequencia = appmobile.sequencia "
                    + " left join appmobiledesc on appmobiledesc.sequencia = appmobile.sequencia "
                    + " where appmobile.plataforma = ? and appmobiledesc.idioma = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Plataforma);
            consult.setString(idioma);
            consult.select();
            AppMobile appmobile;
            while (consult.Proximo()) {
                appmobile = new AppMobile();
                appmobile.setSequencia(consult.getString("sequencia"));
                appmobile.setNome(consult.getString("nome"));
                appmobile.setDescricao(consult.getString("descricao"));
                appmobile.setPacote(consult.getString("pacote"));
                appmobile.setPlataforma(consult.getString("plataforma"));
                appmobile.setParametro(consult.getString("parametro"));
                appmobile.setVersaoAceita(consult.getString("versaoaceita"));
                appmobile.setVersaoAtual(consult.getString("versao"));
                retorno.add(appmobile);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AppMobileDao.CarregaApps - " + e.getMessage() + "\r\n"
                    + "select appmobile.sequencia, appmobiledesc.nome, appmobiledesc.descricao, "
                    + " appmobile.pacote, appmobile.plataforma, appmobile.parametro, "
                    + "       parametmobile.versao, parametmobile.versaoaceita "
                    + " from appmobile "
                    + " left join parametmobile on parametmobile.sequencia = appmobile.sequencia "
                    + " left join appmobiledesc on appmobiledesc.sequencia = appmobile.sequencia "
                    + " where appmobile.plataforma = " + Plataforma + "  and appmobiledesc.idioma = " + idioma);
        }
    }

    public AppMobile CarregaApps(String Plataforma, String idioma, String nome, Persistencia persistencia) throws Exception {
        try {
            String sql = "select appmobile.sequencia, appmobiledesc.nome, appmobiledesc.descricao, "
                    + " appmobile.pacote, appmobile.plataforma, appmobile.parametro, "
                    + "       parametmobile.versao, parametmobile.versaoaceita "
                    + " from appmobile "
                    + " left join parametmobile on parametmobile.sequencia = appmobile.sequencia "
                    + " left join appmobiledesc on appmobiledesc.sequencia = appmobile.sequencia "
                    + " where appmobile.plataforma = ? and appmobiledesc.idioma = ? and appmobiledesc.nome = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Plataforma);
            consult.setString(idioma);
            consult.setString(nome);
            consult.select();
            AppMobile appmobile = new AppMobile();
            if (consult.Proximo()) {
                appmobile.setSequencia(consult.getString("sequencia"));
                appmobile.setNome(consult.getString("nome"));
                appmobile.setDescricao(consult.getString("descricao"));
                appmobile.setPacote(consult.getString("pacote"));
                appmobile.setPlataforma(consult.getString("plataforma"));
                appmobile.setParametro(consult.getString("parametro"));
                appmobile.setVersaoAceita(consult.getString("versaoaceita"));
                appmobile.setVersaoAtual(consult.getString("versao"));
            }
            consult.Close();
            return appmobile;
        } catch (Exception e) {
            throw new Exception("AppMobileDao.CarregaApps - " + e.getMessage() + "\r\n"
                    + "select appmobile.sequencia, appmobiledesc.nome, appmobiledesc.descricao, "
                    + " appmobile.pacote, appmobile.plataforma, appmobile.parametro, "
                    + "       parametmobile.versao, parametmobile.versaoaceita "
                    + " from appmobile "
                    + " left join parametmobile on parametmobile.sequencia = appmobile.sequencia "
                    + " left join appmobiledesc on appmobiledesc.sequencia = appmobile.sequencia "
                    + " where appmobile.plataforma = " + Plataforma + " and appmobiledesc.idioma = " + idioma + " and nome = " + nome);
        }
    }
}
