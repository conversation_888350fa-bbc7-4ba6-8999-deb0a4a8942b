package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.*;
import SasBeansCompostas.UsuarioSatMobWeb;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Complete CRUD for Pessoa class
 *
 * <AUTHOR>
 */
public class PessoaDao {

    public boolean validarSenha(String matricula, String pwweb, Persistencia persistencia) throws Exception {
       String sql = "";
        try {
             sql = " SELECT \n"
                    + "     PWWeb, PwPortal \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Matr = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();
            boolean retorno = false;
            if (consulta.Proximo()) {
                retorno = consulta.getString("PWWeb").replace(".0", "").equals(pwweb);
                
                if(!retorno){
                    retorno = consulta.getString("PwPortal").replace(".0", "").equals(pwweb);
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.validarSenha - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Busca flag de envio de batida de ponto por Whatsapp
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean getEnvPontoWhp(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT \n"
                    + "     EnvPontoWhp \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            boolean retorno = false;
            if (consulta.Proximo()) {
                retorno = consulta.getInt("EnvPontoWhp") == 1;
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getEnvPontoWhp - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     EnvPontoWhp \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = " + codigo + " \n");
        }
    }

    
    public Pessoa getEnvPontoConfiguracoes(String codigo, Persistencia persistencia) throws Exception {
        Pessoa pessoa = new Pessoa();
        String sql = "";
        
        try {
             sql = " SELECT \n"
                    + "     EnvPontoEmail, EnvPontoWhp, email \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            
            while(consulta.Proximo()){
                pessoa.setEmail(consulta.getString("email"));
                pessoa.setEnvPontoEmail(consulta.getString("EnvPontoEmail"));
                pessoa.setEnvPontoWhp(consulta.getString("EnvPontoWhp"));
            }
            consulta.close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getEnvPontoConfiguracoes - " + e.getMessage() + "\r\n" + sql);
        }
    }
    
    /**
     * Busca flag de envio de batida de ponto por email
     *
     * @param codigo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean getEnvPontoEmail(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT \n"
                    + "     EnvPontoEmail \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = ? \n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            boolean retorno = false;
            if (consulta.Proximo()) {
                retorno = consulta.getInt("EnvPontoEmail") == 1;
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getEnvPontoEmail - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     EnvPontoEmail \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = " + codigo + " \n");
        }
    }

    /**
     * Lista todas as pessoas para o tipo de pergunta Pessoa em inspeções.
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> listarPessoasInspecao(String data, String codFil, String operador, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList<>();
            String sql = "Declare @tipoPessoa Varchar(01)\n"
                    + "Declare @data Date\n"
                    + "Declare @codfil int\n"
                    + "Declare @hora1 Varchar(05)\n"
                    + "Declare @qtdEscala int\n"
                    + "Declare @qtdPessoa int\n"
                    + "Declare @operador Varchar(10)\n"
                    + "-------------------------------------------------\n"
                    + "--Definindo parametros---------------------------\n"
                    + "Set @data = ?\n"
                    + "Set @codfil = ?\n"
                    + "Set @hora1 = '08:00'\n"
                    + "Set @tipoPessoa = 'L'\n"
                    + "Set @operador = ?\n"
                    + "-------------------------------------------------\n"
                    + "Select @qtdEscala = Isnull(Count(*),0) From EscalaInd (NoLock) where data = @data and CodFil = @codfil\n"
                    + "Select @qtdPessoa = Isnull(Count(*),0) From pessoa where Situacao = @tipoPessoa\n"
                    + "if (@qtdEscala < @qtdPessoa) begin\n"
                    + "	Insert into EscalaInd (CodFil, Matr, Data, Hora1, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n"
                    + "	Select @codfil, Codigo, @data, @hora1,Substring('AUT-'+@operador,1,10), Convert(Varchar,Getdate(),112), Substring(Convert(Varchar,Getdate(),108),1,5),'' \n"
                    + "	From pessoa where Situacao = @tipoPessoa\n"
                    + "	and Codigo not in (Select Matr from EscalaInd (Nolock) where Data = @data AND EscalaInd.CodFil = @codfil AND Flag_Excl <> '*' and Operador Like 'AUT-%')\n"
                    + "end\n"
                    + "SELECT \n"
                    + "    Pessoa.Nome, Pessoa.Codigo, EscalaInd.* \n"
                    + "FROM \n"
                    + "    EscalaInd \n"
                    + "LEFT JOIN \n"
                    + "    Pessoa ON Pessoa.Codigo = EscalaInd.Matr\n"
                    + "WHERE \n"
                    + "    Data = @data AND EscalaInd.CodFil = @codfil AND Flag_Excl <> '*'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codFil);
            consulta.setString(operador);
            consulta.select();
            Pessoa pessoa;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consulta.getString("Codigo"));
                pessoa.setNome(consulta.getString("Nome"));

                retorno.add(pessoa);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.listarPessoasInspecao - " + e.getMessage() + "\r\n"
                    + "Declare @tipoPessoa Varchar(01)\n"
                    + "Declare @data Date\n"
                    + "Declare @codfil int\n"
                    + "Declare @hora1 Varchar(05)\n"
                    + "Declare @qtdEscala int\n"
                    + "Declare @qtdPessoa int\n"
                    + "Declare @operador Varchar(10)\n"
                    + "-------------------------------------------------\n"
                    + "--Definindo parametros---------------------------\n"
                    + "Set @data = " + data + "\n"
                    + "Set @codfil = " + codFil + "\n"
                    + "Set @hora1 = '08:00'\n"
                    + "Set @tipoPessoa = 'L'\n"
                    + "Set @operador = " + operador + "\n"
                    + "-------------------------------------------------\n"
                    + "Select @qtdEscala = Isnull(Count(*),0) From EscalaInd (NoLock) where data = @data and CodFil = @codfil\n"
                    + "Select @qtdPessoa = Isnull(Count(*),0) From pessoa where Situacao = @tipoPessoa\n"
                    + "if (@qtdEscala < @qtdPessoa) begin\n"
                    + "	Insert into EscalaInd (CodFil, Matr, Data, Hora1, Operador, Dt_Alter, Hr_Alter, Flag_Excl)\n"
                    + "	Select @codfil, Codigo, @data, @hora1,Substring('AUT-'+@operador,1,10), Convert(Varchar,Getdate(),112), Substring(Convert(Varchar,Getdate(),108),1,5),'' \n"
                    + "	From pessoa where Situacao = @tipoPessoa\n"
                    + "	and Codigo not in (Select Matr from EscalaInd (Nolock) where Data = @data and Operador Like 'AUT-%')\n"
                    + "end\n"
                    + "SELECT \n"
                    + "    Pessoa.Nome, Pessoa.Codigo, EscalaInd.* \n"
                    + "FROM \n"
                    + "    EscalaInd \n"
                    + "LEFT JOIN \n"
                    + "    Pessoa ON Pessoa.Codigo = EscalaInd.Matr\n"
                    + "WHERE \n"
                    + "    Data = @data AND Flag_Excl <> '*'");
        }
    }

    public List<Pessoa> listarOperadoresCofre(String data, String codCofre, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList<>();
            Pessoa pessoa;
            String sql = "SELECT Pessoa.Codigo, Pessoa.Nome, count(*) Obs \n"
                    + "FROM TesCofresMov\n"
                    + "INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + " WHERE TesCofresMov.Data = ? AND TesCofresMov.codCofre = ?\n"
                    + "GROUP BY Pessoa.Nome, Pessoa.Codigo";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codCofre);
            consulta.select();
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consulta.getString("codigo"));
                pessoa.setNome(consulta.getString("Nome"));
                pessoa.setObs(consulta.getString("Obs"));

                retorno.add(pessoa);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.listarOperadoresCofre - " + e.getMessage() + "\r\n"
                    + "SELECT Pessoa.Codigo, Pessoa.Nome\n"
                    + "FROM TesCofresMov\n"
                    + "INNER JOIN Pessoa ON Pessoa.Codigo = TesCofresMov.idUsuario\n"
                    + "   WHERE TesCofresMov.Data =  " + data + " AND TesCofresMov.codCofre = " + codCofre + "\n"
                    + "GROUP BY Pessoa.Nome, Pessoa.Codigo");
        }
    }

    public List<Pessoa> listarOperadoresCofreRelatorios(String data, String codCofre, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Pessoa> retorno = new ArrayList<>();
            Pessoa pessoa;
            sql = "SELECT TesCofresMov.NomeUsuario, count(*) Obs \n"
                    + " FROM TesCofresMov\n"
                    + " WHERE TesCofresMov.Data = ? AND TesCofresMov.codCofre = ? AND TesCofresMov.NomeUsuario <> 'Aut-Satellite'\n"
                    + " GROUP BY TesCofresMov.NomeUsuario ORDER BY TesCofresMov.NomeUsuario";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(data);
            consulta.setString(codCofre);
            consulta.select();

            int Contador = 0;

            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(Integer.toString(Contador++));
                pessoa.setNome(consulta.getString("NomeUsuario").replace("_", ""));
                pessoa.setObs(consulta.getString("Obs"));

                retorno.add(pessoa);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.listarOperadoresCofre - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<Pessoa> buscarPessoaCPFEmail(String email, String cpf, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList<>();
            String sql = "SELECT * FROM Pessoa \n"
                    + "WHERE email = ? OR CPF = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpf);
            consulta.setString(email);
            consulta.select();
            Pessoa pessoa;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consulta.getString("Codigo"));
                pessoa.setNome(consulta.getString("Nome"));
                pessoa.setEmail(consulta.getString("Email"));
                pessoa.setCPF(consulta.getString("CPF"));
                pessoa.setPWWeb(consulta.getString("PWWEb"));
                retorno.add(pessoa);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscarPessoaCPFEmail - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM Pessoa \n"
                    + "WHERE email = " + email + " OR CPF = " + cpf);
        }
    }

    public List<Pessoa> listarPrestadoresServicoDatas(String dataIni, String dataFim, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList<>();
            String sql = "SELECT * FROM Pessoa\n"
                    + "WHERE Codigo IN "
                    + "(SELECT PessoaTrajeto.CodPessoa \n"
                    + "FROM PessoaTrajeto \n"
                    + "WHERE DtCompet BETWEEN ? AND ? GROUP BY PessoaTrajeto.CodPessoa)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataIni);
            consulta.setString(dataFim);
            consulta.select();
            Pessoa pessoa;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consulta.getString("Codigo"));
                pessoa.setNome(consulta.getString("Nome"));
                pessoa.setCPF(consulta.getString("CPF"));
                pessoa.setEndereco(consulta.getString("Endereco"));
                pessoa.setPWWeb(consulta.getString("PWWEb"));
                pessoa.setSituacao(consulta.getString("Situacao"));
                pessoa.setFuncao(consulta.getString("Funcao"));
                retorno.add(pessoa);

            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.listarPrestadoresServicoDatas - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM Pessoa\n"
                    + "WHERE Codigo IN "
                    + "(SELECT PessoaTrajeto.CodPessoa \n"
                    + "FROM PessoaTrajeto \n"
                    + "WHERE DtCompet BETWEEN ? AND ? GROUP BY PessoaTrajeto.CodPessoa)");
        }
    }

    /**
     * Insert into 'Pessoa' table
     *
     * @param sCodigo
     * @param pessoa A variable of type Pessoa
     * @param persistencia Connect with database
     * @return 'retorno' A boolean value
     * @throws java.lang.Exception
     * @throw Exception e
     */
    public boolean gravaPessoaCand(String sCodigo, PessoaPRH pessoa, Persistencia persistencia) throws Exception {
        boolean retorno;
        String sql = "insert into Pessoa (Codigo ,Nome ,Endereco ,Bairro ,Cidade ,UF ,CEP ,Fone1 ,Fone2 ,Email ,EstCivil "
                + ",Conjuge ,Pai ,Mae ,RG ,RGOrgEmis ,RgDtEmis , CPF, pretSalario, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat, "
                + " Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc, Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, "
                + " Peso, Indicacao, Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt, CarNacVig, DtValCNV, Dt_Recicl, "
                + " Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta, Obs, Funcao, Dt_Alter, Hr_Alter, Naturalid_UF, Mae_Prof, Mae_Nacion, Pai_Prof, Pai_Nacion, "
                + " Conjuge_Prof, Conjuge_Nasc, Olhos, Cabelo, DefeitosFis, Tatuagem, Camisa, Sapato, Calca, Jaqueta, Tipo_Moradia, Religiao, "
                + " Religiao_Prat, Reservista_RM, NomeEscola, PorteArma, Situacao, Nacionalidade, Ct_Banco, Ct_Agencia, Ct_Conta "
                + "Values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,"
                + " ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sCodigo.replace(".0", ""));
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getEstCivil());
            consulta.setString(pessoa.getConjuge());
            consulta.setString(pessoa.getPai());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getRgDtEmis());
            consulta.setString(pessoa.getCPF());
            consulta.setFloat(Float.parseFloat(pessoa.getPretSalario()));
            consulta.setString(pessoa.getTitEleit());
            consulta.setString(pessoa.getTitZona());
            consulta.setString(pessoa.getTitSecao());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHDtVenc());
            consulta.setString(pessoa.getCNHCat());
            consulta.setString(pessoa.getReservista());
            consulta.setString(pessoa.getReservCat());
            consulta.setString(pessoa.getCTPS_Nro());
            consulta.setString(pessoa.getCTPS_Serie());
            consulta.setString(pessoa.getCTPS_UF());
            consulta.setString(pessoa.getCTPS_Emis());
            consulta.setString(pessoa.getDt_nasc());
            consulta.setString(pessoa.getNaturalid());
            consulta.setInt(pessoa.getInstrucao());
            consulta.setString(pessoa.getSexo());
            consulta.setString(pessoa.getRaca());
            consulta.setString(pessoa.getGrupoSang());
            consulta.setString(pessoa.getAltura());
            consulta.setString(pessoa.getPeso());
            consulta.setString(pessoa.getIndicacao());
            consulta.setString(pessoa.getDt_FormIni());
            consulta.setString(pessoa.getDt_FormFim());
            consulta.setString(pessoa.getLocalForm());
            consulta.setString(pessoa.getCertific());
            consulta.setString(pessoa.getReg_PF());
            consulta.setString(pessoa.getReg_PFUF());
            consulta.setString(pessoa.getReg_PFDt());
            consulta.setString(pessoa.getCarNacVig());
            consulta.setString(pessoa.getDtValCNV());
            consulta.setString(pessoa.getDt_Recicl());
            consulta.setString(pessoa.getDt_VenCurs());
            consulta.setString(pessoa.getExtTV());
            consulta.setString(pessoa.getExtSPP());
            consulta.setString(pessoa.getExtEscolta());
            consulta.setString(pessoa.getObs());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(pessoa.getNaturalid_UF());
            consulta.setString(pessoa.getMae_Prof());
            consulta.setString(pessoa.getMae_Nacion());
            consulta.setString(pessoa.getPai_Prof());
            consulta.setString(pessoa.getPai_Nacion());
            consulta.setString(pessoa.getConjuge_Prof());
            consulta.setString(pessoa.getConjuge_Nasc());
            consulta.setString(pessoa.getOlhos());
            consulta.setString(pessoa.getCabelo());
            consulta.setString(pessoa.getDefeitosFis());
            consulta.setString(pessoa.getTatuagem());
            consulta.setInt(pessoa.getCamisa());
            consulta.setInt(pessoa.getSapato());
            consulta.setInt(pessoa.getCalca());
            consulta.setInt(pessoa.getJaqueta());
            consulta.setString(pessoa.getTipo_Moradia());
            consulta.setString(pessoa.getReligiao());
            consulta.setString(pessoa.getReligiao_Prat());
            consulta.setString(pessoa.getReservista_RM());
            consulta.setString(pessoa.getNomeEscola());
            consulta.setString(pessoa.getPorteArma());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getNacionalidade());
            consulta.setString(pessoa.getCt_Banco());
            consulta.setString(pessoa.getCt_Agencia());
            consulta.setString(pessoa.getCt_Conta());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("PessoaDao.gravaPessoaCand - " + e.getMessage() + "\r\n"
                    + "insert into Pessoa (Codigo ,Nome ,Endereco ,Bairro ,Cidade ,UF ,CEP ,Fone1 ,Fone2 ,Email ,EstCivil "
                    + ",Conjuge ,Pai ,Mae ,RG ,RGOrgEmis ,RgDtEmis , CPF, pretSalario, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat, "
                    + " Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc, Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, "
                    + " Peso, Indicacao, Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt, CarNacVig, DtValCNV, Dt_Recicl, "
                    + " Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta, Obs, Funcao, Dt_Alter, Hr_Alter, Naturalid_UF, Mae_Prof, Mae_Nacion, Pai_Prof, Pai_Nacion, "
                    + " Conjuge_Prof, Conjuge_Nasc, Olhos, Cabelo, DefeitosFis, Tatuagem, Camisa, Sapato, Calca, Jaqueta, Tipo_Moradia, Religiao, "
                    + " Religiao_Prat, Reservista_RM, NomeEscola, PorteArma, Situacao, Nacionalidade, Ct_Banco, Ct_Agencia, Ct_Conta "
                    + "Values (" + sCodigo.replace(".0", "") + "," + pessoa.getNome() + "," + pessoa.getEndereco() + "," + pessoa.getBairro() + ","
                    + pessoa.getCidade() + "," + pessoa.getUF() + "," + pessoa.getCEP() + "," + pessoa.getFone1() + "," + pessoa.getFone2() + "," + pessoa.getEmail() + ","
                    + pessoa.getEstCivil() + "," + pessoa.getConjuge() + "," + pessoa.getPai() + "," + pessoa.getMae() + "," + pessoa.getRG() + ","
                    + pessoa.getRGOrgEmis() + "," + pessoa.getRgDtEmis() + "," + pessoa.getCPF() + "," + Float.parseFloat(pessoa.getPretSalario()) + ","
                    + pessoa.getTitEleit() + "," + pessoa.getTitZona() + "," + pessoa.getTitSecao() + "," + pessoa.getPIS() + "," + pessoa.getCNH() + ","
                    + pessoa.getCNHDtVenc() + "," + pessoa.getCNHCat() + "," + pessoa.getReservista() + "," + pessoa.getReservCat() + "," + pessoa.getCTPS_Nro() + ","
                    + pessoa.getCTPS_Serie() + "," + pessoa.getCTPS_UF() + "," + pessoa.getCTPS_Emis() + "," + pessoa.getDt_nasc() + "," + pessoa.getNaturalid() + ","
                    + pessoa.getInstrucao() + "," + pessoa.getSexo() + "," + pessoa.getRaca() + "," + pessoa.getGrupoSang() + "," + pessoa.getAltura() + ","
                    + pessoa.getPeso() + "," + pessoa.getIndicacao() + "," + pessoa.getDt_FormIni() + "," + pessoa.getDt_FormFim() + "," + pessoa.getLocalForm() + ","
                    + pessoa.getCertific() + "," + pessoa.getReg_PF() + "," + pessoa.getReg_PFUF() + "," + pessoa.getReg_PFDt() + "," + pessoa.getCarNacVig() + ","
                    + pessoa.getDtValCNV() + "," + pessoa.getDt_Recicl() + "," + pessoa.getDt_VenCurs() + "," + pessoa.getExtTV() + "," + pessoa.getExtSPP() + ","
                    + pessoa.getExtEscolta() + "," + pessoa.getObs() + "," + pessoa.getFuncao() + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ","
                    + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + "," + pessoa.getNaturalid_UF() + "," + pessoa.getMae_Prof() + "," + pessoa.getMae_Nacion() + ","
                    + pessoa.getPai_Prof() + "," + pessoa.getPai_Nacion() + "," + pessoa.getConjuge_Prof() + "," + pessoa.getConjuge_Nasc() + "," + pessoa.getOlhos() + ","
                    + pessoa.getCabelo() + "," + pessoa.getDefeitosFis() + "," + pessoa.getTatuagem() + "," + pessoa.getCamisa() + "," + pessoa.getSapato() + ","
                    + pessoa.getCalca() + "," + pessoa.getJaqueta() + "," + pessoa.getTipo_Moradia() + "," + pessoa.getReligiao() + "," + pessoa.getReligiao_Prat() + ","
                    + pessoa.getReservista_RM() + "," + pessoa.getNomeEscola() + "," + pessoa.getPorteArma() + "," + pessoa.getSituacao() + ","
                    + pessoa.getNacionalidade() + "," + pessoa.getCt_Banco() + "," + pessoa.getCt_Agencia() + "," + pessoa.getCt_Conta() + ")");
        }
        return retorno;
    }

    public void atualizPessNSat(String sCodPessoWeb, PessoaPRH pessoa, Persistencia persistencia) throws Exception {
        String sql = " update pessoa set "
                + " Nome = ?, "
                + "CPF = ?, "
                + "RG = ?, "
                + "RGOrgEmis = ?, "
                + "RgDtEmis = ?, "
                + "Situacao = ?, "
                + "CEP = ?, "
                + "Fone1 = ?, "
                + "Fone2 = ?, "
                + "Endereco = ?, "
                + "Bairro = ?, "
                + " Cidade = ?, "
                + "UF = ?, "
                + "Email = ?, "
                + "Funcao = ?, "
                + "Obs = ?, "
                + "Nacionalidade = ?, "
                + "Naturalid = ?, "
                + "Dt_nasc = ?, "
                + "Sexo = ?, "
                + "Instrucao = ?, "
                + " GrupoSang = ?, "
                + "Raca = ?, "
                + "EstCivil = ?, "
                + "Conjuge = ?, "
                + "Pai = ?, "
                + "Mae = ?, "
                + "PIS = ?, "
                + "CTPS_Nro = ?, "
                + "CTPS_Serie = ?, "
                + "CTPS_UF = ?, "
                + " CTPS_Emis = ?, "
                + "CNH = ?, "
                + "CNHCat = ?, "
                + "CNHDtVenc = ?, "
                + "Reservista = ?, "
                + "ReservCat = ?, "
                + "TitEleit = ?, "
                + "TitZona = ?, "
                + "TitSecao = ?, "
                + " Altura = ?, "
                + "Peso = ?, "
                + "Dt_Alter = ?, "
                + "Hr_Alter = ?, "
                + "codpessoaWeb = ?"
                + " WHERE Codigo = ?";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCPF(), ""));
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(pessoa.getRgDtEmis()));
            consulta.setString(pessoa.getSituacao());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCEP(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone1(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone2(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getEndereco(), 0, 50));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getBairro(), 0, 20));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getCidade(), 0, 40));
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getObs());
            consulta.setString(pessoa.getNacionalidade());
            consulta.setString(pessoa.getNaturalid());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getDt_nasc()));
            consulta.setString(pessoa.getSexo());
            consulta.setInt(pessoa.getInstrucao());
            consulta.setString(pessoa.getGrupoSang());
            consulta.setString(pessoa.getRaca());
            consulta.setString(pessoa.getEstCivil());
            consulta.setString(pessoa.getConjuge());
            consulta.setString(pessoa.getPai());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCTPS_Nro());
            consulta.setString(pessoa.getCTPS_Serie());
            consulta.setString(pessoa.getCTPS_UF());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCTPS_Emis()));
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHCat());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCNHDtVenc()));
            consulta.setString(pessoa.getReservista());
            consulta.setString(pessoa.getReservCat());
            consulta.setString(pessoa.getTitEleit());
            consulta.setString(pessoa.getTitZona());
            consulta.setString(pessoa.getTitSecao());
            consulta.setInt(Integer.parseInt(pessoa.getAltura()));
            consulta.setInt(Integer.parseInt(pessoa.getPeso()));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(sCodPessoWeb);
            consulta.setString(pessoa.getCodigo().replace(".0", ""));
            consulta.update();
            consulta.close();
        } catch (SQLException e) {
            throw new RuntimeException("PessoaDao.atualizPessNSat " + e.getMessage() + "\r\n"
                    + " update pessoa set "
                    + " Nome = " + pessoa.getNome() + ", CPF = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCPF(), "") + ", RG = " + pessoa.getRG() + ", "
                    + " RGOrgEmis = " + pessoa.getRGOrgEmis() + ", RgDtEmis = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(pessoa.getRgDtEmis()) + ", "
                    + " Situacao = " + pessoa.getSituacao() + ", CEP = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCEP(), "") + ", "
                    + " Fone1 = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone1(), "") + ", Fone2 = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone2(), "") + ", "
                    + " Endereco = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getEndereco(), 0, 50) + ", Bairro = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getBairro(), 0, 20) + ", "
                    + " Cidade = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getCidade(), 0, 40) + ", UF = " + pessoa.getUF() + ", Email = " + pessoa.getEmail() + ", "
                    + " Funcao = " + pessoa.getFuncao() + ", Obs = " + pessoa.getObs() + ", Nacionalidade = " + pessoa.getNacionalidade() + ", "
                    + " Naturalid = " + pessoa.getNaturalid() + ", Dt_nasc = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getDt_nasc()) + ", "
                    + " Sexo = " + pessoa.getSexo() + ", Instrucao = " + pessoa.getInstrucao() + ", "
                    + " GrupoSang = " + pessoa.getGrupoSang() + ", Raca = " + pessoa.getRaca() + ", EstCivil = " + pessoa.getEstCivil() + ", "
                    + " Conjuge = " + pessoa.getConjuge() + ", Pai = " + pessoa.getPai() + ", Mae = " + pessoa.getMae() + ", PIS = " + pessoa.getPIS() + ", "
                    + " CTPS_Nro = " + pessoa.getCTPS_Nro() + ", CTPS_Serie = " + pessoa.getCTPS_Serie() + ", CTPS_UF = " + pessoa.getCTPS_UF() + ", "
                    + " CTPS_Emis = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCTPS_Emis()) + ", CNH = " + pessoa.getCNH() + ", "
                    + " CNHCat = " + pessoa.getCNHCat() + ", CNHDtVenc = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCNHDtVenc()) + ", "
                    + " Reservista = " + pessoa.getReservista() + ", ReservCat = " + pessoa.getReservCat() + ", TitEleit = " + pessoa.getTitEleit() + ", "
                    + " TitZona = " + pessoa.getTitZona() + ", TitSecao = " + pessoa.getTitSecao() + ", "
                    + " Altura = " + pessoa.getAltura() + ", Peso = " + pessoa.getPeso() + ", Dt_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ", "
                    + " Hr_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ", codpessoaWeb = " + pessoa.getCodigo().replace(".0", "") + ""
                    + " WHERE Codigo = " + sCodPessoWeb + "");
        }
    }

    public void inserePessNSat(String sCod, PessoaPRH pessoa, Persistencia persistencia) throws Exception {
        String sql = " INSERT INTO Pessoa (Nome, CPF, RG, RGOrgEmis, RgDtEmis, Situacao, CEP, Fone1, Fone2 "
                + "  , Endereco, Bairro, Cidade, UF, Email, Funcao, Obs, Nacionalidade, Naturalid, Dt_nasc "
                + "  , Sexo, Instrucao, GrupoSang, Raca, EstCivil, Conjuge, Pai, Mae, PIS, CTPS_Nro, CTPS_Serie "
                + "  , CTPS_UF, CTPS_Emis, CNH, CNHCat, CNHDtVenc, Reservista, ReservCat "
                + "  , TitEleit, TitZona, TitSecao, Altura, Peso, Dt_Alter, Hr_Alter, Codigo, codpessoaWeb) "
                + "  VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCPF(), ""));
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(pessoa.getRgDtEmis()));
            consulta.setString(pessoa.getSituacao());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCEP(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone1(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone2(), ""));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getEndereco(), 0, 50));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getBairro(), 0, 20));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getCidade(), 0, 40));
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getObs());
            consulta.setString(pessoa.getNacionalidade());
            consulta.setString(pessoa.getNaturalid());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getDt_nasc()));

            consulta.setString(pessoa.getSexo());
            consulta.setInt(pessoa.getInstrucao());
            consulta.setString(pessoa.getGrupoSang());
            consulta.setString(pessoa.getRaca());
            consulta.setString(pessoa.getEstCivil());
            consulta.setString(pessoa.getConjuge());
            consulta.setString(pessoa.getPai());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCTPS_Nro());
            consulta.setString(pessoa.getCTPS_Serie());
            consulta.setString(pessoa.getCTPS_UF());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCTPS_Emis()));
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHCat());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCNHDtVenc()));
            consulta.setString(pessoa.getReservista());
            consulta.setString(pessoa.getReservCat());
            consulta.setString(pessoa.getTitEleit());
            consulta.setString(pessoa.getTitZona());
            consulta.setString(pessoa.getTitSecao());
            consulta.setInt(Integer.parseInt(pessoa.getAltura()));
            consulta.setInt(Integer.parseInt(pessoa.getPeso()));

            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.setFloat(Float.parseFloat(sCod));
            consulta.setFloat(Float.parseFloat(sCod));
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.inserePessNSat - " + e.getMessage() + "\r\n"
                    + "INSERT INTO Pessoa (Nome, CPF, RG, RGOrgEmis, RgDtEmis, Situacao, CEP, Fone1, Fone2 "
                    + "  , Endereco, Bairro, Cidade, UF, Email, Funcao, Obs, Nacionalidade, Naturalid, Dt_nasc "
                    + "  , Sexo, Instrucao, GrupoSang, Raca, EstCivil, Conjuge, Pai, Mae, PIS, CTPS_Nro, CTPS_Serie "
                    + "  , CTPS_UF, CTPS_Emis, CNH, CNHCat, CNHDtVenc, Reservista, ReservCat "
                    + "  , TitEleit, TitZona, TitSecao, Altura, Peso, Dt_Alter, Hr_Alter, Codigo, codpessoaWeb) "
                    + "  VALUES (" + pessoa.getNome() + "," + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCPF(), "") + "," + pessoa.getRG() + ","
                    + pessoa.getRGOrgEmis() + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(pessoa.getRgDtEmis()) + "," + pessoa.getSituacao() + ","
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getCEP(), "") + "," + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone1(), "") + ","
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(pessoa.getFone2(), "") + "," + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getEndereco(), 0, 50) + ","
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getBairro(), 0, 20) + ","
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString(pessoa.getCidade(), 0, 40) + "," + pessoa.getUF() + "," + pessoa.getEmail() + ","
                    + pessoa.getFuncao() + "," + pessoa.getObs() + "," + pessoa.getNacionalidade() + "," + pessoa.getNaturalid() + ","
                    + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getDt_nasc()) + "," + pessoa.getSexo() + "," + pessoa.getInstrucao() + "," + pessoa.getGrupoSang() + ","
                    + pessoa.getRaca() + "," + pessoa.getEstCivil() + "," + pessoa.getConjuge() + "," + pessoa.getPai() + "," + pessoa.getMae() + "," + pessoa.getPIS() + ","
                    + pessoa.getCTPS_Nro() + "," + pessoa.getCTPS_Serie() + "," + pessoa.getCTPS_UF() + ","
                    + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCTPS_Emis()) + "," + pessoa.getCNH() + "," + pessoa.getCNHCat() + ","
                    + br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData2(pessoa.getCNHDtVenc()) + "," + pessoa.getReservista() + "," + pessoa.getReservCat() + ","
                    + pessoa.getTitEleit() + "," + pessoa.getTitZona() + "," + pessoa.getTitSecao() + "," + Integer.parseInt(pessoa.getAltura()) + ","
                    + Integer.parseInt(pessoa.getPeso()) + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ","
                    + Float.parseFloat(sCod) + "," + Float.parseFloat(sCod) + ")");
        }
    }

    public String getCodigo(Persistencia persistencia) throws Exception {
        try {
            String pessoa = null;
            BigDecimal cod = new BigDecimal("1");
            Consulta consult = new Consulta("select MAX(codigo) + 1 codigo "
                    + "FROM pessoa", persistencia);
            consult.select();
            while (consult.Proximo()) {
                try {
                    cod = new BigDecimal(consult.getString("Codigo"));
                } catch (Exception e) {
                    cod = new BigDecimal("1");
                }
            }
            pessoa = cod.toPlainString();
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getCodigo - " + e.getMessage() + "\r\n"
                    + "select MAX(codigo) + 1 codigo "
                    + "FROM pessoa");
        }
    }

    public boolean gravaCadastroPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        boolean retorno;
        String sql = "insert into Pessoa (Codigo,RG,RGOrgEmis,RgDtEmis,Nome,Situacao,Fone1,Fone2,CEP,"
                + "Endereco,Numero,Bairro,Cidade,UF,Email,Funcao,Obs,Matr,Dt_Alter, Hr_Alter) "
                + "Values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getRgDtEmis());
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getNumero());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getObs());
            consulta.setBigDecimal(pessoa.getMatr());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            throw new Exception("PessoaDao.gravaCadastroPessoa - " + e.getMessage() + "\r\n"
                    + "insert into Pessoa (Codigo,RG,RGOrgEmis,RgDtEmis,Nome,Situacao,Fone1,Fone2,CEP,"
                    + "Endereco,Numero,Bairro,Cidade,UF,Email,Funcao,Obs,Matr) "
                    + "Values (" + pessoa.getCodigo() + "," + pessoa.getRG() + "," + pessoa.getRGOrgEmis() + "," + pessoa.getRgDtEmis() + "," + pessoa.getNome() + ","
                    + pessoa.getSituacao() + "," + pessoa.getFone1() + "," + pessoa.getFone2() + "," + pessoa.getCEP() + "," + pessoa.getEndereco() + ","
                    + pessoa.getNumero() + "," + pessoa.getBairro() + "," + pessoa.getCidade() + "," + pessoa.getUF() + "," + pessoa.getEmail() + ","
                    + pessoa.getFuncao() + "," + pessoa.getObs() + "," + pessoa.getMatr() + ")");
        }
        return retorno;
    }

    public Boolean existLocal(String cpf, Persistencia persistencia) {
        Boolean retorno = false;
        try {
            Consulta consult = new Consulta("select codigo FROM pessoa WHERE CPF = ?", persistencia);

            consult.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(cpf, ""));
            consult.select();
            while (consult.Proximo()) {
                retorno = true;
            }
            consult.Close();
        } catch (Exception ex) {
            Logger.getLogger(PessoaDao.class.getName()).log(Level.SEVERE, null, ex);
        }
        return retorno;
    }

    /**
     * Select FROM 'Pessoa' table
     *
     * @param sCriterio
     * @param persistencia Connect with database
     * @return listPessoa Return the ArrayList 'listPessoa'
     * @throws Exception e
     */
    // read
    public List<PessoaPRH> getListagemPessoaLocal(String sCriterio, Persistencia persistencia) throws Exception {
        List<PessoaPRH> lPessoa = new ArrayList<>();
        PessoaPRH pessoa;
        String sql;
        try {
            if ("".equals(sCriterio) || sCriterio == null) {
                sql = "SELECT top 50 codigo, Nome, Endereco, Bairro, Cidade, UF, CEP  "
                        + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis  "
                        + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat  "
                        + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                        + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                        + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                        + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                        + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao, CodCli, Complemento "
                        + "FROM pessoa";
            } else {
                sql = "SELECT codigo, Nome, Endereco, Bairro, Cidade, UF, CEP  "
                        + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis  "
                        + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat  "
                        + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                        + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                        + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                        + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                        + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao, CodCli, Complemento "
                        + "FROM pessoa WHERE " + sCriterio;
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                pessoa = new PessoaPRH();
                pessoa.setCodigo(consult.getString("codigo").replace(".0", ""));
                pessoa.setNome(consult.getString("Nome"));
                pessoa.setSituacao(consult.getString("Situacao"));
                pessoa.setEndereco(consult.getString("Endereco"));
                pessoa.setBairro(consult.getString("Bairro"));
                pessoa.setCidade(consult.getString("Cidade"));
                pessoa.setUF(consult.getString("UF"));
                pessoa.setCEP(consult.getString("CEP"));
                pessoa.setFone1(consult.getString("Fone1"));
                pessoa.setFone2(consult.getString("Fone2"));
                pessoa.setEmail(consult.getString("Email"));
                pessoa.setEstCivil(consult.getString("EstCivil"));
                pessoa.setConjuge(consult.getString("Conjuge"));
                pessoa.setPai(consult.getString("Pai"));
                pessoa.setMae(consult.getString("Mae"));
                pessoa.setRG(consult.getString("RG"));
                pessoa.setRGOrgEmis(consult.getString("RGOrgEmis"));
                pessoa.setRgDtEmis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("RgDtEmis"), "ddMMaaaa"));
                pessoa.setCPF(consult.getString("CPF"));
                pessoa.setTitEleit(consult.getString("TitEleit"));
                pessoa.setTitZona(consult.getString("TitZona"));
                pessoa.setTitSecao(consult.getString("TitSecao"));
                pessoa.setPIS(consult.getString("PIS"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CNHDtVenc"), "ddMMaaaa"));
                pessoa.setCNHCat(consult.getString("CNHCat"));
                pessoa.setReservista(consult.getString("Reservista"));
                pessoa.setReservCat(consult.getString("ReservCat"));
                pessoa.setCTPS_Nro(consult.getString("CTPS_Nro"));
                pessoa.setCTPS_Serie(consult.getString("CTPS_Serie"));
                pessoa.setCTPS_UF(consult.getString("CTPS_UF"));
                pessoa.setCTPS_Emis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CTPS_Emis"), "ddMMaaaa"));
                pessoa.setDt_nasc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("Dt_nasc"), "ddMMaaaa"));
                pessoa.setNaturalid(consult.getString("Naturalid"));
                pessoa.setInstrucao(consult.getInt("Instrucao"));
                pessoa.setSexo(consult.getString("Sexo"));
                pessoa.setRaca(consult.getString("Raca"));
                pessoa.setGrupoSang(consult.getString("GrupoSang"));
                pessoa.setAltura(consult.getString("Altura"));
                pessoa.setPeso(consult.getString("Peso"));
                pessoa.setIndicacao(consult.getString("Indicacao"));
                pessoa.setDt_FormIni(consult.getString("Dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("Dt_FormFim"));
                pessoa.setLocalForm(consult.getString("LocalForm"));
                pessoa.setCertific(consult.getString("Certific"));
                pessoa.setReg_PF(consult.getString("Reg_PF"));
                pessoa.setReg_PFUF(consult.getString("Reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("Reg_PFDt"));
                pessoa.setCarNacVig(consult.getString("CarNacVig"));
                pessoa.setDtValCNV(consult.getString("DtValCNV"));
                pessoa.setDt_Recicl(consult.getString("Dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("Dt_VenCurs"));
                pessoa.setExtTV(consult.getString("ExtTV"));
                pessoa.setExtSPP(consult.getString("ExtSPP"));
                pessoa.setExtEscolta(consult.getString("ExtEscolta"));
                pessoa.setObs(consult.getString("Obs"));
                pessoa.setFuncao(consult.getString("Funcao"));
                pessoa.setDt_Alter(consult.getString("Dt_Alter"));
                pessoa.setHr_Alter(consult.getString("Hr_Alter"));
                pessoa.setCodCli(consult.getString("CodCli"));
                pessoa.setComplemento(consult.getString("Complemento"));
                lPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.getListagemPessoaLocal - " + e.getMessage());
        }
        return lPessoa;
    }

    /**
     * Select FROM 'Pessoa' table
     *
     * @param sCriterio
     * @param persistencia Connect with database
     * @return listPessoa Return the ArrayList 'listPessoa'
     * @throws Exception e
     */
    // read
    public List<PessoaPRH> getListagemPessoa(String sCriterio, Persistencia persistencia) throws Exception {
        List<PessoaPRH> lPessoa = new ArrayList<>();
        PessoaPRH pessoa;
        String sql;
        Consulta consult;
        try {
            if ("".equals(sCriterio) || sCriterio == null) {
                sql = "select top 50 codigo, Nome, Endereco, Bairro, Cidade, UF, CEP "
                        + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis "
                        + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat "
                        + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                        + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                        + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                        + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                        + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao, CodCli, Complemento "
                        + "FROM pessoa ";

            } else {
                sql = "select codigo, Nome, Endereco, Bairro, Cidade, UF, CEP "
                        + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis "
                        + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat "
                        + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                        + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                        + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                        + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                        + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao, CodCli, Complemento "
                        + "FROM pessoa WHERE " + sCriterio;
            }
            consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                pessoa = new PessoaPRH();
                pessoa.setCodigo(consult.getString("codigo").replace(".0", ""));
                pessoa.setNome(consult.getString("Nome"));
                pessoa.setSituacao(consult.getString("Situacao"));
                pessoa.setEndereco(consult.getString("Endereco"));
                pessoa.setBairro(consult.getString("Bairro"));
                pessoa.setCidade(consult.getString("Cidade"));
                pessoa.setUF(consult.getString("UF"));
                pessoa.setCEP(consult.getString("CEP"));
                pessoa.setFone1(consult.getString("Fone1"));
                pessoa.setFone2(consult.getString("Fone2"));
                pessoa.setEmail(consult.getString("Email"));
                pessoa.setEstCivil(consult.getString("EstCivil"));
                pessoa.setConjuge(consult.getString("Conjuge"));
                pessoa.setPai(consult.getString("Pai"));
                pessoa.setMae(consult.getString("Mae"));
                pessoa.setRG(consult.getString("RG"));
                pessoa.setRGOrgEmis(consult.getString("RGOrgEmis"));
                pessoa.setRgDtEmis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("RgDtEmis"), "ddMMaaaa"));
                pessoa.setCPF(consult.getString("CPF"));
                pessoa.setTitEleit(consult.getString("TitEleit"));
                pessoa.setTitZona(consult.getString("TitZona"));
                pessoa.setTitSecao(consult.getString("TitSecao"));
                pessoa.setPIS(consult.getString("PIS"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CNHDtVenc"), "ddMMaaaa"));
                pessoa.setCNHCat(consult.getString("CNHCat"));
                pessoa.setReservista(consult.getString("Reservista"));
                pessoa.setReservCat(consult.getString("ReservCat"));
                pessoa.setCTPS_Nro(consult.getString("CTPS_Nro"));
                pessoa.setCTPS_Serie(consult.getString("CTPS_Serie"));
                pessoa.setCTPS_UF(consult.getString("CTPS_UF"));
                pessoa.setCTPS_Emis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CTPS_Emis"), "ddMMaaaa"));
                pessoa.setDt_nasc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("Dt_nasc"), "ddMMaaaa"));
                pessoa.setNaturalid(consult.getString("Naturalid"));
                pessoa.setInstrucao(consult.getInt("Instrucao"));
                pessoa.setSexo(consult.getString("Sexo"));
                pessoa.setRaca(consult.getString("Raca"));
                pessoa.setGrupoSang(consult.getString("GrupoSang"));
                pessoa.setAltura(consult.getString("Altura"));
                pessoa.setPeso(consult.getString("Peso"));
                pessoa.setIndicacao(consult.getString("Indicacao"));
                pessoa.setDt_FormIni(consult.getString("Dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("Dt_FormFim"));
                pessoa.setLocalForm(consult.getString("LocalForm"));
                pessoa.setCertific(consult.getString("Certific"));
                pessoa.setReg_PF(consult.getString("Reg_PF"));
                pessoa.setReg_PFUF(consult.getString("Reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("Reg_PFDt"));
                pessoa.setCarNacVig(consult.getString("CarNacVig"));
                pessoa.setDtValCNV(consult.getString("DtValCNV"));
                pessoa.setDt_Recicl(consult.getString("Dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("Dt_VenCurs"));
                pessoa.setExtTV(consult.getString("ExtTV"));
                pessoa.setExtSPP(consult.getString("ExtSPP"));
                pessoa.setExtEscolta(consult.getString("ExtEscolta"));
                pessoa.setObs(consult.getString("Obs"));
                pessoa.setFuncao(consult.getString("Funcao"));
                pessoa.setDt_Alter(consult.getString("Dt_Alter"));
                pessoa.setHr_Alter(consult.getString("Hr_Alter"));
                pessoa.setCodCli(consult.getString("CodCli"));
                pessoa.setComplemento(consult.getString("Complemento"));
                lPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.getListagemPessoa - " + e.getMessage());
        }
        return lPessoa;
    }

    /**
     * Select FROM 'Pessoa' table
     *
     * @param cpf
     * @param persistencia Connect with database
     * @return listPessoa Return the ArrayList 'listPessoa'
     * @throws Exception e
     */
    // read
    public PessoaPRH getCPF(String cpf, Persistencia persistencia) throws Exception {
        PessoaPRH pessoa = null;
        try {
            Consulta consult = new Consulta("select codigo, Nome, Endereco, Bairro, Cidade, UF, CEP "
                    + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis "
                    + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat "
                    + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                    + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                    + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                    + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                    + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao, CodCli, Complemento "
                    + "FROM pessoa "
                    + " WHERE CPF = ?", persistencia
            );
            consult.setString(br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(cpf, ""));
            consult.select();
            while (consult.Proximo()) {
                pessoa = new PessoaPRH();
                pessoa.setCodigo(consult.getString("codigo").replace(".0", ""));
                pessoa.setNome(consult.getString("Nome"));
                pessoa.setSituacao(consult.getString("Situacao"));
                pessoa.setEndereco(consult.getString("Endereco"));
                pessoa.setBairro(consult.getString("Bairro"));
                pessoa.setCidade(consult.getString("Cidade"));
                pessoa.setUF(consult.getString("UF"));
                pessoa.setCEP(consult.getString("CEP"));
                pessoa.setFone1(consult.getString("Fone1"));
                pessoa.setFone2(consult.getString("Fone2"));
                pessoa.setEmail(consult.getString("Email"));
                pessoa.setEstCivil(consult.getString("EstCivil"));
                pessoa.setConjuge(consult.getString("Conjuge"));
                pessoa.setPai(consult.getString("Pai"));
                pessoa.setMae(consult.getString("Mae"));
                pessoa.setRG(consult.getString("RG"));
                pessoa.setRGOrgEmis(consult.getString("RGOrgEmis"));

                pessoa.setRgDtEmis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("RgDtEmis"), "ddMMaaaa"));

                pessoa.setCPF(consult.getString("CPF"));
                pessoa.setTitEleit(consult.getString("TitEleit"));
                pessoa.setTitZona(consult.getString("TitZona"));
                pessoa.setTitSecao(consult.getString("TitSecao"));
                pessoa.setPIS(consult.getString("PIS"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CNHDtVenc"), "ddMMaaaa"));
                pessoa.setCNHCat(consult.getString("CNHCat"));
                pessoa.setReservista(consult.getString("Reservista"));
                pessoa.setReservCat(consult.getString("ReservCat"));
                pessoa.setCTPS_Nro(consult.getString("CTPS_Nro"));
                pessoa.setCTPS_Serie(consult.getString("CTPS_Serie"));
                pessoa.setCTPS_UF(consult.getString("CTPS_UF"));
                pessoa.setCTPS_Emis(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("CTPS_Emis"), "ddMMaaaa"));
                pessoa.setDt_nasc(br.com.sasw.pacotesuteis.utilidades.DataAtual.voltaData(consult.getString("Dt_nasc"), "ddMMaaaa"));
                pessoa.setNaturalid(consult.getString("Naturalid"));
                pessoa.setInstrucao(consult.getInt("Instrucao"));
                pessoa.setSexo(consult.getString("Sexo"));
                pessoa.setRaca(consult.getString("Raca"));
                pessoa.setGrupoSang(consult.getString("GrupoSang"));
                pessoa.setAltura(consult.getString("Altura"));
                pessoa.setPeso(consult.getString("Peso"));
                pessoa.setIndicacao(consult.getString("Indicacao"));
                pessoa.setDt_FormIni(consult.getString("Dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("Dt_FormFim"));
                pessoa.setLocalForm(consult.getString("LocalForm"));
                pessoa.setCertific(consult.getString("Certific"));
                pessoa.setReg_PF(consult.getString("Reg_PF"));
                pessoa.setReg_PFUF(consult.getString("Reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("Reg_PFDt"));
                pessoa.setCarNacVig(consult.getString("CarNacVig"));
                pessoa.setDtValCNV(consult.getString("DtValCNV"));
                pessoa.setDt_Recicl(consult.getString("Dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("Dt_VenCurs"));
                pessoa.setExtTV(consult.getString("ExtTV"));
                pessoa.setExtSPP(consult.getString("ExtSPP"));
                pessoa.setExtEscolta(consult.getString("ExtEscolta"));
                pessoa.setObs(consult.getString("Obs"));
                pessoa.setFuncao(consult.getString("Funcao"));
                pessoa.setCodCli(consult.getString("CodCli"));
                pessoa.setComplemento(consult.getString("Complemento"));
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.getCPF - " + e.getMessage() + "\r\n"
                    + "select codigo, Nome, Endereco, Bairro, Cidade, UF, CEP "
                    + ",Fone1, Fone2, Email, EstCivil, Conjuge, Pai, Mae, RG, RGOrgEmis, RgDtEmis "
                    + ",CPF, TitEleit, TitZona, TitSecao, PIS, CNH, CNHDtVenc, CNHCat "
                    + ",Reservista, ReservCat, CTPS_Nro, CTPS_Serie, CTPS_UF, CTPS_Emis, Dt_nasc "
                    + ",Naturalid, Instrucao, Sexo, Raca, GrupoSang, Altura, Peso, Indicacao "
                    + ",Dt_FormIni, Dt_FormFim, LocalForm, Certific, Reg_PF, Reg_PFUF, Reg_PFDt "
                    + ",CarNacVig, DtValCNV, Dt_Recicl, Dt_VenCurs, ExtTV, ExtSPP, ExtEscolta "
                    + ",Obs, Funcao, Dt_Alter, Hr_Alter, Situacao "
                    + "FROM pessoa "
                    + " WHERE CPF = " + br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa(cpf, ""));
        }
        return pessoa;
    }

    /**
     * Update 'Pessoa' table
     *
     * @param pessoa Declare a variable of type 'Pessoa'
     * @param persistencia Connect with database
     * @throws SQLException e
     */
    // update
    public void atualizaPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String sql = "update pessoa set Codigo = ?, Nome = ?, Situacao = ?, Dt_Situac = ?, Endereco = ?,"
                + "Numero = ?, Bairro = ?, Cidade = ?, CodCidade = ?, UF = ?, CEP = ?, Fone1 = ?,"
                + "Fone2 = ?, Email = ?, Regiao = ?, EstCivil = ?, Conjuge = ?, Pai = ?, Mae = ?,"
                + "RG = ?, RGOrgEmis = ?, RgDtEmis = ?, CPF = ?, TitEleit = ?, TitZona = ?, TitSecao = ?,"
                + "PIS = ?, CNH = ?, CNHDtVenc = ?, CNHCat = ?, Reservista = ?, ReservCat = ?,"
                + "CTPS_Nro = ?, CTPS_Serie = ?, CTPS_UF = ?, CTPS_Emis = ?, Dt_nasc = ?, Naturalid = ?, "
                + "Instrucao = ?, Sexo = ?, Raca = ?, GrupoSang = ?, Altura = ?, Peso = ?, Indicacao = ?, "
                + "Dt_FormIni = ?, Dt_FormFim = ?, LocalForm = ?, Certific = ?, Reg_PF = ?, Reg_PFUF = ?, "
                + "Reg_PFDt = ?, CarNacVig = ?, DtValCNV = ?, Reg_DRT = ?, Dt_Recicl = ?, Dt_VenCurs = ?, "
                + "Dt_ExameMe = ?, Dt_Psico = ?, ExtTV = ?, ExtSPP = ?, ExtEscolta = ?, Obs = ?, "
                + "Funcao = ?, Matr = ?, Dig01 = ?, Dig02 = ?, Dig03 = ?, Dig04 = ?, Dig05 = ?, Dig06 = ?, "
                + "Dig07 = ?, Dig08 = ?, Dig09 = ?, Dig10 = ?, PW = ?, Bottom = ?, BottomII = ?, "
                + "CodPrest = ?, CodVisit = ?, PretSalario = ?, Apresen = ?, Ct_Banco = ?, Ct_Agencia = ?, "
                + "Ct_Conta = ?, Regional = ?, PWWeb  = ?, CargoPretend = ?, "
                + "SecaoPretend  = ?, EscalaPretend = ?,"
                + "HorarioPretend  = ?, SindicatoPdr = ?, CodFilPretend = ?, PWPortal = ?, "
                + "Dt_UltAcPortal = ?, CodSatellite = ?, Operador = ?, Dt_Alter = ?, Hr_Alter = ? "
                + "WHERE Codigo = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getDt_Situac());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getNumero());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setBigDecimal(pessoa.getCodCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getRegiao());
            consulta.setString(pessoa.getEstCivil());
            consulta.setString(pessoa.getConjuge());
            consulta.setString(pessoa.getPai());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getRgDtEmis());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getTitEleit());
            consulta.setString(pessoa.getTitZona());
            consulta.setString(pessoa.getTitSecao());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHDtVenc());
            consulta.setString(pessoa.getCNHCat());
            consulta.setString(pessoa.getReservista());
            consulta.setString(pessoa.getReservCat());
            consulta.setString(pessoa.getCTPS_Nro());
            consulta.setString(pessoa.getCTPS_Serie());
            consulta.setString(pessoa.getCTPS_UF());
            consulta.setString(pessoa.getCTPS_Emis());
            consulta.setString(pessoa.getDt_nasc());
            consulta.setString(pessoa.getNaturalid());
            consulta.setInt(pessoa.getInstrucao());
            consulta.setString(pessoa.getSexo());
            consulta.setString(pessoa.getRaca());
            consulta.setString(pessoa.getGrupoSang());
            consulta.setBigDecimal(pessoa.getAltura());
            consulta.setBigDecimal(pessoa.getPeso());
            consulta.setString(pessoa.getIndicacao());
            consulta.setString(pessoa.getDt_FormIni());
            consulta.setString(pessoa.getDt_FormFim());
            consulta.setString(pessoa.getLocalForm());
            consulta.setString(pessoa.getCertific());
            consulta.setString(pessoa.getReg_PF());
            consulta.setString(pessoa.getReg_PFUF());
            consulta.setString(pessoa.getReg_PFDt());
            consulta.setString(pessoa.getCarNacVig());
            consulta.setString(pessoa.getDtValCNV());
            consulta.setString(pessoa.getReg_DRT());
            consulta.setString(pessoa.getDt_Recicl());
            consulta.setString(pessoa.getDt_VenCurs());
            consulta.setString(pessoa.getDt_ExameMe());
            consulta.setString(pessoa.getDt_Psico());
            consulta.setString(pessoa.getExtTV());
            consulta.setString(pessoa.getExtSPP());
            consulta.setString(pessoa.getExtEscolta());
            consulta.setString(pessoa.getObs());
            consulta.setString(pessoa.getFuncao());
            consulta.setBigDecimal(pessoa.getMatr());
            consulta.setInt(pessoa.getDig01());
            consulta.setInt(pessoa.getDig02());
            consulta.setInt(pessoa.getDig03());
            consulta.setInt(pessoa.getDig04());
            consulta.setInt(pessoa.getDig05());
            consulta.setInt(pessoa.getDig06());
            consulta.setInt(pessoa.getDig07());
            consulta.setInt(pessoa.getDig08());
            consulta.setInt(pessoa.getDig09());
            consulta.setInt(pessoa.getDig10());
            consulta.setString(pessoa.getPW());
            consulta.setString(pessoa.getBottom());
            consulta.setString(pessoa.getBottomII());
            consulta.setBigDecimal(pessoa.getCodPrest());
            consulta.setBigDecimal(pessoa.getCodVisit());
            consulta.setBigDecimal(pessoa.getPretSalario());
            consulta.setString(pessoa.getApresen());
            consulta.setString(pessoa.getCt_Banco());
            consulta.setString(pessoa.getCt_Agencia());
            consulta.setString(pessoa.getCt_Conta());
            consulta.setInt(pessoa.getRegional());
            consulta.setString(pessoa.getPWWeb());
            consulta.setString(pessoa.getCargoPretend());
            consulta.setString(pessoa.getSecaoPretend());
            consulta.setString(pessoa.getEscalaPretend());
            consulta.setInt(pessoa.getHorarioPretend());
            consulta.setString(pessoa.getSindicatoPdr());
            consulta.setBigDecimal(pessoa.getCodFilPretend());
            consulta.setString(pessoa.getPWPortal());
            consulta.setString(pessoa.getDt_UltAcPortal());
            consulta.setBigDecimal(pessoa.getCodSatellite());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizaPessoa - " + e.getMessage() + "\r\n"
                    + " update pessoa set Codigo = " + pessoa.getCodigo() + ", Nome = " + pessoa.getNome() + ", Situacao = " + pessoa.getSituacao() + ","
                    + " Dt_Situac = " + pessoa.getDt_Situac() + ", Endereco = " + pessoa.getEndereco() + ","
                    + " Numero = " + pessoa.getNumero() + ", Bairro = " + pessoa.getBairro() + ", Cidade = " + pessoa.getCidade() + ","
                    + " CodCidade = " + pessoa.getCodCidade() + ", UF = " + pessoa.getUF() + ", CEP = " + pessoa.getCEP() + ", Fone1 = " + pessoa.getFone1() + ","
                    + " Fone2 = " + pessoa.getFone2() + ", Email = " + pessoa.getEmail() + ", Regiao = " + pessoa.getRegiao() + ", EstCivil = " + pessoa.getEstCivil() + ","
                    + " Conjuge = " + pessoa.getConjuge() + ", Pai = " + pessoa.getPai() + ", Mae = " + pessoa.getMae() + ","
                    + " RG = " + pessoa.getRG() + ", RGOrgEmis = " + pessoa.getRGOrgEmis() + ", RgDtEmis = " + pessoa.getRgDtEmis() + ", CPF = " + pessoa.getCPF() + ","
                    + " TitEleit = " + pessoa.getTitEleit() + ", TitZona = " + pessoa.getTitZona() + ", TitSecao = " + pessoa.getTitSecao() + ","
                    + " PIS = " + pessoa.getPIS() + ", CNH = " + pessoa.getCNH() + ", CNHDtVenc = " + pessoa.getCNHDtVenc() + ", CNHCat = " + pessoa.getCNHCat() + ","
                    + " Reservista = " + pessoa.getReservista() + ", ReservCat = " + pessoa.getReservCat() + ","
                    + " CTPS_Nro = " + pessoa.getCTPS_Nro() + ", CTPS_Serie = " + pessoa.getCTPS_Serie() + ", CTPS_UF = " + pessoa.getCTPS_UF() + ","
                    + " CTPS_Emis = " + pessoa.getCTPS_Emis() + ", Dt_nasc = " + pessoa.getDt_nasc() + ", Naturalid = " + pessoa.getNaturalid() + ","
                    + " Instrucao = " + pessoa.getInstrucao() + ", Sexo = " + pessoa.getSexo() + ", Raca = " + pessoa.getRaca() + ","
                    + " GrupoSang = " + pessoa.getGrupoSang() + ", Altura = " + pessoa.getAltura() + ", Peso = " + pessoa.getPeso() + ","
                    + " Indicacao = " + pessoa.getIndicacao() + ", Dt_FormIni = " + pessoa.getDt_FormIni() + ", Dt_FormFim = " + pessoa.getDt_FormFim() + ","
                    + " LocalForm = " + pessoa.getLocalForm() + ", Certific = " + pessoa.getCertific() + ", Reg_PF = " + pessoa.getReg_PF() + ","
                    + " Reg_PFUF = " + pessoa.getReg_PFUF() + ", Reg_PFDt = " + pessoa.getReg_PFDt() + ", CarNacVig = " + pessoa.getCarNacVig() + ","
                    + " DtValCNV = " + pessoa.getDtValCNV() + ", Reg_DRT = " + pessoa.getReg_DRT() + ", Dt_Recicl = " + pessoa.getDt_Recicl() + ","
                    + " Dt_VenCurs = " + pessoa.getDt_VenCurs() + ", Dt_ExameMe = " + pessoa.getDt_ExameMe() + ", Dt_Psico = " + pessoa.getDt_Psico() + ","
                    + " ExtTV = " + pessoa.getExtTV() + ", ExtSPP = " + pessoa.getExtSPP() + ", ExtEscolta = " + pessoa.getExtEscolta() + ", Obs = " + pessoa.getObs() + ", "
                    + " Funcao = " + pessoa.getFuncao() + ", Matr = " + pessoa.getMatr() + ", Dig01 = " + pessoa.getDig01() + ", Dig02 = " + pessoa.getDig02() + ","
                    + " Dig03 = " + pessoa.getDig03() + ", Dig04 = " + pessoa.getDig04() + ", Dig05 = " + pessoa.getDig05() + ", Dig06 = " + pessoa.getDig06() + ","
                    + " Dig07 = " + pessoa.getDig07() + ", Dig08 = " + pessoa.getDig08() + ", Dig09 = " + pessoa.getDig09() + ", Dig10 = " + pessoa.getDig10() + ","
                    + " PW = " + pessoa.getPW() + ", Bottom = " + pessoa.getBottom() + ", BottomII = " + pessoa.getBottomII() + ","
                    + " CodPrest = " + pessoa.getCodPrest() + ", CodVisit = " + pessoa.getCodVisit() + ", PretSalario = " + pessoa.getPretSalario() + ","
                    + " Apresen = " + pessoa.getApresen() + ", Ct_Banco = " + pessoa.getCt_Banco() + ", Ct_Agencia = " + pessoa.getCt_Agencia() + ","
                    + " Ct_Conta = " + pessoa.getCt_Conta() + ", Regional = " + pessoa.getRegional() + ", PWWeb  = " + pessoa.getPWWeb() + ","
                    + " CargoPretend = " + pessoa.getCargoPretend() + ", "
                    + " SecaoPretend  = " + pessoa.getSecaoPretend() + ", EscalaPretend = " + pessoa.getEscalaPretend() + ","
                    + " HorarioPretend  = " + pessoa.getHorarioPretend() + ", SindicatoPdr = " + pessoa.getSindicatoPdr() + ", CodFilPretend = " + pessoa.getCodFilPretend() + ","
                    + " PWPortal = " + pessoa.getPWPortal() + ", "
                    + " Dt_UltAcPortal = " + pessoa.getDt_UltAcPortal() + ", CodSatellite = " + pessoa.getCodSatellite() + ", Operador = " + pessoa.getOperador() + ","
                    + " Dt_Alter = " + pessoa.getDt_Alter() + ", Hr_Alter = " + pessoa.getHr_Alter()
                    + " WHERE Codigo = " + pessoa.getCodigo());
        }
    }

    /**
     * Delete FROM table 'Pessoa' WHERE 'Codigo = ?'
     *
     * @param pessoa Declare a 'Pessoa' varible with the name 'pessoa'
     * @param persistencia Connect with database
     * @throws java.lang.Exception
     */
    public void excluirPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String sql = "delete FROM pessoa WHERE Codigo = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.excluirPessoa - " + e.getMessage() + "\r\n"
                    + "delete FROM pessoa WHERE Codigo = " + pessoa.getCodigo());
        }
    }

    /**
     * Busca pessoa por email
     *
     * @param Email - email da pesquisa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> BuscaEmail(String Email, Persistencia persistencia) throws Exception {
        List<Pessoa> listPessoa;
        try {
            Pessoa pessoa;
            Consulta consult = new Consulta("Select Pessoa.Codigo, Pessoa.PWWeb, Pessoa.Nome, saspw.Situacao \n"
                    + "FROM Pessoa LEFT JOIN saspw ON Pessoa.Codigo = saspw.CodPessoa WHERE Pessoa.Email=?", persistencia);
            consult.setString(Email);
            consult.select();
            listPessoa = new ArrayList();
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getString("Codigo"));
                pessoa.setNome(consult.getString("Nome"));
                pessoa.setPWWeb(consult.getString("PWWEb"));
                pessoa.setSituacao(consult.getString("Situacao"));
                listPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            listPessoa = null;
            throw new Exception("PessoaDao.BuscaEmail - " + e.getMessage() + "\r\n"
                    + "Select Codigo, PWWeb, Nome, Situacao FROM pessoa WHERE Email=" + Email);
        }
        return listPessoa;
    }

    public static List<Pessoa> BuscaCodPessoaMatr(String sCodPessoaWeb, Persistencia persistencia) throws Exception {
        List<Pessoa> listPessoa;
        try {
            Pessoa pessoa;
            Consulta consult = new Consulta("SELECT "
                    + "pessoa.Codigo "
                    + "FROM pessoa "
                    + "WHERE codpessoaWeb = ?", persistencia);
            consult.setString(sCodPessoaWeb);
            consult.select();
            listPessoa = new ArrayList();
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                //BigDecimal bdNum = new BigDecimal(consult.getFloat("Matr"));
                //pessoa.setMatr(consult.getString("Matr"));
                pessoa.setCodigo(consult.getString("Codigo"));
                listPessoa.add(pessoa);
            }
            consult.Close();
        } catch (Exception e) {
            listPessoa = null;
            throw new Exception("PessoaDao.BuscaCodPessoaMatr - " + e.getMessage() + "\r\n"
                    + "SELECT "
                    + "pessoa.Codigo "
                    + "FROM pessoa "
                    + "WHERE codpessoaWeb = " + sCodPessoaWeb);
        }
        return listPessoa;
    }

    /**
     * Atualiza a hora do último acesso ao Portal RH
     *
     * @param sCodigo - Codigo da pessoa
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void ultAcPortal(String sCodigo, Persistencia persistencia) throws Exception {
        try {
            String sql = "update pessoa set Dt_UltAcPortal=? WHERE codigo=?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(sCodigo);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.ultAcPortal - " + e.getMessage() + "\r\n"
                    + "update pessoa set Dt_UltAcPortal=" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + " WHERE codigo=" + sCodigo);
        }
    }

    /**
     * Troca a senha de acesso ao PortalRH do funcionário
     *
     * @param sNovaSenha
     * @param sMatricula
     * @param sOperador
     * @param persistencia
     * @throws Exception
     */
    public void atualizaSenha(String sNovaSenha, String sMatricula, String sOperador, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update Pessoa"
                    + " set PWPortal  = ?,Dt_Alter = ?, Hr_Alter = ?, Operador = ?"
                    + " WHERE Matr = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sNovaSenha);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(sOperador);
            consulta.setString(sMatricula);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizaSenha - " + e.getMessage() + "\r\n"
                    + "Update Pessoa"
                    + " set PWPortal  = " + sNovaSenha + ",Dt_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ", Hr_Alter = "
                    + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ", Operador = " + sOperador
                    + " WHERE Matr = " + sMatricula);
        }
    }

    public void atualizaSenhaSatMob(Pessoa p, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update Pessoa"
                    + " set PWWeb  = ?, Dt_Alter = ?, Hr_Alter = ?, Operador = ?"
                    + " WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(p.getPWWeb());
            consulta.setString(p.getDt_Alter());
            consulta.setString(p.getHr_Alter());
            consulta.setString(p.getOperador());
            consulta.setBigDecimal(p.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizaSenhaSatMob - " + e.getMessage() + "\r\n"
                    + "Update Pessoa"
                    + " set PWWeb  = " + p.getPWWeb() + ", Dt_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ", "
                    + " Hr_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ", Operador = " + p.getOperador()
                    + " WHERE codigo = " + p.getCodigo());
        }
    }

    public void atualizaNomeEmail(Pessoa p, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update Pessoa"
                    + " set Nome  = ?, Email = ? "
                    + " WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(p.getNome());
            consulta.setString(p.getEmail());
            consulta.setBigDecimal(p.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizaNomeEmail - " + e.getMessage() + "\r\n"
                    + "Update Pessoa"
                    + " set Nome  = " + p.getNome() +  ", Email = " + p.getEmail()
                    + " WHERE codigo = " + p.getCodigo());
        }
    }

    public void envioMensagemSatMob(BigDecimal codDest, BigDecimal codOri, String mensagem, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into comunicacoes (numero, dtcadastro, hrcadastro, codremet, coddest, detalhes, situacao, flag_excl) \n"
                    + "Select Isnull(max(Numero),0)+1, Convert(Date,Getdate()), Substring(convert(Varchar,getdate(),108),1,5),  \n"
                    + " ? , ? ,  ? , 0, '' \n"
                    + "From Comunicacoes (NoLock)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codOri);
            consulta.setBigDecimal(codDest);
            consulta.setString(mensagem);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizaSenhaSatMob - " + e.getMessage());
        }
    }

    /**
     * Retorna lista de PWs das Pessoas
     *
     * @param lcp - objeto contendo lista de Pessoas ligadas a Clientes,
     * fundamental codigoweb
     * @param persistencia - conexão do banco
     * @return
     * @throws Exception - pode gerar Exception
     */
    public List<Pessoa> getPWs(List<ClientesPessoas> lcp, Persistencia persistencia) throws Exception {
        String sql = "Select Codigo, PW, Nome FROM pessoa WHERE codigo in (";
        try {
            List<Pessoa> retorno = new ArrayList();
            for (ClientesPessoas cp : lcp) {
                sql += "'" + cp.getCodPessoaWEB() + "',";
            }
            sql += ")";
            sql = sql.replace(",)", ")");
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                Pessoa pessoa = new Pessoa();
                pessoa.setCodigo(consult.getString("Codigo"));
                pessoa.setNome(consult.getString("Nome"));
                pessoa.setPW(consult.getString("PW"));
                retorno.add(pessoa);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getPWs - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Retorna lista de PWs das Pessoas
     *
     * @param CodPessoa
     * @param SenhaCorreta
     * @param persistencia - conexão do banco
     * @param Dt_Alter
     * @param Hr_Alter
     * @throws Exception - pode gerar Exception
     */
    public void guardarAcesso(BigDecimal CodPessoa, boolean SenhaCorreta, String Dt_Alter, String Hr_Alter, Persistencia persistencia) throws Exception {
        StringBuilder sql = new StringBuilder();
        try {
            // Inserir Linha SASpw - [se não existir]
            sql.append("INSERT INTO saspw (Nome, Situacao, Acessos, Codigo, NomeCompleto, PW, CodPessoa, Operador, Dt_Alter, Hr_Alter)\n");
            sql.append(" SELECT\n");
            sql.append(" RTRIM(SUBSTRING(Pessoa.Nome,  1, CHARINDEX(' ', Pessoa.Nome))),\n");
            sql.append(" 'A',\n");
            sql.append(" 0,\n");
            sql.append(" Pessoa.Codigo,\n");
            sql.append(" Pessoa.Nome,\n");
            sql.append(" Pessoa.PWWeb,\n");
            sql.append(" Pessoa.Codigo,\n");
            sql.append(" SUBSTRING(RTRIM(SUBSTRING(Pessoa.Nome, 1, CHARINDEX(' ', Pessoa.Nome))),1,10),\n");
            sql.append(" ?,\n");
            sql.append(" ?\n");
            sql.append(" FROM Pessoa\n");
            sql.append(" LEFT JOIN saspw\n");
            sql.append("   ON Pessoa.Codigo = saspw.CodPessoa\n");
            sql.append("  AND saspw.Nome    = RTRIM(SUBSTRING(Pessoa.Nome,  1, CHARINDEX(' ', Pessoa.Nome)))");
            sql.append(" WHERE pessoa.Codigo = ?\n");
            sql.append(" AND   saspw.CodPessoa IS NULL;");

            if (SenhaCorreta) {
                // Senha Correta
                sql.append(" UPDATE saspw\n");
                sql.append(" SET saspw.Acessos  = 0,\n");
                sql.append("     saspw.Operador = SUBSTRING(RTRIM(SUBSTRING(Pessoa.Nome, 1, CHARINDEX(' ', Pessoa.Nome))),1,10),\n");
                sql.append("     saspw.Dt_Alter = ?,\n");
                sql.append("     saspw.Hr_Alter = ?\n");
                sql.append(" FROM saspw\n");
                sql.append(" JOIN Pessoa\n");
                sql.append("   ON saspw.CodPessoa = Pessoa.Codigo\n");
                sql.append(" WHERE saspw.CodPessoa = ?\n");
                sql.append(" AND   saspw.Situacao  = 'A';");

                sql.append("INSERT INTO saspwacesso (Nome, Data, Hora)\n");
                sql.append(" SELECT\n");
                sql.append(" RTRIM(SUBSTRING(Pessoa.Nome,  1, CHARINDEX(' ', Pessoa.Nome))),\n");
                sql.append(" ?,\n");
                sql.append(" ?\n");
                sql.append(" FROM Pessoa\n");
                sql.append(" LEFT JOIN saspwacesso\n");
                sql.append("   ON RTRIM(SUBSTRING(Pessoa.Nome,  1, CHARINDEX(' ', Pessoa.Nome))) = saspwacesso.Nome\n");
                sql.append("  AND saspwacesso.Hora = '").append(Hr_Alter).append("'");
                sql.append(" WHERE pessoa.Codigo = ?");
                sql.append(" AND   saspwacesso.Data IS NULL;");

            } else {
                // Senha Errada
                sql.append(" UPDATE saspw\n");
                sql.append(" SET saspw.Acessos  = (saspw.Acessos + 1),\n");
                sql.append("     saspw.Situacao = CASE WHEN saspw.Acessos = 4 THEN 'B' ELSE saspw.Situacao END,\n");
                sql.append("     saspw.Operador = SUBSTRING(RTRIM(SUBSTRING(Pessoa.Nome, 1, CHARINDEX(' ', Pessoa.Nome))),1,10),\n");
                sql.append("     saspw.Dt_Alter = ?,\n");
                sql.append("     saspw.Hr_Alter = ?\n");
                sql.append(" FROM saspw\n");
                sql.append(" JOIN Pessoa\n");
                sql.append("   ON saspw.CodPessoa = Pessoa.Codigo\n");
                sql.append(" WHERE saspw.CodPessoa = ?\n");
                sql.append(" AND   saspw.Acessos   < 5;");

            }

            Consulta consult = new Consulta(sql.toString(), persistencia);
            consult.setString(Dt_Alter);
            consult.setString(Hr_Alter);
            consult.setBigDecimal(CodPessoa);

            consult.setString(Dt_Alter);
            consult.setString(Hr_Alter);
            consult.setBigDecimal(CodPessoa);

            if (SenhaCorreta) {
                consult.setString(Dt_Alter);
                consult.setString(Hr_Alter);
                consult.setBigDecimal(CodPessoa);
            }

            consult.insert();
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.guardarAcesso - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    /**
     * Consulta para verificação da matricula do código de pessoa passado
     *
     * @param parametro - campo importante Código
     * @param persistencia - conexão com banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> getMatricula(Pessoa parametro, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList();
            String sql = "select matr FROM pessoa WHERE codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(parametro.getCodigo());
            consult.select();
            while (consult.Proximo()) {
                Pessoa pessoa = new Pessoa();
                pessoa.setCodigo(parametro.getCodigo().toString());
                pessoa.setMatr(consult.getString("matr"));
                retorno.add(pessoa);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getMatricula - " + e.getMessage() + "\r\n"
                    + "select matr FROM pessoa WHERE codigo = " + parametro.getCodigo());
        }
    }

    /**
     * Busca CPF da pessoa pelo código passado
     *
     * @param Codigo - código de pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Pessoa getPessoaCPF(String Codigo, Persistencia persistencia) throws Exception {
        try {
            Pessoa retorno = new Pessoa();
            String sql = "select cpf, codPessoaWeb FROM pessoa WHERE codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Codigo);
            consult.select();
            while (consult.Proximo()) {
                retorno.setCPF(consult.getString("cpf"));
                retorno.setCodPessoaWEB(consult.getString("codPessoaWeb"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getPessoaCPF - " + e.getMessage() + "\r\n"
                    + "select cpf FROM pessoa WHERE codigo = " + Codigo);
        }
    }

    /**
     * Busca dados em pessoa
     *
     * @param CodPessoa - código da pessoa
     * @param persistencia - conexão ao banco de dados
     * @return - dados de retorno: codigo, nome e email
     * @throws Exception
     */
    public Pessoa getPessoaCodigo(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            Pessoa pessoa = new Pessoa();
            String sql = "select codigo, nome, email, pwweb, funcao, matr FROM pessoa WHERE codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setMatr(consult.getString("matr"));
            }
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getPessoaCodigo - " + e.getMessage() + "\r\n"
                    + "select codigo, nome, email, pwweb, funcao, matr FROM pessoa WHERE codigo = " + CodPessoa);
        }
    }

    /**
     * Carrega o código de pessoa
     *
     * @param CPF
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getPessoaCPFCentral(String CPF, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codigo FROM pessoa WHERE cpf = ?";
            String codigo = "";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CPF);
            consult.select();
            while (consult.Proximo()) {
                codigo = consult.getString("codigo");
            }
            consult.Close();
            return codigo;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getPessoaCPFCentral - " + e.getMessage() + "\r\n"
                    + "select codigo FROM pessoa WHERE cpf = " + CPF);
        }
    }

    /**
     * Inserção simplificada de pessoa
     *
     * @param pessoa - objeto pessoa - campos: codigo, nome, cpf, rg
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void inserirPessoaSimples(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "insert into pessoa (codigo,nome,cpf,rg,email) values (?,?,?,?,?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getEmail());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.inserirPessoaSimples - " + e.getMessage() + "\r\n"
                    + "insert into pessoa (codigo,nome,cpf,rg,email) values (" + pessoa.getCodigo() + "," + pessoa.getNome() + "," + pessoa.getCPF() + ","
                    + pessoa.getRG() + "," + pessoa.getEmail() + ")");
        }
    }

    public boolean verificaEmail(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String sql = " select codigo FROM pessoa WHERE email = ? ";
        Consulta consulta = new Consulta(sql, persistencia);
        consulta.setString(pessoa.getEmail());
        consulta.select();
        while (consulta.Proximo()) {
            return true;
        }
        return false;
    }

    /**
     * Inserção simplificada de pessoa, retornando o código da pessoa inserida
     *
     * @param pessoa - objeto pessoa - campos: , rg
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String inserirPessoaExpressa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String codigo = "0";
        String sql = " select codigo FROM pessoa WHERE email = ? ";
        Consulta consulta = new Consulta(sql, persistencia);
        consulta.setString(pessoa.getEmail());
        consulta.select();
        while (consulta.Proximo()) {
            codigo = consulta.getString("codigo");
        }
        if (null == codigo || codigo.equals("") || codigo.equals("0")) {
            sql = "insert into pessoa (codigo, nome, email, pwweb, codpessoaweb) values ((select max(codigo) + 1 sequencia FROM pessoa),?,?,?,?)";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome().toUpperCase());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getPWWeb());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.insert();
            consulta.close();
            sql = " select codigo FROM pessoa WHERE email = ? ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getEmail());
            consulta.select();
            while (consulta.Proximo()) {
                codigo = consulta.getString("codigo");
            }
        } else {
            sql = "update pessoa set nome = ?, email = ?, pwweb = ?, codpessoaweb = ? "
                    + " WHERE codigo = ? ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome().toUpperCase());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getPWWeb());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(codigo);
            consulta.insert();
            consulta.close();
        }
        persistencia.FechaConexao();
        return codigo;
    }

    /**
     * Inserção simplificada de pessoa, retornando o código da pessoa inserida
     *
     * @param pessoa - objeto pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String inserirPessoaExpressaCentral(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String codigo = "0";
        String sql = " select codigo FROM pessoa WHERE email = ? ";
        Consulta consulta = new Consulta(sql, persistencia);
        consulta.setString(pessoa.getEmail());
        consulta.select();
        // Verifica existencia do email no cadastro de pessoas
        if (consulta.Proximo()) {
            codigo = consulta.getString("codigo");
        }
        // Se não existir, insere e retorna o código
        if (null == codigo || codigo.equals("") || codigo.equals("0")) {
            sql = "insert into pessoa (codigo, nome, email) values ((select max(codigo) + 1 sequencia FROM pessoa),?,?)";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome().toUpperCase());
            consulta.setString(pessoa.getEmail());
            consulta.insert();
            consulta.close();
            sql = " select codigo FROM pessoa WHERE email = ? ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getEmail());
            consulta.select();
            if (consulta.Proximo()) {
                codigo = consulta.getString("codigo");
            }
        } else {
            // Se o email existir, apenas atualiza o cadastro
            sql = "update pessoa set nome = ?, email = ? "
                    + " WHERE codigo = ? ";
            consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome().toUpperCase());
            consulta.setString(pessoa.getEmail());
            consulta.setString(codigo);
            consulta.insert();
            consulta.close();
        }
        persistencia.FechaConexao();
        return codigo;
    }

    public String inserirPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE pessoa\n"
                    + "SET nome = ?,\n"
                    + "     email= ?,\n"
                    + "     cpf = ?,\n"
                    + "     RG = ?,\n"
                    + "     RGOrgEmis = ?,\n"
                    + "     Situacao = ?,\n"
                    + "     codPessoaWeb = ?,\n"
                    + "     Operador = ?,\n"
                    + "     Dt_Alter = ?,\n"
                    + "     Hr_Alter = ?\n"
                    + "WHERE cpf = ?\n"
                    + "\n"
                    + "INSERT INTO pessoa (codigo, nome, cpf, RG, RGOrgEmis, Situacao, email, codPessoaWeb, operador, Dt_Alter, Hr_Alter)\n"
                    + "SELECT TOP 1\n"
                    + "CASE WHEN A.qtde_cadastrado = 0 THEN (SELECT ISNULL(MAX(codigo), 0) +1 FROM pessoa) ELSE NULL END,\n"
                    + "?, ?, ?, ?, ?, ?, ?, ?, ?, ? \n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM pessoa\n"
                    + "      WHERE cpf = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getSituacao());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());
            consulta.setString(pessoa.getCPF());

            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getEmail());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());

            consulta.setString(pessoa.getCPF());
            consulta.insert();
            consulta.Close();

            Consulta consulta2 = new Consulta("SELECT codigo FROM pessoa WHERE cpf = ?", persistencia);
            consulta2.setString(pessoa.getCPF());

            consulta2.select();
            String retorno = null;
            if (consulta2.Proximo()) {
                retorno = consulta2.getString("Codigo");
            }
            consulta2.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.inserirPessoa (" + persistencia.getEmpresa() + ") - " + e.getMessage() + "\r\n"
                    + "UPDATE pessoa\n"
                    + "SET nome = " + pessoa.getNome() + ",\n"
                    + "     email= " + pessoa.getEmail() + ",\n"
                    + "     cpf = " + pessoa.getCPF() + ",\n"
                    + "     RG = " + pessoa.getRG() + ",\n"
                    + "     RGOrgEmis = " + pessoa.getRGOrgEmis() + ",\n"
                    + "     Situacao = " + pessoa.getSituacao() + ",\n"
                    + "     codPessoaWeb = " + pessoa.getCodPessoaWEB() + ",\n"
                    + "     Operador = " + pessoa.getOperador() + ",\n"
                    + "     Dt_Alter = " + pessoa.getDt_Alter() + ",\n"
                    + "     Hr_Alter = " + pessoa.getHr_Alter() + "\n"
                    + "WHERE cpf = " + pessoa.getCPF() + "\n"
                    + "\n"
                    + "INSERT INTO pessoa (codigo, nome, cpf, RG, RGOrgEmis, Situacao, email, codPessoaWeb, operador, Dt_Alter, Hr_Alter)\n"
                    + "SELECT TOP 1\n"
                    + "CASE WHEN A.qtde_cadastrado = 0 THEN (SELECT ISNULL(MAX(codigo), 0) +1 FROM pessoa) ELSE NULL END,\n"
                    + "" + pessoa.getNome() + ", " + pessoa.getCPF() + ", " + pessoa.getRG() + ", " + pessoa.getRGOrgEmis() + ", " + pessoa.getSituacao() + ", "
                    + pessoa.getEmail() + ", " + pessoa.getCodPessoaWEB() + ", " + pessoa.getOperador() + ", " + pessoa.getDt_Alter() + ", " + pessoa.getHr_Alter() + " \n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM pessoa\n"
                    + "      WHERE cpf = " + pessoa.getCPF() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;\n"
                    + "\n"
                    + "SELECT codigo FROM pessoa WHERE cpf = " + pessoa.getCPF() + "");
        }
    }

    /**
     * Realiza a edição dos dados referente a pessoa
     *
     * @param pessoa objeto pessoa - campos: codigo, nome, cpf, rg, email
     * @param persistencia Conexão com o banco
     * @throws Exception
     */
    public void editarPessoaSimples(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE pessoa SET nome = ?, cpf = ?, rg = ?, email = ? WHERE codigo = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getEmail());
            consulta.setBigDecimal(pessoa.getCodigo());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.editarPessoaSimples - " + e.getMessage() + "\r\n"
                    + "UPDATE pessoa SET nome = " + pessoa.getNome() + ", cpf = " + pessoa.getCPF() + ", rg = " + pessoa.getRG() + ","
                    + " email = " + pessoa.getEmail() + " WHERE codigo = " + pessoa.getCodigo());
        }
    }

    /**
     * Atualização simplificada de pessoa
     *
     * @param pessoa - objeto pessoa - campos: codigo, nome, cpf, rg
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void atualizarPessoaSimples(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "update pessoa "
                    + "set "
                    + "nome = ?, cpf = ?, rg = ? "
                    + "WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizarPessoaSimples - " + e.getMessage() + "\r\n"
                    + "update pessoa "
                    + "set nome = " + pessoa.getNome() + ", cpf = " + pessoa.getCPF() + ", rg = " + pessoa.getRG()
                    + " WHERE codigo = " + pessoa.getCodigo());
        }
    }

    /**
     * Listagem de pessoas central (dados mínimos por segurança)
     *
     * @param pessoa - objeto pessoa Dados válidos: código, nome, email, cpf, rg
     * (são excludentes, em caso de nenhum setado, devolve a listagem completa)
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> ListagemPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        String sql = "select codigo, codpessoaweb, nome, email FROM pessoa ";
        String parametro;
        Pessoa p;
        if (!"".equals(pessoa.getNome())) {
            sql += "WHERE nome like = ?";
            parametro = pessoa.getNome();
        } else if (pessoa.getCodigo().compareTo(new BigDecimal("0")) > 0) {
            sql += "WHERE codigo = ?";
            parametro = pessoa.getCodigo().toPlainString();
        } else if (!"".equals(pessoa.getEmail())) {
            sql += "WHERE email = ?";
            parametro = pessoa.getEmail();
        } else if (!"".equals(pessoa.getCPF())) {
            sql += "WHERE cpf = ?";
            parametro = pessoa.getCPF();
        } else if (!"".equals(pessoa.getRG())) {
            sql += "WHERE rg = ?";
            parametro = pessoa.getRG();
        } else {
            parametro = "";
        }
        Consulta consult = new Consulta(sql, persistencia);
        if (!"".equals(parametro)) {
            consult.setString(parametro);
        }
        consult.select();
        while (consult.Proximo()) {
            p = new Pessoa();
            p.setCodigo(consult.getString("codigo"));
            p.setNome(consult.getString("nome"));
            p.setEmail(consult.getString("email"));
            p.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
            retorno.add(p);
        }
        consult.Close();
        return retorno;
    }

    /**
     * Busca pessoas que sejam funcionários.
     *
     * @param query
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> buscarPessoaEscala(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList();
            String sql = "select top 20 Pessoa.codigo, Pessoa.nome, Pessoa.email, Pessoa.situacao, Pessoa.matr, Pessoa.pwweb, "
                    + " Funcion.Funcao \n"
                    + " FROM pessoa \n"
                    + " LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr \n"
                    + " WHERE Pessoa.nome like ? AND Pessoa.situacao = 'F' AND codFil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString(codFil);
            consult.select();
            Pessoa p;
            while (consult.Proximo()) {
                p = new Pessoa();
                p.setCodigo(consult.getString("codigo"));
                p.setNome(consult.getString("nome"));
                p.setEmail(consult.getString("email"));
                p.setSituacao(consult.getString("situacao"));
                p.setMatr(consult.getString("matr"));
                p.setFuncao(consult.getString("funcao"));
                p.setPWWeb(consult.getString("pwweb"));
                retorno.add(p);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscarPessoaEscala - " + e.getMessage() + "\r\n"
                    + "select top 20 Pessoa.codigo, Pessoa.nome, Pessoa.email, Pessoa.situacao, Pessoa.matr, Pessoa.pwweb, "
                    + " Funcion.Funcao \n"
                    + " FROM pessoa \n"
                    + " LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr \n"
                    + " WHERE nome like %" + query + "% AND situacao = 'F' AND codFil = " + codFil);
        }
    }

    /**
     * Busca pessoas que sejam funcionários.
     *
     * @param query
     * @param funcao
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> buscarPessoaEscalaFuncao(String query, String funcao, String codFil, Persistencia persistencia) throws Exception {
        try {
            List<Pessoa> retorno = new ArrayList();
            String sql = "select top 20 Pessoa.codigo, Pessoa.nome, Pessoa.email, Pessoa.situacao, Pessoa.matr, Pessoa.pwweb, "
                    + " Funcion.Funcao \n"
                    + " FROM pessoa \n"
                    + " LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr \n"
                    + " WHERE Pessoa.nome like ? AND Funcion.Funcao = ? AND codFil = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString(funcao);
            consult.setString(codFil);
            consult.select();
            Pessoa p;
            while (consult.Proximo()) {
                p = new Pessoa();
                p.setCodigo(consult.getString("codigo"));
                p.setNome(consult.getString("nome"));
                p.setEmail(consult.getString("email"));
                p.setSituacao(consult.getString("situacao"));
                p.setMatr(consult.getString("matr"));
                p.setFuncao(consult.getString("funcao"));
                p.setPWWeb(consult.getString("pwweb"));
                retorno.add(p);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscarPessoaEscalaFuncao - " + e.getMessage() + "\r\n"
                    + "select top 20 Pessoa.codigo, Pessoa.nome, Pessoa.email, Pessoa.situacao, Pessoa.matr, Pessoa.pwweb, "
                    + " Funcion.Funcao \n"
                    + " FROM pessoa \n"
                    + " LEFT JOIN Funcion ON Funcion.Matr = Pessoa.Matr \n"
                    + " WHERE Pessoa.nome like %" + query + "% AND Funcion.Funcao = " + funcao + " AND codFil = " + codFil);
        }
    }

    /**
     * Listagem de pessoas central por algum parametro (dados mínimos por
     * segurança)
     *
     * @param query - string com a busca Dados válidos: código, nome, email,
     * cpf, rg (são excludentes, em caso de nenhum setado, devolve a listagem
     * completa)
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> listagemPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        String sql = "select top 20 codigo, nome, email, situacao, codpessoaweb, CPF, RG, RGOrgEmis, Matr FROM pessoa \n"
                + " WHERE nome like ? \n"
                + " or email like ? \n"
                + " ORDER BY Nome ";
        Consulta consult = new Consulta(sql, persistencia);
        consult.setString("%" + query.toUpperCase() + "%");
        consult.setString("%" + query.toUpperCase() + "%");
        consult.select();
        Pessoa p;
        while (consult.Proximo()) {
            p = new Pessoa();
            p.setCodigo(consult.getString("codigo"));
            p.setNome(consult.getString("nome"));
            p.setCPF(consult.getString("CPF"));
            p.setRG(consult.getString("RG"));
            p.setRGOrgEmis(consult.getString("RGOrgEmis"));
            p.setMatr(consult.getString("Matr"));
            p.setEmail(consult.getString("email"));
            p.setSituacao(consult.getString("situacao"));
            p.setCodPessoaWEB(consult.getString("codpessoaweb"));
            retorno.add(p);
        }
        consult.Close();
        return retorno;
    }

    public List<Pessoa> ListagemPessoaQueryValida(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        String sql = "select top 20 pessoa.codigo, pessoa.nome, pessoa.email, pessoa.situacao, funcion.nome_guer "
                + " FROM pessoa "
                + " left join funcion ON funcion.matr = pessoa.matr "
                + " WHERE (pessoa.nome like ? or pessoa.email like ?) "
                + " AND (pessoa.codpessoaweb is not null or pessoa.codpessoaweb not like '') ";
        Consulta consult = new Consulta(sql, persistencia);
        consult.setString("%" + query.toUpperCase() + "%");
        consult.setString("%" + query.toUpperCase() + "%");
        consult.select();
        Pessoa p;
        while (consult.Proximo()) {
            p = new Pessoa();
            p.setCodigo(consult.getString("codigo"));
            p.setNome(consult.getString("nome"));
            p.setEmail(consult.getString("email"));
            p.setSituacao(consult.getString("situacao"));
            /**
             * Salvando o nome de guerra para uso no cadastro de usuário
             * SatMobWeb
             */
            p.setEndereco(consult.getString("nome_guer"));
            retorno.add(p);
        }
        consult.Close();
        return retorno;
    }

    /**
     * Listagem de pessoas geral para o SatMobWeb
     *
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> ListaPessoaMobWeb(Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<Pessoa> retorno = new ArrayList();
            sql = "select top 50 codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + "situacao,obs,matr,funcao, sexo, altura, peso, operador, dt_alter, hr_alter,email,"
                    + "codpessoaweb, dt_situac, pwweb, codcli, complemento "
                    + "FROM pessoa"
                    + " order BY nome asc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            Pessoa pessoa;
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setOperador(consult.getString("operador"));
                pessoa.setDt_Alter(consult.getString("dt_alter"));
                pessoa.setHr_Alter(consult.getString("hr_alter"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setCodCli(consult.getString("codcli"));
                pessoa.setComplemento(consult.getString("complemento"));
                retorno.add(pessoa);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.ListaPessoaMobWeb - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public Pessoa ListaPessoaMobWebFromGTVe(String CodPessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        Pessoa pessoa = new Pessoa();

        try {
            sql = "select * FROM Pessoa where codigo = ?";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodPessoa);
            consult.select();

            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setOperador(consult.getString("operador"));
                pessoa.setDt_Alter(consult.getString("dt_alter"));
                pessoa.setHr_Alter(consult.getString("hr_alter"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setCodCli(consult.getString("codcli"));
                pessoa.setComplemento(consult.getString("complemento"));
            }
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.ListaPessoaMobWebFromGTVe - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public String gerarRG(Persistencia persistencia) throws Exception {
        String sql = "";
        String RG = "";

        try {
            sql = "SELECT \n"
                    + " ISNULL((select \n"
                    + "         ISNULL(MAX(RG), 20010000) + 1\n"
                    + "         from Pessoa\n"
                    + "         WHERE RGOrgEmis = 'MSL'\n"
                    + "         AND LEFT(RG,4) = '2001'), '20010001') RG";

            Consulta consult = new Consulta(sql, persistencia);
            consult.select();

            while (consult.Proximo()) {
                RG = consult.getString("RG");
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.gerarRG - " + e.getMessage() + "\r\n" + sql);
        }
        return RG;
    }

    public void InserirPessoaSatMobWeb(Pessoa pessoa, Persistencia persistencia) throws Exception {

        try {
            String sql = "insert into pessoa (codigo,codpessoaweb,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + "obs,matr,funcao, sexo, altura, peso,situacao,pwweb, operador, dt_alter, hr_alter, email, dt_situac, "
                    + " CarNacVig, DtValCNV, mae, PIS, dt_nasc, CNH, CNHDtVenc,"
                    + "CodCli,"
                    + " Complemento) values "
                    + "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,"
                    + "?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getObs());
            consulta.setBigDecimal(pessoa.getMatr());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getSexo());
            consulta.setBigDecimal(pessoa.getAltura());
            consulta.setBigDecimal(pessoa.getPeso());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getPWWeb());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getDt_Situac());
            consulta.setString(pessoa.getCarNacVig());
            consulta.setString(pessoa.getDtValCNV());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getDt_nasc());
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHDtVenc());
            consulta.setString(pessoa.getCodCli());
            consulta.setString(pessoa.getComplemento());

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("PessaoDao.InserirPessoaSatMobWeb - " + e.getMessage() + "\r\n"
                    + " insert into pessoa (codigo,codpessoaweb,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " obs,matr,funcao, sexo, altura, peso,situacao,pwweb, operador, dt_alter, hr_alter, email, dt_situac) values "
                    + " (" + pessoa.getCodigo() + "," + pessoa.getCodPessoaWEB() + "," + pessoa.getNome() + "," + pessoa.getRG() + "," + pessoa.getRGOrgEmis() + ","
                    + pessoa.getCPF() + "," + pessoa.getFone1() + "," + pessoa.getFone2() + "," + pessoa.getEndereco() + "," + pessoa.getBairro() + "," + pessoa.getCidade()
                    + "," + pessoa.getUF() + "," + pessoa.getCEP() + "," + pessoa.getObs() + "," + pessoa.getMatr() + "," + pessoa.getFuncao() + "," + pessoa.getSexo() + ","
                    + pessoa.getAltura() + "," + pessoa.getPeso() + "," + pessoa.getSituacao() + "," + pessoa.getPWWeb() + "," + pessoa.getOperador() + ","
                    + pessoa.getDt_Alter() + "," + pessoa.getHr_Alter() + "," + pessoa.getEmail() + "," + pessoa.getDt_Situac() + ")");
        }
    }

    public void atualizarPessoaDieta(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            if (pessoa.getSituacao().equals("I")) {
                sql = "SELECT top 1 CodDieta FROM PessoaDieta WHERE PessoaDieta.CodPessoa = ";
                if (null != pessoa.getCodigo() && !pessoa.getCodigo().equals(0) && !pessoa.getCodigo().equals(0.0)) {
                    sql += new Float(pessoa.getCodigo().toPlainString());

                } else {
                    sql += "(SELECT MAX(Codigo) FROM Pessoa)";
                }
                sql += " ORDER BY Dt_alter DESC";
                Consulta consulta = new Consulta(sql, persistencia);
                consulta.select();

                boolean Execute = true;

                while (consulta.Proximo()) {
                    if (consulta.getFloat("CodDieta") == new Float(pessoa.getCodDieta())) {
                        Execute = false;
                    }
                }

                if (Execute) {
                    sql = "insert into PessoaDieta (CodPessoa, Data, CodDieta, Operador, Dt_alter, Hr_Alter) values(";
                    if (null != pessoa.getCodigo() && !pessoa.getCodigo().equals(0) && !pessoa.getCodigo().equals(0.0)) {
                        sql += new Float(pessoa.getCodigo().toPlainString());

                    } else {
                        sql += "(SELECT MAX(Codigo) FROM Pessoa)";
                    }
                    sql += ", '" + pessoa.getDt_Alter() + "',"
                            + " " + new Float(pessoa.getCodDieta()) + ","
                            + " '" + pessoa.getOperador() + "',"
                            + " '" + pessoa.getDt_Alter() + "',"
                            + " '" + pessoa.getHr_Alter() + "')";

                    consulta = new Consulta(sql, persistencia);
                    consulta.insert();
                    consulta.close();
                }
            }
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizarPessoaDieta - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void atualizaPessoaSatMob(Pessoa pessoa, Persistencia persistencia) throws Exception {
        String sql = "";
        try {

            sql = "DECLARE @CodIbge AS BIGINT;\n"
                    + " SET @CodIbge = (SELECT TOP 1 CONVERT(BIGINT, CodIBGE) FROM Municipios where Nome = '" + pessoa.getCidade() + "' AND UF = '" + pessoa.getUF() + "');";

            sql += " UPDATE pessoa SET "
                    + "nome = ?, "
                    + "cpf = ?, "
                    + "rg = ?, "
                    + "rgorgemis = ?, "
                    + "email = ?, "
                    + "fone1 = ?, "
                    + "fone2 = ?, "
                    + "endereco = ?, "
                    + " bairro = ?, "
                    + "cidade = ?, "
                    + "uf = ?, "
                    + "cep = ?, "
                    + "CodCidade = @CodIbge, "
                    + "obs = ?, "
                    + "matr = ?, "
                    + "funcao = ?, "
                    + "sexo = ?, "
                    + "altura = ?, "
                    + "peso = ?, "
                    + "situacao = ?, "
                    + " operador = ?, "
                    + "dt_alter = ?, "
                    + "hr_alter = ?, "
                    + "dt_situac = ?, "
                    + "indicacao = ?, "
                    + "dt_FormIni = ?, "
                    + "dt_FormFim = ?, "
                    + "localForm = ?, "
                    + " certific = ?, "
                    + "dt_Recicl = ?, "
                    + "dt_VenCurs = ?, "
                    + "reg_PF = ?, "
                    + "reg_PFUF = ?, "
                    + "reg_PFDt = ?, "
                    + "CNH = ?, "
                    //+ "CNHDtVenc = ?, "
                    + "extTV = ?, "
                    + " extSPP = ?, "
                    + "extEscolta = ?, "
                    //+ "dt_nasc = ?, "
                    + "mae = ?,  "
                    + "CarNacVig = ?, "
                    //+ "DtValCNV = ?,
                    + "PIS = ?, "
                    + "Codcli = ?, "
                    + "Complemento = ? ";
            if (null != pessoa.getCodPessoaWEB()) {
                sql = sql + ", codpessoaweb = ? ";
            }
            if (null != pessoa.getCodigo()) {
                sql = sql + " WHERE codigo = ?";
            }
            /*else {
                sql = sql + " WHERE matr = ?";
            }*/

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getObs());
            consulta.setBigDecimal(pessoa.getMatr());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getSexo());
            consulta.setBigDecimal(pessoa.getAltura());
            consulta.setBigDecimal(pessoa.getPeso());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getOperador());
            //consulta.setString(pessoa.getDt_Alter());
            //consulta.setString(pessoa.getHr_Alter());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            //consulta.setString(pessoa.getDt_Situac());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(pessoa.getIndicacao());
            consulta.setString(pessoa.getDt_FormIni());
            consulta.setString(pessoa.getDt_FormFim());
            consulta.setString(pessoa.getLocalForm());
            consulta.setString(pessoa.getCertific());
            consulta.setString(pessoa.getDt_Recicl());
            consulta.setString(pessoa.getDt_VenCurs());
            consulta.setString(pessoa.getReg_PF());
            consulta.setString(pessoa.getReg_PFUF());
            consulta.setString(pessoa.getReg_PFDt());
            consulta.setString(pessoa.getCNH());
            //consulta.setString(pessoa.getCNHDtVenc().replace(" 00:00:00.0", ""));
            consulta.setString(pessoa.getExtTV());
            consulta.setString(pessoa.getExtSPP());
            consulta.setString(pessoa.getExtEscolta());
            //consulta.setString(pessoa.getDt_nasc());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getCarNacVig());
            //consulta.setString(pessoa.getDtValCNV());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCodCli());
            consulta.setString(pessoa.getComplemento());

            if (null != pessoa.getCodPessoaWEB()) {
                consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            }
            if (null != pessoa.getCodigo()) {
                consulta.setBigDecimal(pessoa.getCodigo());
            }
            /*else {
                consulta.setBigDecimal(pessoa.getMatr());
            }*/

            if (null != pessoa.getCodigo()) {
                consulta.update();
            }
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.AtualizaPessoaSatMob - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void atualizaCandidatoSatMob(Pessoa pessoa, Persistencia persistencia, boolean isPersistenciaCentral) throws Exception {
        String sql = "";
        try {
            if (pessoa.getCodigo() == null) {
                if (isPersistenciaCentral) {
                    throw new Exception("ErroSalvarBD"); // TODO
                } else if (pessoa.getMatr() == null || pessoa.getMatr().equals("0")) {
                    throw new Exception("ErroSalvarBD"); // TODO
                }
            }

            sql = "DECLARE @CodIbge AS BIGINT;\n"
                    + " SET @CodIbge = (SELECT TOP 1 CONVERT(BIGINT, CodIBGE) FROM Municipios where Nome = '" + pessoa.getCidade() + "' AND UF = '" + pessoa.getUF() + "');";

            sql += "UPDATE pessoa SET "
                    + "nome = ?, "
                    + "rg = ?, "
                    + "rgorgemis = ?, "
                    + "email = ?, "
                    + "fone1 = ?, "
                    + "fone2 = ?, "
                    + "endereco = ?, "
                    + " bairro = ?, "
                    + "cidade = ?, "
                    + "uf = ?, "
                    + "cep = ?, "
                    + "CodCidade = @CodIbge, "
                    + "obs = ?, "
                    + "matr = ?, "
                    + "funcao = ?, "
                    + "sexo = ?, "
                    + "altura = ?, "
                    + "peso = ?, "
                    + "situacao = ?, "
                    + " operador = ?, "
                    + "dt_alter = ?, "
                    + "hr_alter = ?, "
                    + "dt_situac = ?, "
                    + "indicacao = ?, "
                    + "dt_FormIni = ?, "
                    + "dt_FormFim = ?, "
                    + "localForm = ?, "
                    + " certific = ?, "
                    + "dt_Recicl = ?, "
                    + "dt_VenCurs = ?, "
                    + "reg_PF = ?, "
                    + "reg_PFUF = ?, "
                    + "reg_PFDt = ?, "
                    + "CNH = ?, "
                    + "CNHDtVenc = ?, "
                    + "extTV = ?, "
                    + " extSPP = ?, "
                    + "extEscolta = ?, "
                    + "dt_nasc = ?, "
                    + "mae = ?,  "
                    + "CarNacVig = ?, "
                    + "DtValCNV = ?, "
                    + "PIS = ?, "
                    + "Codcli = ?, "
                    + "Complemento = ? ";
            if (null != pessoa.getCodPessoaWEB()) {
                sql = sql + ", codpessoaweb = ? ";
            }
            if (null != pessoa.getCodigo()) {
                sql = sql + " WHERE codigo = ?";
            } else {
                sql = sql + " WHERE matr = ?";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getFone1());
            consulta.setString(pessoa.getFone2());
            consulta.setString(pessoa.getEndereco());
            consulta.setString(pessoa.getBairro());
            consulta.setString(pessoa.getCidade());
            consulta.setString(pessoa.getUF());
            consulta.setString(pessoa.getCEP());
            consulta.setString(pessoa.getObs());
            consulta.setBigDecimal(pessoa.getMatr());
            consulta.setString(pessoa.getFuncao());
            consulta.setString(pessoa.getSexo());
            consulta.setBigDecimal(pessoa.getAltura());
            consulta.setBigDecimal(pessoa.getPeso());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getOperador());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(pessoa.getIndicacao());
            consulta.setString(pessoa.getDt_FormIni());
            consulta.setString(pessoa.getDt_FormFim());
            consulta.setString(pessoa.getLocalForm());
            consulta.setString(pessoa.getCertific());
            consulta.setString(pessoa.getDt_Recicl());
            consulta.setString(pessoa.getDt_VenCurs());
            consulta.setString(pessoa.getReg_PF());
            consulta.setString(pessoa.getReg_PFUF());
            consulta.setString(pessoa.getReg_PFDt());
            consulta.setString(pessoa.getCNH());
            consulta.setString(pessoa.getCNHDtVenc());
            consulta.setString(pessoa.getExtTV());
            consulta.setString(pessoa.getExtSPP());
            consulta.setString(pessoa.getExtEscolta());
            consulta.setString(pessoa.getDt_nasc());
            consulta.setString(pessoa.getMae());
            consulta.setString(pessoa.getCarNacVig());
            consulta.setString(pessoa.getDtValCNV());
            consulta.setString(pessoa.getPIS());
            consulta.setString(pessoa.getCodCli());
            consulta.setString(pessoa.getComplemento());

            if (null != pessoa.getCodPessoaWEB()) {
                consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            }
            if (null != pessoa.getCodigo()) {
                consulta.setBigDecimal(pessoa.getCodigo());
            } else {
                consulta.setBigDecimal(pessoa.getMatr());
            }
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.AtualizaPessoaSatMob - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void AtualizaPessoaSatMobRH(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE pessoa SET codpessoaweb = ?, pwportal = ?"
                    + " WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getPWPortal());
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.AtualizaPessoaSatMobRH - " + e.getMessage() + "\r\n"
                    + "UPDATE pessoa SET codpessoaweb = " + pessoa.getCodPessoaWEB() + ", pwportal = " + pessoa.getPWPortal()
                    + " WHERE codigo = " + pessoa.getCodigo());
        }
    }

    public Pessoa buscarPessoaCPF(String cpf, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep, "
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac, indicacao, dt_FormIni, dt_FormFim, localForm, "
                    + " certific, dt_Recicl, dt_VenCurs, reg_PF, reg_PFUF, reg_PFDt, CNH, CNHDtVenc, extTV, extSPP, extEscolta "
                    + " FROM pessoa "
                    + " WHERE cpf = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cpf);
            consult.select();
            Pessoa pessoa = new Pessoa();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setIndicacao(consult.getString("indicacao"));
                pessoa.setDt_FormIni(consult.getString("dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("dt_FormFim"));
                pessoa.setLocalForm(consult.getString("localForm"));
                pessoa.setCertific(consult.getString("certific"));
                pessoa.setDt_Recicl(consult.getString("dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("dt_VenCurs"));
                pessoa.setReg_PF(consult.getString("reg_PF"));
                pessoa.setReg_PFUF(consult.getString("reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("reg_PFDt"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(consult.getString("CNHDtVenc"));
                pessoa.setExtTV(consult.getString("extTV"));
                pessoa.setExtSPP(consult.getString("extSPP"));
                pessoa.setExtEscolta(consult.getString("extEscolta"));
                return pessoa;
            }
            pessoa.setCPF(cpf);
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.BuscarPessoaCPF - " + e.getMessage() + "\r\n"
                    + "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac "
                    + " FROM pessoa"
                    + " WHERE cpf=" + cpf);
        }
    }

    public Pessoa buscarPessoaCPFAndEmail(String cpf, String email, Persistencia persistencia) throws Exception {
        Consulta consulta = null;
        String sql = "SELECT codigo,\n"
                + "       altura,\n"
                + "       bairro,\n"
                + "       carNacVig,\n"
                + "       cep,\n"
                + "       certific,\n"
                + "       cidade,\n"
                + "       CNH,\n"
                + "       CNHDtVenc,\n"
                + "       CodPessoaWEB,\n"
                + "       cpf,\n"
                + "       dt_FormFim,\n"
                + "       dt_FormIni,\n"
                + "       Dt_nasc,\n"
                + "       dt_Recicl,\n"
                + "       dt_situac,\n"
                + "       dt_VenCurs,\n"
                + "       dtValCNV,\n"
                + "       email,\n"
                + "       endereco,\n"
                + "       extEscolta,\n"
                + "       extSPP,\n"
                + "       extTV,\n"
                + "       fone1,\n"
                + "       fone2,\n"
                + "       funcao,\n"
                + "       indicacao,\n"
                + "       localForm,\n"
                + "       Mae,\n"
                + "       matr,\n"
                + "       nome,\n"
                + "       obs,\n"
                + "       peso,\n"
                + "       PIS,\n"
                + "       reg_PF,\n"
                + "       reg_PFDt,\n"
                + "       reg_PFUF,\n"
                + "       rg,\n"
                + "       rgorgemis,\n"
                + "       sexo,\n"
                + "       situacao,\n"
                + "       uf\n"
                + "FROM pessoa\n"
                + "WHERE cpf = ? \n"
                + "  AND email = ? ";

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(cpf);
            consulta.setString(email);
            consulta.select();
            Pessoa pessoa = new Pessoa();

            while (consulta.Proximo()) {
                pessoa.setAltura(consulta.getString("altura"));
                pessoa.setBairro(consulta.getString("bairro"));
                pessoa.setCarNacVig(consulta.getString("carNacVig"));
                pessoa.setCEP(consulta.getString("cep"));
                pessoa.setCertific(consulta.getString("certific"));
                pessoa.setCidade(consulta.getString("cidade"));
                pessoa.setCNH(consulta.getString("CNH"));
                pessoa.setCNHDtVenc(consulta.getString("CNHDtVenc"));
                pessoa.setCodigo(consulta.getBigDecimal("codigo"));
                pessoa.setCodPessoaWEB(consulta.getString("CodPessoaWEB"));
                pessoa.setCPF(consulta.getString("cpf"));
                pessoa.setDt_FormFim(consulta.getString("dt_FormFim"));
                pessoa.setDt_FormIni(consulta.getString("dt_FormIni"));
                pessoa.setDt_nasc(consulta.getString("Dt_nasc"));
                pessoa.setDt_Recicl(consulta.getString("dt_Recicl"));
                pessoa.setDt_Situac(consulta.getString("dt_situac"));
                pessoa.setDt_VenCurs(consulta.getString("dt_VenCurs"));
                pessoa.setDtValCNV(consulta.getString("dtValCNV"));
                pessoa.setEmail(consulta.getString("email"));
                pessoa.setEndereco(consulta.getString("endereco"));
                pessoa.setExtEscolta(consulta.getString("extEscolta"));
                pessoa.setExtSPP(consulta.getString("extSPP"));
                pessoa.setExtTV(consulta.getString("extTV"));
                pessoa.setFone1(consulta.getString("fone1"));
                pessoa.setFone2(consulta.getString("fone2"));
                pessoa.setFuncao(consulta.getString("funcao"));
                pessoa.setIndicacao(consulta.getString("indicacao"));
                pessoa.setLocalForm(consulta.getString("localForm"));
                pessoa.setMae(consulta.getString("Mae"));
                pessoa.setMatr(consulta.getString("matr"));
                pessoa.setNome(consulta.getString("nome"));
                pessoa.setObs(consulta.getString("obs"));
                pessoa.setPeso(consulta.getString("peso"));
                pessoa.setPIS(consulta.getString("PIS"));
                pessoa.setReg_PF(consulta.getString("reg_PF"));
                pessoa.setReg_PFDt(consulta.getString("reg_PFDt"));
                pessoa.setReg_PFUF(consulta.getString("reg_PFUF"));
                pessoa.setRG(consulta.getString("rg"));
                pessoa.setRGOrgEmis(consulta.getString("rgorgemis"));
                pessoa.setSexo(consulta.getString("sexo"));
                pessoa.setSituacao(consulta.getString("situacao"));
                pessoa.setUF(consulta.getString("uf"));

                return pessoa;
            }
            return null;
        } catch (Exception e) {
            throw e;
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public Pessoa buscarPessoaCodigo(BigDecimal codigoPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep, "
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac, indicacao, dt_FormIni, dt_FormFim, localForm, "
                    + " certific, dt_Recicl, dt_VenCurs, reg_PF, reg_PFUF, reg_PFDt, CNH, CNHDtVenc, extTV, extSPP, extEscolta, Pwweb "
                    + " FROM pessoa "
                    + " WHERE codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codigoPessoa);
            consult.select();
            Pessoa pessoa = new Pessoa();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setIndicacao(consult.getString("indicacao"));
                pessoa.setDt_FormIni(consult.getString("dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("dt_FormFim"));
                pessoa.setLocalForm(consult.getString("localForm"));
                pessoa.setCertific(consult.getString("certific"));
                pessoa.setDt_Recicl(consult.getString("dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("dt_VenCurs"));
                pessoa.setReg_PF(consult.getString("reg_PF"));
                pessoa.setReg_PFUF(consult.getString("reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("reg_PFDt"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(consult.getString("CNHDtVenc"));
                pessoa.setExtTV(consult.getString("extTV"));
                pessoa.setExtSPP(consult.getString("extSPP"));
                pessoa.setExtEscolta(consult.getString("extEscolta"));
                pessoa.setPWWeb(consult.getString("Pwweb"));
            }
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscarPessoaCodigo - " + e.getMessage() + "\r\n"
                    + "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac "
                    + " FROM pessoa"
                    + " WHERE codigo=" + codigoPessoa.toPlainString());
        }
    }

    public Pessoa BuscarPessoaCPFCentral(String cpf, Persistencia persistencia) throws Exception {
        try {
            String sql = "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac "
                    + " FROM pessoa"
                    + " WHERE cpf=?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(cpf);
            consult.select();
            Pessoa pessoa = new Pessoa();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                return pessoa;
            }
            pessoa.setCPF("-1");
            consult.Close();
            return pessoa;
        } catch (Exception e) {
            throw new Exception("PessoaDao.BuscarPessoaCPFCentral - " + e.getMessage() + "\r\n"
                    + "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " situacao,obs,matr,funcao, sexo, altura, peso, email, dt_situac "
                    + " FROM pessoa"
                    + " WHERE cpf=" + cpf);
        }
    }

    public String inserirPessoaRG(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE pessoa\n"
                    + "SET nome = ?,\n"
                    + "     email= ?,\n"
                    + "     pwweb= ?,\n"
                    + "     cpf = ?,\n"
                    + "     RG = ?,\n"
                    + "     RGOrgEmis = ?,\n"
                    + "     Situacao = ?,\n"
                    + "     codPessoaWeb = ?,\n"
                    + "     Operador = ?,\n"
                    + "     Dt_Alter = ?,\n"
                    + "     Hr_Alter = ?\n"
                    + "WHERE rg = ? AND rgorgemis = ?\n"
                    + "\n"
                    + "INSERT INTO pessoa (codigo, nome, cpf, RG, RGOrgEmis, Situacao, email, pwweb, codPessoaWeb, operador, Dt_Alter, Hr_Alter)\n"
                    + "SELECT TOP 1\n"
                    + "CASE WHEN A.qtde_cadastrado = 0 THEN (SELECT ISNULL(MAX(codigo), 0) +1 FROM pessoa) ELSE NULL END,\n"
                    + "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? \n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM pessoa\n"
                    + "      WHERE rg = ? AND rgorgemis = ?) AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;\n";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getPWWeb());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getSituacao());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());

            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());

            consulta.setString(pessoa.getNome());
            consulta.setString(pessoa.getCPF());
            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());
            consulta.setString(pessoa.getSituacao());
            consulta.setString(pessoa.getEmail());
            consulta.setString(pessoa.getPWWeb());
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setString(pessoa.getOperador());
            consulta.setString(pessoa.getDt_Alter());
            consulta.setString(pessoa.getHr_Alter());

            consulta.setString(pessoa.getRG());
            consulta.setString(pessoa.getRGOrgEmis());

            consulta.insert();
            consulta.Close();

            Consulta consulta2 = new Consulta("SELECT codigo FROM pessoa WHERE rg = ? AND rgorgemis = ?", persistencia);

            consulta2.setString(pessoa.getRG());
            consulta2.setString(pessoa.getRGOrgEmis());

            consulta2.select();
            String retorno = null;
            if (consulta2.Proximo()) {
                retorno = consulta2.getString("Codigo");
            }
            consulta2.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.inserirPessoa (" + persistencia.getEmpresa() + ") - " + e.getMessage() + "\r\n"
                    + "UPDATE pessoa\n"
                    + "SET nome = " + pessoa.getNome() + ",\n"
                    + "     email= " + pessoa.getEmail() + ",\n"
                    + "     cpf = " + pessoa.getCPF() + ",\n"
                    + "     RG = " + pessoa.getRG() + ",\n"
                    + "     RGOrgEmis = " + pessoa.getRGOrgEmis() + ",\n"
                    + "     Situacao = " + pessoa.getSituacao() + ",\n"
                    + "     codPessoaWeb = " + pessoa.getCodPessoaWEB() + ",\n"
                    + "     Operador = " + pessoa.getOperador() + ",\n"
                    + "     Dt_Alter = " + pessoa.getDt_Alter() + ",\n"
                    + "     Hr_Alter = " + pessoa.getHr_Alter() + "\n"
                    + "WHERE cpf = " + pessoa.getCPF() + "\n"
                    + "\n"
                    + "INSERT INTO pessoa (codigo, nome, cpf, RG, RGOrgEmis, Situacao, email, codPessoaWeb, operador, Dt_Alter, Hr_Alter)\n"
                    + "SELECT TOP 1\n"
                    + "CASE WHEN A.qtde_cadastrado = 0 THEN (SELECT ISNULL(MAX(codigo), 0) +1 FROM pessoa) ELSE NULL END,\n"
                    + "" + pessoa.getNome() + ", " + pessoa.getCPF() + ", " + pessoa.getRG() + ", " + pessoa.getRGOrgEmis() + ", " + pessoa.getSituacao() + ", "
                    + pessoa.getEmail() + ", " + pessoa.getCodPessoaWEB() + ", " + pessoa.getOperador() + ", " + pessoa.getDt_Alter() + ", " + pessoa.getHr_Alter() + " \n"
                    + "FROM (SELECT\n"
                    + "      COUNT(*) AS qtde_cadastrado \n"
                    + "      FROM pessoa\n"
                    + "      WHERE cpf = " + pessoa.getCPF() + ") AS A\n"
                    + "WHERE A.qtde_cadastrado = 0;\n"
                    + "\n"
                    + "SELECT codigo FROM pessoa WHERE cpf = " + pessoa.getCPF() + "");
        }
    }

    public UsuarioSatMobWeb buscarPessoaRG(String rg, String rgorgemis, Persistencia persistencia) throws Exception {
        try {
            String sql = " select pessoa.situacao situacaoPessoa, pessoa.nome pnome, \n"
                    + " saspw.nome snome, saspw.descricao sdesc, ISNULL(saspw.codgrupo, 21) scodgrupo,  ISNULL(saspw.nivelx, 1) snivelx, "
                    + " * \n"
                    + " FROM pessoa \n"
                    + " left join saspw ON saspw.codpessoa = pessoa.codigo \n"
                    + " WHERE rg = ? AND rgorgemis = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rg);
            consulta.setString(rgorgemis);
            consulta.select();
            UsuarioSatMobWeb usuario = null;
            Pessoa p;
            Saspw s;
            PessoaLogin pl;
            SASGrupos g;
            if (consulta.Proximo()) {
                usuario = new UsuarioSatMobWeb();
                p = new Pessoa();
                p.setCPF(consulta.getString("CPF"));
                p.setRG(consulta.getString("RG"));
                p.setRGOrgEmis(consulta.getString("RGOrgEmis"));
                p.setSituacao(consulta.getString("situacaoPessoa"));
                p.setNome(consulta.getString("pnome"));
                p.setPWWeb(consulta.getString("PWWEB"));
                p.setEmail(consulta.getString("email"));
                p.setCodigo(consulta.getString("codigo"));
                p.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));

                s = new Saspw();
                s.setCodFil(consulta.getString("codfil"));
                s.setSituacao(consulta.getString("situacao"));
                s.setNome(consulta.getString("snome"));
                s.setDescricao(consulta.getString("sdesc"));
                s.setNivelx(consulta.getString("snivelx"));
                s.setNivelOP(consulta.getString("NivelOP"));
                s.setCodGrupo(consulta.getInt("scodgrupo"));
                s.setMotivo(consulta.getString("motivo"));
                s.setOperador(consulta.getString("operador"));
                s.setDt_Alter(consulta.getString("dt_alter"));
                s.setHr_Alter(consulta.getString("hr_alter"));
                s.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                usuario.setGrupo(new SASGrupos());
                usuario.setPessoa(p);
                usuario.setPessoalogin(new PessoaLogin());
                usuario.setSaspw(s);

            }
            consulta.Close();
            return usuario;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscarPessoaRG - " + e.getMessage() + "\r\n"
                    + " select pessoa.situacao situacaoPessoa, pessoa.nome pnome, \n"
                    + " saspw.nome snome, saspw.descricao sdesc, * \n"
                    + " FROM pessoa \n"
                    + " left join saspw ON saspw.codpessoa = pessoa.codigo \n"
                    + " WHERE rg = " + rg + " rgorgemis = " + rgorgemis);
        }
    }

    public List<Pessoa> pesquisaPessoaSatMobWeb(Pessoa pessoa, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        try {
            String sql = "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + "situacao,obs,matr,funcao, sexo, altura, peso, operador, dt_alter, hr_alter,email,codpessoaweb, dt_situac "
                    + "FROM pessoa WHERE codigo is not null";
            if (!pessoa.getNome().equals("")) {
                sql = sql + " AND nome like ?";
            }
            if (null != pessoa.getSituacao()) {
                sql = sql + " AND situacao like ?";
            }
            if (null != pessoa.getCidade()) {
                sql = sql + " AND cidade like ?";
            }
            if (!pessoa.getUF().equals("")) {
                sql = sql + " AND uf like ?";
            }
            Consulta consult = new Consulta(sql, persistencia);
            if (!pessoa.getNome().equals("")) {
                consult.setString("%" + pessoa.getNome().toUpperCase() + "%");
            }
            if (null != pessoa.getSituacao()) {
                consult.setString("%" + pessoa.getSituacao().toUpperCase() + "%");
            }
            if (null != pessoa.getCidade()) {
                consult.setString("%" + pessoa.getCidade().toUpperCase() + "%");
            }
            if (!pessoa.getUF().equals("")) {
                consult.setString("%" + pessoa.getUF().toUpperCase() + "%");
            }
            consult.select();
            Pessoa p;
            while (consult.Proximo()) {
                p = new Pessoa();
                p.setCodigo(consult.getBigDecimal("codigo"));
                p.setNome(consult.getString("nome"));
                p.setRG(consult.getString("rg"));
                p.setRGOrgEmis(consult.getString("rgorgemis"));
                p.setCPF(consult.getString("cpf"));
                p.setFone1(consult.getString("fone1"));
                p.setFone2(consult.getString("fone2"));
                p.setEndereco(consult.getString("endereco"));
                p.setBairro(consult.getString("bairro"));
                p.setCidade(consult.getString("cidade"));
                p.setUF(consult.getString("uf"));
                p.setCEP(consult.getString("cep"));
                p.setSituacao(consult.getString("situacao"));
                p.setObs(consult.getString("obs"));
                p.setMatr(consult.getString("matr"));
                p.setFuncao(consult.getString("funcao"));
                p.setSexo(consult.getString("sexo"));
                p.setAltura(consult.getString("altura"));
                p.setPeso(consult.getString("peso"));
                p.setOperador(consult.getString("operador"));
                p.setDt_Alter(consult.getString("dt_alter"));
                p.setHr_Alter(consult.getString("hr_alter"));
                p.setEmail(consult.getString("email"));
                p.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                p.setDt_Situac(consult.getString("dt_situac"));
                retorno.add(p);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.pesquisaPessoaSatMobWeb - " + e.getMessage() + "\r\n"
                    + "select codigo,nome,rg,rgorgemis,cpf,fone1,fone2, endereco, bairro,cidade,uf,cep,"
                    + " situacao,obs,matr,funcao, sexo, altura, peso, operador, dt_alter, hr_alter,email,codpessoaweb, dt_situac"
                    + " FROM pessoa WHERE codigo is not null"
                    + (!pessoa.getNome().equals("") ? " AND nome like %" + pessoa.getNome().toUpperCase() + "%" : "")
                    + (null != pessoa.getSituacao() ? " AND situacao like %" + pessoa.getSituacao().toUpperCase() + "%" : "")
                    + (null != pessoa.getCidade() ? " AND cidade like %" + pessoa.getCidade().toUpperCase() + "%" : "")
                    + (!pessoa.getUF().equals("") ? " AND uf like %" + pessoa.getUF().toUpperCase() + "%" : ""));
        }
    }

    public List<Pessoa> BuscaPessoa(Pessoa pessoa, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        try {
            String sql = "select pessoa.codigo, pessoa.codpessoaweb, pessoa.nome, pessoa.email, pessoa.matr, pessoa.situacao, funcion.nome_guer "
                    + " FROM pessoa "
                    + " left join funcion ON funcion.matr = pessoa.matr "
                    + " WHERE pessoa.codigo <> '-1'";
            Pessoa p;
            if (null != pessoa.getNome() && !"".equals(pessoa.getNome())) {
                sql += " AND pessoa.nome = ?";
            }
            if (null != pessoa.getCodigo() && pessoa.getCodigo().compareTo(BigDecimal.ZERO) > 0) {
                sql += " AND pessoa.codigo = ?";
            }
            if (null != pessoa.getEmail() && !"".equals(pessoa.getEmail())) {
                sql += " AND pessoa.email = ?";
            }
            if (null != pessoa.getCPF() && !"".equals(pessoa.getCPF())) {
                sql += " AND cpessoa.pf = ?";
            }
            if (null != pessoa.getRG() && !"".equals(pessoa.getRG())) {
                sql += " AND pessoa.rg = ?";
            }
            Consulta consult = new Consulta(sql, persistencia);
            if (null != pessoa.getNome() && !"".equals(pessoa.getNome())) {
                consult.setString(pessoa.getNome());
            }
            if (null != pessoa.getCodigo() && pessoa.getCodigo().compareTo(BigDecimal.ZERO) > 0) {
                consult.setBigDecimal(pessoa.getCodigo());
            }
            if (null != pessoa.getEmail() && !"".equals(pessoa.getEmail())) {
                consult.setString(pessoa.getEmail());
            }
            if (null != pessoa.getCPF() && !"".equals(pessoa.getCPF())) {
                consult.setString(pessoa.getCPF());
            }
            if (null != pessoa.getRG() && !"".equals(pessoa.getRG())) {
                consult.setString(pessoa.getRG());
            }
            consult.select();
            while (consult.Proximo()) {
                p = new Pessoa();
                p.setCodigo(consult.getString("codigo"));
                p.setNome(consult.getString("nome"));
                p.setEmail(consult.getString("email"));
                p.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                p.setMatr(consult.getString("matr"));
                p.setSituacao(consult.getString("situacao"));
                /**
                 * Salvando o nome de guerra para uso no cadastro de usuário
                 * SatMobWeb
                 */
                p.setEndereco(consult.getString("nome_guer"));
                retorno.add(p);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.BuscaPessoa - " + e.getMessage() + "\r\n"
                    + "select codigo, codpessoaweb, nome, email, matr, situacao FROM pessoa "
                    + " WHERE codigo <> '-1'"
                    + (null != pessoa.getNome() && !"".equals(pessoa.getNome()) ? " AND nome = " + pessoa.getNome() : "")
                    + (null != pessoa.getCodigo() && pessoa.getCodigo().compareTo(BigDecimal.ZERO) > 0 ? " AND codigo = " + pessoa.getCodigo() : "")
                    + (null != pessoa.getEmail() && !"".equals(pessoa.getEmail()) ? " AND email = " + pessoa.getEmail() : "")
                    + (null != pessoa.getCPF() && !"".equals(pessoa.getCPF()) ? " AND cpf = " + pessoa.getCPF() : "")
                    + (null != pessoa.getRG() && !"".equals(pessoa.getRG()) ? " AND rg = " + pessoa.getRG() : ""));
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de pessoas cadastradas no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalPessoaMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total FROM pessoa"
                    + " WHERE ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null AND Nome is not null AND Nome <> ''";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.TotalPessoaMobWeb - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY pessoa.nome ) AS RowNum, pessoa.*, \n"
                    + " (Select top 1 CodDieta FROM Pessoadieta WHERE PessoaDieta.CodPessoa = Pessoa.Codigo order BY Data desc, Hr_Alter desc) CodDieta, \n"
                    + " (select top 1 ISNULL(saspw.PW,'') from saspw WHERE saspw.CodPessoa = pessoa.Codigo) Senha, \n"
                    + " (select top 1 ISNULL(saspw.Nivelx,'') from saspw WHERE saspw.CodPessoa = pessoa.Codigo) Nivelx, \n"
                    + " CONVERT(Varchar, Dt_nasc, 112) Dt_nascF, \n"
                    + " CONVERT(Varchar, DtValCNV, 112) DtValCNVF \n"
                    + "  FROM Pessoa \n"
                    + " WHERE \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " pessoa.codigo IS NOT null AND pessoa.Nome is not null AND pessoa.Nome <> '') AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Pessoa pessoa;
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setOperador(consult.getString("operador"));
                pessoa.setDt_Alter(consult.getString("dt_alter"));
                pessoa.setHr_Alter(consult.getString("hr_alter"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setIndicacao(consult.getString("indicacao"));
                pessoa.setDt_FormIni(consult.getString("dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("dt_FormFim"));
                pessoa.setLocalForm(consult.getString("localForm"));
                pessoa.setCertific(consult.getString("certific"));
                pessoa.setDt_Recicl(consult.getString("dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("dt_VenCurs"));
                pessoa.setReg_PF(consult.getString("reg_PF"));
                pessoa.setReg_PFUF(consult.getString("reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("reg_PFDt"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(consult.getString("CNHDtVenc"));
                pessoa.setExtTV(consult.getString("extTV"));
                pessoa.setExtSPP(consult.getString("extSPP"));
                pessoa.setExtEscolta(consult.getString("extEscolta"));
                pessoa.setDt_nasc(consult.getString("Dt_nascF"));
                pessoa.setCarNacVig(consult.getString("CarNacVig"));
                pessoa.setDtValCNV(consult.getString("DtValCNVF"));
                pessoa.setMae(consult.getString("mae"));
                pessoa.setPIS(consult.getString("PIS"));

                pessoa.setCodCli(consult.getString("CodCli"));
                pessoa.setComplemento(consult.getString("Complemento"));
                pessoa.setCodDieta(consult.getString("CodDieta"));

                pessoa.setSenha(consult.getString("Senha"));
                pessoa.setNivelx(consult.getString("Nivelx"));

                retorno.add(pessoa);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.ListaPaginada - " + e.getMessage());
        }
    }

    public List<Pessoa> listaCompleta(Map filtros, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        try {
            String sql = "SELECT  pessoa.*, \n"
                    + " (Select top 1 CodDieta FROM Pessoadieta WHERE PessoaDieta.CodPessoa = Pessoa.Codigo order BY Data desc, Hr_Alter desc) CodDieta, \n"
                    + " (select top 1 ISNULL(saspw.PW,'') from saspw WHERE saspw.CodPessoa = pessoa.Codigo) Senha, \n"
                    + " (select top 1 ISNULL(saspw.Nivelx,'') from saspw WHERE saspw.CodPessoa = pessoa.Codigo) Nivelx, \n"
                    + " CONVERT(Varchar, Dt_nasc, 112) Dt_nascF, \n"
                    + " CONVERT(Varchar, DtValCNV, 112) DtValCNVF \n"
                    + "  FROM Pessoa \n"
                    + " WHERE \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " pessoa.codigo IS NOT null AND pessoa.Nome is not null AND pessoa.Nome <> '' ORDER BY pessoa.nome";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            Pessoa pessoa;
            while (consult.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setRGOrgEmis(consult.getString("rgorgemis"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setFone1(consult.getString("fone1"));
                pessoa.setFone2(consult.getString("fone2"));
                pessoa.setEndereco(consult.getString("endereco"));
                pessoa.setBairro(consult.getString("bairro"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setUF(consult.getString("uf"));
                pessoa.setCEP(consult.getString("cep"));
                pessoa.setSituacao(consult.getString("situacao"));
                pessoa.setObs(consult.getString("obs"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setFuncao(consult.getString("funcao"));
                pessoa.setSexo(consult.getString("sexo"));
                pessoa.setAltura(consult.getString("altura"));
                pessoa.setPeso(consult.getString("peso"));
                pessoa.setOperador(consult.getString("operador"));
                pessoa.setDt_Alter(consult.getString("dt_alter"));
                pessoa.setHr_Alter(consult.getString("hr_alter"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                pessoa.setDt_Situac(consult.getString("dt_situac"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setIndicacao(consult.getString("indicacao"));
                pessoa.setDt_FormIni(consult.getString("dt_FormIni"));
                pessoa.setDt_FormFim(consult.getString("dt_FormFim"));
                pessoa.setLocalForm(consult.getString("localForm"));
                pessoa.setCertific(consult.getString("certific"));
                pessoa.setDt_Recicl(consult.getString("dt_Recicl"));
                pessoa.setDt_VenCurs(consult.getString("dt_VenCurs"));
                pessoa.setReg_PF(consult.getString("reg_PF"));
                pessoa.setReg_PFUF(consult.getString("reg_PFUF"));
                pessoa.setReg_PFDt(consult.getString("reg_PFDt"));
                pessoa.setCNH(consult.getString("CNH"));
                pessoa.setCNHDtVenc(consult.getString("CNHDtVenc"));
                pessoa.setExtTV(consult.getString("extTV"));
                pessoa.setExtSPP(consult.getString("extSPP"));
                pessoa.setExtEscolta(consult.getString("extEscolta"));
                pessoa.setDt_nasc(consult.getString("Dt_nascF"));
                pessoa.setCarNacVig(consult.getString("CarNacVig"));
                pessoa.setDtValCNV(consult.getString("DtValCNVF"));
                pessoa.setMae(consult.getString("mae"));
                pessoa.setPIS(consult.getString("PIS"));

                pessoa.setCodCli(consult.getString("CodCli"));
                pessoa.setComplemento(consult.getString("Complemento"));
                pessoa.setCodDieta(consult.getString("CodDieta"));

                pessoa.setSenha(consult.getString("Senha"));
                pessoa.setNivelx(consult.getString("Nivelx"));

                retorno.add(pessoa);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.ListaPaginada - " + e.getMessage());
        }
    }

    /**
     * Conta o número de pessoas cadastradas no banco
     *
     * @param filtros
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer totalPessoaMobWebQuick(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) total FROM pessoa\n"
                    + "WHERE\n"
                    + "    codigo IS NOT NULL\n"
                    + "    AND Nome IS NOT NULL\n"
                    + "    AND Nome <> ''\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> key : filtro.entrySet()) {
                if (!key.getValue().equals("")) {
                    sql += " AND " + key.getKey() + "\n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> key : filtro.entrySet()) {
                if (!key.getValue().equals("")) {
                    consulta.setString(key.getValue());
                }
            }
            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.TotalPessoaMobWeb - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Pessoa> listaPaginadaQuick(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList();
        try {
            String sql = "SELECT pessoa.*, (Select top 1 CodDieta FROM Pessoadieta WHERE PessoaDieta.CodPessoa = Pessoa.Codigo order BY Data desc, Hr_Alter desc) CodDieta \n"
                    + " FROM Pessoa\n"
                    + " WHERE\n"
                    + "    Pessoa.codigo IS NOT NULL\n"
                    + "    AND Pessoa.Nome IS NOT NULL\n"
                    + "    AND Pessoa.Nome <> ''\n";

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> key : filtro.entrySet()) {
                if (!key.getValue().equals("")) {
                    sql += " AND " + key.getKey() + "\n";
                }
            }

            sql += "ORDER BY Nome\n"
                    + "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> key : filtro.entrySet()) {
                if (!key.getValue().equals("")) {
                    consulta.setString(key.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas - 1);
            consulta.select();
            Pessoa pessoa;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setCodigo(consulta.getBigDecimal("codigo"));
                pessoa.setNome(consulta.getString("nome"));
                pessoa.setRG(consulta.getString("rg"));
                pessoa.setRGOrgEmis(consulta.getString("rgorgemis"));
                pessoa.setCPF(consulta.getString("cpf"));
                pessoa.setFone1(consulta.getString("fone1"));
                pessoa.setFone2(consulta.getString("fone2"));
                pessoa.setEndereco(consulta.getString("endereco"));
                pessoa.setBairro(consulta.getString("bairro"));
                pessoa.setCidade(consulta.getString("cidade"));
                pessoa.setUF(consulta.getString("uf"));
                pessoa.setCEP(consulta.getString("cep"));
                pessoa.setSituacao(consulta.getString("situacao"));
                pessoa.setObs(consulta.getString("obs"));
                pessoa.setMatr(consulta.getString("matr"));
                pessoa.setFuncao(consulta.getString("funcao"));
                pessoa.setSexo(consulta.getString("sexo"));
                pessoa.setAltura(consulta.getString("altura"));
                pessoa.setPeso(consulta.getString("peso"));
                pessoa.setOperador(consulta.getString("operador"));
                pessoa.setDt_Alter(consulta.getString("dt_alter"));
                pessoa.setHr_Alter(consulta.getString("hr_alter"));
                pessoa.setEmail(consulta.getString("email"));
                pessoa.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                pessoa.setDt_Situac(consulta.getString("dt_situac"));
                pessoa.setPWWeb(consulta.getString("pwweb"));
                pessoa.setIndicacao(consulta.getString("indicacao"));
                pessoa.setDt_FormIni(consulta.getString("dt_FormIni"));
                pessoa.setDt_FormFim(consulta.getString("dt_FormFim"));
                pessoa.setLocalForm(consulta.getString("localForm"));
                pessoa.setCertific(consulta.getString("certific"));
                pessoa.setDt_Recicl(consulta.getString("dt_Recicl"));
                pessoa.setDt_VenCurs(consulta.getString("dt_VenCurs"));
                pessoa.setReg_PF(consulta.getString("reg_PF"));
                pessoa.setReg_PFUF(consulta.getString("reg_PFUF"));
                pessoa.setReg_PFDt(consulta.getString("reg_PFDt"));
                pessoa.setCNH(consulta.getString("CNH"));
                pessoa.setCNHDtVenc(consulta.getString("CNHDtVenc"));
                pessoa.setExtTV(consulta.getString("extTV"));
                pessoa.setExtSPP(consulta.getString("extSPP"));
                pessoa.setExtEscolta(consulta.getString("extEscolta"));
                pessoa.setDt_nasc(consulta.getString("Dt_nasc"));
                pessoa.setCarNacVig(consulta.getString("CarNacVig"));
                pessoa.setDtValCNV(consulta.getString("DtValCNV"));
                pessoa.setMae(consulta.getString("mae"));
                pessoa.setPIS(consulta.getString("PIS"));

                pessoa.setCodCli(consulta.getString("CodCli"));
                pessoa.setComplemento(consulta.getString("Complemento"));
                pessoa.setCodDieta(consulta.getString("CodDieta"));
                retorno.add(pessoa);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.ListaPaginada - " + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
    /**
     * Busca informações do usuário para cadastro de primeiro acesso
     *
     * @param matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Pessoa primeiroAcessoRH(String matricula, Persistencia persistencia) throws Exception {
        Pessoa pessoa = new Pessoa();
        try {
            String sql = "select pessoa.codigo, pessoa.matr, pessoa.dt_ultacportal, pessoa.nome, pessoa.email, \n"
                    + "funcion.cidade, funcion.rg, funcion.cpf, convert(varchar, funcion.dt_nasc, 112) dt_nasc \n"
                    + "FROM pessoa as pessoa \n"
                    + "Left Join funcion as funcion ON funcion.matr=pessoa.matr \n"
                    + "WHERE funcion.situacao<>'D' AND funcion.matr= ? \n";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(matricula);
            consult.select();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setDt_nasc(consult.getString("dt_nasc"));
                pessoa.setDt_UltAcPortal(consult.getString("dt_ultacportal"));
                return pessoa;
            }
            return null;
        } catch (Exception e) {
            throw new Exception("PessoaDao.primeiroAcessoRH - " + e.getMessage() + "\r\n"
                    + "select pessoa.codigo, pessoa.matr, pessoa.dt_ultacportal, pessoa.nome, pessoa.email, \n"
                    + "funcion.cidade, funcion.rg, funcion.cpf, convert(varchar, funcion.dt_nasc, 112) dt_nasc \n"
                    + "FROM pessoa as pessoa \n"
                    + "Left Join funcion as funcion ON funcion.matr=pessoa.matr \n"
                    + "WHERE funcion.situacao<>'D' AND funcion.matr= " + matricula);
        }
    }

    public BigDecimal getCodPessoa(BigDecimal codpessoaweb, Persistencia persistencia) throws Exception {
        BigDecimal retorno = codpessoaweb;
        String sql = "SELECT codigo FROM pessoa WHERE codpessoaweb = ?";
        Consulta consulta;

        try {

            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoaweb);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = new BigDecimal(consulta.getString("codigo"));
            }

        } catch (Exception e) {
            throw new Exception("PessoaDao.getCodPessoa - " + e.getMessage() + "\r\n"
                    + "SELECT codigo FROM pessoa WHERE codpessoaweb = " + codpessoaweb);
        }

        return retorno;
    }

    public void updateCodPessoaWeb(Pessoa pessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "UPDATE pessoa SET codpessoaweb = ? \n"
                    + " WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(pessoa.getCodPessoaWEB());
            consulta.setBigDecimal(pessoa.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.updateCodPessoaWeb - " + e.getMessage() + "\r\n"
                    + "UPDATE pessoa SET codpessoaweb = " + pessoa.getCodPessoaWEB()
                    + " WHERE codigo = " + pessoa.getCodigo());
        }
    }

    public BigDecimal getCodPessoaWeb(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        BigDecimal retorno = null;
        String sql = "SELECT codpessoaweb FROM pessoa WHERE codigo = ?";
        Consulta consulta;

        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codpessoa);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = new BigDecimal(consulta.getString("codpessoaweb"));
            }

        } catch (Exception e) {
            throw new Exception("PessoaDao.getCodPessoaWeb - " + e.getMessage() + "\r\n"
                    + "SELECT codpessoaweb FROM pessoa WHERE codigo = " + codpessoa);
        }

        return retorno;
    }

    /**
     * Obtem codigo pessoa da base geral
     *
     * @param codpessoa codigo pessoa da base local
     * @param persistencia conexao com a base gerla
     * @return codigopessoa da base geral
     * @throws Exception
     */
    public BigDecimal getCodPessoaWeb(String codpessoa, Persistencia persistencia) throws Exception {
        BigDecimal retorno = null;
        String sql = "SELECT codpessoaweb FROM pessoa WHERE codigo = ?";
        Consulta consulta;

        try {

            consulta = new Consulta(sql, persistencia);
            consulta.setString(codpessoa);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("codpessoaweb");
            }

        } catch (Exception e) {
            throw new Exception("PessoaDao.getCodPessoaWeb - " + e.getMessage() + "\r\n"
                    + "SELECT codpessoaweb FROM pessoa WHERE codigo = " + codpessoa);
        }

        return retorno;
    }

    /**
     * Obtem registros do nome pessoa
     *
     * @param codPessoa codpessoa
     * @param persistencia conexao com o banco de
     * @return nome da pessoa
     * @throws Exception
     */
    public String obterNomeCompleto(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        String nomeCompleto = "";
        try {
            String sql = "SELECT nome FROM pessoa WHERE codigo = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa.toPlainString());
            consulta.select();

            while (consulta.Proximo()) {
                nomeCompleto = consulta.getString("nome");
            }
        } catch (Exception e) {
            throw new Exception("PessoaDao.obterNomeCompleto - " + e.getMessage() + "\r\n"
                    + "SELECT nome FROM pessoa WHERE codigo = " + codPessoa);
        }
        return nomeCompleto;
    }

    public String getEmail(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String email = null;
            String sql = " SELECT email FROM pessoa WHERE codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa.toPlainString());
            consulta.select();

            if (consulta.Proximo()) {
                email = consulta.getString("email");
            }
            consulta.Close();
            return email;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getEmail - " + e.getMessage() + "\r\n"
                    + "SELECT email FROM pessoa WHERE codigo = " + codPessoa);
        }
    }

    public String getFone(String codPessoa, Persistencia persistencia) throws Exception {
        try {
            String fone1 = null;
            String sql = " SELECT \n"
                    + "     Fone1 \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            consulta.select();

            if (consulta.Proximo()) {
                fone1 = consulta.getString("Fone1");
            }
            consulta.Close();
            return fone1;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getFone - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     Fone1 \n"
                    + " FROM \n"
                    + "     Pessoa \n"
                    + " WHERE \n"
                    + "     Codigo = " + codPessoa);
        }
    }

    /**
     * Busca o código de pessoa a partir da matrícula
     *
     * @param matr
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String getCodigo(String matr, Persistencia persistencia) throws Exception {
        String codigo = null;
        try {
            String sql = " SELECT Codigo FROM pessoa WHERE Matr = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.select();

            if (consulta.Proximo()) {
                codigo = consulta.getString("Codigo");
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.getMatr - " + e.getMessage() + "\r\n"
                    + "SELECT Codigo FROM pessoa WHERE Matr = " + matr);
        }
        return codigo;
    }

    /**
     * Busca a pessoa para login no Kanban
     *
     * @param senha
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Pessoa loginKanban(String senha, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * FROM pessoa WHERE pwtickets = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(senha);
            consulta.select();
            Pessoa retorno = new Pessoa();
            while (consulta.Proximo()) {
                retorno.setNome(consulta.getString("nome"));
                retorno.setCodigo(consulta.getString("codigo"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.loginKanban - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM pessoa WHERE pwtickets = " + senha);
        }
    }

    /**
     * Busca senha pwticket cadastrada para um dado email
     *
     * @param email - email da pesquisa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String verificaEmailKanban(String email, Persistencia persistencia) throws Exception {
        try {
            Consulta consult = new Consulta("SELECT * FROM pessoa WHERE email= ?", persistencia);
            consult.setString(email);
            consult.select();
            String ee = null;
            while (consult.Proximo()) {
                ee = consult.getString("email");
            }
            consult.Close();
            return ee;
        } catch (Exception e) {
            throw new Exception("PessoaDao.verificaEmailKanban - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM pessoa WHERE email= " + email);
        }
    }

    /**
     * Busca senha pwticket cadastrada para um dado email
     *
     * @param email - email da pesquisa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public String verificaSenhaKanban(String email, Persistencia persistencia) throws Exception {
        try {
            Consulta consult = new Consulta("Select pwtickets FROM pessoa WHERE email= ?", persistencia);
            consult.setString(email);
            consult.select();
            String pwticket = null;
            while (consult.Proximo()) {
                pwticket = consult.getString("pwtickets");
            }
            consult.Close();
            return pwticket;
        } catch (Exception e) {
            throw new Exception("PessoaDao.verificaSenhaKanban - " + e.getMessage() + "\r\n"
                    + "Select pwtickets FROM pessoa WHERE email= " + email);
        }
    }

    /**
     * Verifica se uma senha já existe no banco
     *
     * @param senha
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String verificaExisteSenhaKanban(String senha, Persistencia persistencia) throws Exception {
        try {
            Consulta consult = new Consulta("Select pwtickets FROM pessoa WHERE pwtickets= ?", persistencia);
            consult.setString(senha);
            consult.select();
            String pwticket = null;
            while (consult.Proximo()) {
                pwticket = consult.getString("pwtickets");
            }
            consult.Close();
            return pwticket;
        } catch (Exception e) {
            throw new Exception("PessoaDao.verificaExisteSenhaKanban - " + e.getMessage() + "\r\n"
                    + "Select pwtickets FROM pessoa WHERE pwtickets= " + senha);
        }
    }

    /**
     * Cadastra uma senha para dado email
     *
     * @param email
     * @param senha
     * @param persistencia
     * @throws Exception
     */
    public void cadastrarSenhaKanban(String email, String senha, Persistencia persistencia) throws Exception {
        String sql = "update pessoa set pwtickets = ? WHERE email = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(senha);
            consulta.setString(email);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.cadastrarSenhaKanban - " + e.getMessage() + "\r\n"
                    + "update pessoa set pwtickets = " + senha + " WHERE email = " + email);
        }
    }

    /**
     * Carrega a lista de usuários do kanban
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> listarUsuariosKanban(Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            String sql = "select codigo, nome FROM pessoa WHERE pwtickets is not null";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Pessoa pessoa;
            while (consulta.Proximo()) {
                pessoa = new Pessoa();
                pessoa.setNome(FuncoesString.RecortaAteEspaço(consulta.getString("nome"), 0, 100).equals("LUIZ") ? "PARIZOTTO" : FuncoesString.RecortaAteEspaço(consulta.getString("nome"), 0, 100));
                pessoa.setCodigo(consulta.getBigDecimal("codigo"));
                retorno.add(pessoa);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.listarUsuariosKanban - " + e.getMessage() + "\r\n"
                    + "select codigo, nome FROM pessoa WHERE pwtickets is not null");
        }
    }

    public Pessoa buscaMatr(String matricula, Persistencia persistencia) throws Exception {
        Pessoa pessoa = new Pessoa();
        try {
            String sql = "select pessoa.codigo, pessoa.codpessoaweb, pessoa.matr,pessoa.nome, pessoa.email, "
                    + "funcion.cidade, funcion.rg, funcion.cpf, funcion.dt_nasc,  Pessoa.pwweb, "
                    + " Funcion.Funcao "
                    + "FROM pessoa as pessoa "
                    + "Left Join funcion as funcion ON funcion.matr=pessoa.matr "
                    + "WHERE funcion.matr= ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(matricula);
            consult.select();
            while (consult.Proximo()) {
                pessoa.setCodigo(consult.getBigDecimal("codigo"));
                pessoa.setCodPessoaWEB(consult.getBigDecimal("codpessoaweb"));
                pessoa.setNome(consult.getString("nome"));
                pessoa.setRG(consult.getString("rg"));
                pessoa.setCPF(consult.getString("cpf"));
                pessoa.setCidade(consult.getString("cidade"));
                pessoa.setEmail(consult.getString("email"));
                pessoa.setMatr(consult.getString("matr"));
                pessoa.setDt_nasc(consult.getString("dt_nasc"));
                pessoa.setPWWeb(consult.getString("pwweb"));
                pessoa.setFuncao(consult.getString("Funcao"));
                return pessoa;
            }
            return null;
        } catch (Exception e) {
            throw new Exception("PessoaDao.buscaMatr - " + e.getMessage() + "\r\n"
                    + "select pessoa.codigo, pessoa.codpessoaweb, pessoa.matr,pessoa.nome, pessoa.email, "
                    + "funcion.cidade, funcion.rg, funcion.cpf, funcion.dt_nasc "
                    + "FROM pessoa as pessoa "
                    + "Left Join funcion as funcion ON funcion.matr=pessoa.matr "
                    + "WHERE funcion.matr= " + matricula);
        }
    }

    public Pessoa getInfoCandidato(String codigo, Persistencia persistencia) throws Exception {
        try {
            String sql = " select indicacao, dt_FormIni, dt_FormFim, localForm, certific, dt_Recicl, dt_VenCurs, "
                    + " reg_PF, reg_PFUF, reg_PFDt, CNH, CNHDtVenc, extTV, extSPP, extEscolta "
                    + " FROM pessoa WHERE codigo = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.select();
            Pessoa retorno = new Pessoa();
            while (consulta.Proximo()) {
                retorno.setIndicacao(consulta.getString("indicacao"));
                retorno.setDt_FormIni(consulta.getString("dt_FormIni"));
                retorno.setDt_FormFim(consulta.getString("dt_FormFim"));
                retorno.setLocalForm(consulta.getString("localForm"));
                retorno.setCertific(consulta.getString("certific"));
                retorno.setDt_Recicl(consulta.getString("dt_Recicl"));
                retorno.setDt_VenCurs(consulta.getString("dt_VenCurs"));
                retorno.setReg_PF(consulta.getString("reg_PF"));
                retorno.setReg_PFUF(consulta.getString("reg_PFUF"));
                retorno.setReg_PFDt(consulta.getString("reg_PFDt"));
                retorno.setCNH(consulta.getString("CNH"));
                retorno.setCNHDtVenc(consulta.getString("CNHDtVenc"));
                retorno.setExtTV(consulta.getString("extTV"));
                retorno.setExtSPP(consulta.getString("extSPP"));
                retorno.setExtEscolta(consulta.getString("extEscolta"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.getInfoCandidato - " + e.getMessage() + "\r\n"
                    + " select indicacao, dt_FormIni, dt_FormFim, localForm, certific, dt_Recicl, dt_VenCurs, "
                    + " reg_PF, reg_PFUF, reg_PFDt, CNH, CNHDtVenc, extTV, extSPP, extEscolta "
                    + " FROM pessoa WHERE codigo = " + codigo);
        }
    }

    public void atualizarUsuario(Pessoa p, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update Pessoa"
                    + " set Nome = ?, email = ?, PWWeb  = ?, Dt_Alter = ?, Hr_Alter = ?, Operador = ?"
                    + " WHERE codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(p.getNome());
            consulta.setString(p.getEmail());
            consulta.setString(p.getPWWeb());
            consulta.setString(p.getDt_Alter());
            consulta.setString(p.getHr_Alter());
            consulta.setString(p.getOperador());
            consulta.setBigDecimal(p.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.atualizarUsuario - " + e.getMessage() + "\r\n"
                    + "Update Pessoa"
                    + " set PWWeb  = " + p.getPWWeb() + ", Dt_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ", "
                    + " Hr_Alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ", Operador = " + p.getOperador()
                    + " WHERE codigo = " + p.getCodigo());
        }
    }

    public String gravarAutorizacaoPessoa(Pessoa pessoa, SegAutorizaArea segAutorizaArea, AcessoAut acessoAut, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "DECLARE @Sequencia INT;\n"
                    + " SET @Sequencia = (SELECT ISNULL((SELECT MAX(Sequencia) FROM AcessoAut), 0) + 1);\n"
                    + " \n"
                    // Insere em AcessoAut
                    + " INSERT INTO AcessoAut (Sequencia,\n"
                    + "                        CodFil,\n"
                    + "                        CodPessoa,\n"
                    + "                        Data,\n"
                    + "                        Situacao,\n"
                    + "                        Operador,\n"
                    + "                        Dt_Alter,\n"
                    + "                        Hr_Alter)\n"
                    + " VALUES(@Sequencia,?,?,?,?,?,?,?);\n"
                    + " \n"
                    // Insere em SegAutorizaArea
                    + " INSERT INTO SegAutorizaArea (Sequencia,\n"
                    + "                              CodFil,\n"
                    + "                              CodArea,\n"
                    + "                              HrEntrada,\n"
                    + "                              HrSaida,\n"
                    + "                              Operador,\n"
                    + "                              Dt_alter,\n"
                    + "                              Hr_alter)\n"
                    + " VALUES(@Sequencia,?,?,?,?,?,?,?);\n";

            Consulta consulta = new Consulta(sql, persistencia);
            // Parametros de Inser em AcessoAut
            consulta.setString(segAutorizaArea.getCodFil());
            consulta.setString(pessoa.getCodigo().toPlainString().replace(".0", ""));
            consulta.setString(acessoAut.getData());
            consulta.setString(acessoAut.getSituacao());
            consulta.setString(acessoAut.getOperador());
            consulta.setString(acessoAut.getDt_Alter());
            consulta.setString(acessoAut.getHr_Alter());

            // Parametros de Insere em SegAutorizaArea
            consulta.setString(segAutorizaArea.getCodFil());
            consulta.setString(segAutorizaArea.getCodArea());
            consulta.setString(segAutorizaArea.getHrEntrada());
            consulta.setString(segAutorizaArea.getHrSaida());
            consulta.setString(segAutorizaArea.getOperador());
            consulta.setString(segAutorizaArea.getDt_alter());
            consulta.setString(segAutorizaArea.getHr_alter());

            consulta.insert();

            sql = "SELECT MAX(Sequencia) AS sequencia FROM AcessoAut;";
            consulta = new Consulta(sql, persistencia);
            consulta.select();

            String retorno = "";
            while (consulta.Proximo()) {
                retorno = consulta.getString("sequencia");
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.gravarAutorizacaoPessoa - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void desbloquearUsuario(String email, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "UPDATE saspw \n"
                    + "SET Situacao = 'A', Acessos = 1\n"
                    + "WHERE codigo IN(SELECT Codigo FROM Pessoa WHERE email = ?);\n";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(email);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("PessoaDao.desbloquearUsuario - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public String pesquisarChaveAcesso(Pessoa pessoa, Persistencia persistenciaLocal, Persistencia persistenciaCentral) throws Exception {
        String sql = "", retorno = "";

        try {
            sql = "SELECT Chave\n"
                    + "FROM GTVeAcesso\n"
                    + "WHERE Parametro = ?\n"
                    + "AND   CodPessoa = ?";

            Consulta consulta = new Consulta(sql, persistenciaCentral);
            consulta.setString(persistenciaLocal.getEmpresa());
            consulta.setBigDecimal(pessoa.getCodigo());

            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getString("Chave");
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.pesquisarChaveAcesso - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public String gerarChaveAcesso(Persistencia persistenciaCentral) throws Exception {
        String sql = "", retorno = "";

        try {
            sql = "SELECT \n"
                    + "'S0100' + CONVERT(VARCHAR, (MAX(RIGHT(Chave,3)) + 1)) Chave\n"
                    + "FROM GTVeAcesso";

            Consulta consulta = new Consulta(sql, persistenciaCentral);
            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getString("Chave");
            }

            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("PessoaDao.gerarChaveAcesso - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
