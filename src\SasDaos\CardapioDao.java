/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Cardapio;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CardapioDao {

    private final String sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

    private String whereClauseListarPaginada(Map<String, String> filters) {
        String sql = "";
        boolean first = true;

        for (Map.Entry<String, String> entrada : filters.entrySet()) {
            if (!entrada.getValue().equals("")) {
                String keyword = first ? "WHERE " : "AND ";
                sql += keyword + entrada.getKey() + " \n";
                first = false;
            }
        }

        return sql;
    }

    private List<Cardapio> select(Consulta consulta) throws Exception {
        List<Cardapio> lista = new ArrayList<>();
        try {
            while (consulta.Proximo()) {
                Cardapio cardapio = new Cardapio();

                cardapio.setCodigo(consulta.getString("Sequencia"));
                cardapio.setDescricao(consulta.getString("Descricao"));
                cardapio.setEspecificacao(consulta.getString("Especificacao"));
                cardapio.setOperador(consulta.getString("Operador"));
                cardapio.setDt_alter(consulta.getString("Dt_alter"));
                cardapio.setHr_Alter(consulta.getString("Hr_Alter"));

                lista.add(cardapio);
            }
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    private int getTotal(Consulta consulta) throws Exception {
        try {
            while (consulta.Proximo()) {
                return consulta.getInt("total");
            }
            return 0;
        } catch (Exception e) {
            throw e;
        }
    }

    public List<Cardapio> listarCardapiosPaginada(
            int primeiro,
            int linhas,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT * \n"
                    + "FROM Cardapio \n"
                    + whereClauseListarPaginada(filters)
                    + "ORDER BY Sequencia \n" + sqlPaginacao;

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);
            consulta.select();
            List<Cardapio> cardapiosDia = select(consulta);
            consulta.Close();
            return cardapiosDia;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.listarCardapiosPaginada - " + e.getMessage());
        }
    }

    public int contarCardapiosTotal(
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) AS total \n"
                    + "FROM Cardapio \n"
                    + whereClauseListarPaginada(filters);

            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int total = getTotal(consulta);
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("CardapiosDao.contarCardapiosTotal - " + e.getMessage());
        }
    }
}
