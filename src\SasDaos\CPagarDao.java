package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CPagar;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CPagarDao {

    /**
     * usada internamente para carregar a lista vinda da consulta
     *
     * @param consult = Objeto com a consulta executada
     * @return - Lista montada
     * @throws Exception
     */
    private List<CPagar> carrega_lista_consulta(Consulta consult) throws Exception {
        List<CPagar> retorno = new ArrayList();
        CPagar cpagar;
        while (consult.Proximo()) {
            cpagar = new CPagar();
            cpagar.setSequencia(consult.getString("Sequencia"));
            cpagar.setCodFil(consult.getString("CodFil"));
            cpagar.setOP(consult.getString("OP"));
            cpagar.setCodFornec(consult.getString("CodFornec"));
            cpagar.setFornecedor(consult.getString("Fornecedor"));
            cpagar.setNF(consult.getString("NF"));
            cpagar.setPedido(consult.getString("Pedido"));
            cpagar.setTipoTit(consult.getInt("TipoTit"));
            cpagar.setFormaPgto(consult.getInt("FormaPgto"));
            cpagar.setDtVenc(consult.getString("DtVenc"));
            cpagar.setDtPrevPg(consult.getString("DtPrevPg"));
            cpagar.setCompet(consult.getString("Compet"));
            cpagar.setCompetPg(consult.getString("CompetPg"));
            cpagar.setDtPagto(consult.getString("DtPagto"));
            cpagar.setValor(consult.getString("Valor"));
            cpagar.setCodBarras(consult.getString("CodBarras"));
            cpagar.setObs(consult.getString("Obs"));
            cpagar.setContaFin(consult.getInt("ContaFin"));
            cpagar.setCCusto(consult.getString("CCusto"));
            cpagar.setValorPago(consult.getString("ValorPago"));
            cpagar.setJuros(consult.getString("Juros"));
            cpagar.setDesconto(consult.getString("Desconto"));
            cpagar.setCodConta(consult.getString("CodConta"));
            cpagar.setCheque(consult.getString("Cheque"));
            cpagar.setBanco(consult.getString("Banco"));
            cpagar.setAgencia(consult.getString("Agencia"));
            cpagar.setContaC(consult.getString("ContaC"));
            cpagar.setSituacao(consult.getString("Situacao"));
            cpagar.setSeqExp(consult.getString("SeqExp"));
            cpagar.setCodPessoaAutPg(consult.getString("CodPessoaAutPg"));
            cpagar.setDt_AutPg(consult.getLocalDate("Dt_AutPg"));
            cpagar.setHr_AutPg(consult.getString("Hr_AutPg"));
            cpagar.setOperador(consult.getString("Operador"));
            cpagar.setDt_Alter(consult.getString("Dt_Alter"));
            cpagar.setHr_Alter(consult.getString("Hr_Alter"));
            retorno.add(cpagar);
        }
        consult.Close();
        return retorno;
    }

    /**
     * Lista titulos a pagar por perído, com paginação, deve-se passar o último
     * número de sequencia listado ou primeiro
     *
     * @param DtInicio - inicio do período
     * @param DtFinal - fim do período
     * @param Sequencia - sequencia dentro do período
     * @param Sequenciaini - primerio elemento da listagem - informar para
     * navegar para trás
     * @param Sequenciafim - último elemento da listagem - informar para navegar
     * a frente
     * @param persistencia - conexão com banco
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarPeriodo(String DtInicio, String DtFinal, String Sequencia,
            String Sequenciaini, String Sequenciafim, Persistencia persistencia) throws Exception {
        List<CPagar> retorno;
        String operador = "";
        String sequencia = "";
        try {
            if (Sequenciaini != null) {
                operador = "<=";
                sequencia = Sequenciaini;
            } else if (Sequenciafim != null) {
                operador = ">=";
                sequencia = Sequenciafim;
            } else if (Sequencia != null) {
                operador = "=";
                sequencia = Sequencia;
            } else {
                operador = ">=";
                sequencia = "0";
            }
            String sql = "select top 20 * from cpagar "
                    + " where sequencia " + operador + " ?"
                    + " and dtprevpg between ? and ?"
                    + " order by sequencia";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sequencia);
            consult.setString(DtInicio);
            consult.setString(DtFinal);
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarPeriodo - " + e.getMessage() + "\r\n"
                    + "select top 20 * from cpagar "
                    + " where sequencia " + operador + " " + sequencia
                    + " and dtprevpg between " + DtInicio + " and " + DtFinal
                    + " order by sequencia");
        }
    }

    /**
     * Busca titulo a pagar pelo número
     *
     * @param Titulo - Númeo do título que deseja-se procurar
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception pode lançar exception
     */
    public List<CPagar> buscaCPagarTitulo(String Titulo, Persistencia persistencia) throws Exception {
        try {
            BigDecimal titulo;
            try {
                titulo = new BigDecimal(Titulo);
            } catch (Exception e) {
                titulo = new BigDecimal("0");
            }
            String sql = "select * "
                    + " from cpagar "
                    + " where sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(titulo);
            consult.select();
            List<CPagar> retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarTitulo - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where sequencia = " + Titulo);
        }
    }

    /**
     * Busca por número de título (sequencia na tabela
     *
     * @param Sequencia número de título (sequencia na tabela)
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarSequencia(String Sequencia, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where sequencia = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(new BigDecimal(Sequencia));
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarSequencia - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where sequencia = " + Sequencia);
        }
    }

    /**
     * Busca por número de NF
     *
     * @param NF número de NF
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarNF(String NF, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where NF = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(NF);
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarNF - " + e.getMessage() + "\r\n"
                    + "Select * "
                    + " from cpagar "
                    + " where NF = " + NF);
        }
    }

    /**
     * Busca por número de Ordem de Compra
     *
     * @param OCompra - ordem de compra
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarOCompra(String OCompra, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where OCompra = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(OCompra);
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarOCompra - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where OCompra = " + OCompra);
        }
    }

    /**
     * Busca por Fornecedor
     *
     * @param Fornecedor - Fornecedor
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarFornecedor(String Fornecedor, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where Fornecedor like ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + Fornecedor + "%");
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarFornecedor - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where Fornecedor like " + Fornecedor);
        }
    }

    /**
     * Busca por Centro de Custo
     *
     * @param CCusto - Centro de Custo
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarCCusto(String CCusto, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where CCusto like ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CCusto + "%");
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarCCusto - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where CCusto like " + CCusto);
        }
    }

    /**
     * Busca por Conta Finenceira
     *
     * @param ContaFin - Conta Financeira
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarContaFin(String ContaFin, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar "
                    + " where ContaFin = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(ContaFin);
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarContaFin - " + e.getMessage() + "\r\n"
                    + "select * "
                    + " from cpagar "
                    + " where ContaFin =" + ContaFin);
        }
    }

    /**
     * Busca por Data de Vencimento
     *
     * @param DtVencimento - Data de Vencimento no formato YYYYmmDD
     * @param DtVencimento2 - Data de Vencimento no formato YYYYmmDD, se
     * informada a consulta devolverá os titulos com vencimento no intervalo
     * entre DtVencimento e DtVencimento2,
     * @param persistencia - conexão com o banco de dados
     * @return
     * @throws Exception
     */
    public List<CPagar> buscaCPagarDtVencimento(String DtVencimento, String DtVencimento2, Persistencia persistencia) throws Exception {
        try {
            List<CPagar> retorno;
            String sql = "select * "
                    + " from cpagar ";
            if (DtVencimento2 != null) {
                sql += " where Dtvencimento between ? and ?";
            } else {
                sql += " where DtVencimento = ?";
            }
            Consulta consult = new Consulta(sql, persistencia);
            if (DtVencimento2 != null) {
                consult.setString(DtVencimento);
                consult.setString(DtVencimento2);
            } else {
                consult.setString(DtVencimento);
            }
            consult.select();
            retorno = this.carrega_lista_consulta(consult);
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.buscaCPagarDtVencimento - " + e.getMessage());
        }
    }

    /**
     * Grava código de barras no titulo passado
     *
     * @param cpagar - informar sequencia (número do título) e codbarras
     * @param persistencia - conexão com o banco
     * @return - quantidade de registros afetados
     * @throws Exception - pode lançar exception
     */
    public int GravaCodBar(CPagar cpagar, Persistencia persistencia) throws Exception {
        try {
            String sql = "update cpagar set codbarras = ? where sequencia = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpagar.getCodBarras());
            consulta.setBigDecimal(cpagar.getSequencia());
            int results = consulta.update();
            consulta.close();
            return results;
        } catch (Exception e) {
            throw new Exception("CPagarDao.GravaCodBar - " + e.getMessage() + "\r\n"
                    + "update cpagar set codbarras = " + cpagar.getCodBarras() + " where sequencia = " + cpagar.getSequencia());
        }
    }

    /**
     * Grava autorização de pagamento para o título
     *
     * @param CodPessoa - Código da pessoa que autoriza o pagamento
     * @param Titulos - Númeoro dos títulos, separados por , (virgula)
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void AutorizaPagto(String CodPessoa, String Titulos, Persistencia persistencia) throws Exception {
        try {
            String sql = "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = ?,"
                    + " Hr_AutPg = ?"
                    + " where Sequencia in (" + Titulos + ")";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodPessoa);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CPagarDao.AutorizaPagto - " + e.getMessage() + "\r\n"
                    + "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = " + DataAtual.getDataAtual("SQL") + ","
                    + " Hr_AutPg = " + DataAtual.getDataAtual("HORA") + ","
                    + " where Sequencia in (" + Titulos + ")");
        }
    }

    public void autorizaPagto(String CodPessoa, String Titulos, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = ?,"
                    + " Hr_AutPg = ?"
                    + " where Sequencia in (" + Titulos + ")";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodPessoa);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CPagarDao.autorizaPagto - " + e.getMessage() + "\r\n"
                    + "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = " + dataAtual + ","
                    + " Hr_AutPg = " + horaAtual + ","
                    + " where Sequencia in (" + Titulos + ")");
        }
    }

    /**
     * Grava a desautorização de pagamento para o título
     *
     * @param Titulos - Númeoro dos títulos, separados por , (virgula)
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void AutorizaPagtoVoltar(String Titulos, Persistencia persistencia) throws Exception {
        try {
            String sql = "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = ?,"
                    + " Hr_AutPg = ?"
                    + " where Sequencia in (" + Titulos + ")";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(null);
            consulta.setString(null);
            consulta.setString(null);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CPagarDao.AutorizaPagtoVoltar - " + e.getMessage() + "\r\n"
                    + "update cpagar set CodPessoaAutPg = ?, "
                    + " Dt_AutPg = " + null + ","
                    + " Hr_AutPg = " + null + ","
                    + " where Sequencia in (" + null + ")");
        }
    }

    public List<CPagar> listaContasPagar(String dataTela, String dataInicio, String dataFim, String codFil, Persistencia persistencia) throws Exception {
        String sql = "";
        List<CPagar> retorno = new ArrayList<>();

        try {
            sql = "DECLARE @DataAtual AS VARCHAR(10);\n"
                    + " SET @DataAtual = ?;\n"
                    + "\n"
                    + " Select \n"
                    + " CPagar.CodFil, \n"
                    + " Cpagar.Fornecedor, \n"
                    + " CPagar.NF, \n"
                    + " CPagar.DtVenc, \n"
                    + " Cpagar.Valor, \n"
                    + " CPagar.Obs, \n"
                    + " Cpagar.DtPrevPg, \n"
                    + " Cpagar.Detalhes,\n"
                    + " Cpagar.DtPagto, \n"
                    + " CASE WHEN Cpagar.DtPagto IS NULL AND DATEDIFF(day, Cpagar.DtVenc,@DataAtual) > 0 THEN DATEDIFF(day, Cpagar.DtVenc,@DataAtual) ELSE 0 END DiasAtraso\n"
                    + " from CPagar \n"
                    + " Left Join TiposTit on TiposTit.codigo = CPagar.TipoTit \n"
                    + " Where CPagar.DtVenc >= ?\n"
                    + "   and CPagar.DtVenc <= ?\n"
                    + "   and CPagar.Codfil  = ?\n"
                    + "   and (CPagar.ValorPago is null or CPagar.ValorPago = 0) \n"
                    + "   and TiposTit.Descricao not like '%REGATE%'\n"
                    + "   and TiposTit.Descricao not like '%APLICA%'\n"
                    + "   and TiposTit.Descricao not like '%APL%'              \n"
                    + "   and TiposTit.Descricao not like '%GARANTIDA%'\n"
                    + "  Order by CPagar.DtVenc, CPagar.Fornecedor";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataTela);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(codFil);
            consulta.select();

            CPagar cPagar;

            while (consulta.Proximo()) {
                cPagar = new CPagar();
                cPagar.setCodFil(consulta.getString("Codfil"));
                cPagar.setFornecedor(consulta.getString("Fornecedor"));
                cPagar.setNF(consulta.getString("NF"));
                cPagar.setDtPagto(consulta.getString("DtPagto"));
                cPagar.setDtVenc(consulta.getString("DtVenc"));
                cPagar.setDtPrevPg(consulta.getString("DtPrevPg"));
                cPagar.setValor(consulta.getString("Valor"));
                cPagar.setObs(consulta.getString("Detalhes"));
                cPagar.setDiasAtraso(consulta.getString("DiasAtraso"));

                retorno.add(cPagar);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarDao.listaContasPagar - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
}
