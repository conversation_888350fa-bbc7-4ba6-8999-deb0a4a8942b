package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ChavesMov;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ChavesMovDao {

    /**
     * Busca Chaves pelo número de remessa passado
     *
     * @param codremessa - código da remessa (sequencia de rota + número da
     * remessa)
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<ChavesMov> BuscaChavesRemessa(String codremessa, Persistencia persistencia) throws Exception {
        try {
            List<ChavesMov> retorno = new ArrayList();
            String sql = "select NroChave from chavesmov where CodRemessa = ? and DtRecMob is null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codremessa);
            consult.select();
            ChavesMov chavesmov;
            while (consult.Proximo()) {
                chavesmov = new ChavesMov();
                chavesmov.setCodRemessa(codremessa);
                chavesmov.setNroChave(consult.getString("NroChave"));
                retorno.add(chavesmov);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ChavesMovDao.BuscaChavesRemessa - " + e.getMessage() + "\r\n"
                    + "select NroChave from chavesmov where CodRemessa = " + codremessa + " and DtRecMob is null");
        }
    }

    /**
     * Grava Data e Hora de recebimento da remessa de chaves no mobile Data e
     * hora imputado automaticamente pelo relógio do servidor
     *
     * @param codremessa - código da remessa
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void RecebimentoRemessaMobile(String codremessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "update chavesmov set DtRecMob = ?, HrRecMob = ? where CodRemessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(DataAtual.getDataAtual("SQL"));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(codremessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ChavesMovDao.RecebimentoRemessaMobile - " + e.getMessage() + "\r\n"
                    + "update chavesmov set DtRecMob = " + DataAtual.getDataAtual("SQL") + ", HrRecMob = " + DataAtual.getDataAtual("HORA")
                    + " where CodRemessa = " + codremessa);
        }
    }

    public void RecebimentoRemessaMobile(String codremessa, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "update chavesmov set DtRecMob = ?, HrRecMob = ? where CodRemessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(codremessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ChavesMovDao.RecebimentoRemessaMobile - " + e.getMessage() + "\r\n"
                    + "update chavesmov set DtRecMob = " + dataAtual + ", HrRecMob = " + horaAtual
                    + " where CodRemessa = " + codremessa);
        }
    }

    public List<ChavesMov> chavePendentes(Persistencia persistencia) throws Exception {
        try {
            List<ChavesMov> retorno = new ArrayList<>();
            String sql = " Select Paramet.pathEagle, Clientes.Nred, Clientes.NroChave, Filiais.Descricao, ChavesMov.CodFil, Count(*) Qtde from Chaves "
                    + " left Join Clientes on Clientes.CodFil = Chaves.CodFil "
                    + "                 and Clientes.NroChave = Chaves.NroChave "
                    + "                 and Clientes.Situacao = 'A' "
                    + " left join ChavesMov on ChavesMov.CodFil = Chaves.CodFil "
                    + "                  and ChavesMov.NroChave = Chaves.NroChave "
                    + "                 and ChavesMov.Sequencia = (Select Max(Sequencia) from ChavesMov where CodFil = Chaves.CodFil and NroChave = Chaves.NroChave and Ordem = Chaves.Ordem) "
                    + " left join Escala on Escala.CodFil = ChavesMov.CodFil "
                    + "                   and Escala.Rota = ChavesMov.Rota "
                    + "                   and Escala.Data = ChavesMov.Data "
                    + " left join Funcion on  Funcion.Matr = Escala.MatrChe "
                    + "                 and Funcion.CodFil = Escala.CodFil "
                    + " left join Filiais on Filiais.codfil = ChavesMov.CodFil "
                    + " Left join Paramet on Paramet.Filial_pdr = ChavesMov.CodFil "
                    + " where Chaves.NroChave > 0 "
                    //+ "  and Clientes.Codigo is not null "
                    + "  and DtEnt is null and DtSai <= getDate() "
                    + "  and Chaves.Situacao = 'A' "
                    + "  and DtEnt is null and DtSai is not null "
                    + " Group by Paramet.pathEagle, ChavesMov.CodFil, Filiais.Descricao, Clientes.Nred, Clientes.NroChave "
                    + " Order by ChavesMov.CodFil ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            ChavesMov chave;
            while (consulta.Proximo()) {
                chave = new ChavesMov();
                chave.setNred(consulta.getString("Nred"));
                chave.setNroChave(consulta.getString("NroChave"));
                chave.setCodFil(consulta.getString("CodFil"));
                chave.setFilial(consulta.getString("Descricao"));
                chave.setQuantidade(consulta.getBigDecimal("Qtde"));
                chave.setEmail(consulta.getString("pathEagle"));
                retorno.add(chave);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ChavesMovDao.chavePendentes - " + e.getMessage() + "\r\n"
                    + " Select Paramet.pathEagle, Clientes.Nred, Clientes.NroChave, Filiais.Descricao, ChavesMov.CodFil, Count(*) Qtde from Chaves "
                    + " left Join Clientes on Clientes.CodFil = Chaves.CodFil "
                    + "                 and Clientes.NroChave = Chaves.NroChave "
                    + "                 and Clientes.Situacao = 'A' "
                    + " left join ChavesMov on ChavesMov.CodFil = Chaves.CodFil "
                    + "                  and ChavesMov.NroChave = Chaves.NroChave "
                    + "                 and ChavesMov.Sequencia = (Select Max(Sequencia) from ChavesMov where CodFil = Chaves.CodFil and NroChave = Chaves.NroChave and Ordem = Chaves.Ordem) "
                    + " left join Escala on Escala.CodFil = ChavesMov.CodFil "
                    + "                   and Escala.Rota = ChavesMov.Rota "
                    + "                   and Escala.Data = ChavesMov.Data "
                    + " left join Funcion on  Funcion.Matr = Escala.MatrChe "
                    + "                 and Funcion.CodFil = Escala.CodFil "
                    + " left join Filiais on Filiais.codfil = ChavesMov.CodFil "
                    + " Left join Paramet on Paramet.Filial_pdr = ChavesMov.CodFil "
                    + " where Chaves.NroChave > 0 "
                    + "  and Clientes.Codigo is not null "
                    + "  and Chaves.Situacao = 'A' "
                    + "  and DtEnt is null and DtSai is not null "
                    + " Group by Paramet.pathEagle, ChavesMov.CodFil, Filiais.Descricao, Clientes.Nred, Clientes.NroChave "
                    + " Order by ChavesMov.CodFil ");
        }
    }
}
