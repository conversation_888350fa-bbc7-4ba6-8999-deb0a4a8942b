/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CheckInOut;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CheckInOutDao {

    public List<CheckInOut> listarPontos(LocalDate data, Persistencia persistencia) throws Exception {
        List<CheckInOut> retorno = new ArrayList<>();
        try {
            String sql = "Select UserInfo.badgenumber Matr, checkinout.id, checkinout.checktime, checkinout.Reserved, UserInfo.name from checkinout"
                    + " Left join UserInfo on checkinout.userid = UserInfo.userid "
                    + " Where checkinout.checktime >= ? "
                    + " and checkinout.Reserved <> 'Imp-Sat'";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setDate(DataAtual.LC2Date(data));
            consulta.select();
            CheckInOut ponto;
            while (consulta.Proximo()) {
                ponto = new CheckInOut();
                ponto.setBadgeNumber(consulta.getString("Matr"));
                ponto.setId(consulta.getString("id"));
                ponto.setCheckTime(consulta.getLocalDate("checktime"));
                ponto.setReserved(consulta.getString("reserved"));
                ponto.setName(consulta.getString("name"));
                retorno.add(ponto);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CheckInOutDao.listarPontos - " + e.getMessage() + "\r\n"
                    + "Select UserInfo.badgenumber Matr, checkinout.id, checkinout.checktime, checkinout.Reserved, UserInfo.name from checkinout"
                    + " Left join UserInfo on checkinout.userid = UserInfo.userid "
                    + " Where checkinout.checktime >= " + data
                    + " and checkinout.Reserved <> 'Imp-Sat'");
        }
    }
}
