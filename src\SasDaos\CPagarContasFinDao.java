package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CPagar;
import SasBeans.ContasFin;
import SasBeansCompostas.CPagarContasFin;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CPagarContasFinDao {

    /**
     * Total de contas a pagar por período
     *
     * @param inicio - data inicial
     * @param fim - data final
     * @param persistencia - conexão ao banco de dados
     * @return - retrona valores agrupados por competência ano/mês
     * @throws Exception
     */
    public List<CPagarContasFin> ContasAPagar(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        try {
            List<CPagarContasFin> retorno = new ArrayList();
            String sql = "Select YEAR(CPagar.DtPagto) Ano, MONTH(CPagar.DtPagto) Mes, "
                    + " Su<PERSON>(Valor) <PERSON><PERSON>, <PERSON><PERSON>(ValorPago) ValorPago, <PERSON><PERSON>(ValorPago-Valor) Juros "
                    + " from CPagar"
                    + " left join ContasFin on ContasFin.Codigo = CPagar.ContaFin"
                    + " where dtPagto >= ? " //'20160101'" 
                    + " and DtPagto <= ?" //'20160630'" 
                    + " and (Descricao not like 'transfe%' and (descricao not like 'acordo judicial') and (descricao not like 'deposito judicial') "
                    + " and (descricao not like 'emprestimo consignado') and (descricao not like 'estorno de ted'))"
                    + " group by YEAR(CPagar.DtPagto), MONTH(CPagar.DtPagto)"
                    + " order by YEAR(CPagar.DtPagto), MONTH(CPagar.DtPagto)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CPagar cpagar;
            ContasFin contasfin;
            CPagarContasFin cpagarcontasfin;
            while (consult.Proximo()) {
                cpagar = new CPagar();
                contasfin = new ContasFin();
                cpagarcontasfin = new CPagarContasFin();
                cpagar.setDtPagto(consult.getString("Ano") + consult.getString("Mes"));
                cpagar.setValor(consult.getString("Valor"));
                cpagar.setValorPago(consult.getString("ValorPago"));
                cpagar.setJuros(consult.getString("Juros"));
                cpagarcontasfin.setCpagar(cpagar);
                cpagarcontasfin.setContasfin(contasfin);
                retorno.add(cpagarcontasfin);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarContasFinDao.ContasAPagar - " + e.getMessage() + "\r\n"
                    + "Select YEAR(CPagar.DtPagto) Ano, MONTH(CPagar.DtPagto) Mes, "
                    + " Sum(Valor) Valor, Sum(ValorPago) ValorPago, Sum(ValorPago-Valor) Juros "
                    + " from CPagar"
                    + " left join ContasFin on ContasFin.Codigo = CPagar.ContaFin"
                    + " where dtPagto >= " + inicio.toString() //'20160101'" 
                    + " and DtPagto <= " + fim.toString() //'20160630'" 
                    + " and (Descricao not like 'transfe%' and (descricao not like 'acordo judicial') and (descricao not like 'deposito judicial') "
                    + " and (descricao not like 'emprestimo consignado') and (descricao not like 'estorno de ted'))"
                    + " group by YEAR(CPagar.DtPagto), MONTH(CPagar.DtPagto)"
                    + " order by YEAR(CPagar.DtPagto), MONTH(CPagar.DtPagto)");
        }
    }

    /**
     * Total de contas a pagar por período com descrição de conta financeira
     *
     * @param inicio - data inicial
     * @param fim - data final
     * @param persistencia - conexão ao banco de dados
     * @return - retrona valores agrupados por competência ano/mês
     * @throws Exception
     */
    public List<CPagarContasFin> ContasAPagarDescricao(LocalDate inicio, LocalDate fim, Persistencia persistencia) throws Exception {
        try {
            List<CPagarContasFin> retorno = new ArrayList();
            String sql = "Select ContasFin.Descricao, Sum(Valor) Valor, Sum(ValorPago) ValorPago, Sum(ValorPago-Valor) Juros from CPagar"
                    + " left join ContasFin on ContasFin.Codigo = CPagar.ContaFin"
                    + " where dtPagto >= ?" //'20160101'" 
                    + " and DtPagto <= ?" //'20160630'" 
                    + " and (Descricao not like 'transfe%' and (descricao not like 'acordo judicial') and (descricao not like 'deposito judicial')"
                    + " and (descricao not like 'emprestimo consignado') and (descricao not like 'estorno de ted'))"
                    + " group by ContasFin.Descricao"
                    + " order by Valor desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setDate(Date.valueOf(inicio));
            consult.setDate(Date.valueOf(fim));
            consult.select();
            CPagar cpagar;
            ContasFin contasfin;
            CPagarContasFin cpagarcontasfin;
            while (consult.Proximo()) {
                cpagar = new CPagar();
                contasfin = new ContasFin();
                cpagarcontasfin = new CPagarContasFin();
                cpagar.setDtPagto(consult.getString("Ano") + consult.getString("Mes"));
                cpagar.setValor(consult.getString("Valor"));
                cpagar.setValorPago(consult.getString("ValorPago"));
                cpagar.setJuros(consult.getString("Juros"));
                cpagarcontasfin.setCpagar(cpagar);
                cpagarcontasfin.setContasfin(contasfin);
                retorno.add(cpagarcontasfin);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CPagarContasFinDao.ContasAPagarDescricao - " + e.getMessage() + "\r\n"
                    + "Select ContasFin.Descricao, Sum(Valor) Valor, Sum(ValorPago) ValorPago, Sum(ValorPago-Valor) Juros from CPagar"
                    + " left join ContasFin on ContasFin.Codigo = CPagar.ContaFin"
                    + " where dtPagto >= " + inicio //'20160101'" 
                    + " and DtPagto <= " + fim //'20160630'" 
                    + " and (Descricao not like 'transfe%' and (descricao not like 'acordo judicial') and (descricao not like 'deposito judicial')"
                    + " and (descricao not like 'emprestimo consignado') and (descricao not like 'estorno de ted'))"
                    + " group by ContasFin.Descricao"
                    + " order by Valor desc");
        }
    }
}
