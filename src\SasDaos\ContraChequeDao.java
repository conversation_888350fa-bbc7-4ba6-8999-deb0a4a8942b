package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CCusto;
import SasBeans.Cargos;
import SasBeans.FPLancamentos;
import SasBeans.FPMensal;
import SasBeans.FPPeriodos;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.PstServ;
import SasBeans.SASLog;
import SasBeans.Verbas;
import SasBeansCompostas.ContraCheque;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContraChequeDao {

    String sql;

    /**
     * Retorna dados da FPMensal
     *
     * @param sCodmovfp -
     * @param sMatr - Código matrícula
     * @param sTipoFP - Tipo
     * @param persistencia - conexão com o banco
     * @return - retorna lista com tipoFP
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<ContraCheque> getCCcabecalho(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        sql = "Select top 1 "
                + " Funcion.Matr, "
                + " Funcion.Nome, "
                + " PstServ.Local LocalPosto, "
                + " PstServ.Secao, "
                + " Cargos.Descricao DescCargo, "
                + " Filiais.Descricao DescFil, "
                + " Funcion.CPF, "
                + " Funcion.RG, "
                + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                + " Funcion.Pis, "
                + " Funcion.Codfil funcioncodfil, "
                + " CCusto.Descricao ccustodescricao, "
                + " Funcion.Bairro funcionbairro, "
                + " Funcion.Cidade funcioncidade, "
                + " Funcion.Cep funcioncep, "
                + " Funcion.UF funcionuf, "
                + " Convert(VarChar, FPMensalOP.DtPagto, 103) DtPagto, "
                + " Funcion.Vinculo funcionvinculo, "
                + " Funcion.DepIr, "
                + " Funcion.DepSf, "
                + " Filiais.RazaoSocial," //21
                + " Filiais.Endereco filiaisendereco,"
                + " Filiais.Bairro filiaisbairro,"
                + " Filiais.Cidade filiaiscidade,"
                + " Filiais.UF filiaisuf,"
                + " Filiais.CEP filiaiscep,"
                + " Filiais.CNPJ,"
                + " Cargos.CBO"
                + " from FPMensal as FPMensal "
                + " Left join Funcion as Funcion on Funcion.Matr = FPMensal.Matr"
                + " Left join PstServ as PstServ on PstServ.Secao = FPMensal.Secao and PstServ.CodFil = FPMensal.CodFil "
                + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                + " Left Join CCusto as CCusto on CCusto.CCusto = Funcion.CCusto"
                + " Left Join FPMensalOP as FPMensalOP on FPMensalOP.CodMovFP=FPMensal.CodMovFP and"
                + " FPMensalOP.TipoFP=FPMensal.TipoFP and FPMensalOP.Matr=FPMensal.Matr"
                + " where FPMensal.Matr = ? "
                + " and FPMensal.CodMovFP = ? "
                + " and FPMensal.TipoFP = ? ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sMatr);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            ContraCheque oContraCheque = null;
            stm.select();

            while (stm.Proximo()) {
                Funcion oFuncion = new Funcion();
                PstServ oPstServ = new PstServ();
                Cargos oCargos = new Cargos();
                Filiais oFiliais = new Filiais();
                CCusto oCCusto = new CCusto();
                //FPMensalOP oFPMensalOP = new FPMensalOP();
                oContraCheque = new ContraCheque();

                oFuncion.setMatr(stm.getString("Matr"));
                oFuncion.setNome(stm.getString("Nome"));
                oPstServ.setLocal(stm.getString("LocalPosto"));
                oPstServ.setSecao(stm.getString("Secao"));
                oCargos.setDescricao(stm.getString("DescCargo"));
                oFiliais.setDescricao(stm.getString("DescFil"));
                oFuncion.setCPF(stm.getString("CPF"));
                oFuncion.setRG(stm.getString("RG"));
                oFuncion.setDt_Admis(stm.getString("Dt_Admis"));
                oFuncion.setPIS(stm.getString("Pis"));
                oFuncion.setCodFil(stm.getString("funcioncodfil"));
                oCCusto.setDescricao(stm.getString("ccustodescricao"));
                oFuncion.setBairro(stm.getString("funcionbairro"));
                oFuncion.setCidade(stm.getString("funcioncidade"));
                oFuncion.setCEP(stm.getString("funcioncep"));
                oFuncion.setUF(stm.getString("funcionUF"));
                //oFPMensalOP.setDtPagto(LocalDate.parse(stm.getString("DtPagto")));
                oFuncion.setVinculo(stm.getString("funcionvinculo"));
                oFuncion.setDepIR(stm.getString("DepIr"));
                oFuncion.setDepSF(stm.getString("DepSf"));
                oFiliais.setRazaoSocial(stm.getString("RazaoSocial")); //21
                oFiliais.setEndereco(stm.getString("filiaisendereco"));
                oFiliais.setBairro(stm.getString("filiaisbairro"));
                oFiliais.setCidade(stm.getString("filiaiscidade"));
                oFiliais.setUF(stm.getString("filiaisUF"));
                oFiliais.setCEP(stm.getString("filiaisCEP"));
                oFiliais.setCNPJ(stm.getString("CNPJ"));
                oCargos.setCBO(stm.getString("CBO"));

                oContraCheque.setFuncion(oFuncion);
                oContraCheque.setPstServ(oPstServ);
                oContraCheque.setCargos(oCargos);
                oContraCheque.setFiliais(oFiliais);
                oContraCheque.setcCusto(oCCusto);
                //oContraCheque.setfPMensalOP(oFPMensalOP);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getCCcabecalho - " + e.getMessage() + "\r\n"
                    + "Select top 1 "
                    + " Funcion.Matr, "
                    + " Funcion.Nome, "
                    + " PstServ.Local LocalPosto, "
                    + " PstServ.Secao, "
                    + " Cargos.Descricao DescCargo, "
                    + " Filiais.Descricao DescFil, "
                    + " Funcion.CPF, "
                    + " Funcion.RG, "
                    + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                    + " Funcion.Pis, "
                    + " Funcion.Codfil funcioncodfil, "
                    + " CCusto.Descricao ccustodescricao, "
                    + " Funcion.Bairro funcionbairro, "
                    + " Funcion.Cidade funcioncidade, "
                    + " Funcion.Cep funcioncep, "
                    + " Funcion.UF funcionuf, "
                    + " Convert(VarChar, FPMensalOP.DtPagto, 103) DtPagto, "
                    + " Funcion.Vinculo funcionvinculo, "
                    + " Funcion.DepIr, "
                    + " Funcion.DepSf, "
                    + " Filiais.RazaoSocial," //21
                    + " Filiais.Endereco filiaisendereco,"
                    + " Filiais.Bairro filiaisbairro,"
                    + " Filiais.Cidade filiaiscidade,"
                    + " Filiais.UF filiaisuf,"
                    + " Filiais.CEP filiaiscep,"
                    + " Filiais.CNPJ,"
                    + " Cargos.CBO"
                    + " from FPMensal as FPMensal "
                    + " Left join Funcion as Funcion on Funcion.Matr = FPMensal.Matr"
                    + " Left join PstServ as PstServ on PstServ.Secao = Funcion.Secao and PstServ.CodFil = Funcion.CodFil "
                    + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                    + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                    + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                    + " Left Join CCusto as CCusto on CCusto.CCusto = Funcion.CCusto"
                    + " Left Join FPMensalOP as FPMensalOP on FPMensalOP.CodMovFP=FPMensal.CodMovFP and"
                    + " FPMensalOP.TipoFP=FPMensal.TipoFP and FPMensalOP.Matr=FPMensal.Matr"
                    + " where FPMensal.Matr = " + sMatr
                    + " and FPMensal.CodMovFP = " + sCodmovfp
                    + " and FPMensal.TipoFP = " + sTipoFP);
        }
    }

    /**
     * Retorna dados da Folha Pontos
     *
     * @param sCodmovfp -
     * @param sMatr - Código matrícula
     * @param sTipoFP - Tipo
     * @param persistencia - conexão com o banco
     * @return - retorna lista com tipoFP
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<ContraCheque> getCCComposicao(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        sql = "Select "
                + "FPLancamentos.CodMovFP, "
                + "FPLancamentos.TipoFP, "
                + "FPLancamentos.Verba, "
                + "Verbas.Descricao, "
                + "FPLancamentos.Valor, "
                + "FPLancamentos.Tipo, "
                + "FPLancamentos.ValorCalc "
                + "from FPLancamentos "
                + "Left join Verbas on Verbas.Verba=FPLancamentos.Verba "
                + "where FPLancamentos.CodMovFP = ? "
                + "and FPLancamentos.TipoFP = ? "
                + "and FPLancamentos.Matr = ? "
                + "and FPLancamentos.Flag_Excl <> '*' ";
        if (persistencia.getEmpresa().equals("SATLOYAL")) {
            sql = sql + "AND FPLancamentos.Verba <> 1391 "
                    + "AND FPLancamentos.Verba <> 1392 ";
        }
        sql = sql + "order by FPLancamentos.Tipo desc, FPLancamentos.Verba asc ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPLancamentos oFPLancamentos = new FPLancamentos();
                Verbas oVerbas = new Verbas();
                oFPLancamentos.setCodMovFP(stm.getString("CodMovFP"));
                oFPLancamentos.setTipoFP(stm.getString("TipoFP"));
                oFPLancamentos.setVerba(stm.getString("Verba"));
                oVerbas.setDescricao(stm.getString("Descricao"));
                oFPLancamentos.setValor(stm.getString("Valor"));
                oFPLancamentos.setTipo(stm.getString("Tipo"));
                oFPLancamentos.setValorCalc(stm.getString("ValorCalc"));

                oContraCheque.setfPLancamentos(oFPLancamentos);
                oContraCheque.setVerbas(oVerbas);

                lContraCheque.add(oContraCheque);
            }
            stm.Close();
            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getCCComposicao - " + e.getMessage() + "\r\n"
                    + "Select "
                    + "FPLancamentos.CodMovFP, "
                    + "FPLancamentos.TipoFP, "
                    + "FPLancamentos.Verba, "
                    + "Verbas.Descricao, "
                    + "FPLancamentos.Valor, "
                    + "FPLancamentos.Tipo, "
                    + "FPLancamentos.ValorCalc "
                    + "from FPLancamentos "
                    + "Left join Verbas on Verbas.Verba=FPLancamentos.Verba "
                    + "where FPLancamentos.CodMovFP = " + sCodmovfp
                    + "and FPLancamentos.TipoFP = " + sTipoFP
                    + "and FPLancamentos.Matr = " + sMatr
                    + "and FPLancamentos.Flag_Excl <> '*' "
                    + (persistencia.getEmpresa().equals("SATLOYAL") ? "AND FPLancamentos.Verba <> 1391 AND FPLancamentos.Verba <> 1392 " : "")
                    + "order by FPLancamentos.Tipo desc");
        }
    }

    public List<ContraCheque> getBaseCalcs(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        sql = "select BaseINSS, "
                + " BaseFGTS, "
                + " FGTS,"
                + " BaseIR"
                + " from FPMensal "
                + " where "
                + " codmovfp=? and "
                + " TipoFP=? "
                + " and Matr=? ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPMensal oFPMensal = new FPMensal();
                oFPMensal.setBaseINSS(stm.getString("BaseINSS"));
                oFPMensal.setBaseFGTS(stm.getString("BaseFGTS"));
                oFPMensal.setFGTS(stm.getString("FGTS"));
                oFPMensal.setBaseIR(stm.getString("BaseIR"));

                oContraCheque.setfPMensal(oFPMensal);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getBaseCalcs - " + e.getMessage() + "\r\n"
                    + "select BaseINSS, "
                    + " BaseFGTS, "
                    + " FGTS,"
                    + " BaseIR"
                    + " from FPMensal "
                    + " where "
                    + " codmovfp= " + sCodmovfp + " and "
                    + " TipoFP= " + sTipoFP
                    + " and Matr=" + sMatr);
        }

    }

//----------------------------------------- coleção para o XML
    public List<ContraCheque> getColecao(String sCodmovfp, String sMatr, String sTipoFP, Persistencia persistencia) throws Exception {
        List<ContraCheque> lContraCheque = new ArrayList();
        sql = "Select top 1 "
                + " Funcion.Matr, "
                + " Funcion.Nome, "
                + " PstServ.Local LocalPosto, "
                + " PstServ.Secao, "
                + " Cargos.Descricao DescCargo, "
                + " Filiais.Descricao DescFil, "
                + " Funcion.CPF, "
                + " Funcion.RG, "
                + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                + " Funcion.Pis, "
                + " Funcion.Codfil, "
                + " CCusto.Descricao, "
                + " Funcion.Bairro, "
                + " Funcion.Cidade, "
                + " Funcion.Cep, "
                + " Funcion.UF, "
                + " Convert(VarChar, FPMensalOP.DtPagto, 103) DtPagto, "
                + " Funcion.Vinculo, "
                + " Funcion.DepIr, "
                + " Funcion.DepSf, "
                + " Filiais.RazaoSocial," //21
                + " Filiais.Endereco,"
                + " Filiais.Bairro,"
                + " Filiais.Cidade,"
                + " Filiais.UF,"
                + " Filiais.CEP,"
                + " Filiais.CNPJ,"
                + " Cargos.CBO"
                + " from FPMensal as FPMensal "
                + " Left join Funcion as Funcion on Funcion.Matr = FPMensal.Matr"
                + " Left join PstServ as PstServ on PstServ.Secao = Funcion.Secao and PstServ.CodFil = Funcion.CodFil "
                + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                + " Left Join CCusto as CCusto on CCusto.CCusto = Funcion.CCusto"
                + " Left Join FPMensalOP as FPMensalOP on FPMensalOP.CodMovFP=FPMensal.CodMovFP and"
                + " FPMensalOP.TipoFP=FPMensal.TipoFP and FPMensalOP.Matr=FPMensal.Matr"
                + " where FPMensal.Matr = ? "
                + " and FPMensal.CodMovFP = ? "
                + " and FPMensal.TipoFP = ? ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sMatr);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            ContraCheque oContraCheque = null;
            stm.select();

            while (stm.Proximo()) {
                Funcion oFuncion = new Funcion();
                PstServ oPstServ = new PstServ();
                Cargos oCargos = new Cargos();
                Filiais oFiliais = new Filiais();
                CCusto oCCusto = new CCusto();
                //FPMensalOP oFPMensalOP = new FPMensalOP();
                oContraCheque = new ContraCheque();

                oFuncion.setMatr(stm.getString("Matr"));
                oFuncion.setNome(stm.getString("Nome"));
                oPstServ.setLocal(stm.getString("LocalPosto"));
                oPstServ.setSecao(stm.getString("Secao"));
                oCargos.setDescricao(stm.getString("DescCargo"));
                oFiliais.setDescricao(stm.getString("DescFil"));
                oFuncion.setCPF(stm.getString("CPF"));
                oFuncion.setRG(stm.getString("RG"));
                oFuncion.setDt_Admis(stm.getString("Dt_Admis"));
                oFuncion.setPIS(stm.getString("Pis"));
                oFuncion.setCodFil(stm.getString("Codfil"));
                oCCusto.setDescricao(stm.getString("Descricao"));
                oFuncion.setBairro(stm.getString("Bairro"));
                oFuncion.setCidade(stm.getString("Cidade"));
                oFuncion.setCEP(stm.getString("Cep"));
                oFuncion.setUF(stm.getString("UF"));
                //oFPMensalOP.setDtPagto(LocalDate.parse(stm.getString("DtPagto")));
                oFuncion.setVinculo(stm.getString("Vinculo"));
                oFuncion.setDepIR(stm.getString("DepIr"));
                oFuncion.setDepSF(stm.getString("DepSf"));
                oFiliais.setRazaoSocial(stm.getString("RazaoSocial")); //21
                oFiliais.setEndereco(stm.getString("Endereco"));
                oFiliais.setBairro(stm.getString("Bairro"));
                oFiliais.setCidade(stm.getString("Cidade"));
                oFiliais.setUF(stm.getString("UF"));
                oFiliais.setCEP(stm.getString("CEP"));
                oFiliais.setCNPJ(stm.getString("CNPJ"));
                oCargos.setCBO(stm.getString("CBO"));

                oContraCheque.setFuncion(oFuncion);
                oContraCheque.setPstServ(oPstServ);
                oContraCheque.setCargos(oCargos);
                oContraCheque.setFiliais(oFiliais);
                oContraCheque.setcCusto(oCCusto);
                //oContraCheque.setfPMensalOP(oFPMensalOP);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            // return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getColecao - " + e.getMessage() + "\r\n"
                    + "Select top 1 "
                    + " Funcion.Matr, "
                    + " Funcion.Nome, "
                    + " PstServ.Local LocalPosto, "
                    + " PstServ.Secao, "
                    + " Cargos.Descricao DescCargo, "
                    + " Filiais.Descricao DescFil, "
                    + " Funcion.CPF, "
                    + " Funcion.RG, "
                    + " Convert(VarChar, Funcion.Dt_Admis, 103)Dt_Admis, "
                    + " Funcion.Pis, "
                    + " Funcion.Codfil, "
                    + " CCusto.Descricao, "
                    + " Funcion.Bairro, "
                    + " Funcion.Cidade, "
                    + " Funcion.Cep, "
                    + " Funcion.UF, "
                    + " Convert(VarChar, FPMensalOP.DtPagto, 103) DtPagto, "
                    + " Funcion.Vinculo, "
                    + " Funcion.DepIr, "
                    + " Funcion.DepSf, "
                    + " Filiais.RazaoSocial," //21
                    + " Filiais.Endereco,"
                    + " Filiais.Bairro,"
                    + " Filiais.Cidade,"
                    + " Filiais.UF,"
                    + " Filiais.CEP,"
                    + " Filiais.CNPJ,"
                    + " Cargos.CBO"
                    + " from FPMensal as FPMensal "
                    + " Left join Funcion as Funcion on Funcion.Matr = FPMensal.Matr"
                    + " Left join PstServ as PstServ on PstServ.Secao = Funcion.Secao and PstServ.CodFil = Funcion.CodFil "
                    + " Left join Sindicatos as Sindicatos on Sindicatos.Codigo = Funcion.Sindicato "
                    + " Left Join Filiais as Filiais on Filiais.CodFil = Funcion.Codfil "
                    + " Left Join Cargos as Cargos on Cargos.Cargo = Funcion.Cargo "
                    + " Left Join CCusto as CCusto on CCusto.CCusto = Funcion.CCusto"
                    + " Left Join FPMensalOP as FPMensalOP on FPMensalOP.CodMovFP=FPMensal.CodMovFP and"
                    + " FPMensalOP.TipoFP=FPMensal.TipoFP and FPMensalOP.Matr=FPMensal.Matr"
                    + " where FPMensal.Matr = " + sMatr
                    + " and FPMensal.CodMovFP = " + sCodmovfp
                    + " and FPMensal.TipoFP = " + sTipoFP);
        }

        sql = "select BaseINSS, "
                + " BaseFGTS, "
                + " FGTS,"
                + " BaseIR"
                + " from FPMensal "
                + " where "
                + " codmovfp=? and "
                + " TipoFP=? "
                + " and Matr=? ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPMensal oFPMensal = new FPMensal();
                oFPMensal.setBaseINSS(stm.getString("BaseINSS"));
                oFPMensal.setBaseINSS(stm.getString("BaseFGTS"));
                oFPMensal.setBaseINSS(stm.getString("FGTS"));
                oFPMensal.setBaseINSS(stm.getString("BaseIR"));

                oContraCheque.setfPMensal(oFPMensal);
                lContraCheque.add(oContraCheque);
            }
            stm.Close();

            //    return lContraCheque;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getColecao - " + e.getMessage() + "\r\n"
                    + "select BaseINSS, "
                    + " BaseFGTS, "
                    + " FGTS,"
                    + " BaseIR"
                    + " from FPMensal "
                    + " where "
                    + " codmovfp=" + sCodmovfp + " and "
                    + " TipoFP= " + sTipoFP
                    + " and Matr= " + sMatr);
        }

        sql = "Select "
                + "FPLancamentos.CodMovFP, "
                + "FPLancamentos.TipoFP, "
                + "FPLancamentos.Verba, "
                + "Verbas.Descricao, "
                + "FPLancamentos.Valor, "
                + "FPLancamentos.Tipo, "
                + "FPLancamentos.ValorCalc "
                + "from FPLancamentos "
                + "Left join Verbas on Verbas.Verba=FPLancamentos.Verba "
                + "where FPLancamentos.CodMovFP = ? "
                + "and FPLancamentos.TipoFP = ? "
                + "and FPLancamentos.Matr = ? "
                + "and FPLancamentos.Flag_Excl <> '*' "
                + "order by FPLancamentos.Tipo desc ";
        try {
            Consulta stm = new Consulta(sql, persistencia);
            stm.setString(sCodmovfp);
            stm.setString(sTipoFP);
            stm.setString(sMatr);
            stm.select();

            while (stm.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPLancamentos oFPLancamentos = new FPLancamentos();
                Verbas oVerbas = new Verbas();
                oFPLancamentos.setCodMovFP(stm.getString("CodMovFP"));
                oFPLancamentos.setTipoFP(stm.getString("TipoFP"));
                oFPLancamentos.setVerba(stm.getString("Verba"));
                oVerbas.setDescricao(stm.getString("Descricao"));
                oFPLancamentos.setValor(stm.getString("Valor"));
                oFPLancamentos.setTipo(stm.getString("Tipo"));
                oFPLancamentos.setValorCalc(stm.getString("ValorCalc"));

                oContraCheque.setfPLancamentos(oFPLancamentos);
                oContraCheque.setVerbas(oVerbas);

                lContraCheque.add(oContraCheque);
            }
            stm.Close();

        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getColecao - " + e.getMessage() + "\r\n"
                    + "Select "
                    + "FPLancamentos.CodMovFP, "
                    + "FPLancamentos.TipoFP, "
                    + "FPLancamentos.Verba, "
                    + "Verbas.Descricao, "
                    + "FPLancamentos.Valor, "
                    + "FPLancamentos.Tipo, "
                    + "FPLancamentos.ValorCalc "
                    + "from FPLancamentos "
                    + "Left join Verbas on Verbas.Verba=FPLancamentos.Verba "
                    + "where FPLancamentos.CodMovFP = " + sCodmovfp
                    + "and FPLancamentos.TipoFP = " + sTipoFP
                    + "and FPLancamentos.Matr = " + sMatr
                    + "and FPLancamentos.Flag_Excl <> '*' "
                    + "order by FPLancamentos.Tipo desc ");
        }

        sql = "select top 1 DtInicio, CodMovFP"
                + " from fpperiodos "
                + " where dtpublicweb<? "
                //+ " and CodMovFP=? "
                + " order by dtinicio desc";
        try {
            Consulta cFpPeriodos = new Consulta(sql, persistencia);
            cFpPeriodos.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            //cFpPeriodos.setString(sCodmovfp);
            cFpPeriodos.select();

            while (cFpPeriodos.Proximo()) {
                ContraCheque oContraCheque = new ContraCheque();
                FPPeriodos oFpPeriodos = new FPPeriodos();

                oFpPeriodos.setDtInicio(cFpPeriodos.getDate("DtInicio").toLocalDate());
                oFpPeriodos.setCodMovFP(cFpPeriodos.getString("CodMovFP"));
                oContraCheque.setfPeriodos(oFpPeriodos);
                lContraCheque.add(oContraCheque);

            }
            cFpPeriodos.Close();
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.getColecao - " + e.getMessage() + "\r\n"
                    + "select top 1 DtInicio, CodMovFP"
                    + " from fpperiodos "
                    + " where dtpublicweb< " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL")
                    + " order by dtinicio desc");
        }

        return lContraCheque;
    }

    public List<ContraCheque> logContraChequeGride(String codMovFPIni, String codMovFPFim, String codFil, String matr, String secao, String nomeColaborador, Persistencia Persistencia) throws Exception {
        String sql = "";

        try {
            List<ContraCheque> Retorno = new ArrayList<>();

            sql = "SELECT DISTINCT\n"
                    + "FPPeriodos.CodMovFP,\n"
                    + "('20' + LEFT(FPPeriodos.CodMovFP,2) + '-' + RIGHT(FPPeriodos.CodMovFP,2) + '-01') Competencia,\n"
                    + "Funcion.CodFil,\n"
                    + "CONVERT(BIGINT,Funcion.Matr) Matr,\n"
                    + "Funcion.Nome,\n"
                    + "Funcion.Secao,\n"
                    + "PstServ.Local,\n"
                    + "FPMensal.Ccusto,\n"
                    + "Cargos.Descricao Cargo,\n"
                    + "FPMensal.TipoFP,\n"
                    + "FPMensal.Operador,\n"
                    + "FPMensal.Dt_alter,\n"
                    + "FPMensal.Hr_Alter,\n"
                    + "SASLOGportal.caminhoAssinatura\n"
                    + "FROM FPPeriodos\n"
                    + "JOIN FPMensal\n"
                    + "  ON FPPeriodos.CodMovFP = FPMensal.CodMovFP\n"
                    + "JOIN Funcion\n"
                    + "  ON FPMensal.Matr = Funcion.Matr\n"
                    + " AND FPMensal.codfil = Funcion.codfil\n"
                    + "LEFT JOIN PstServ\n"
                    + "  ON FPMensal.Secao = PstServ.Secao\n"
                    + " AND FPMensal.codfil = PstServ.codfil\n"
                    + "LEFT JOIN Cargos\n"
                    + "  ON FPMensal.Cargo = Cargos.Cargo\n"
                    + "JOIN FPLancamentos \n"
                    + "  ON FPPeriodos.CodMovFP = FPLancamentos.CodMovFP\n"
                    + " AND FPMensal.codfil = FPLancamentos.codfil\n"
                    + " AND FPMensal.matr = FPLancamentos.matr\n"
                    + "LEFT JOIN(SELECT \n"
                    + "          MAX(Sequencia) Sequencia,\n"
                    + "          CodFil,\n"
                    + "          RIGHT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2) + \n"
                    + "          LEFT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2) codMovFP,\n"
                    + "          Matr\n"
                    + "          FROM SASLOGportal\n"
                    + "          WHERE Descricao LIKE 'Contracheque gerado%'\n"
                    + "          AND   RIGHT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2) + \n"
                    + "                LEFT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2) BETWEEN ? AND ?\n"
                    + "          GROUP BY Matr, CodFil, RIGHT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2) + \n"
                    + "                                 LEFT(REPLACE(SUBSTRING(REPLACE(Descricao, 'Contracheque gerado ',''), 1, (CHARINDEX('-', REPLACE(Descricao, 'Contracheque gerado ','')) - 1)),' ',''),2)) AssinaturaRef\n"
                    + "  ON FPPeriodos.CodMovFP = AssinaturaRef.CodMovFP\n"
                    + " AND FPMensal.codfil = AssinaturaRef.codfil\n"
                    + " AND FPMensal.matr = AssinaturaRef.matr\n"
                    + " LEFT JOIN SASLOGportal\n"
                    + "   ON AssinaturaRef.Sequencia = SASLOGportal.Sequencia\n"
                    + "WHERE FPPeriodos.Dtpublicweb <= CONVERT(date, GETDATE())\n"
                    + "AND   FPPeriodos.CodMovFP BETWEEN ? AND ?\n";

            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                sql += "AND   FPMensal.CodFil = ?\n";
            }

            if (null != secao && !secao.equals("")) {
                sql += "AND   PstServ.local LIKE '%" + secao + "%'\n";
            }

            if (null != matr && !matr.equals("") && !matr.equals("0")) {
                sql += "AND   FPMensal.matr = ?\n";
            }

            if (null != nomeColaborador && !nomeColaborador.equals("")) {
                sql += "AND   Funcion.Nome LIKE '%" + nomeColaborador + "%'\n";
            }

            sql += "ORDER BY Funcion.Nome, FPPeriodos.CodMovFP";

            Consulta consulta = new Consulta(sql, Persistencia);
            consulta.setString(codMovFPIni);
            consulta.setString(codMovFPFim);
            consulta.setString(codMovFPIni);
            consulta.setString(codMovFPFim);

            if (null != codFil && !codFil.equals("") && !codFil.equals("0")) {
                consulta.setString(codFil);
            }

            if (null != matr && !matr.equals("") && !matr.equals("0")) {
                consulta.setString(matr);
            }

            consulta.select();

            ContraCheque contraCheque;
            Funcion funcion;
            FPMensal fpMensal;

            while (consulta.Proximo()) {
                contraCheque = new ContraCheque();
                funcion = new Funcion();
                fpMensal = new FPMensal();

                funcion.setCodFil(consulta.getString("CodFil"));
                funcion.setNome(consulta.getString("Nome"));
                funcion.setMatr(consulta.getString("Matr"));
                funcion.setSecao(consulta.getString("Secao"));
                funcion.setLocalRef(consulta.getString("Local"));
                funcion.setCCusto(consulta.getString("Ccusto"));
                funcion.setCargo(consulta.getString("Cargo"));

                contraCheque.setFuncion(funcion);

                fpMensal.setCodMovFP(consulta.getString("CodMovFP"));
                fpMensal.setTipoFP(consulta.getString("TipoFP"));
                fpMensal.setCompetenciaDescr(consulta.getString("Competencia"));
                fpMensal.setOperador(consulta.getString("Operador"));
                fpMensal.setDt_Alter(consulta.getLocalDate("Dt_alter"));
                fpMensal.setHr_Alter(consulta.getString("Hr_Alter"));
                fpMensal.setCaminhoAssinatura(consulta.getString("caminhoAssinatura"));

                contraCheque.setfPMensal(fpMensal);

                Retorno.add(contraCheque);
            }

            consulta.Close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.logContraChequeGride - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<SASLog> logMatr(String matr, String codFil, Persistencia persistencia) throws Exception {
        List<SASLog> retorno = new ArrayList<>();
        SASLog sasLog;
        String sql = "";

        try {
            sql = "SELECT *\n"
                    + "FROM SASLOGPortal\n"
                    + "WHERE Matr   = ?\n"
                    + "AND   codFil = ?\n"
                    + "AND   (descricao LIKE 'Contracheque%'\n"
                    + "OR    descricao LIKE 'Folha%') ORDER BY Data desc, Hora desc";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matr);
            consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                sasLog = new SASLog();
                sasLog.setData(consulta.getString("Data"));
                sasLog.setHora(consulta.getString("Hora"));
                sasLog.setComando(consulta.getString("Descricao"));
                
                retorno.add(sasLog);

            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ContraChequeDao.logMatr - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
