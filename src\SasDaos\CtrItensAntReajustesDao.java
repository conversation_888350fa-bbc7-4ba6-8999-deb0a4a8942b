/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CtrItensAntReajustes;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CtrItensAntReajustesDao {

    private final String sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param primeiro elemento inicial de paginação
     * @param linhas quantidade de elementos de paginação
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<CtrItensAntReajustes> listarItensAnterioresPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            List<CtrItensAntReajustes> itens = new ArrayList<>();

            String sql = "Select CtrItensAnt.*, CtrItens.Descricao, CtrReajustes.DtBase, CtrReajustes.IndiceFinal Indice from CtrItensAnt \n"
                    + "left Join Ctritens on ((CtrItens.Contrato = CtrItensAnt.Contrato) \n"
                    + "    and (CtrItens.CodFil = CtrItensAnt.CodFil) \n"
                    + "    and (CtrItens.TipoPosto = CtrItensAnt.TipoPosto)) \n"
                    + "left join CtrReajustes on CtrReajustes.Contrato = CtrItensAnt.Contrato \n"
                    + "    and CtrReajustes.CodFil = CtrItensAnt.CodFil \n"
                    + "    and CtrReajustes.DtBase = CtrItensAnt.Data \n"
                    + "    and CtrReajustes.Ordem  = CtrItensAnt.Ordem \n"
                    + "where CtrItensAnt.Contrato = ? \n";
            // + "    and CtrItensAnt.CodFil = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            sql += "ORDER BY CtrItensAnt.Data Desc, CtrItens.TipoPosto \n" + sqlPaginacao;
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);
            consulta.select();
            CtrItensAntReajustes item;
            while (consulta.Proximo()) {
                item = new CtrItensAntReajustes();

                item.setContrato(consulta.getString("Contrato"));
                item.setCodFil(consulta.getString("CodFil"));
                item.setTipoPosto(consulta.getString("TipoPosto"));
                item.setSeqReaj(consulta.getString("SeqReaj"));
                item.setData(consulta.getString("Data"));
                item.setOrdem(consulta.getString("Ordem"));
                item.setValorPosto(consulta.getString("ValorPosto"));
                item.setHEDiurna(consulta.getString("HEDiurna"));
                item.setHENoturna(consulta.getString("HENoturna"));
                item.setHEDiurna2(consulta.getString("HEDiurna2"));
                item.setHENoturna2(consulta.getString("HENoturna2"));
                item.setReforco(consulta.getString("Reforco"));
                item.setValorRot(consulta.getString("ValorRot"));
                item.setFranquiaRot(consulta.getString("FranquiaRot"));
                item.setValorEve(consulta.getString("ValorEve"));
                item.setFranquiaEve(consulta.getString("FranquiaEve"));
                item.setValorEsp(consulta.getString("ValorEsp"));
                item.setFranquiaEsp(consulta.getString("FranquiaEsp"));
                item.setValorAst(consulta.getString("ValorAst"));
                item.setFranquiaAst(consulta.getString("FranquiaAst"));
                item.setSalario(consulta.getString("Salario"));
                item.setInibirReajuste(consulta.getString("inibirReajuste"));
                item.setOperador(consulta.getString("Operador"));
                item.setDt_alter(consulta.getString("Dt_alter"));
                item.setHr_alter(consulta.getString("Hr_alter"));
                item.setDescricao(consulta.getString("Descricao"));
                item.setDtBase(consulta.getString("DtBase"));
                item.setIndice(consulta.getString("Indice"));

                itens.add(item);
            }

            return itens;
        } catch (Exception e) {
            throw new Exception("CtrItensAntReajustesDao.listarItensPaginada - " + e.getMessage());
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public int contagemItensAnteriores(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "Select COUNT(*) AS total from CtrItensAnt \n"
                    + "left Join Ctritens on ((CtrItens.Contrato = CtrItensAnt.Contrato) \n"
                    + "    and (CtrItens.CodFil = CtrItensAnt.CodFil) \n"
                    + "    and (CtrItens.TipoPosto = CtrItensAnt.TipoPosto)) \n"
                    + "left join CtrReajustes on CtrReajustes.Contrato = CtrItensAnt.Contrato \n"
                    + "    and CtrReajustes.CodFil = CtrItensAnt.CodFil \n"
                    + "    and CtrReajustes.DtBase = CtrItensAnt.Data \n"
                    + "    and CtrReajustes.Ordem  = CtrItensAnt.Ordem \n"
                    + "where CtrItensAnt.Contrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.select();
            int total = 0;
            while (consulta.Proximo()) {
                total = consulta.getInt("total");
            }

            return total;
        } catch (Exception e) {
            throw new Exception("CtrItensAntReajustesDao.contagemItensAnteriores - " + e.getMessage());
        }
    }
}
