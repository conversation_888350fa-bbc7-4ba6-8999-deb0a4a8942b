/*
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ContrVig;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ContrVigDao {

    private final String sqlPaginacao = "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY\n";

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param CodFil - codigo da filial para buscar contratos
     * @param query
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<ContrVig> ListarContratos(BigDecimal CodFil, String query, Persistencia persistencia) throws Exception {
        try {
            List<ContrVig> contratos = new ArrayList<>();
            String sql = "select top 20 clientes.nred, contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " left join clientes on clientes.codigo = contrvig.clifat "
                    + "                   and clientes.codfil = contrvig.codfil "
                    + " where contrvig.codfil = ? and "
                    + " (clientes.nred like ? or contrvig.descricao like ? or contrvig.contrato like ? or contrvig.identif like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            ContrVig contrato;
            while (consult.Proximo()) {
                contrato = new ContrVig();
                contrato.setDescricao(consult.getString("descricao"));
                contrato.setContrato(consult.getString("contrato"));
                contrato.setIdentif(consult.getString("identif"));
                contrato.setNRed((consult.getString("nred")));
                contratos.add(contrato);
            }
            consult.Close();
            return contratos;
        } catch (Exception e) {
            throw new Exception("ContrVigDao.ListarContratos - " + e.getMessage() + "\r\n"
                    + "select top 20 clientes.nred, contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " left join clientes on clientes.codigo = contrvig.clifat "
                    + "                   and clientes.codfil = contrvig.codfil "
                    + " where contrvig.codfil = " + CodFil + " and "
                    + " (clientes.nred like %" + query + "% or contrvig.descricao like %" + query + "% or "
                    + "contrvig.contrato like %" + query + "% or contrvig.identif like %" + query + "%)");
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param CodFil - codigo da filial para buscar contratos
     * @param query
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<ContrVig> listarContratos(String CodFil, String query, Persistencia persistencia) throws Exception {
        try {
            List<ContrVig> contratos = new ArrayList<>();
            String sql = "select contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " where contrvig.codfil = ? and "
                    + " (contrvig.descricao like ? or contrvig.contrato like ? or contrvig.identif like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            ContrVig contrato;
            while (consult.Proximo()) {
                contrato = new ContrVig();
                contrato.setDescricao(consult.getString("descricao"));
                contrato.setContrato(consult.getString("contrato"));
                contrato.setIdentif(consult.getString("identif"));
                contratos.add(contrato);
            }
            consult.Close();
            return contratos;
        } catch (Exception e) {
            throw new Exception("ContrVigDao.listarContratos - " + e.getMessage() + "\r\n"
                    + "select contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " where contrvig.codfil = " + CodFil + " and "
                    + " (contrvig.descricao like %" + query + "% or contrvig.contrato like %" + query + "% or contrvig.identif like %" + query + "%)");
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param primeiro elemento inicial de paginação
     * @param linhas quantidade de elementos de paginação
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public List<ContrVig> listarSubContratosPaginada(
            int primeiro,
            int linhas,
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            List<ContrVig> contratos = new ArrayList<>();
            String sql = "SELECT * \n"
                    + "FROM contrvig \n"
                    + "WHERE contrvig.IDContrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            sql += "ORDER BY Contrato \n" + sqlPaginacao;
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro);
            consulta.setInt(linhas);

            consulta.select();
            ContrVig contrato;
            while (consulta.Proximo()) {
                contrato = new ContrVig();
                contrato.setContrato(consulta.getString("Contrato"));
                contrato.setCodFil(consulta.getBigDecimal("CodFil"));
                contrato.setDescricao(consulta.getString("Descricao"));
                contrato.setTipo(consulta.getString("Tipo"));
                contrato.setSituacao(consulta.getString("Situacao"));
                contrato.setIdentif(consulta.getString("Identif"));
                contrato.setDt_Inicio(consulta.getLocalDate("Dt_inicio"));
                contrato.setDt_Termino(consulta.getLocalDate("Dt_Termino"));
                contrato.setCliFat(consulta.getString("CliFat"));
                contrato.setIDContrato(consulta.getString("IDContrato"));
                contrato.setContratoCli(consulta.getString("ContratoCli"));
                contrato.setRefArq(consulta.getString("RefArq"));
                contrato.setOBS(consulta.getString("OBS"));
                contrato.setOperador(consulta.getString("Operador"));
                contrato.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                contrato.setHr_Alter(consulta.getString("Hr_Alter"));
                contratos.add(contrato);
            }
            consulta.Close();
            return contratos;
        } catch (Exception e) {
            throw new Exception("ContrVigDAO.listarSubContratosPaginada - " + e.getMessage());
        }
    }

    /**
     * Lista contratos (codigo do contrato, descricao do contrato, identificacao
     * e NRed do cliente) de uma filial
     *
     * @param idContrato
     * @param filters Map de cláusulas where
     * @param persistencia conexao com o banco
     * @return lista contratos daquela filial
     * @throws Exception
     */
    public int contagemSubContratos(
            String idContrato,
            Map<String, String> filters,
            Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) AS total \n"
                    + "FROM contrvig \n"
                    + "WHERE contrvig.IDContrato = ? \n";

            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql += "AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(idContrato);
            for (Map.Entry<String, String> entrada : filters.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            int total = 0;
            while (consulta.Proximo()) {
                total = consulta.getInt("total");
            }
            consulta.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("ContrVigDAO.contagemSubContratos - " + e.getMessage());
        }
    }

    public ContrVig buscarContrato(String codfil, String cc, Persistencia persistencia) throws Exception {
        try {
            String sql = "select contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " where contrvig.codfil = ? and contrvig.contrato = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(cc);
            consult.select();
            ContrVig contrato = null;
            while (consult.Proximo()) {
                contrato = new ContrVig();
                contrato.setDescricao(consult.getString("descricao"));
                contrato.setContrato(consult.getString("contrato"));
                contrato.setIdentif(consult.getString("identif"));
            }
            consult.Close();
            return contrato;
        } catch (Exception e) {
            throw new Exception("ContrVigDao.listarContratos - " + e.getMessage() + "\r\n"
                    + "select contrvig.descricao, contrvig.contrato, contrvig.identif "
                    + " from contrvig "
                    + " where contrvig.codfil = " + codfil + " and contrvig.contrato = " + cc);
        }
    }

    /**
     * Obtem registros da sequencia do contrato
     *
     * @param contrato contrato criado em contrato
     * @param persistencia Conexao com o banco de dados
     * @return sequencia do contratos
     * @throws Exception
     */
    public int obterSequenciaContrato(String contrato, Persistencia persistencia) throws Exception {
        int sequencia = 0;
        try {
            String sql = "Select "
                    + "Max(Substring(Contrato,(Len(?)+2),5))+1 sequencia "
                    + "from ContrVig "
                    + "where Contrato like ?";

            //Processando listagem
            Consulta consulta = new Consulta(sql, persistencia);
            //consulta.setString(contrato);
            consulta.setString("%" + contrato + "%");
            consulta.select();

            //obtem registros da sequencia
            while (consulta.Proximo()) {
                sequencia = consulta.getInt("sequencia");
            }

            if (sequencia == 0) {
                sequencia = 1;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.obterSequenciaContrato - " + e.getMessage() + "\r\n"
                    + "Select "
                    + "Max(Substring(Contrato,(Len(?)+2),5))+1 sequencia "
                    + "from ContrVig "
                    + "where Contrato like " + contrato);
        }
        return sequencia;
    }

    /**
     * Inserir registros do contrato em vigilancia
     *
     * @param contratoVigilancia Objeto contendo registros do contrato vigilncia
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void inserirContrato(ContrVig contratoVigilancia, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ContrVig (contrato, codfil, descricao, "
                    + "tipo, situacao, identif,  clifat, "
                    + "idContrato, ContratoCli, RefArq, Obs, Operador, hr_alter, dt_alter,"
                    + "dt_inicio, dt_termino) VALUES ("
                    + "?+'.'+(Select Convert(varchar,isnull(Count(*),0)+1) from ContrVig where contrato = ?),"
                    + "?,?,?,?,?,?,?,?,?,?,?,?,"
                    + "CONVERT(DATE, getDate()),CONVERT(DATE, getDate()),"
                    + "CONVERT(DATE, getDate() + 18250))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoVigilancia.getContrato());
            consulta.setString(contratoVigilancia.getContrato());
            consulta.setBigDecimal(contratoVigilancia.getCodFil());
            consulta.setString(contratoVigilancia.getDescricao());
            consulta.setString(contratoVigilancia.getTipo());
            consulta.setString(contratoVigilancia.getSituacao());
            consulta.setString(contratoVigilancia.getIdentif());
            consulta.setString(contratoVigilancia.getCliFat());
            consulta.setString(contratoVigilancia.getIDContrato());
            consulta.setString(contratoVigilancia.getContratoCli());
            consulta.setString(contratoVigilancia.getRefArq());
            consulta.setString(contratoVigilancia.getOBS());
            consulta.setString(contratoVigilancia.getOperador());
            consulta.setString(contratoVigilancia.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.inserirContrato - " + e.getMessage() + "\r\n"
                    + "INSERT INTO ContrVig (contrato, codfil, descricao, "
                    + "tipo, situacao, identif,  clifat, "
                    + "idContrato, ContratoCli, RefArq, Obs, Operador, hr_alter, dt_alter,"
                    + "dt_inicio, dt_termino) VALUES (" + contratoVigilancia.getContrato() + "," + contratoVigilancia.getCodFil() + ","
                    + contratoVigilancia.getDescricao() + "," + contratoVigilancia.getTipo() + "," + contratoVigilancia.getSituacao() + ","
                    + contratoVigilancia.getIdentif() + "," + contratoVigilancia.getCliFat() + "," + contratoVigilancia.getIDContrato() + ","
                    + contratoVigilancia.getContratoCli() + "," + contratoVigilancia.getRefArq() + "," + contratoVigilancia.getOBS() + ","
                    + contratoVigilancia.getOperador() + "," + contratoVigilancia.getHr_Alter() + ","
                    + "CONVERT(DATE, getDate()),CONVERT(DATE, getDate()),"
                    + "CONVERT(DATE, getDate() + 18250))");

        }
    }

    public void inserirContrato(ContrVig contratoVigilancia, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "INSERT INTO ContrVig (contrato, codfil, descricao, "
                    + "tipo, situacao, identif,  clifat, "
                    + "idContrato, ContratoCli, RefArq, Obs, Operador, hr_alter, dt_alter,"
                    + "dt_inicio, dt_termino) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,"
                    + "?,?, CONVERT(DATE, getDate() + 18250))";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(contratoVigilancia.getContrato());
            consulta.setBigDecimal(contratoVigilancia.getCodFil());
            consulta.setString(contratoVigilancia.getDescricao());
            consulta.setString(contratoVigilancia.getTipo());
            consulta.setString(contratoVigilancia.getSituacao());
            consulta.setString(contratoVigilancia.getIdentif());
            consulta.setString(contratoVigilancia.getCliFat());
            consulta.setString(contratoVigilancia.getIDContrato());
            consulta.setString(contratoVigilancia.getContratoCli());
            consulta.setString(contratoVigilancia.getRefArq());
            consulta.setString(contratoVigilancia.getOBS());
            consulta.setString(contratoVigilancia.getOperador());
            consulta.setString(contratoVigilancia.getHr_Alter());
            consulta.setString(dataAtual);
            consulta.setString(dataAtual);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.inserirContrato - " + e.getMessage() + "\r\n"
                    + "INSERT INTO ContrVig (contrato, codfil, descricao, "
                    + "tipo, situacao, identif,  clifat, "
                    + "idContrato, ContratoCli, RefArq, Obs, Operador, hr_alter, dt_alter,"
                    + "dt_inicio, dt_termino) VALUES (" + contratoVigilancia.getContrato() + "," + contratoVigilancia.getCodFil() + ","
                    + contratoVigilancia.getDescricao() + "," + contratoVigilancia.getTipo() + "," + contratoVigilancia.getSituacao() + ","
                    + contratoVigilancia.getIdentif() + "," + contratoVigilancia.getCliFat() + "," + contratoVigilancia.getIDContrato() + ","
                    + contratoVigilancia.getContratoCli() + "," + contratoVigilancia.getRefArq() + "," + contratoVigilancia.getOBS() + ","
                    + contratoVigilancia.getOperador() + "," + contratoVigilancia.getHr_Alter() + "," + dataAtual + "," + dataAtual
                    + ", CONVERT(DATE, getDate() + 18250))");
        }
    }

    /**
     * Verificando a existencia do contrato
     *
     * @param contrato contrato
     * @param idContrato identificador do contrato
     * @param persistencia conexão com o banco de dados
     * @return verifica a existencia do banco
     * @throws Exception
     */
    public boolean existeContrato(String contrato, String idContrato, Persistencia persistencia) throws Exception {
        boolean existe = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM ContrVig WHERE idcontrato = ? AND contrato = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idContrato);
            consulta.setString(contrato);
            consulta.select();

            int contador = 0;
            while (consulta.Proximo()) {
                contador = consulta.getInt("qtd");
            }

            if (contador > 0) {
                existe = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.existeContrato - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM ContrVig WHERE idcontrato = " + idContrato + " AND contrato = " + contrato);
        }
        return existe;
    }

    /**
     * Obtem informações do contrato vig
     *
     * @param codFil
     * @param contrato
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String obterContrato(String codFil, String contrato, Persistencia persistencia) throws Exception {
        String ContrVig = "0";
        try {
            String sql = "SELECT TOP 1 Contrato FROM ContrVig WHERE CodFil = ? AND IDContrato = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(contrato);
            consulta.select();

            while (consulta.Proximo()) {
                ContrVig = consulta.getString("Contrato");
            }

            if ("".equals(ContrVig)) {
                ContrVig = "0";
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.obterContrato - " + e.getMessage() + "\r\n"
                    + "SELECT TOP 1 Contrato FROM ContrVig WHERE CodFil = " + codFil + " AND IDContrato = " + contrato);
        }
        return ContrVig;
    }

    public void editarContrato(ContrVig contratoVigilancia, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update ContrVig set \n"
                    //                    + "contrato = ?, \n"
                    //                    + "codfil = ?, \n"
                    + "descricao = ?, \n"
                    + "tipo = ?, \n"
                    + "situacao = ?, \n"
                    + "identif = ?, \n"
                    + "clifat = ?, \n"
                    + "idContrato = ?, \n"
                    + "ContratoCli = ?, \n"
                    + "RefArq = ?, \n"
                    + "Obs = ? , \n"
                    + "Operador = ?, \n"
                    + "hr_alter = ?, \n"
                    + "dt_alter = ?, \n"
                    + "dt_inicio = ?, \n"
                    + "dt_termino = ? \n"
                    + "where Contrato = ? \n"
                    + "  and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
//            consulta.setString(contratoVigilancia.getContrato());
//            consulta.setBigDecimal(contratoVigilancia.getCodFil()); 
            consulta.setString(contratoVigilancia.getDescricao());
            consulta.setString(contratoVigilancia.getTipo());
            consulta.setString(contratoVigilancia.getSituacao());
            consulta.setString(contratoVigilancia.getIdentif());
            consulta.setString(contratoVigilancia.getCliFat());
            consulta.setString(contratoVigilancia.getIDContrato());
            consulta.setString(contratoVigilancia.getContratoCli());
            consulta.setString(contratoVigilancia.getRefArq());
            consulta.setString(contratoVigilancia.getOBS());
            consulta.setString(contratoVigilancia.getOperador());
            consulta.setString(contratoVigilancia.getHr_Alter());
            consulta.setString(contratoVigilancia.getDt_Alter().toString());
            consulta.setString(contratoVigilancia.getDt_Inicio().toString());
            consulta.setString(contratoVigilancia.getDt_Termino().toString());
            consulta.setString(contratoVigilancia.getContrato());
            consulta.setString(contratoVigilancia.getCodFil().toString().replace(".0", ""));
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ContrVigDao.editarContrato - " + e.getMessage());
        }
    }
}
