package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ClientesGuia;
import SasBeans.ClientesImg;
import SasBeans.GTVeAcesso;
import SasBeans.Pessoa;
import SasBeans.SasPWFill;
import SasBeansCompostas.ClientesContatos;
import SasBeansCompostas.ClientesContatos.Postos;
import SasBeansCompostas.ClientesMobileHW;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.LC2Date;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.pacotesuteis.utilidades.Sqls;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ClientesDao {

    /**
     * Busca Cliente por InterfExt ou Codigo
     *
     * @param codigo
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Clientes buscarCliente(String codigo, String codFil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1 * FROM Clientes WHERE (InterfExt = ? OR Codigo = ?) AND CodFil = ? ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codigo);
            consult.setString(codigo);
            consult.setString(codFil);
            consult.select();
            if (consult.Proximo()) {
                retorno = new Clientes();
                retorno.setCEP(consult.getString("cep"));
                retorno.setFone1(consult.getString("fone1"));
                retorno.setFone2(consult.getString("fone2"));
                retorno.setCodFil(consult.getString("codfil"));
                retorno.setBanco(consult.getString("Banco"));
                retorno.setTpCli(consult.getString("TpCli"));
                retorno.setCodCli(consult.getString("CodCli"));
                retorno.setCodigo(consult.getString("codigo"));
                retorno.setNRed(consult.getString("nred"));
                retorno.setRegiao(consult.getString("regiao"));
                retorno.setEnde(consult.getString("ende"));
                retorno.setBairro(consult.getString("bairro"));
                retorno.setCidade(consult.getString("cidade"));
                retorno.setEstado(consult.getString("estado"));
                retorno.setLatitude(consult.getString("latitude"));
                retorno.setLongitude(consult.getString("longitude"));
                retorno.setCodFil(consult.getString("codfil"));
                retorno.setSituacao(consult.getString("situacao"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarCliente - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1 * FROM Clientes WHERE (InterfExt = " + codigo + " OR Codigo = " + codigo + ") AND CodFil = " + codFil);
        }
    }

    public int cadastrarCofre(String Codigo, String CodFil, String CodCofre, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Clientes SET CodCofre = ? WHERE Codigo = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodCofre);
            consulta.setString(Codigo);
            consulta.setString(CodFil);
            int retorno = consulta.update();
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.cadastrarCofre - " + e.getMessage() + "\r\n"
                    + " UPDATE Clientes SET CodCofre = " + CodCofre + " WHERE Codigo = " + Codigo + " AND CodFil = " + CodFil);
        }
    }

    public Clientes buscarTesouraria(String banco, String tpCli, String subAgencia, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT * from Clientes WHERE banco = ? AND tpCli = ? AND SubAgencia = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(banco);
            consulta.setString(tpCli);
            consulta.setString(subAgencia);
            consulta.select();
            Clientes retorno = null;
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setNRed(consulta.getString("nred"));
                retorno.setCodigo(consulta.getString("codigo"));
                retorno.setRegiao(consulta.getString("regiao"));
                retorno.setCodFil(consulta.getString("codfil"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarTesouraria - " + e.getMessage() + "\r\n"
                    + " SELECT * from Clientes WHERE banco = " + banco + " AND tpCli = " + tpCli + " AND SubAgencia =" + subAgencia);
        }
    }

    public void inserirPontoServico(String codigo, String codfil, String idpontoservico, String agencia,
            String subagencia, String razaosocial, String cep, String endereco, String bairro, String cidade,
            String uf, String cnpj, String cpf, String email, String fone, String fone2, String operador, String dt_alter, String hr_alter,
            Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO Clientes (codigo, codfil, banco, tpcli, codcli, InterfExt, agencia, \n"
                    + " subagencia, nred, nome, cep, Ende, bairro, cidade, \n"
                    + " Estado, CGC, cpf, email, fone1, fone2, situacao, Oper_Inc, Dt_Cad, Oper_Alt, Dt_Alter, Hr_Alter) \n"
                    + " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codigo);
            consulta.setString(codfil);
            consulta.setString(codigo.substring(0, 3));
            consulta.setString(codigo.substring(3, 4));
            consulta.setString(codigo.substring(4));
            consulta.setString(FuncoesString.RecortaString(idpontoservico, 0, 80));
            consulta.setString(FuncoesString.RecortaString(agencia, 0, 6));
            consulta.setString(FuncoesString.RecortaString(subagencia, 0, 3));
            consulta.setString(FuncoesString.RecortaString(razaosocial, 0, 20 - FuncoesString.RecortaString(idpontoservico, 0, 20).length())
                    + FuncoesString.RecortaString(idpontoservico, 0, 20));
            consulta.setString(FuncoesString.RecortaString(razaosocial, 0, 60));
            consulta.setString(FuncoesString.RecortaString(cep, 0, 8));
            consulta.setString(FuncoesString.RecortaString(endereco, 0, 60));
            consulta.setString(FuncoesString.RecortaString(bairro, 0, 25));
            consulta.setString(FuncoesString.RecortaString(cidade, 0, 25));
            consulta.setString(FuncoesString.RecortaString(uf, 0, 2));
            consulta.setString(FuncoesString.RecortaString(cnpj, 0, 14));
            consulta.setString(FuncoesString.RecortaString(cpf, 0, 11));
            consulta.setString(FuncoesString.RecortaString(email, 0, 80));
            consulta.setString(FuncoesString.RecortaString(fone, 0, 11));
            consulta.setString(FuncoesString.RecortaString(fone2, 0, 11));
            consulta.setString("A");
            consulta.setString(operador);
            consulta.setString(dt_alter);
            consulta.setString(operador);
            consulta.setString(dt_alter);
            consulta.setString(hr_alter);
                    
                    
            consulta.insert();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.inserirPontoServico - " + e.getMessage() + "\r\n"
                    + " INSERT INTO Clientes (codigo, codfil, banco, tpcli, codcli, InterfExt, agencia, \n"
                    + " subagencia, nred, nome, cep, Ende, bairro, cidade, \n"
                    + " Estado, CGC, cpf, email, fone1, fone2) \n"
                    + " VALUES (" + codigo + ", " + codfil + ", " + codigo.substring(0, 3) + ", " + codigo.substring(3, 4) + ", " + codigo.substring(4) + ", "
                    + idpontoservico + ", " + agencia + ", " + subagencia + ", " + FuncoesString.RecortaString(razaosocial, 0, 20 - FuncoesString.RecortaString(idpontoservico, 0, 20).length())
                    + FuncoesString.RecortaString(idpontoservico, 0, 20) + ", " + razaosocial + ", " + cep + ", " + endereco + ", " + bairro + ", " + cidade + ", "
                    + uf + ", " + cnpj + ", " + cpf + ", " + email + ", " + fone + ", " + fone2 + ",)");

        }
    }

    public void alterarSituacaoPontoServico(String idpontoservico, String situacao, Persistencia persistencia) throws Exception {
        try {
            String sql = " UPDATE Clientes SET Situacao = ? WHERE InterfExt = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(situacao);
            consulta.setString(idpontoservico);
            consulta.update();
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.alterarSituacaoPontoServico - " + e.getMessage() + "\r\n"
                    + "UPDATE Clientes SET Situacao = " + situacao + " WHERE InterfExt = " + idpontoservico);
        }
    }

    public int existePontoServico(String idpontoservico, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Situacao FROM Clientes WHERE InterfExt = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idpontoservico);
            consulta.select();
            int retorno = 0;
            if (consulta.Proximo()) {
                if (consulta.getString("Situacao").equals("A")) {
                    retorno = 1;
                } else {
                    retorno = 2;
                }
            } else {
                retorno = 0;
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.existeCliente - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM Clientes WHERE InterfExt = " + idpontoservico);
        }
    }

    public Clientes buscaPontoServico(String idpontoservico, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT NRed, Codigo, Regiao, CodFil FROM Clientes WHERE InterfExt = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(idpontoservico);
            consulta.select();
            Clientes retorno = null;
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setNRed(consulta.getString("nred"));
                retorno.setCodigo(consulta.getString("codigo"));
                retorno.setRegiao(consulta.getString("regiao"));
                retorno.setCodFil(consulta.getString("codfil"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscaPontoServico - " + e.getMessage() + "\r\n"
                    + "SELECT NRed, Codigo, Regiao, CodFil FROM Clientes WHERE InterfExt = " + idpontoservico);
        }
    }

    public List<Clientes> listaClientesDistancia(String latitude, String longitude, String distMax,
            Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " Select Clientes.CodFil, Clientes.Codigo CodCli, \n"
                    + " Clientes.NRed, Clientes.Cidade, Clientes.Estado UF, Clientes.Latitude, Clientes.Longitude, \n"
                    + " dbo.fun_CalcDistancia(?, ?, Clientes.Latitude, Clientes.Longitude) Distancia, "
                    + " Clientes.Ende Endereco, Filiais.Descricao "
                    + " from Clientes "
                    + " LEFT JOIN Filiais on Filiais.CodFil = Clientes.CodFil"
                    + " where Clientes.latitude <> ''"
                    + "   and dbo.fun_CalcDistancia(?, ?, Clientes.Latitude, Clientes.Longitude) <= ? "
                    + " order by Distancia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(distMax);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consulta.getString("CodFil"));
                cliente.setCodCli(consulta.getString("CodCli"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setEnde(consulta.getString("Endereco"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setEstado(consulta.getString("UF"));
                cliente.setLimite(consulta.getString("Distancia"));
                cliente.setLatitude(consulta.getString("Latitude"));
                cliente.setLongitude(consulta.getString("Longitude"));

                cliente.setInterfExt(consulta.getString("Descricao"));

                retorno.add(cliente);
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaClientesDistancia - " + e.getMessage() + "\r\n"
                    + " Select Clientes.Codigo CodCli, "
                    + " Clientes.NRed, Clientes.Cidade, Clientes.Estado UF,  Clientes.Latitude, Clientes.Longitude, \n"
                    + " dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Clientes.Latitude, Clientes.Longitude) Distancia "
                    + " from Clientes "
                    + " where Clientes.latitude <> ''"
                    + "   and dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Clientes.Latitude, Clientes.Longitude) <= " + distMax
                    + " order by Distancia ");
        }
    }

    /**
     *
     * @param latitude
     * @param longitude
     * @param distMax
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<ClientesContatos> listaClientesPessoaTrajeto(String latitude, String longitude, String distMax,
            Persistencia persistencia) throws Exception {
        try {
            List<ClientesContatos> retorno = new ArrayList<>();
            String sql = " Select Filiais.CodFil, Filiais.Descricao, Contatos.Codigo CodCont, Clientes.Codigo CodCli, "
                    + " Clientes.NRed, Clientes.Cidade, Clientes.Estado UF, dbo.fun_CalcDistancia(?, ?, Clientes.Latitude, Clientes.Longitude) Distancia, "
                    + " PstServ.Secao Secao, PstServ.Local Local, Clientes.Ende Endereco "
                    + " from Clientes "
                    + " left join Filiais on Filiais.CodFil = Clientes.CodFil "
                    + " left join Contatos on Contatos.CodCli = Clientes.Codigo "
                    + "                   and Contatos.CodFil = Clientes.CodFil "
                    + " left join PstServ on PstServ.CodCli = Clientes.Codigo "
                    + "                  and PstServ.CodFil = Clientes.CodFil "
                    + " where Clientes.latitude <> ''"
                    + "   and dbo.fun_CalcDistancia(?, ?, Clientes.Latitude, Clientes.Longitude) <= ? "
                    + " union "
                    + " Select Filiais.CodFil, Filiais.Descricao, Contatos.Codigo CodCont, Contatos.CodCli, Contatos.Fantasia, Contatos.Cidade, "
                    + " Contatos.UF, dbo.fun_CalcDistancia(?, ?, Contatos.Latitude, Contatos.Longitude) Distancia, '' Secao, '' Local, "
                    + " Contatos.Endereco Endereco "
                    + " from Contatos "
                    + " left join Filiais on Filiais.CodFil = Contatos.CodFil "
                    + " where Contatos.latitude <> '' "
                    + "   and dbo.fun_CalcDistancia(?, ?, Contatos.Latitude, Contatos.Longitude) <= ? "
                    + " order by Distancia ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(distMax);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(distMax);
            consulta.select();
            ClientesContatos cliente;
            Postos posto;
            int indice;
            while (consulta.Proximo()) {
                cliente = new ClientesContatos();
                cliente.setCodFil(consulta.getString("CodFil"));
                cliente.setDescricao(consulta.getString("Descricao"));
                cliente.setCodCont(consulta.getString("CodCont"));
                cliente.setCodCli(consulta.getString("CodCli"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setEndereco(consulta.getString("Endereco"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setUF(consulta.getString("UF"));
                cliente.setDistancia(consulta.getString("Distancia"));

                posto = new Postos();
                posto.setSecao(consulta.getString("Secao"));
                posto.setLocal(consulta.getString("Local"));

                // Verifica se a entrada já existe na lista
                indice = retorno.indexOf(cliente);
                // Se existir, adiciona o posto a lista de postos do cliente.
                if (indice >= 0) {
                    retorno.get(indice).getPostos().add(posto);
                } else {
                    // Se não, adiciona o cliente a lista
                    cliente.setPostos(new ArrayList<>());

                    cliente.getPostos().add(posto);
                    retorno.add(cliente);
                }
            }

            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaClientesPessoaTrajeto - " + e.getMessage() + "\r\n"
                    + " Select Filiais.CodFil, Filiais.Descricao, Contatos.Codigo CodCont, Clientes.Codigo CodCli, "
                    + " Clientes.NRed, Clientes.Cidade, Clientes.Estado UF, dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Clientes.Latitude, Clientes.Longitude) Distancia "
                    + " from Clientes "
                    + " left join Filiais on Filiais.CodFil = Clientes.CodFil "
                    + " left join Contatos on Contatos.CodCli = Clientes.Codigo "
                    + "                   and Contatos.CodFil = Clientes.CodFil "
                    + " where Clientes.latitude <> ''"
                    + "   and dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Clientes.Latitude, Clientes.Longitude) <= " + distMax
                    + " union "
                    + " Select Filiais.CodFil, Filiais.Descricao, Contatos.Codigo CodCont, Contatos.CodCli, Contatos.Fantasia, Contatos.Cidade, "
                    + " Contatos.UF, dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Contatos.Latitude, Contatos.Longitude) Distancia "
                    + " from Contatos "
                    + " left join Filiais on Filiais.CodFil = Contatos.CodFil "
                    + " where Contatos.latitude <> '' "
                    + "   and dbo.fun_CalcDistancia(" + latitude + ", " + longitude + ", Contatos.Latitude, Contatos.Longitude) <= " + distMax
                    + " order by Distancia ");
        }
    }

    /**
     * Lista os clientes liberados para um grpo de usuários
     *
     * @param codGrupo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> listaClientesGrupo(String codGrupo, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select clientes.ende + ' ' + clientes.cidade + '/' +clientes.Estado ende,   "
                    + " clientes.nred, clientes.codigo, clientes.codfil, count(*) qtd  "
                    + " from saspw  "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa  "
                    + " left join pessoacliaut on pessoacliaut.codigo = saspw.codpessoa  "
                    + " left join clientes on clientes.codigo = pessoacliaut.codcli "
                    + "                  and clientes.codfil = pessoacliaut.codfil "
                    + " where saspw.nomecompleto is not null "
                    + " AND  saspw.codGrupo = ?  AND saspw.situacao = 'A'"
                    + " and pessoacliaut.flag_excl <> '*' "
                    + " group by clientes.ende + ' ' + clientes.cidade + '/' +clientes.Estado, clientes.codigo, clientes.nred, clientes.codfil "
                    + " order by nred ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codGrupo);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setEnde(consulta.getString("ende"));
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setNRed(consulta.getString("nred"));
                cliente.setLote(consulta.getString("qtd"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select clientes.ende + ' ' + clientes.cidade + '/' +clientes.Estado ende,   "
                    + " clientes.nred, clientes.codigo  "
                    + " from saspw  "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa  "
                    + " left join pessoacliaut on pessoacliaut.codigo = saspw.codpessoa  "
                    + " left join clientes on clientes.codigo = pessoacliaut.codcli "
                    + "                  and clientes.codfil = saspw.codfil "
                    + " where saspw.nomecompleto is not null "
                    + " AND  saspw.codGrupo = " + codGrupo
                    + " and pessoacliaut.flag_excl <> '*' "
                    + " group by clientes.ende + ' ' + clientes.cidade + '/' +clientes.Estado, clientes.codigo, clientes.nred "
                    + " order by nred ");
        }
    }

    /**
     * Lista a localização de todos os clientes por filial;
     *
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> localizacaoClientes(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select codigo, latitude, longitude from clientes "
                    + " where codfil = ? and situacao = 'A' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select codigo, latitude, longitude from clientes "
                    + " where codfil = " + codfil + " situacao = 'A'");
        }
    }

    public List<ClientesImg> listarImagensCliente(Clientes cliente, Persistencia persistencia) throws Exception {
        List<ClientesImg> Retorno = new ArrayList<>();
        String sql = "";

        try {
            sql = "SELECT \n"
                    + "*\n"
                    + "FROM ClientesImg\n"
                    + "WHERE Codigo = ?\n"
                    + "AND   CodFil = ?\n"
                    + "ORDER BY FotoPdr DESC, Sequencia ASC";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cliente.getCodigo());
            consulta.setBigDecimal(cliente.getCodFil());

            consulta.select();

            ClientesImg clientesImg;

            while (consulta.Proximo()) {
                clientesImg = new ClientesImg();

                clientesImg.setCodFil(consulta.getString("CodFil"));
                clientesImg.setCodigo(consulta.getString("Codigo"));
                clientesImg.setDt_Alter(consulta.getString("Dt_Alter"));
                clientesImg.setFotoPdr(consulta.getString("FotoPdr"));
                clientesImg.setHr_Alter(consulta.getString("Hr_Alter"));
                clientesImg.setImagem(consulta.getString("Imagem"));
                clientesImg.setOperador(consulta.getString("Operador"));
                clientesImg.setSequencia(consulta.getString("Sequencia"));

                Retorno.add(clientesImg);
            }

            consulta.close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.listarImagensCliente - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Lista a localização de todos os clientes por filial;
     *
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> localizacaoClientes(Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select codigo, latitude, longitude from clientes "
                    + " where situacao = 'A' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select codigo, latitude, longitude from clientes "
                    + " where situacao = 'A'");
        }
    }

    /**
     * Lista a localização de todos os clientes por filial;
     *
     * @param codfil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> localizacaoClientesW(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' "
                    + " and latitude is not null and latitude <> '' and longitude is not null and longitude <> '' ";
            if (codfil != null && !codfil.equals("")) {
                sql += " and codfil = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            if (codfil != null && !codfil.equals("")) {
                consulta.setString(codfil);
            }
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setNRed(consulta.getString("nred"));
                cliente.setEnde(consulta.getString("ende"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' ");
        }
    }

    /**
     * Lista a localização de todos os clientes por filial;
     *
     * @param codfil
     * @param nred
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> localizacaoClientesNred(String codfil, String nred, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' and nred like ? "
                    + " and latitude is not null and latitude <> '' and longitude is not null and longitude <> '' ";
            if (codfil != null && !codfil.equals("")) {
                sql += " and codfil = ? ";
            }
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + nred + "%");
            if (codfil != null && !codfil.equals("")) {
                consulta.setString(codfil);
            }
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setNRed(consulta.getString("nred"));
                cliente.setEnde(consulta.getString("ende"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' ");
        }
    }

    /**
     * Lista a localização de todos os clientes por filial;
     *
     * @param codfil
     * @param nred
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> localizacaoClientesNred(String nred, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String sql = " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' and nred like ? "
                    + " and latitude is not null and latitude <> '' and longitude is not null and longitude <> '' ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + nred + "%");
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setNRed(consulta.getString("nred"));
                cliente.setEnde(consulta.getString("ende"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.localizacaoClientes - " + e.getMessage() + "\r\n"
                    + " select codigo, nred, ende, codfil, latitude, longitude from clientes "
                    + " where situacao = 'A' ");
        }
    }

    /**
     * Busca o cliente de um posto de serviço.
     *
     * @param secao
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Clientes clientePosto(String secao, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select pstserv.local, Clientes.* from pstserv\n"
                    + "left join clientes on pstserv.codcli = clientes.codigo\n"
                    + "                   and pstserv.codfil = clientes.codfil\n"
                    + "where pstserv.secao = ?\n"
                    + "and pstserv.codfil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(secao);
            consulta.setString(codFil);
            consulta.select();
            Clientes retorno = new Clientes();
            while (consulta.Proximo()) {
                retorno.setCodigo(consulta.getString("codigo"));
                retorno.setNRed(consulta.getString("nred"));
                retorno.setEnde(consulta.getString("ende"));
                retorno.setCEP(consulta.getString("cep"));
                retorno.setEstado(consulta.getString("estado"));
                retorno.setCidade(consulta.getString("cidade"));
                retorno.setNome(consulta.getString("nome"));
                retorno.setBairro(consulta.getString("bairro"));
                retorno.setEmail(consulta.getString("email"));
                // Salvando PstServ.Local em contato
                retorno.setContato(consulta.getString("local"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.clientePosto - " + e.getMessage() + "\r\n"
                    + "select Clientes.* from pstserv\n"
                    + "left join clientes on pstserv.codcli = clientes.codigo\n"
                    + "                   and pstserv.codfil = clientes.codfil\n"
                    + "where pstserv.secao = " + secao
                    + "and pstserv.codfil = " + codFil);
        }
    }

    /**
     * Busca um contato e retorna formatado como cliente
     *
     * @param codContato
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Clientes clienteContato(String codContato, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT \n"
                    + "\n"
                    + "CASE WHEN Contatos.Email IS NOT NULL THEN Contatos.Email\n"
                    + "ELSE Clientes.Email END Email,\n"
                    + "CASE WHEN Contatos.Endereco IS NOT NULL THEN Contatos.Endereco\n"
                    + "ELSE Clientes.Ende END Ende,\n"
                    + "CASE WHEN Contatos.Bairro IS NOT NULL THEN Contatos.Bairro\n"
                    + "ELSE Clientes.Bairro END Bairro,\n"
                    + "CASE WHEN Contatos.Cidade IS NOT NULL THEN Contatos.Cidade\n"
                    + "ELSE Clientes.Cidade END Cidade,\n"
                    + "CASE WHEN Contatos.UF IS NOT NULL THEN Contatos.UF\n"
                    + "ELSE Clientes.Estado END Estado,\n"
                    + "CASE WHEN Contatos.Nome IS NOT NULL THEN Contatos.Nome\n"
                    + "ELSE Clientes.NRed END Contato,\n"
                    + "CASE WHEN Contatos.CEP IS NOT NULL THEN Contatos.CEP\n"
                    + "ELSE Clientes.CEP END CEP\n"
                    + "\n"
                    + "FROM Contatos\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Contatos.CodCli\n"
                    + "                  AND Clientes.CodFil = Contatos.CodFil\n"
                    + "WHERE Contatos.Codigo = ? AND Contatos.CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codContato);
            consulta.setString(codFil);
            consulta.select();
            Clientes retorno = new Clientes();
            while (consulta.Proximo()) {
//                retorno.setCodigo(consulta.getString("codigo"));
//                retorno.setNRed(consulta.getString("nred"));
                retorno.setEnde(consulta.getString("ende"));
                retorno.setCEP(consulta.getString("cep"));
                retorno.setEstado(consulta.getString("estado"));
                retorno.setCidade(consulta.getString("cidade"));
//                retorno.setNome(consulta.getString("nome"));
                retorno.setBairro(consulta.getString("bairro"));
                retorno.setEmail(consulta.getString("email"));
                retorno.setContato(consulta.getString("Contato"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.clientePosto - " + e.getMessage() + "\r\n"
                    + "SELECT \n"
                    + "\n"
                    + "CASE WHEN Contatos.Email IS NOT NULL THEN Contatos.Email\n"
                    + "ELSE Clientes.Email END Email,\n"
                    + "CASE WHEN Contatos.Endereco IS NOT NULL THEN Contatos.Endereco\n"
                    + "ELSE Clientes.Ende END Endereco,\n"
                    + "CASE WHEN Contatos.Bairro IS NOT NULL THEN Contatos.Bairro\n"
                    + "ELSE Clientes.Bairro END Bairro,\n"
                    + "CASE WHEN Contatos.Cidade IS NOT NULL THEN Contatos.Cidade\n"
                    + "ELSE Clientes.Cidade END Cidade,\n"
                    + "CASE WHEN Contatos.UF IS NOT NULL THEN Contatos.UF\n"
                    + "ELSE Clientes.Estado END UF,\n"
                    + "CASE WHEN Contatos.Nome IS NOT NULL THEN Contatos.Nome\n"
                    + "ELSE Clientes.NRed END Contato,\n"
                    + "CASE WHEN Contatos.CEP IS NOT NULL THEN Contatos.CEP\n"
                    + "ELSE Clientes.CEP END CEP\n"
                    + "\n"
                    + "FROM Contatos\n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Contatos.CodCli\n"
                    + "                  AND Clientes.CodFil = Contatos.CodFil\n"
                    + "WHERE Contatos.Codigo = " + codContato + " AND Contatos.CodFil = " + codFil);
        }
    }

    /**
     * Busca o cliente de um posto de serviço.
     *
     * @param codFil
     * @param banco
     * @param agencia
     * @param subAgencia
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Clientes clienteImportacaoPedido(String codFil, String banco, String agencia,
            String subAgencia, Persistencia persistencia) throws Exception {
        try {
            String sql = " select Codigo, NRed, Regiao, Agencia, SubAgencia "
                    + " from Clientes "
                    + " where Banco = ? "
                    + "     and Agencia = ? "
                    + "     and SubAgencia = ? "
                    + "     and Situacao = 'A' "
                    + "     and CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(banco);
            consulta.setString(agencia);
            consulta.setString(subAgencia);
            consulta.setString(codFil);
            consulta.select();
            Clientes retorno = new Clientes();
            while (consulta.Proximo()) {
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.clienteImportacaoPedido - " + e.getMessage() + "\r\n"
                    + "select Codigo, NRed, Regiao, Agencia, SubAgencia "
                    + " from Clientes "
                    + " where Banco = " + banco
                    + "     and Agencia = " + agencia
                    + "     and SubAgencia = " + subAgencia
                    + "     and Situacao = 'A' "
                    + "     and CodFil = " + codFil);
        }
    }

    public ClientesGuia listaClientesEntrega(String sequencia, String parada, Persistencia persistencia) throws Exception {
        ClientesGuia cliente = new ClientesGuia();
        try {
            String sql = " Select Rotas.Rota, Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada,"
                    + "         clientes.NRed nredorigem, Clientes.Ende EnderecoOrigem,  "
                    + "         Clientes.Bairro BairroOrigem, Clientes.Cidade CidadeOrigem, Clientes.Estado EstadoOrigem,  "
                    + "         dst.NRed NredDestino, dst.Ende EnderecoDestino, dst.Bairro BairroDestino, dst.Estado EstadoDestino, "
                    + "         dst.Cidade CidadeDestino, dst.email EmailDestino, dst.codigo CodigoDestino, Clientes.Email EmailOrigem  "
                    + "         from Rt_Perc    "
                    + " Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia    "
                    + " Left join cxforte on cxforte.CodFil = Rotas.Codfil    "
                    + " left join clientes on clientes.codigo  = cxforte.codcli "
                    + "                    and clientes.codfil = cxforte.codfil "
                    + " Left join Clientes dst on dst.Codigo  = Rt_Perc.CodCli1 "
                    + "                        and dst.CodFil = Rotas.Codfil    "
                    + " WHERE  Rt_Perc.Sequencia = ? AND Rt_Perc.Parada = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                cliente = new ClientesGuia();
                cliente.setEr(consulta.getString("er"));
                cliente.setHrSaida(consulta.getString("HrSaida"));
                cliente.setHrChegada(consulta.getString("HrCheg"));
                cliente.setParada(consulta.getInt("rota")); // SETANDO O NÚMERO DE ROTA NA PARADA PARA NÃO PRECISAR CRIAR UM CAMPO NOVO
                cliente.setNredOrigem(consulta.getString("NredOrigem"));
                cliente.setEnderecoOrigem(consulta.getString("EnderecoOrigem"));
                cliente.setBairroOrigem(consulta.getString("BairroOrigem"));
                cliente.setCidadeOrigem(consulta.getString("CidadeOrigem"));
                cliente.setEstadoOrigem(consulta.getString("EstadoOrigem"));
                cliente.setNredDestino(consulta.getString("NredDestino"));
                cliente.setEmailDestino(consulta.getString("EmailDestino"));
                cliente.setEmailOrigem(consulta.getString("EmailOrigem"));
                cliente.setEnderecoDestino(consulta.getString("EnderecoDestino"));
                cliente.setBairroDestino(consulta.getString("BairroDestino"));
                cliente.setEstadoDestino(consulta.getString("EstadoDestino"));
                cliente.setCidadeDestino(consulta.getString("CidadeDestino"));
                cliente.setCodigoDestino(consulta.getString("CodigoDestino"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaClientesEntrega - " + e.getMessage() + "\r\n"
                    + "Select Rotas.Rota, Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada,"
                    + "         clientes.NRed nredorigem, Clientes.Ende EnderecoOrigem,  "
                    + "         Clientes.Bairro BairroOrigem, Clientes.Cidade CidadeOrigem, Clientes.Estado EstadoOrigem,  "
                    + "         dst.NRed NredDestino, dst.Ende EnderecoDestino, dst.Bairro BairroDestino, dst.Estado EstadoDestino, "
                    + "         dst.Cidade CidadeDestino, dst.email EmailDestino,  Clientes.Email EmailOrigem  "
                    + "         from Rt_Perc    "
                    + " Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia    "
                    + " Left join cxforte on cxforte.CodFil = Rotas.Codfil    "
                    + " left join clientes on clientes.codigo  = cxforte.codcli "
                    + "                    and clientes.codfil = cxforte.codfil "
                    + " Left join Clientes dst on dst.Codigo  = Rt_Perc.CodCli1 "
                    + "                        and dst.CodFil = Rotas.Codfil    "
                    + " WHERE  Rt_Perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return cliente;
    }

    /**
     * Carrega informacoes da lista do cliente
     *
     * @param sequencia sequencia da rota
     * @param parada numero da parada
     * @param persistencia conexao do banco de dados
     * @return lista da guias
     * @throws Exception
     */
    public ClientesGuia listaClientes(String sequencia, String parada, Persistencia persistencia) throws Exception {
        ClientesGuia cliente = new ClientesGuia();
        try {
            String sql = "Select Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada, Rt_Perc.NRed+' - '+"
                    + " Convert(varchar,Clientes.Agencia) nredorigem, Clientes.Ende EnderecoOrigem, "
                    + " Clientes.Bairro BairroOrigem, Clientes.Cidade CidadeOrigem, Clientes.Estado EstadoOrigem, "
                    + " dst.NRed NredDestino, dst.Ende EnderecoDestino, dst.Bairro BairroDestino, dst.Estado EstadoDestino,"
                    + " dst.Cidade CidadeDestino, dst.email EmailDestino,  Clientes.Email EmailOrigem "
                    + " from Rt_Perc   "
                    + "                       Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia   "
                    + "                       Left join Clientes on Clientes.Codigo = Rt_Perc.CodCli1   "
                    + "                                                     and Clientes.CodFil = Rotas.Codfil   "
                    + "                         Left join Clientes dst on dst.Codigo = Rt_Perc.CodCli2   "
                    + "                                                     and dst.CodFil = Rotas.Codfil   "
                    + "                       WHERE  Rt_Perc.Sequencia = ? AND Rt_Perc.Parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                cliente = new ClientesGuia();
                cliente.setEr(consulta.getString("er"));
                cliente.setHrSaida(consulta.getString("HrSaida"));
                cliente.setHrChegada(consulta.getString("HrCheg"));
                cliente.setParada(consulta.getInt("parada"));
                cliente.setNredOrigem(consulta.getString("NredOrigem"));
                cliente.setEnderecoOrigem(consulta.getString("EnderecoOrigem"));
                cliente.setBairroOrigem(consulta.getString("BairroOrigem"));
                cliente.setCidadeOrigem(consulta.getString("CidadeOrigem"));
                cliente.setEstadoOrigem(consulta.getString("EstadoOrigem"));
                cliente.setNredDestino(consulta.getString("NredDestino"));
                cliente.setEmailDestino(consulta.getString("EmailDestino"));
                cliente.setEmailOrigem(consulta.getString("EmailOrigem"));
                cliente.setEnderecoDestino(consulta.getString("EnderecoDestino"));
                cliente.setBairroDestino(consulta.getString("BairroDestino"));
                cliente.setEstadoDestino(consulta.getString("EstadoDestino"));
                cliente.setCidadeDestino(consulta.getString("CidadeDestino"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaClientes - " + e.getMessage() + "\r\n"
                    + "Select Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada, Rt_Perc.NRed+' - '+"
                    + " Convert(varchar,Clientes.Agencia) nredorigem, Clientes.Ende EnderecoOrigem, "
                    + " Clientes.Bairro BairroOrigem, Clientes.Cidade CidadeOrigem, Clientes.Estado EstadoOrigem, "
                    + " dst.NRed NredDestino, dst.Ende EnderecoDestino, dst.Bairro BairroDestino, dst.Estado EstadoDestino,"
                    + " dst.Cidade CidadeDestino, dst.email EmailDestino,  Clientes.Email EmailOrigem "
                    + " from Rt_Perc   "
                    + "                       Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia   "
                    + "                       Left join Clientes on Clientes.Codigo = Rt_Perc.CodCli1   "
                    + "                                                     and Clientes.CodFil = Rotas.Codfil   "
                    + "                         Left join Clientes dst on dst.Codigo = Rt_Perc.CodCli2   "
                    + "                                                     and dst.CodFil = Rotas.Codfil   "
                    + "                       WHERE  Rt_Perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return cliente;
    }

    /**
     * Carrega informacoes da lista do cliente
     *
     * @param sequencia sequencia da rota
     * @param parada numero da parada
     * @param persistencia conexao do banco de dados
     * @return lista da guias
     * @throws Exception
     */
    public ClientesGuia listaClientesPedido(String sequencia, String parada, Persistencia persistencia) throws Exception {
        ClientesGuia cliente = new ClientesGuia();
        try {
            String sql = "Select Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada, "
                    + " clientes.NRed+' - '+Convert(varchar,Clientes.Agencia) nredorigem, Clientes.Ende EnderecoOrigem, Clientes.Bairro BairroOrigem, "
                    + " Clientes.Cidade CidadeOrigem, Clientes.Email EmailOrigem, Clientes.Estado EstadoOrigem, dst.NRed NredDestino, dst.Ende EnderecoDestino, "
                    + " dst.Bairro BairroDestino, dst.Estado EstadoDestino, dst.Cidade CidadeDestino, dst.email EmailDestino"
                    + " from Rt_Perc "
                    + "     Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "     left join pedido on pedido.numero = rt_perc.pedido "
                    + "     left join os_vig on os_vig.os = pedido.os "
                    + "                 and os_vig.codfil = pedido.codfil "
                    + "     Left join Clientes on Clientes.Codigo = os_vig.cliente "
                    + "                       and Clientes.CodFil = os_vig.Codfil "
                    + "     Left join Clientes dst on dst.Codigo = os_vig.clidst "
                    + "                           and dst.CodFil = os_vig.Codfil "
                    + " WHERE  Rt_Perc.Sequencia = ? AND Rt_Perc.Parada = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            while (consulta.Proximo()) {
                cliente = new ClientesGuia();
                cliente.setEr(consulta.getString("er"));
                cliente.setHrSaida(consulta.getString("HrSaida"));
                cliente.setHrChegada(consulta.getString("HrCheg"));
                cliente.setParada(consulta.getInt("parada"));
                cliente.setNredOrigem(consulta.getString("NredOrigem"));
                cliente.setEnderecoOrigem(consulta.getString("EnderecoOrigem"));
                cliente.setBairroOrigem(consulta.getString("BairroOrigem"));
                cliente.setCidadeOrigem(consulta.getString("CidadeOrigem"));
                cliente.setEstadoOrigem(consulta.getString("EstadoOrigem"));
                cliente.setNredDestino(consulta.getString("NredDestino"));
                cliente.setEmailDestino(consulta.getString("EmailDestino"));
                cliente.setEmailOrigem(consulta.getString("EmailOrigem"));
                cliente.setEnderecoDestino(consulta.getString("EnderecoDestino"));
                cliente.setBairroDestino(consulta.getString("BairroDestino"));
                cliente.setEstadoDestino(consulta.getString("EstadoDestino"));
                cliente.setCidadeDestino(consulta.getString("CidadeDestino"));
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaClientesPedido - " + e.getMessage() + "\r\n"
                    + "Select Rt_Perc.HrCheg, Rt_Perc.Er er, Rt_Perc.HrSaida, Rt_Perc.Parada, "
                    + " clientes.NRed+' - '+Convert(varchar,Clientes.Agencia) nredorigem, Clientes.Ende EnderecoOrigem, Clientes.Bairro BairroOrigem, "
                    + " Clientes.Cidade CidadeOrigem, Clientes.Email EmailOrigem, Clientes.Estado EstadoOrigem, dst.NRed NredDestino, dst.Ende EnderecoDestino, "
                    + " dst.Bairro BairroDestino, dst.Estado EstadoDestino, dst.Cidade CidadeDestino, dst.email EmailDestino"
                    + " from Rt_Perc "
                    + "     Left join Rotas on Rotas.Sequencia = Rt_Perc.Sequencia "
                    + "     left join pedido on pedido.numero = rt_perc.pedido "
                    + "     left join os_vig on os_vig.os = pedido.os "
                    + "                 and os_vig.codfil = pedido.codfil "
                    + "     Left join Clientes on Clientes.Codigo = os_vig.cliente "
                    + "                       and Clientes.CodFil = os_vig.Codfil "
                    + "     Left join Clientes dst on dst.Codigo = os_vig.clidst "
                    + "                           and dst.CodFil = os_vig.Codfil "
                    + " WHERE  Rt_Perc.Sequencia = " + sequencia + " AND Rt_Perc.Parada = " + parada);
        }
        return cliente;
    }

    public void updateLglt(String latitude, String longitude, String sequencia, String parada, Persistencia persistencia) throws Exception {

        String sql = "update Clientes set latitude=?, longitude=? where Codigo = (select codcli1 from rt_perc "
                + " where sequencia=? and parada=? )";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(latitude);
            consulta.setString(longitude);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.updateLglt - " + e.getMessage() + "\r\n"
                    + "update Clientes set latitude=" + latitude + ", longitude=" + longitude + " where Codigo = (select codcli1 from rt_perc "
                    + " where sequencia=" + sequencia + " and parada=" + parada + " )");
        }
    }

    /**
     * Busca o cofre pelo interfExt
     *
     * @param machineID
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Clientes buscarCofre(String machineID, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT \n"
                    + "     Codigo, CodCofre\n"
                    + " FROM\n"
                    + "     Clientes\n"
                    + " WHERE\n"
                    + "     InterfExt = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(machineID);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarCofre - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     Codigo, CodCofre\n"
                    + " FROM\n"
                    + "     Clientes\n"
                    + " WHERE\n"
                    + "     InterfExt = " + machineID);
        }
    }

    public Clientes buscarCofreCaos(String machineID, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT \n"
                    + "     Codigo, CodCofre\n"
                    + " FROM\n"
                    + "     Clientes\n"
                    + " WHERE\n"
                    + "     CodCofre = ? and idusuario not like '%999999%'  ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(machineID);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarCofre - " + e.getMessage() + "\r\n"
                    + " SELECT \n"
                    + "     Codigo, CodCofre\n"
                    + " FROM\n"
                    + "     Clientes\n"
                    + " WHERE\n"
                    + "     InterfExt = " + machineID);
        }
    }

    public List<Clientes> getAllCodigoCofres(Persistencia persistencia) throws Exception {
        List<Clientes> listp = new ArrayList();
        String sql = "select nome, codcofre from clientes where codcofre > 0";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            while (consult.Proximo()) {
                Clientes pl = new Clientes();
                pl.setNome(consult.getString("nome"));
                pl.setCodCofre(consult.getString("codcofre"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getAllCodigoCofres - " + e.getMessage() + "\r\n"
                    + "select nome, codcofre from clientes where codcofre > 0");
        }
    }

    public List<Clientes> getAllCofresGunnebo(Persistencia persistencia) throws Exception {
        List<Clientes> listp = new ArrayList();
        String sql = "Select CodCofre, CodPtoCli, CodIntCli \n"
                + " From Clientes \n"
                + "where MarcaATM like '%GUNNEBO%'\n"
                + " and CodPtoCli <> '' \n"
                + "Group By CodCofre, CodPtoCli, CodIntCli ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.select();
            Clientes pl;
            while (consult.Proximo()) {
                pl = new Clientes();
                pl.setCodCofre(consult.getString("CodCofre"));
                pl.setCodPtoCli(consult.getString("CodPtoCli"));
                pl.setCodIntCli(consult.getString("CodIntCli"));
                listp.add(pl);
            }
            consult.Close();
            return listp;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getAllCofresGunnebo - " + e.getMessage() + "\r\n"
                    + "Select CodCofre, CodPtoCli\n"
                    + " From Clientes \n"
                    + "where MarcaATM like '%GUNNEBO%'\n"
                    + " and CodPtoCli <> '' \n"
                    + "Group By CodCofre, CodPtoCli");
        }
    }

    public List<String> obterCodigoCofres(String marcaATM, Persistencia persistencia) throws Exception {
        List<String> lista = new ArrayList();
        try {
            String sql = "select codcofre from clientes where marcaATM like ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + marcaATM + "%");
            consult.select();
            while (consult.Proximo()) {
                lista.add(consult.getString("codcofre"));
            }
            consult.Close();
            return lista;
        } catch (Exception e) {
            throw new Exception("ClientesDao.obterCodigoCofres - " + e.getMessage() + "\r\n"
                    + "select codcofre from clientes where marcaATM like " + marcaATM);
        }
    }

    /**
     * Busca clientes com ativos por filial
     *
     * @param CodFil - Código da Filial
     * @param persistencia - conexão ao banco de dados
     * @return - codigo, nred, ende, bairro, cidade, estado, latitude, longitude
     * @throws Exception
     */
    public List<Clientes> getClientesMobile(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> lcli = new ArrayList();
            Clientes clientes;
            String sql = "select top 40 *"
                    + " from clientes"
                    + " where codfil = ? "
                    + " and situacao = 'A'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                clientes = new Clientes();
                clientes.setCEP(consult.getString("cep"));
                clientes.setFone1(consult.getString("fone1"));
                clientes.setFone2(consult.getString("fone2"));
                clientes.setCodFil(consult.getString("codfil"));
                clientes.setBanco(consult.getString("Banco"));
                clientes.setTpCli(consult.getString("TpCli"));
                clientes.setCodCli(consult.getString("CodCli"));
                clientes.setCodigo(consult.getString("codigo"));
                clientes.setNRed(consult.getString("nred"));
                clientes.setRegiao(consult.getString("regiao"));
                clientes.setEnde(consult.getString("ende"));
                clientes.setBairro(consult.getString("bairro"));
                clientes.setCidade(consult.getString("cidade"));
                clientes.setEstado(consult.getString("estado"));
                clientes.setLatitude(consult.getString("latitude"));
                clientes.setLongitude(consult.getString("longitude"));
                clientes.setCodFil(consult.getString("codfil"));
                lcli.add(clientes);
            }
            consult.Close();
            return lcli;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getClientesMobile - " + e.getMessage() + "\r\n"
                    + "select top 40 *"
                    + " from clientes"
                    + " where codfil = " + CodFil
                    + " and situacao = 'A'");
        }
    }

    public List<Clientes> buscarClientes(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT Clientes.*, Regiao.Descricao RegiaoDescricao \n"
                    + " FROM Clientes\n"
                    + " LEFT JOIN Regiao ON Regiao.Regiao = Clientes.Regiao\n"
                    + "                 AND Regiao.CodFil = Clientes.CodFil"
                    + " WHERE (NRed like ? OR Nome like ?) AND Clientes.CodFil = ? AND clientes.situacao = 'A' ";
            List<Clientes> retorno = new ArrayList<>();
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("%" + query + "%");
            consulta.setString("%" + query + "%");
            consulta.setString(codFil);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {

                cliente = new Clientes();
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setNome(consulta.getString("nome"));
                cliente.setNRed(consulta.getString("nred"));
                cliente.setEnde(consulta.getString("ende"));
                cliente.setBairro(consulta.getString("bairro"));
                cliente.setCidade(consulta.getString("cidade"));
                cliente.setEstado(consulta.getString("estado"));
                cliente.setCEP(consulta.getString("cep"));
                cliente.setFone1(consulta.getString("fone1"));
                cliente.setFone2(consulta.getString("fone2"));
                cliente.setEmail(consulta.getString("email"));
                cliente.setCGC(consulta.getString("cgc"));
                cliente.setIE(consulta.getString("ie"));
                cliente.setInsc_Munic(consulta.getString("insc_munic"));
                cliente.setCPF(consulta.getString("cpf"));
                cliente.setRG(consulta.getString("rg"));
                cliente.setLatitude(consulta.getString("latitude"));
                cliente.setLongitude(consulta.getString("longitude"));
                cliente.setOper_Alt(consulta.getString("oper_alt"));
                cliente.setDt_Alter(consulta.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consulta.getString("hr_alter"));
                cliente.setInterfExt(consulta.getString("RegiaoDescricao"));
                cliente.setRegiao(consulta.getString("Regiao"));

                cliente.setCodExt(consulta.getString("codigo") + " " + consulta.getString("nred"));
                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClientes - " + e.getMessage() + "\r\n"
                    + " SELECT * FROM Clientes WHERE (NRed like %" + query + "% OR Nome like %" + query + "%) AND CodFil = " + codFil);
        }
    }

    /**
     * Busca clientes com ativos por filial
     *
     * @param CodFil - Código da Filial
     * @param buscar - Texto que deseja
     * @param persistencia - conexão ao banco de dados
     * @return - codigo, nred, ende, bairro, cidade, estado, latitude, longitude
     * @throws Exception
     */
    public List<Clientes> getBuscarClientesMobile(BigDecimal CodFil, String buscar, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> lcli = new ArrayList();
            Clientes clientes;
            String sql = "select top 40 *"
                    + " from clientes"
                    + " where codfil = ? "
                    + " and situacao = 'A' AND nred LIKE ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.setString("%" + buscar + "%");
            consult.select();
            while (consult.Proximo()) {
                clientes = new Clientes();
                clientes.setCEP(consult.getString("cep"));
                clientes.setFone1(consult.getString("fone1"));
                clientes.setFone2(consult.getString("fone2"));
                clientes.setCodFil(consult.getString("codfil"));
                clientes.setBanco(consult.getString("Banco"));
                clientes.setTpCli(consult.getString("TpCli"));
                clientes.setCodCli(consult.getString("CodCli"));
                clientes.setCodigo(consult.getString("codigo"));
                clientes.setNRed(consult.getString("nred"));
                clientes.setRegiao(consult.getString("regiao"));
                clientes.setEnde(consult.getString("ende"));
                clientes.setBairro(consult.getString("bairro"));
                clientes.setCidade(consult.getString("cidade"));
                clientes.setEstado(consult.getString("estado"));
                clientes.setLatitude(consult.getString("latitude"));
                clientes.setLongitude(consult.getString("longitude"));
                clientes.setCodFil(consult.getString("codfil"));
                lcli.add(clientes);
            }
            consult.Close();
            return lcli;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getBuscarClientesMobile - " + e.getMessage() + "\r\n"
                    + "select top 40 *"
                    + " from clientes"
                    + " where codfil = " + CodFil
                    + " and situacao = 'A' AND nred LIKE " + buscar);
        }
    }

    /**
     * Busca clientes com ativos por filial
     *
     * @param CodFil - Código da Filial
     * @param Codigo - código do cliente
     * @param persistencia - conexão ao banco de dados
     * @return - codigo, nred, ende, bairro, cidade, estado, latitude, longitude
     * @throws Exception
     */
    public Clientes getClientesMobile(String CodFil, String Codigo, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = "select clientes.codigo, clientes.codfil, clientes.Fone1, clientes.Fone2, \n"
                    + " clientes.nome, clientes.nred, clientes.regiao, clientes.ende, clientes.bairro, clientes.cidade, \n"
                    + " clientes.estado, clientes.cep, clientes.email, clientes.EmailCob, clientes.latitude, clientes.longitude, \n"
                    + " clientes.CodCofre, Clientes.Obs,  Regiao.Descricao RegiaoDescricao \n"
                    + " from clientes\n"
                    + " LEFT JOIN Regiao ON Regiao.Regiao = Clientes.Regiao\n"
                    + "                 AND Regiao.CodFil = Clientes.CodFil"
                    + " where clientes.codfil = ? "
                    + " and clientes.codigo = ?"
                    + " and clientes.situacao = 'A'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString(Codigo);
            consult.select();
            if (consult.Proximo()) {
                retorno = new Clientes();
                retorno.setCodigo(consult.getString("codigo"));
                retorno.setCodFil(consult.getString("codfil"));
                retorno.setNome(consult.getString("nome"));
                retorno.setNRed(consult.getString("nred"));
                retorno.setRegiao(consult.getString("regiao"));
                retorno.setEnde(consult.getString("ende"));
                retorno.setCEP(consult.getString("cep"));
                retorno.setFone1(consult.getString("Fone1"));
                retorno.setFone2(consult.getString("Fone2"));
                retorno.setBairro(consult.getString("bairro"));
                retorno.setCidade(consult.getString("cidade"));
                retorno.setEstado(consult.getString("estado"));
                retorno.setLatitude(consult.getString("latitude"));
                retorno.setLongitude(consult.getString("longitude"));
                retorno.setCodCofre(consult.getString("CodCofre"));
                retorno.setEmail(consult.getString("Email"));
                retorno.setEmailCob(consult.getString("EmailCob"));
                retorno.setObs(consult.getString("Obs"));
                

                retorno.setInterfExt(consult.getString("RegiaoDescricao"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getClientesMobile - " + e.getMessage() + "\r\n"
                    + "select clientes.codigo, clientes.codfil, clientes.Fone1, clientes.Fone2, \n"
                    + " clientes.nome, clientes.nred, clientes.regiao, clientes.ende, clientes.bairro, clientes.cidade, \n"
                    + " clientes.estado, clientes.cep, clientes.email, clientes.EmailCob, clientes.latitude, clientes.longitude, \n"
                    + " clientes.CodCofre, Regiao.Descricao RegiaoDescricao \n"
                    + " from clientes\n"
                    + " LEFT JOIN Regiao ON Regiao.Regiao = Clientes.Regiao\n"
                    + "                 AND Regiao.CodFil = Clientes.CodFil"
                    + " where clientes.codfil = " + CodFil
                    + " and clientes.codigo = " + Codigo
                    + " and clientes.situacao = 'A'");
        }
    }

    /**
     * Busca o cliente do GetLock
     *
     * @param CodFil - Código da Filial
     * @param Codigo - código do cliente
     * @param persistencia - conexão ao banco de dados
     * @return - codigo, nred, ende, bairro, cidade, estado, latitude, longitude
     * @throws Exception
     */
    public ClientesMobileHW getClienteGetLock(String CodFil, String Codigo, Persistencia persistencia) throws Exception {
        try {
            ClientesMobileHW retorno = null;
            String sql = "SELECT OS_VIG.OS, MobileHW.CodFil, MobileHW.*, Clientes.CodFil, Clientes.* \n"
                    + "FROM Clientes \n"
                    + "LEFT JOIN OS_VIG ON OS_VIG.Cliente = Clientes.Codigo \n"
                    + "                 AND OS_VIG.CodFil = Clientes.CodFil \n"
                    + "LEFT JOIN MobileHW ON MobileHW.CodCli = Clientes.Codigo \n"
                    + "                 AND MobileHW.CodFil = Clientes.CodFil \n"
                    + "WHERE Clientes.CodFil = ? AND Clientes.Codigo = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(CodFil);
            consult.setString(Codigo);
            consult.select();
            if (consult.Proximo()) {
                retorno = new ClientesMobileHW();

                retorno.getCliente().setCodigo(consult.getString("codigo"));
                retorno.getCliente().setCodFil(consult.getString("codfil"));
                retorno.getCliente().setNome(consult.getString("nome"));
                retorno.getCliente().setNRed(consult.getString("nred"));
                retorno.getCliente().setRegiao(consult.getString("regiao"));
                retorno.getCliente().setEnde(consult.getString("ende"));
                retorno.getCliente().setCEP(consult.getString("cep"));
                retorno.getCliente().setFone1(consult.getString("Fone1"));
                retorno.getCliente().setBairro(consult.getString("bairro"));
                retorno.getCliente().setCidade(consult.getString("cidade"));
                retorno.getCliente().setEstado(consult.getString("estado"));
                retorno.getCliente().setLatitude(consult.getString("latitude"));
                retorno.getCliente().setLongitude(consult.getString("longitude"));
                retorno.getCliente().setCodCofre(consult.getString("CodCofre"));
                retorno.getCliente().setSituacao(consult.getString("Situacao"));
                retorno.getCliente().setTpCli(consult.getString("TpCli"));
                retorno.getCliente().setObs(consult.getString("OS"));

                retorno.getMobileHW().setIMEI(consult.getString("IMEI"));
                retorno.getMobileHW().setParametro(consult.getString("Parametro"));
                retorno.getMobileHW().setCodEquip(consult.getString("CodEquip"));
                retorno.getMobileHW().setTipoEquip(consult.getString("TipoEquip"));
                retorno.getMobileHW().setMarcaEquip(consult.getString("MarcaEquip"));
                retorno.getMobileHW().setSerialEquip(consult.getString("SerialEquip"));
                retorno.getMobileHW().setCodCli(consult.getString("CodCli"));
                retorno.getMobileHW().setCodFil(consult.getString("CodFil"));
                retorno.getMobileHW().setSaldo(consult.getString("Saldo"));
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getClienteGetLock - " + e.getMessage() + "\r\n"
                    + "SELECT OS_VIG.OS, MobileHW.CodFil, MobileHW.*, Clientes.CodFil, Clientes.* \n"
                    + "FROM Clientes \n"
                    + "LEFT JOIN OS_VIG ON OS_VIG.Cliente = Clientes.Codigo \n"
                    + "                 AND OS_VIG.CodFil = Clientes.CodFil \n"
                    + "LEFT JOIN MobileHW ON MobileHW.CodCli = Clientes.Codigo \n"
                    + "                 AND MobileHW.CodFil = Clientes.CodFil \n"
                    + "WHERE Clientes.CodFil = " + CodFil + " AND Clientes.Codigo = " + Codigo);
        }
    }

    public void inserirImagem(ClientesImg clientesImg, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql += " INSERT INTO ClientesImg (CodFil, Codigo, Sequencia, Imagem, FotoPdr, Operador, Dt_Alter, Hr_Alter) VALUES(";
            sql += " ?,?,(SELECT (ISNULL(MAX(Sequencia), 0) + 1) FROM ClientesImg WHERE Codigo = ? AND CodFil = ?),?,(SELECT ISNULL(CASE WHEN COUNT(*) > 0 THEN 'N' ELSE 'S' END,'S') FROM ClientesImg WHERE Codigo = ? AND CodFil = ?),?,?,?";
            sql += ");";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(clientesImg.getCodFil());
            consulta.setString(clientesImg.getCodigo());
            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());

            consulta.setString(clientesImg.getImagem());
            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());
            consulta.setString(clientesImg.getOperador());
            consulta.setString(clientesImg.getDt_Alter());
            consulta.setString(clientesImg.getHr_Alter());

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("ClientesDao.inserirImagem - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void excluirImagem(ClientesImg clientesImg, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql += " DELETE FROM ClientesImg WHERE Codigo = ? AND CodFil = ? AND Sequencia = ?";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());
            consulta.setString(clientesImg.getSequencia());

            consulta.delete();
            consulta.close();

            sql = "";
            sql = "SELECT COUNT(CASE WHEN FotoPdr = 'S' THEN Sequencia ELSE NULL END) qtde, MIN(Sequencia) seqImagem from clientesimg where codigo = ? AND CodFil = ?";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());
            consulta.select();

            String qtde = "";
            String SeqImagem = "";

            while (consulta.Proximo()) {
                qtde = consulta.getString("qtde");
                SeqImagem = consulta.getString("seqImagem");
            }

            consulta.close();

            if (qtde.equals("0") && null != SeqImagem && !SeqImagem.equals("")) {
                sql = "";
                sql = "UPDATE clientesimg SET fotoPdr = 'S' where codigo = ? AND CodFil = ? AND Sequencia = ?";

                consulta = new Consulta(sql, persistencia);
                consulta.setString(clientesImg.getCodigo());
                consulta.setBigDecimal(clientesImg.getCodFil());
                consulta.setString(SeqImagem.replace(".0", ""));

                consulta.update();
                consulta.close();
            }

        } catch (Exception e) {
            throw new Exception("ClientesDao.excluirImagem - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void tornarImagemPadrao(ClientesImg clientesImg, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            Consulta consulta = new Consulta(sql, persistencia);

            sql = "";
            sql = "UPDATE clientesimg SET fotoPdr = 'N' where codigo = ? AND CodFil = ?; UPDATE clientesimg SET fotoPdr = 'S' where codigo = ? AND CodFil = ? AND Sequencia = ?";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());

            consulta.setString(clientesImg.getCodigo());
            consulta.setBigDecimal(clientesImg.getCodFil());
            consulta.setString(clientesImg.getSequencia());

            consulta.update();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("ClientesDao.tornarImagemPadrao - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Faz inserção de cliente
     *
     * @param clientes - objeto cliente populado
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void inserir(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            String sql = Sqls.montaInsert(clientes);
            sql += ";";

            int Contador = 1;

            if (null != clientes.getFoto()
                    && !clientes.getFoto().trim().equals("")) {

                for (String item : clientes.getFoto().split(";")) {
                    if (!item.split("=")[0].toLowerCase().equals("none")) {
                        sql += " INSERT INTO ClientesImg (CodFil, Codigo, Sequencia, Imagem, FotoPdr, Operador, Dt_Alter, Hr_Alter) VALUES(";
                        sql += " ?,?,?,?,?,?,?,?";
                        sql += ");";
                    }
                }

            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setBigDecimal(clientes.getCodFil());
            consulta.setString(clientes.getBanco());
            consulta.setString(clientes.getTpCli());
            consulta.setString(clientes.getCodCli());
            consulta.setString(clientes.getAgencia());
            consulta.setString(clientes.getSubAgencia());
            consulta.setString(clientes.getLote());
            consulta.setString(clientes.getNRed());
            consulta.setString(clientes.getNome());
            consulta.setString(clientes.getEnde());
            consulta.setString(clientes.getBairro());
            consulta.setString(clientes.getCodCidade());
            consulta.setString(clientes.getCidade());
            consulta.setString(clientes.getEstado());
            consulta.setString(clientes.getCEP());
            consulta.setString(clientes.getFone1());
            consulta.setString(clientes.getFone2());
            consulta.setString(clientes.getFax());
            consulta.setString(clientes.getContato());
            consulta.setString(clientes.getEmail());
            consulta.setString(clientes.getSenhaWEB());
            consulta.setString(clientes.getRamoAtiv());
            consulta.setString(clientes.getRegiao());
            consulta.setString(clientes.getLatitude());
            consulta.setString(clientes.getLongitude());
            consulta.setString(clientes.getPrdApoio());
            consulta.setString(clientes.getRisco());
            consulta.setInt(clientes.getMalotes());
            consulta.setBigDecimal(clientes.getNroChave());
            consulta.setInt(clientes.getGrpChave());
            consulta.setString(clientes.getCGC());
            consulta.setString(clientes.getIE());
            consulta.setString(clientes.getInsc_Munic());
            consulta.setString(clientes.getCEI());
            consulta.setString(clientes.getCPF());
            consulta.setString(clientes.getRG());
            consulta.setString(clientes.getRateioFat());
            consulta.setString(clientes.getRateioTes());
            consulta.setInt(clientes.getDiaFechaFat());
            consulta.setInt(clientes.getDiaVencNF());
            consulta.setString(clientes.getRetencoesFat());
            consulta.setString(clientes.getMarcaATM());
            consulta.setString(clientes.getRetorno());
            consulta.setString(clientes.getCheque());
            consulta.setBigDecimal(clientes.getVr_A());
            consulta.setBigDecimal(clientes.getCed_A());
            consulta.setBigDecimal(clientes.getCed_AP());
            consulta.setBigDecimal(clientes.getVr_B());
            consulta.setBigDecimal(clientes.getCed_B());
            consulta.setBigDecimal(clientes.getCed_BP());
            consulta.setBigDecimal(clientes.getVr_C());
            consulta.setBigDecimal(clientes.getCed_C());
            consulta.setBigDecimal(clientes.getCed_CP());
            consulta.setBigDecimal(clientes.getVr_D());
            consulta.setBigDecimal(clientes.getCed_D());
            consulta.setBigDecimal(clientes.getCed_DP());
            consulta.setBigDecimal(clientes.getVr_E());
            consulta.setBigDecimal(clientes.getCed_E());
            consulta.setBigDecimal(clientes.getCed_EP());
            consulta.setString(clientes.getEndCob());
            consulta.setBigDecimal(clientes.getCodCidCod());
            consulta.setString(clientes.getCidCob());
            consulta.setString(clientes.getUFCob());
            consulta.setString(clientes.getCEPCob());
            consulta.setString(clientes.getEmailCob());
            consulta.setString(clientes.getObs());
            consulta.setString(clientes.getSituacao());
            consulta.setString(clientes.getInterfExt());
            consulta.setString(clientes.getCodExt());
            consulta.setString(clientes.getCodIntCli());
            consulta.setString(clientes.getCodPtoCli());
            consulta.setBigDecimal(clientes.getCercaElet());
            consulta.setString(clientes.getDtSituacao());
            consulta.setDate(LC2Date(clientes.getDt_Cad()));
            consulta.setDate(LC2Date(clientes.getDt_Alter()));
            consulta.setString(clientes.getHr_Alter());
            consulta.setDate(LC2Date(clientes.getDt_UltMov()));
            consulta.setString(clientes.getOper_Inc());
            consulta.setString(clientes.getOper_Alt());
            consulta.setString(clientes.getCodigo());
            consulta.setString(clientes.getCCusto());
            consulta.setString(clientes.getTipoPagto());
            consulta.setBigDecimal(clientes.getLimite());
            consulta.setString(clientes.getLimiteSeguro());
            consulta.setString(clientes.getLimiteColeta());
            consulta.setString(clientes.getProprietario());
            consulta.setString(clientes.getRepresentante());
            consulta.setString(clientes.getAtivEconomica());
            consulta.setString(clientes.getCodCofre());
            consulta.setString(clientes.getGrpRota());
            consulta.setString(clientes.getEnvelope());
            consulta.setString(clientes.getPatrimonio());

            if (null != clientes.getFoto()
                    && !clientes.getFoto().trim().equals("")) {

                for (String item : clientes.getFoto().split(";")) {
                    if (!item.split("=")[0].toLowerCase().equals("none")) {
                        String[] parts = item.split("=");

                        consulta.setBigDecimal(clientes.getCodFil());
                        consulta.setString(clientes.getCodigo());
                        consulta.setInt(Contador);

                        consulta.setString(parts[0]);
                        consulta.setString(parts[1].toUpperCase());
                        consulta.setString(clientes.getOper_Alt());
                        consulta.setDate(LC2Date(clientes.getDt_Alter()));
                        consulta.setString(clientes.getHr_Alter());

                        Contador++;
                    }
                }
            }

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("ClientesDao.inserir - " + e.getMessage());
        }
    }

    /**
     * Faz a atualização do clientes
     *
     * @param clientes - objeto cliente populado
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void atualizar(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            String sql = Sqls.montarUpdate(clientes, "CodFil = ? AND Banco = ? AND TpCli = ? AND CodCli = ?");
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(clientes.getCodFil());
            consulta.setString(clientes.getBanco());
            consulta.setString(clientes.getTpCli());
            consulta.setString(clientes.getCodCli());
            consulta.setString(clientes.getAgencia());
            consulta.setString(clientes.getSubAgencia());
            consulta.setString(clientes.getLote());
            consulta.setString(clientes.getNRed());
            consulta.setString(clientes.getNome());
            consulta.setString(clientes.getEnde());
            consulta.setString(clientes.getBairro());
            consulta.setString(clientes.getCodCidade());
            consulta.setString(clientes.getCidade());
            consulta.setString(clientes.getEstado());
            consulta.setString(clientes.getCEP());
            consulta.setString(clientes.getFone1());
            consulta.setString(clientes.getFone2());
            consulta.setString(clientes.getFax());
            consulta.setString(clientes.getContato());
            consulta.setString(clientes.getEmail());
            consulta.setString(clientes.getSenhaWEB());
            consulta.setString(clientes.getRamoAtiv());
            consulta.setString(clientes.getRegiao());
            consulta.setString(clientes.getLatitude());
            consulta.setString(clientes.getLongitude());
            consulta.setString(clientes.getPrdApoio());
            consulta.setString(clientes.getRisco());
            consulta.setInt(clientes.getMalotes());
            consulta.setBigDecimal(clientes.getNroChave());
            consulta.setInt(clientes.getGrpChave());
            consulta.setString(clientes.getCGC());
            consulta.setString(clientes.getIE());
            consulta.setString(clientes.getInsc_Munic());
            consulta.setString(clientes.getCEI());
            consulta.setString(clientes.getCPF());
            consulta.setString(clientes.getRG());
            consulta.setString(clientes.getRateioFat());
            consulta.setString(clientes.getRateioTes());
            consulta.setInt(clientes.getDiaFechaFat());
            consulta.setInt(clientes.getDiaVencNF());
            consulta.setString(clientes.getRetencoesFat());
            consulta.setString(clientes.getMarcaATM());
            consulta.setString(clientes.getRetorno());
            consulta.setString(clientes.getCheque());
            consulta.setBigDecimal(clientes.getVr_A());
            consulta.setBigDecimal(clientes.getCed_A());
            consulta.setBigDecimal(clientes.getCed_AP());
            consulta.setBigDecimal(clientes.getVr_B());
            consulta.setBigDecimal(clientes.getCed_B());
            consulta.setBigDecimal(clientes.getCed_BP());
            consulta.setBigDecimal(clientes.getVr_C());
            consulta.setBigDecimal(clientes.getCed_C());
            consulta.setBigDecimal(clientes.getCed_CP());
            consulta.setBigDecimal(clientes.getVr_D());
            consulta.setBigDecimal(clientes.getCed_D());
            consulta.setBigDecimal(clientes.getCed_DP());
            consulta.setBigDecimal(clientes.getVr_E());
            consulta.setBigDecimal(clientes.getCed_E());
            consulta.setBigDecimal(clientes.getCed_EP());
            consulta.setString(clientes.getEndCob());
            consulta.setBigDecimal(clientes.getCodCidCod());
            consulta.setString(clientes.getCidCob());
            consulta.setString(clientes.getUFCob());
            consulta.setString(clientes.getCEPCob());
            consulta.setString(clientes.getEmailCob());
            consulta.setString(clientes.getObs());
            consulta.setString(clientes.getSituacao());
            consulta.setString(clientes.getInterfExt());
            consulta.setString(clientes.getCodExt());
            consulta.setString(clientes.getCodIntCli());
            consulta.setString(clientes.getCodPtoCli());
            consulta.setBigDecimal(clientes.getCercaElet());
            consulta.setString(clientes.getDtSituacao());
            consulta.setDate(LC2Date(clientes.getDt_Cad()));
            consulta.setDate(LC2Date(clientes.getDt_Alter()));
            consulta.setString(clientes.getHr_Alter());
            consulta.setDate(LC2Date(clientes.getDt_UltMov()));
            consulta.setString(clientes.getOper_Inc());
            consulta.setString(clientes.getOper_Alt());
            consulta.setString(clientes.getCodigo());
            consulta.setString(clientes.getCCusto());
            consulta.setString(clientes.getTipoPagto());
            consulta.setBigDecimal(clientes.getLimite());
            consulta.setString(clientes.getLimiteSeguro());
            consulta.setString(clientes.getLimiteColeta());
            consulta.setString(clientes.getProprietario());
            consulta.setString(clientes.getRepresentante());
            consulta.setString(clientes.getAtivEconomica());
            consulta.setString(clientes.getCodCofre());
            consulta.setString(clientes.getGrpRota());
            consulta.setString(clientes.getEnvelope());
            consulta.setString(clientes.getPatrimonio());

            consulta.setBigDecimal(clientes.getCodFil());
            consulta.setString(clientes.getBanco());
            consulta.setString(clientes.getTpCli());
            consulta.setString(clientes.getCodCli());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.atualizar - " + e.getMessage());
        }
    }

    public String getCodCliImportacaoSPM(String codFil, String banco, Persistencia persistencia) throws Exception {
        try {
            String sql = "select isnull(MAX(CodCli),0)+1 codcli from clientes where CodFil = ? and banco = ? ";
            String retorno = "0";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(banco);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = consulta.getString("codcli");
            }
            consulta.Close();
            return String.format("%03d", Integer.parseInt(retorno));
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCliImportacaoSPM - " + e.getMessage() + "\r\n"
                    + "select isnull(MAX(CodCli),0)+1 codcli from clientes where CodFil = " + codFil + " and banco = " + banco);
        }
    }

    /**
     * Busca o último codcli da filial do tipo MOB O xxx
     *
     * @param CodFil - Código da filial
     * @param tpCli
     * @param persistencia - conexão ao banco de dados
     * @return - último código cadastrado, somar 1 para usar
     * @throws Exception
     */
    public String getCodCliMobile(BigDecimal CodFil, String tpCli, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select  Case when Min(Banco) is null then Isnull((Select Max(Banco) from Clientes z where z.Codfil = Clientes.Codfil "
                    + "  and z.Banco < 999),600)\n"
                    + "      else Isnull((Select Max(Banco) from Clientes z where z.Codfil = Clientes.Codfil  and z.Banco < 999),600) \n"
                    + "end +1 Banco\n"
                    + " From filiais\n"
                    + "left join clientes on clientes.codfil = Filiais.CodFil\n"
                    + " where Filiais.CodFil = ?\n"
                    + "   and (Banco is null or Banco >= 600)\n"
                    + "Group by Clientes.Codfil;";
            BigDecimal banco = new BigDecimal("0");
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                try {
                    banco = new BigDecimal(consult.getString("Banco"));
                } catch (Exception e) {
                    banco = new BigDecimal("0");
                }
            }
            String sql2 = "select isnull(MAX(CodCli),0)+1 codcli from clientes where CodFil = ? and banco = ? and TpCli = ?";
            BigDecimal retorno = new BigDecimal("0");
            Consulta consult2 = new Consulta(sql2, persistencia);
            consult2.setBigDecimal(CodFil);
            consult2.setBigDecimal(banco);
            consult2.setString(tpCli);
            consult2.select();
            while (consult2.Proximo()) {
                try {
                    retorno = new BigDecimal(consult2.getString("codcli"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            consult2.Close();
            return "" + FuncoesString.PreencheEsquerda(banco.toPlainString().replace(".0", ""), 3, "0")
                    + tpCli.replace(".0", "")
                    + String.format("%03d", Integer.parseInt(retorno.toPlainString()));
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCliMobile - " + e.getMessage());
        }
    }

    /**
     * Busca o último codigo de cliente por banco e tpcli
     *
     * @param CodFil - Código da filial
     * @param Banco - Código de banco desejado
     * @param tpCli - Tipo de cliente
     * @param persistencia - conexão ao banco de dados
     * @return - último código cadastrado, somar 1 para usar
     * @throws Exception
     */
    public String getCodCliBancoTpCli(BigDecimal CodFil, String Banco, String tpCli, Persistencia persistencia) throws Exception {
        try {
//            String sql = "Select  isnull(Max(CodCli),0)+1 codigo\n"
//                    + " From filiais\n"
//                    + "left join clientes on clientes.codfil = Filiais.CodFil\n"
//                    + " where Filiais.CodFil = ?\n"
//                    + "   and Clientes.Banco = ?\n"
//                    + "   and Clientes.TpCli = ?\n"
//                    + "Group by Clientes.Codfil;";
//            BigDecimal banco = new BigDecimal("0");
//            Consulta consult = new Consulta(sql, persistencia);
//            consult.setBigDecimal(CodFil);
//            consult.setBigDecimal(Banco);
//            consult.setBigDecimal(tpCli);
//            consult.select();
//            while (consult.Proximo()) {
//                try {
//                    banco = new BigDecimal(consult.getString("Banco"));
//                } catch (Exception e) {
//                    banco = new BigDecimal("0");
//                }
//            }
            String sql2 = "select isnull(MAX(CodCli),0)+1 codcli from clientes where CodFil = ? and banco = ? and TpCli = ?";
            BigDecimal retorno = new BigDecimal("0");
            Consulta consult2 = new Consulta(sql2, persistencia);
            consult2.setBigDecimal(CodFil);
            consult2.setBigDecimal(Banco);
            consult2.setString(tpCli);
            consult2.select();
            while (consult2.Proximo()) {
                try {
                    retorno = new BigDecimal(consult2.getString("codcli"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            //consult.Close();
            consult2.Close();
            return "" + FuncoesString.PreencheEsquerda(Banco.replace(".0", ""), 3, "0")
                    + tpCli.replace(".0", "")
                    + String.format("%03d", Integer.parseInt(retorno.toPlainString()));
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCliMobile - " + e.getMessage());
        }
    }

    /**
     * Busca o último codcli da filial do tipo MOB O xxx
     *
     * @param CodFil - Código da filial
     * @param persistencia - conexão ao banco de dados
     * @return - último código cadastrado, somar 1 para usar
     * @throws Exception
     */
    public String getCodCliMobile(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select  Case when Min(Banco) is null then Isnull((Select Max(Banco) from Clientes z where z.Codfil = Clientes.Codfil "
                    + " and z.Banco < 999),600)\n"
                    + "      else Isnull((Select Max(Banco) from Clientes z where z.Codfil = Clientes.Codfil  and z.Banco < 999),600) \n"
                    + "end +1 Banco\n"
                    + " From filiais\n"
                    + "left join clientes on clientes.codfil = Filiais.CodFil\n"
                    + " where Filiais.CodFil = ?\n"
                    + "   and (Banco is null or Banco >= 600)\n"
                    + "Group by Clientes.Codfil;";
            BigDecimal banco = new BigDecimal("0");
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                try {
                    banco = new BigDecimal(consult.getString("Banco"));
                } catch (Exception e) {
                    banco = new BigDecimal("0");
                }
            }
            String sql2 = "select isnull(MAX(CodCli),0)+1 codcli from clientes where CodFil = ? and banco = ? ";//and TpCli = '6'";
            BigDecimal retorno = new BigDecimal("0");
            Consulta consult2 = new Consulta(sql2, persistencia);
            consult2.setBigDecimal(CodFil);
            consult2.setBigDecimal(banco);
            consult2.select();
            while (consult2.Proximo()) {
                try {
                    retorno = new BigDecimal(consult2.getString("codcli"));
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            consult2.Close();
            return "" + FuncoesString.PreencheEsquerda(banco.toPlainString().replace(".0", ""), 3, "0")
                    //+ "6"
                    + String.format("%04d", Integer.parseInt(retorno.toPlainString()));
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCliMobile - " + e.getMessage());
        }
    }

    /**
     * retorna o próximo código de cliente, incia-se em 601 0 001
     *
     * @param CodFil - Código da Filial
     * @param persistencia - Conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal getCodCliMobile601(BigDecimal CodFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "select MAX(Codigo)+1 Codigo from clientes "
                    + " where codfil = ? and banco >= '601' and banco <'999'";
            BigDecimal retorno = new BigDecimal("6010001");
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodFil);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = new BigDecimal(consult.getString("Codigo"));
                } catch (Exception e) {
                    retorno = new BigDecimal("6010001");
                }

            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCliMobile601 - " + e.getMessage() + "\r\n"
                    + "select MAX(Codigo)+1 Codigo from clientes "
                    + " where codfil = " + CodFil + " and banco >= '601' and banco <'999'");
        }
    }

    /**
     * Busca lista de clientes pelo código
     *
     * @param codfil - código da filial
     * @param codigo - código do cliente
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaClienteCodigo(String codfil, String codigo, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select top 5 codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil =?"
                    + " and codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(codigo);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaClienteCodigo - " + e.getMessage() + "\r\n"
                    + "select top 5 codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil = " + codfil
                    + " and codigo = " + codigo);
        }
    }

    /**
     * Busca lista de clientes pelo código
     *
     * @param codfil - código da filial
     * @param nome - nome do cliente
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaClienteNome(String codfil, String nome, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil = ? "
                    + " and nome = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(nome);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaClienteNome - " + e.getMessage() + "\r\n"
                    + "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil = " + codfil
                    + " and nome = " + nome);
        }
    }

    /**
     * Busca lista de clientes pelo nred
     *
     * @param codfil - código da filial
     * @param nred - nred do cliente
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaClienteNred(String codfil, String nred, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil = ? "
                    + " and nred = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(nred);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaClienteNred - " + e.getMessage() + "\r\n"
                    + "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter"
                    + " from clientes"
                    + " where codfil = " + codfil
                    + " and nred = " + nred);
        }
    }

    /**
     * Busca lista de clientes pela agencia
     *
     * @param codfil - código da filial
     * @param agencia - agência do cliente
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaClienteAgencia(String codfil, String agencia, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + "ie,insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter,agencia "
                    + "from clientes "
                    + "where codfil = ?"
                    + "and agencia = ?";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(agencia);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaClienteAgencia - " + e.getMessage() + "\r\n"
                    + "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + "ie,insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter,agencia "
                    + "from clientes "
                    + "where codfil = " + codfil
                    + "and agencia = " + agencia);
        }
    }

    /**
     * Lista os clientes de um banco pelo nome
     *
     * @param nome
     * @param query
     * @param banco
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> buscaClientesBanco(String nome, String query, String banco, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select top 10 * from clientes "
                    + " where codfil in (select saspwfil.codfilac from saspwfil"
                    + "                  left join saspw on saspw.codfil = saspwfil.codfilac"
                    + "                                 and saspw.nome =  saspwfil.nome"
                    + "                  where saspwfil.nome = ?)"
                    + " and (clientes.nome like ? or clientes.nred like ?) and clientes.situacao = 'A' and clientes.banco = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(nome);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString(banco);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo") + "}" + consult.getString("codfil"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }
            consult.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n"
                    + "select top 10 * from clientes "
                    + " where codfil in (select saspwfil.codfilac from saspwfil"
                    + "                  left join saspw on saspw.codfil = saspwfil.codfilac"
                    + "                                 and saspw.nome =  saspwfil.nome"
                    + "                  where saspwfil.nome = " + nome + ")"
                    + " and (clientes.nome like " + query.toUpperCase() + " or clientes.nred "
                    + "like " + query.toUpperCase() + ") and clientes.situacao = 'A'");
        }
    }

    /**
     * Lista clientes das filiais liberadas para o usuário. SALVA A FILIAL JUNTO
     * AO CÓDIGO codigo}codfil
     *
     * @param filiais filiais que o usuário tem acesso
     * @param query busca
     * @param banco
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<Clientes> buscaClientesBanco(List<SasPWFill> filiais, String query, String banco, Persistencia persistencia) throws Exception {
        String sql = "select top 20 * from clientes where codfil in ( ";
        try {
            List<Clientes> retorno = new ArrayList();
            for (SasPWFill f : filiais) {
                sql = sql + " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + ") "
                    + " and (clientes.nome like ? or clientes.nred like ?)  and clientes.banco = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            for (SasPWFill f : filiais) {
                consult.setBigDecimal(f.getCodfilAc());
            }
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString(banco);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo") + "}" + consult.getString("codfil"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }

            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Busca lista de clientes pelo nred
     *
     * @param codfil - codigo da filial
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaCliente(String codfil, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter, email"
                    + " from clientes"
                    + " where codfil = ? ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                cliente.setEmail(consult.getString("email"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaCliente - " + e.getMessage() + "\r\n"
                    + "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter, email"
                    + " from clientes"
                    + " where codfil = " + codfil);
        }
    }

    public void criarContratoPstAutomaticamente(Clientes clientes, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            sql = "INSERT INTO PstServ(Secao, CodFil, Regional, Local, Situacao, Dt_Situacao, Contrato, TipoPosto, CodCli, Posto, OS, GrpGerencial, Operador, Dt_Alter, Hr_Alter)\n"
                    + " SELECT\n"
                    + " LEFT(Clientes.Codigo,3) + '.' + REPLICATE('0', 5 - LEN(SUBSTRING(Clientes.Codigo,5,LEN(Clientes.Codigo)))) + RTrim(SUBSTRING(Clientes.Codigo,5,LEN(Clientes.Codigo))) + '.001A',\n"
                    + " Clientes.CodFil,\n"
                    + " 999,\n"
                    + " LEFT(Clientes.Nome, 40),\n"
                    + " 'A',\n"
                    + " Clientes.Dt_Alter,\n"
                    + " '999.O001.1',\n"
                    + " 'A01',\n"
                    + " Clientes.Codigo,\n"
                    + " LEFT(Clientes.Nred, 15),\n"
                    + " '00001',\n"
                    + " 'GERAL',\n"
                    + " Clientes.Oper_Inc,\n"
                    + " Clientes.Dt_Alter,\n"
                    + " Clientes.Hr_Alter\n"
                    + " FROM (\n"
                    + "     SELECT\n"
                    + "     MAX(ClientesDt.Dt_Alter) Dt_Alter,\n"
                    + "     (SELECT MAX(Hr_Alter) Hr_Alter FROM Clientes ClientesHr WHERE ClientesHr.CodFil = ClientesDt.CodFil AND ClientesHr.Dt_Alter = MAX(ClientesDt.Dt_Alter)) Hr_Alter,\n"
                    + "     ClientesDt.CodFil\n"
                    + "     FROM Clientes ClientesDt\n"
                    + "     WHERE ClientesDt.CodFil = ?\n"
                    + "     GROUP BY ClientesDt.CodFil\n"
                    + "     ) AS UltimoCliente\n"
                    + " JOIN Clientes\n"
                    + "   ON UltimoCliente.CodFil = Clientes.CodFil\n"
                    + "  AND UltimoCliente.Dt_Alter = Clientes.Dt_Alter\n"
                    + "  AND UltimoCliente.Hr_Alter = Clientes.Hr_Alter\n"
                    + " LEFT JOIN PstServ\n"
                    + "   ON PstServ.Secao = LEFT(Clientes.Codigo,3) + '.' + REPLICATE('0', 5 - LEN(SUBSTRING(Clientes.Codigo,5,LEN(Clientes.Codigo)))) + RTrim(SUBSTRING(Clientes.Codigo,5,LEN(Clientes.Codigo))) + '.001A' \n"
                    + " WHERE PstServ.Secao IS NULL";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(clientes.getCodFil());
            consult.insert();
            consult.close();

        } catch (Exception e) {
            throw new Exception("cliente.gravarContratoPstAutomaticamente" + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Grava alterações do cadastro de clientes
     *
     * @param cliente - objeto clientes
     * @param persistencia - conexão ao banco de dados
     * @throws Exception
     */
    public void GravaClienteMobile(Clientes cliente, Persistencia persistencia) throws Exception {
        try {
            String sql = "update clientes set codfil = ?, nome = ?, nred = ?, ende = ?, bairro = ?, "
                    + " cidade = ?, estado = ?, cep = ?, fone1 = ?, fone2 = ?, cgc = ?, "
                    + " ie = ?, insc_munic = ?, cpf = ?, rg = ?, latitude = ?, longitude = ?, interfext = ?, "
                    + " oper_alt = ?, dt_alter = ?, hr_alter = ?, email = ?"
                    + " where codigo = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(cliente.getCodFil());
            consulta.setString(cliente.getNome());
            consulta.setString(cliente.getNRed());
            consulta.setString(cliente.getEnde());
            consulta.setString(cliente.getBairro());
            consulta.setString(cliente.getCidade());
            consulta.setString(cliente.getEstado());
            consulta.setString(cliente.getCEP());
            consulta.setString(cliente.getFone1());
            consulta.setString(cliente.getFone2());
            consulta.setString(cliente.getCGC());
            consulta.setString(cliente.getIE());
            consulta.setString(cliente.getInsc_Munic());
            consulta.setString(cliente.getCPF());
            consulta.setString(cliente.getRG());
            consulta.setString(cliente.getLatitude());
            consulta.setString(cliente.getLongitude());
            consulta.setString(cliente.getInterfExt());
            consulta.setString(cliente.getOper_Alt());
            consulta.setDate(DataAtual.LC2Date(cliente.getDt_Alter()));
            consulta.setString(cliente.getHr_Alter());
            consulta.setString(cliente.getEmail());
            consulta.setString(cliente.getCodigo());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.GravaClienteMobile - " + e.getMessage() + "\r\n"
                    + "update clientes set codfil = " + cliente.getCodFil() + ", nome = " + cliente.getNome() + ", nred = " + cliente.getNRed() + ", "
                    + " ende = " + cliente.getEnde() + ", bairro = " + cliente.getBairro() + ", "
                    + " cidade = " + cliente.getCidade() + ", estado = " + cliente.getEstado() + ", cep = " + cliente.getCEP() + ", "
                    + " fone1 = " + cliente.getFone1() + ", fone2 = " + cliente.getFone2() + ", cgc = " + cliente.getCGC() + ", "
                    + " ie = " + cliente.getIE() + ", insc_munic = " + cliente.getInsc_Munic() + ", cpf = " + cliente.getCPF() + ", "
                    + " rg = " + cliente.getRG() + ", latitude = " + cliente.getLatitude() + ", longitude = " + cliente.getLongitude() + ", "
                    + " interfext = " + cliente.getInterfExt() + ", "
                    + " oper_alt = " + cliente.getOper_Alt() + ", dt_alter = " + cliente.getDt_Alter() + ", hr_alter = " + cliente.getHr_Alter() + ", "
                    + " email = " + cliente.getEmail() + ""
                    + " where codigo = " + cliente.getCodigo());
        }
    }

    /**
     * Lista simplificada de clientes da filial
     *
     * @param CodFil codigo da filial
     * @param query
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListagemSimplesCliente(String CodFil, String query, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select codigo, codfil, nome, nred, cgc, cpf, ende, bairro, cidade, estado, cep, regiao "
                    + " from clientes"
                    + " where (nome like ? or nred like ?)";
            if (!CodFil.equals(new BigDecimal("-1"))) {
                sql = sql + " and codfil = ?";
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            if (!CodFil.equals("-1")) {
                consult.setString(CodFil);
            }
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setRegiao(consult.getString("regiao"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListagemSimplesCliente - " + e.getMessage() + "\r\n"
                    + "select codigo, nome, nred, cgc, cpf, ende, bairro, cidade, estado, cep, regiao "
                    + " from clientes"
                    + " where (nome like " + query.toUpperCase() + " or nred like " + query.toUpperCase() + ") and codfil = " + CodFil);
        }
    }

    public Clientes consultaSimplesCliente(String CodCli, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            sql = "select codigo, codfil, nome, nred, cgc, cpf, ende, bairro, cidade, estado, cep, regiao "
                    + " from clientes"
                    + " where Codigo = ? ";

            Consulta consult = new Consulta(sql, persistencia);

            consult.setString(CodCli);
            consult.select();
            Clientes cliente = new Clientes();

            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setRegiao(consult.getString("regiao"));
            }
            consult.Close();

            return cliente;
        } catch (Exception e) {
            throw new Exception("ClientesDao.consultaSimplesCliente - " + e.getMessage() + "\r\n" + sql.toString());
        }
    }

    public List<Clientes> PesquisaClientesSatMob(String codfil, Clientes cliente, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter, email"
                    + " from clientes"
                    + " where codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)";
            if (!codfil.equals("")) {
                sql = sql + " and codfil = ?";
            }
            if (!cliente.getNome().equals("")) {
                sql = sql + " and nome like ?";
            }
            if (!cliente.getNRed().equals("")) {
                sql = sql + " and nred like ?";
            }
            sql = sql + " order by codfil";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            if (!codfil.equals("")) {
                consult.setString(codfil);
            }
            if (!cliente.getNome().equals("")) {
                consult.setString("%" + cliente.getNome() + "%");
            }
            if (!cliente.getNRed().equals("")) {
                consult.setString("%" + cliente.getNRed() + "%");
            }
            consult.select();
            Clientes c;
            while (consult.Proximo()) {
                c = new Clientes();
                c.setCodFil(consult.getString("codfil"));
                c.setCodigo(consult.getString("codigo"));
                c.setNome(consult.getString("nome"));
                c.setNRed(consult.getString("nred"));
                c.setEnde(consult.getString("ende"));
                c.setBairro(consult.getString("bairro"));
                c.setCidade(consult.getString("cidade"));
                c.setEstado(consult.getString("estado"));
                c.setCEP(consult.getString("cep"));
                c.setFone1(consult.getString("fone1"));
                c.setFone2(consult.getString("fone2"));
                c.setCGC(consult.getString("cgc"));
                c.setIE(consult.getString("ie"));
                c.setInsc_Munic(consult.getString("insc_munic"));
                c.setCPF(consult.getString("cpf"));
                c.setRG(consult.getString("rg"));
                c.setLatitude(consult.getString("latitude"));
                c.setLongitude(consult.getString("longitude"));
                c.setInterfExt(consult.getString("interfext"));
                c.setOper_Alt(consult.getString("oper_alt"));
                c.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                c.setHr_Alter(consult.getString("hr_alter"));
                c.setEmail(consult.getString("email"));
                retorno.add(c);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.PesquisaClientesSatMob - " + e.getMessage() + "\r\n"
                    + "select codfil, codigo, nome, nred, ende, bairro, cidade, estado, cep, fone1, fone2, cgc, "
                    + " ie, insc_munic, cpf, rg, latitude, longitude, interfext, oper_alt, dt_alter, hr_alter, email"
                    + " from clientes"
                    + " where codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = " + CodPessoa + " and paramet.path = " + persistencia.getEmpresa() + ")"
                    + " and codfil = " + codfil + " and nome like " + cliente.getNome() + " and nred like " + cliente.getNRed() + " order by codfil");
        }
    }

    /**
     * Obter agencia do cliente
     *
     * @param codFil codigo da filal
     * @param persistencia conexão com o banco de dados
     * @return numero da agencia
     */
    public String obterAgencia(String codFil, Persistencia persistencia) {
        int agencia = 0;
        try {
            String sql = "SELECT MAX(Agencia) + 1 agencia FROM Clientes WHERE CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                agencia = consulta.getInt("agencia");
            }

            if (agencia == 0) {
                agencia = 1;
            }
            consulta.Close();
        } catch (Exception e) {
            agencia = 1;
        }
        return String.valueOf(agencia);
    }

    /**
     * Obtem informações do banco
     *
     * @param codfil codigo da filial
     * @param persistencia conexao com o banco de dados
     * @return codigo do banco
     */
    public String obterBanco(String codfil, Persistencia persistencia) {
        int banco = 0;
        try {
            String sql = "SELECT MAX(Banco) + 1 banco FROM Clientes WHERE CodFil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                banco = consulta.getInt("banco");
            }

            //O mobile tera o código do cliente iniciado aparti do 600
            if (banco < 600 || banco == 1000) {
                banco = 601;
            }
            consulta.Close();
        } catch (Exception e) {
            banco = 601;
        }
        return String.valueOf(banco);
    }

    /**
     * Obtem informações sobre o codcli
     *
     * @param codFil codigo da filial
     * @param banco numero baaco
     * @param persistencia conexao com o banco
     * @return
     */
    public String getCodCliMobile(String codFil, String banco, Persistencia persistencia) {
        String codCli = "001";
        try {
            String sql = "select MAX(convert(int, CodCli))+1 Codigo from Clientes where codfil = ? and banco = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(banco);
            consulta.select();

            while (consulta.Proximo()) {
                codCli = consulta.getString("Codigo");
            }
            consulta.Close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return codCli;
    }

    public String getCodCli(String codFil, String banco, String tpcli, Persistencia persistencia) throws Exception {
        String codCli;
        try {
            String sql = " select isnull(max(codcli),0) + 1 codcli from clientes where codfil = ? and banco = ? and tpcli = ? ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(banco);
            consulta.setString(tpcli);
            consulta.select();

            if (consulta.Proximo()) {
                codCli = FuncoesString.PreencheEsquerda(consulta.getString("codcli"), 3, "0");
            } else {
                return null;
            }
            consulta.Close();
            return codCli;
        } catch (Exception e) {
            throw new Exception("ClientesDao.getCodCli - " + e.getMessage() + "\r\n"
                    + " select isnull(max(codcli),0) + 1 from clientes where codfil = " + codFil + " and banco = " + banco + " and tpcli = " + tpcli);
        }
    }

    /**
     * Verifica a existencia do banco
     *
     * @param banco Numero do banco
     * @param codFil Codigo da filial
     * @param persistencia Conexão com o banco
     * @return se existe
     * @throws Exception
     */
    public boolean existeBanco(String banco, String codFil, Persistencia persistencia) throws Exception {
        boolean exite = false;
        try {
            String sql = "SELECT COUNT(*) qtd FROM Clientes WHERE banco = ? AND codfil = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(banco);
            consulta.setString(codFil);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("qtd");
            }

            if (quantidade > 0) {
                exite = true;
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.existeBanco - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) qtd FROM Clientes WHERE banco = " + banco + " AND codfil = " + codFil);
        }
        return exite;
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de funcionários cadastrados no banco
     *
     * @param filtros filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalClientesMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from clientes "
                    + " WHERE clientes.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.TotalClientesMobWeb - " + e.getMessage());
        }
    }

    public Integer totalClientesMobWeb(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from clientes "
                    + " WHERE codigo IS NOT null ";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.TotalClientesMobWeb - " + e.getMessage());
        }
    }

    public Integer totalClientesMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from clientes \n"
                    + " LEFT JOIN PessoaCliAut ON PessoaCliAut.CodCli = Clientes.Codigo \n"
                    + "                        AND PessoaCliAut.CodFil = Clientes.CodFil \n"
                    + " WHERE PessoaCliAut.Codigo = ? \n";
            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + "\n";
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.TotalClientesMobWeb - " + e.getMessage());
        }
    }

    public List<Clientes> ListaClientesQrCode(Persistencia persistencia) throws Exception {
        String sql = "";
        List<Clientes> retorno = new ArrayList();

        try {
            sql = "SELECT\n"
                    + " Clientes.Codigo,\n"
                    + " Clientes.CodPtoCli,\n"
                    + " UPPER(Clientes.Nred) Nred,\n"
                    + " Clientes.Situacao,\n"
                    + " Clientes.CodFil\n"
                    + " FROM Clientes\n"
                    + " WHERE Clientes.Nred IS NOT NULL\n"
                    + " AND   Clientes.Situacao = 'A'\n"
                    + " AND   Clientes.CodPtoCli IS NOT NULL\n"
                    + " AND   Clientes.CodPtoCli <> '' \n"
                    + " ORDER BY Clientes.Nred";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();

                cliente.setCodigo(consulta.getString("Codigo"));
                cliente.setCodPtoCli(consulta.getString("CodPtoCli"));
                cliente.setNRed(consulta.getString("Nred"));
                cliente.setSituacao(consulta.getString("Situacao"));
                cliente.setCodFil(consulta.getString("CodFil"));

                retorno.add(cliente);
            }

            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaPaginada - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Clientes> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            Map<String, String> filtro = filtros;
            String Ordenacao = "Nred";

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().toUpperCase().indexOf("CODFIL") == -1 && entrada.getKey().toUpperCase().indexOf("SITUACAO") == -1 && entrada.getKey().toUpperCase().indexOf("LAT") == -1) {
                    Ordenacao = "LTRIM(" + entrada.getKey().split(" ")[0] + ")";
                    if (Ordenacao.toUpperCase().indexOf("NRED") == -1) {
                        Ordenacao += ", LTRIM(NRed)";
                    }
                }
            }

            String sql = "SELECT  * "
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY " + Ordenacao + " ) AS RowNum, clientes.*, ClientesImg.Imagem "
                    + "          FROM  Clientes   clientes "
                    + "          LEFT JOIN ClientesImg ON clientes.Codigo = ClientesImg.Codigo AND ClientesImg.FotoPdr = 'S'"
                    + " WHERE clientes.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";
            filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "clientes.codigo IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Clientes cliente;
            
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consulta.getBigDecimal("CodFil"));
                cliente.setBanco(consulta.getString("Banco"));
                cliente.setTpCli(consulta.getString("TpCli"));
                cliente.setCodCli(consulta.getString("CodCli"));
                cliente.setAgencia(consulta.getString("Agencia"));
                cliente.setSubAgencia(consulta.getString("SubAgencia"));
                cliente.setLote(consulta.getString("Lote"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setNome(consulta.getString("Nome"));
                cliente.setEnde(consulta.getString("Ende"));
                cliente.setBairro(consulta.getString("Bairro"));
                cliente.setCodCidade(consulta.getString("CodCidade"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setEstado(consulta.getString("Estado"));
                cliente.setCEP(consulta.getString("CEP"));
                cliente.setFone1(consulta.getString("Fone1"));
                cliente.setFone2(consulta.getString("Fone2"));
                cliente.setFax(consulta.getString("Fax"));
                cliente.setContato(consulta.getString("Contato"));
                cliente.setEmail(consulta.getString("Email"));
                cliente.setSenhaWEB(consulta.getString("SenhaWEB"));
                cliente.setRamoAtiv(consulta.getString("RamoAtiv"));
                cliente.setRegiao(consulta.getString("Regiao"));
                cliente.setLatitude(consulta.getString("Latitude"));
                cliente.setLongitude(consulta.getString("Longitude"));
                cliente.setPrdApoio(consulta.getString("PrdApoio"));
                cliente.setRisco(consulta.getString("Risco"));
                cliente.setMalotes(consulta.getInt("Malotes"));
                cliente.setNroChave(consulta.getString("NroChave"));
                cliente.setGrpChave(consulta.getInt("GrpChave"));
                cliente.setCGC(consulta.getString("CGC"));
                cliente.setIE(consulta.getString("IE"));
                cliente.setInsc_Munic(consulta.getString("Insc_Munic"));
                cliente.setCPF(consulta.getString("CPF"));
                cliente.setRG(consulta.getString("RG"));
                cliente.setRateioFat(consulta.getString("RateioFat"));
                cliente.setRateioTes(consulta.getString("RateioTes"));
                cliente.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                cliente.setDiaVencNF(consulta.getInt("DiaVencNF"));
                cliente.setRetencoesFat(consulta.getString("RetencoesFat"));
                cliente.setMarcaATM(consulta.getString("MarcaATM"));
                cliente.setRetorno(consulta.getString("Retorno"));
                cliente.setCheque(consulta.getString("Cheque"));
                cliente.setVr_A(consulta.getString("Vr_A"));
                cliente.setCed_A(consulta.getString("Ced_A"));
                cliente.setCed_AP(consulta.getString("Ced_AP"));
                cliente.setVr_B(consulta.getString("Vr_B"));
                cliente.setCed_B(consulta.getString("Ced_B"));
                cliente.setCed_BP(consulta.getString("Ced_BP"));
                cliente.setVr_C(consulta.getString("Vr_C"));
                cliente.setCed_C(consulta.getString("Ced_C"));
                cliente.setCed_CP(consulta.getString("Ced_CP"));
//                cliente.setVr_D(consulta.getString("Vr_D"));
//                cliente.setCed_D(consulta.getString("Ced_D"));
                cliente.setCed_DP(consulta.getString("Ced_DP"));
                cliente.setVr_E(consulta.getString("Vr_E"));
                cliente.setCed_E(consulta.getString("Ced_E"));
                cliente.setCed_EP(consulta.getString("Ced_EP"));
                cliente.setEndCob(consulta.getString("EndCob"));
                cliente.setCodCidCod(consulta.getString("CodCidCob"));
                cliente.setCidCob(consulta.getString("CidCob"));
                cliente.setUFCob(consulta.getString("UFCob"));
                cliente.setCEPCob(consulta.getString("CEPCob"));
                cliente.setEmailCob(consulta.getString("EmailCob"));
                cliente.setObs(consulta.getString("Obs"));
                cliente.setSituacao(consulta.getString("Situacao"));
                cliente.setInterfExt(consulta.getString("InterfExt"));
                cliente.setCodExt(consulta.getString("CodExt"));
                cliente.setCodIntCli(consulta.getString("CodIntCli"));
//                cliente.setCodPtoCli(consulta.getString("CodPtoCli"));
                cliente.setCercaElet(consulta.getString("CercaElet"));
                cliente.setDtSituacao(consulta.getString("DtSituacao"));
                cliente.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                cliente.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                cliente.setHr_Alter(consulta.getString("Hr_Alter"));
                cliente.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                cliente.setOper_Inc(consulta.getString("Oper_Inc"));
//                cliente.setOper_Alt(consulta.getString("Oper_Alt"));
                cliente.setCodigo(consulta.getString("Codigo"));
                cliente.setCCusto(consulta.getString("CCusto"));
                cliente.setTipoPagto(consulta.getString("TipoPagto"));
                cliente.setLimite(consulta.getString("Limite"));
                cliente.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                cliente.setLimiteColeta(consulta.getString("LimiteColeta"));
                cliente.setProprietario(consulta.getString("Proprietario"));
                cliente.setRepresentante(consulta.getString("Representante"));
                cliente.setAtivEconomica(consulta.getString("AtivEconomica"));
                cliente.setCodCofre(consulta.getString("CodCofre"));
                cliente.setEnvelope(consulta.getString("Envelope"));
                cliente.setPatrimonio(consulta.getString("Patrimonio"));
                cliente.setFoto(consulta.getString("Imagem"));

                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaPaginada - " + e.getMessage());
        }
    }

    public List<Clientes> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, String Ordenacao, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY " + Ordenacao + " ) AS RowNum, * "
                    + "          FROM      clientes "
                    + " WHERE clientes.codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";

            Map<String, String> filtro = filtros;
            filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
            consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Clientes cliente;
            while (consulta.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consulta.getBigDecimal("CodFil"));
                cliente.setBanco(consulta.getString("Banco"));
                cliente.setTpCli(consulta.getString("TpCli"));
                cliente.setCodCli(consulta.getString("CodCli"));
                cliente.setAgencia(consulta.getString("Agencia"));
                cliente.setSubAgencia(consulta.getString("SubAgencia"));
                cliente.setLote(consulta.getString("Lote"));
                cliente.setNRed(consulta.getString("NRed"));
                cliente.setNome(consulta.getString("Nome"));
                cliente.setEnde(consulta.getString("Ende"));
                cliente.setBairro(consulta.getString("Bairro"));
                cliente.setCodCidade(consulta.getString("CodCidade"));
                cliente.setCidade(consulta.getString("Cidade"));
                cliente.setEstado(consulta.getString("Estado"));
                cliente.setCEP(consulta.getString("CEP"));
                cliente.setFone1(consulta.getString("Fone1"));
                cliente.setFone2(consulta.getString("Fone2"));
                cliente.setFax(consulta.getString("Fax"));
                cliente.setContato(consulta.getString("Contato"));
                cliente.setEmail(consulta.getString("Email"));
                cliente.setSenhaWEB(consulta.getString("SenhaWEB"));
                cliente.setRamoAtiv(consulta.getString("RamoAtiv"));
                cliente.setRegiao(consulta.getString("Regiao"));
                cliente.setLatitude(consulta.getString("Latitude"));
                cliente.setLongitude(consulta.getString("Longitude"));
                cliente.setPrdApoio(consulta.getString("PrdApoio"));
                cliente.setRisco(consulta.getString("Risco"));
                cliente.setMalotes(consulta.getInt("Malotes"));
                cliente.setNroChave(consulta.getString("NroChave"));
                cliente.setGrpChave(consulta.getInt("GrpChave"));
                cliente.setCGC(consulta.getString("CGC"));
                cliente.setIE(consulta.getString("IE"));
                cliente.setInsc_Munic(consulta.getString("Insc_Munic"));
                cliente.setCPF(consulta.getString("CPF"));
                cliente.setRG(consulta.getString("RG"));
                cliente.setRateioFat(consulta.getString("RateioFat"));
                cliente.setRateioTes(consulta.getString("RateioTes"));
                cliente.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                cliente.setDiaVencNF(consulta.getInt("DiaVencNF"));
                cliente.setRetencoesFat(consulta.getString("RetencoesFat"));
                cliente.setMarcaATM(consulta.getString("MarcaATM"));
                cliente.setRetorno(consulta.getString("Retorno"));
                cliente.setCheque(consulta.getString("Cheque"));
                cliente.setVr_A(consulta.getString("Vr_A"));
                cliente.setCed_A(consulta.getString("Ced_A"));
                cliente.setCed_AP(consulta.getString("Ced_AP"));
                cliente.setVr_B(consulta.getString("Vr_B"));
                cliente.setCed_B(consulta.getString("Ced_B"));
                cliente.setCed_BP(consulta.getString("Ced_BP"));
                cliente.setVr_C(consulta.getString("Vr_C"));
                cliente.setCed_C(consulta.getString("Ced_C"));
                cliente.setCed_CP(consulta.getString("Ced_CP"));
                cliente.setVr_D(consulta.getString("Vr_D"));
                cliente.setCed_D(consulta.getString("Ced_D"));
                cliente.setCed_DP(consulta.getString("Ced_DP"));
                cliente.setVr_E(consulta.getString("Vr_E"));
                cliente.setCed_E(consulta.getString("Ced_E"));
                cliente.setCed_EP(consulta.getString("Ced_EP"));
                cliente.setEndCob(consulta.getString("EndCob"));
                cliente.setCodCidCod(consulta.getString("CodCidCod"));
                cliente.setCidCob(consulta.getString("CidCob"));
                cliente.setUFCob(consulta.getString("UFCob"));
                cliente.setCEPCob(consulta.getString("CEPCob"));
                cliente.setEmailCob(consulta.getString("EmailCob"));
                cliente.setObs(consulta.getString("Obs"));
                cliente.setSituacao(consulta.getString("Situacao"));
                cliente.setInterfExt(consulta.getString("InterfExt"));
                cliente.setCodExt(consulta.getString("CodExt"));
                cliente.setCodIntCli(consulta.getString("CodIntCli"));
                cliente.setCodPtoCli(consulta.getString("CodPtoCli"));
                cliente.setCercaElet(consulta.getString("CercaElet"));
                cliente.setDtSituacao(consulta.getString("DtSituacao"));
                cliente.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                cliente.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                cliente.setHr_Alter(consulta.getString("Hr_Alter"));
                cliente.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                cliente.setOper_Inc(consulta.getString("Oper_Inc"));
                cliente.setOper_Alt(consulta.getString("Oper_Alt"));
                cliente.setCodigo(consulta.getString("Codigo"));
                cliente.setCCusto(consulta.getString("CCusto"));
                cliente.setTipoPagto(consulta.getString("TipoPagto"));
                cliente.setLimite(consulta.getString("Limite"));
                cliente.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                cliente.setLimiteColeta(consulta.getString("LimiteColeta"));
                cliente.setProprietario(consulta.getString("Proprietario"));
                cliente.setRepresentante(consulta.getString("Representante"));
                cliente.setAtivEconomica(consulta.getString("AtivEconomica"));
                cliente.setCodCofre(consulta.getString("CodCofre"));
                cliente.setEnvelope(consulta.getString("Envelope"));
                cliente.setPatrimonio(consulta.getString("Patrimonio"));

                retorno.add(cliente);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaPaginada - " + e.getMessage());
        }
    }

    public List<Clientes> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "SELECT  * \n"
                    + " FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY nome ) AS RowNum, * \n"
                    + "          FROM      clientes \n"
                    + " WHERE codigo IS NOT null \n";
            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum \n";
            Consulta consult = new Consulta(sql, persistencia);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("NRed"));
                cliente.setSituacao(consult.getString("situacao"));
                cliente.setAgencia(consult.getString("Agencia"));
                cliente.setSubAgencia(consult.getString("SubAgencia"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                cliente.setEmail(consult.getString("email"));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaPaginada - " + e.getMessage());
        }
    }

    public List<Clientes> listaCliAutCliFat(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "SELECT clientes.NRED, Clientes.Codigo, Clientes.CodFil, Clientes.ende,\n"
                    + " Clientes.bairro, Clientes.cidade, Clientes.estado, \n"
                    + " MAX(OS_Vig.Contrato) Contrato, MAX(OS_Vig.OSGrp) OSGrp, MAX(OS_Vig.Agrupador) Agrupador \n"
                    + "FROM clientes \n"
                    + "LEFT JOIN PessoaCliAut ON PessoaCliAut.CodCli = Clientes.Codigo \n"
                    + "                       AND PessoaCliAut.CodFil = Clientes.CodFil \n"
                    + "INNER JOIN OS_Vig ON OS_Vig.clifat = Clientes.Codigo\n"
                    + "                  AND OS_Vig.CodFil = Clientes.CodFil \n"
                    + "                  AND OS_Vig.DtFim >= getDate()\n"
                    + "WHERE PessoaCliAut.Codigo = ?\n"
                    + "GROUP BY clientes.NRED, Clientes.Codigo, Clientes.CodFil, Clientes.ende,\n"
                    + " Clientes.bairro, Clientes.cidade, Clientes.estado \n";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));

                cliente.setContato(consult.getString("Contrato")); // Contrato em Contato
                cliente.setObs(consult.getString("OSGrp")); // OSGrp Em OBs
                cliente.setAgencia(consult.getString("Agrupador")); // Agrupador em Agencia
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.listaCliAutCliFat - " + e.getMessage() + "\r\n"
                    + "SELECT clientes.NRED, Clientes.Codigo, Clientes.CodFil, Clientes.ende,\n"
                    + " Clientes.bairro, Clientes.cidade, Clientes.estado, \n"
                    + " Contrato, MAX(OS_Vig.OSGrp) OSGrp, MAX(OS_Vig.Agrupador) Agrupador "
                    + "FROM clientes \n"
                    + "LEFT JOIN PessoaCliAut ON PessoaCliAut.CodCli = Clientes.Codigo \n"
                    + "                       AND PessoaCliAut.CodFil = Clientes.CodFil \n"
                    + "INNER JOIN OS_Vig ON OS_Vig.clifat = Clientes.Codigo\n"
                    + "                  AND OS_Vig.CodFil = Clientes.CodFil \n"
                    + "                  AND OS_Vig.DtFim >= getDate()\n"
                    + "WHERE PessoaCliAut.Codigo = " + CodPessoa + "\n"
                    + "GROUP BY clientes.NRED, Clientes.Codigo, Clientes.CodFil, Clientes.ende,\n"
                    + " Clientes.bairro, Clientes.cidade, Clientes.estado \n");
        }
    }

    public List<Clientes> listaPaginadaCliAut(BigDecimal CodPessoa, int primeiro, int linhas,
            Map filtros, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "SELECT * \n"
                    + " FROM (SELECT ROW_NUMBER() OVER ( ORDER BY nome ) AS RowNum, clientes.*, isnull(GTVQtde,0) GTVQtde \n"
                    + "          FROM clientes \n"
                    + " LEFT JOIN PessoaCliAut ON PessoaCliAut.CodCli = Clientes.Codigo \n"
                    + "                        AND PessoaCliAut.CodFil = Clientes.CodFil \n"
                    + " LEFT JOIN OS_Vig ON OS_Vig.Cliente = Clientes.Codigo\n"
                    + "                   AND OS_Vig.CodFil = Clientes.CodFil \n"
                    //+ " WHERE PessoaCliAut.Codigo = ? \n";
                    + " Where Clientes.Codigo in ( Select b.Cliente from PessoaCliAut a \n"
                    + "                       Left Join OS_Vig b   on a.CodCli = b.CliFat \n"
                    + "                                           and a.CodFil = b.CodFil \n"
                    + "                       where a.CodFil = Clientes.CodFil \n"
                    + "                         and a.Codigo = ? \n"
                    + "                         and a.Flag_Excl <> '*') \n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + " AND " + entrada.getKey() + "\n";
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ? \n"
                    + "    AND RowNum < ? \n"
                    + "ORDER BY RowNum";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consult.setString(entry);
                    }
                }
            }
            consult.setInt(primeiro + 1);
            consult.setInt(primeiro + 1 + linhas);
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setSituacao(consult.getString("situacao"));
                cliente.setAgencia(consult.getString("Agencia"));
                cliente.setSubAgencia(consult.getString("SubAgencia"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setEnde(consult.getString("ende"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setRegiao(consult.getString("Regiao"));
                cliente.setRamoAtiv(consult.getString("RamoAtiv"));
                cliente.setFone1(consult.getString("fone1"));
                cliente.setFone2(consult.getString("fone2"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setIE(consult.getString("ie"));
                cliente.setInsc_Munic(consult.getString("insc_munic"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setRG(consult.getString("rg"));
                cliente.setLatitude(consult.getString("latitude"));
                cliente.setLongitude(consult.getString("longitude"));
                cliente.setInterfExt(consult.getString("interfext"));
                cliente.setOper_Alt(consult.getString("oper_alt"));
                cliente.setDt_Alter(consult.getDate("dt_alter").toLocalDate());
                cliente.setHr_Alter(consult.getString("hr_alter"));
                cliente.setEmail(consult.getString("email"));
                cliente.setObs(consult.getString("GTVQtde").replace(".0", ""));
                retorno.add(cliente);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.ListaPaginada - " + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
    public List<Clientes> QueryCliente(BigDecimal CodPessoa, String query, Persistencia persistencia) throws Exception {
        List<Clientes> retorno = new ArrayList();
        try {
            String sql = "select top 20 codfil, codigo, nome, nred"
                    + " from clientes"
                    + " where codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)"
                    + " and (nred like ? OR nome like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(CodPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
            consult.setString(persistencia.getEmpresa());
//            }
            consult.setString("%" + query + "%");
            consult.setString("%" + query + "%");
            consult.select();
            Clientes c;
            while (consult.Proximo()) {
                c = new Clientes();
                c.setCodFil(consult.getString("codfil"));
                c.setCodigo(consult.getString("codigo"));
                c.setNome(consult.getString("nome"));
                c.setNRed(consult.getString("nred"));
                retorno.add(c);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.QueryCliente - " + e.getMessage() + "\r\n"
                    + "select top 20 codfil, codigo, nome, nred"
                    + " from clientes"
                    + " where codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = " + CodPessoa + " and paramet.path = " + persistencia.getEmpresa() + ")"
                    + " and nred like " + query + " and nome like " + query);
        }
    }

    public Clientes clienteEcoVisao(String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = "SELECT Clientes.Contato, Clientes.Codigo,  Clientes.CodFil, \n"
                    + "CASE WHEN Clientes.Email IS NOT NULL THEN Clientes.Email ELSE CliFat.Email END Email \n"
                    + "FROM Rt_perc \n"
                    + "LEFT JOIN OS_Vig ON OS_Vig.OS = Rt_perc.OS \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat \n"
                    + "                   AND CliFat.CodFil = OS_Vig.CodFil \n"
                    + "WHERE Sequencia = ? AND Rt_Perc.Parada = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCodFil(consulta.getString("CodFil"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.clienteEcoVisao - " + e.getMessage() + "\r\n"
                    + "SELECT Clientes.Contato, CASE WHEN Clientes.Email IS NOT NULL THEN Clientes.Email ELSE CliFat.Email END Email \n"
                    + "FROM Rt_perc \n"
                    + "LEFT JOIN OS_Vig ON OS_Vig.OS = Rt_perc.OS \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = OS_Vig.Cliente \n"
                    + "                   AND Clientes.CodFil = OS_Vig.CodFil \n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat \n"
                    + "                   AND CliFat.CodFil = OS_Vig.CodFil \n"
                    + "WHERE Sequencia = " + Sequencia + " AND Rt_Perc.Parada = " + Parada);
        }
    }

    public Clientes clienteEcoVisao2(String Sequencia, String Parada, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = "SELECT Clientes.Contato, Clientes.Codigo,  Clientes.CodFil \n"
                    + "FROM Rt_perc \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Rt_perc.CodCli1 \n"
                    + "                   AND Clientes.CodFil = Rt_perc.CodFil \n"
                    + "WHERE Sequencia = ? AND Rt_Perc.Parada = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(Sequencia);
            consulta.setString(Parada);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setContato(consulta.getString("Contato"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCodFil(consulta.getString("CodFil"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.clienteEcoVisao - " + e.getMessage() + "\r\n"
                    + "SELECT Clientes.Contato, Clientes.Codigo,  Clientes.CodFil \n"
                    + "FROM Rt_perc \n"
                    + "LEFT JOIN Clientes ON Clientes.Codigo = Rt_perc.CodCli1 \n"
                    + "                   AND Clientes.CodFil = Rt_perc.CodFil \n"
                    + "WHERE Sequencia = " + Sequencia + " AND Rt_Perc.Parada = " + Parada);
        }
    }

    public Clientes buscarClienteCPNJ(String cnpj, String nred, String codfil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1* FROM Clientes WHERE CGC = ? AND NRed = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cnpj);
            consulta.setString(nred);
            consulta.setString(codfil);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setBanco(consulta.getString("Banco"));
                retorno.setTpCli(consulta.getString("TpCli"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
                retorno.setLote(consulta.getString("Lote"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setNome(consulta.getString("Nome"));
                retorno.setEnde(consulta.getString("Ende"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCodCidade(consulta.getString("CodCidade"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setEstado(consulta.getString("Estado"));
                retorno.setCEP(consulta.getString("CEP"));
                retorno.setFone1(consulta.getString("Fone1"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setFax(consulta.getString("Fax"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setSenhaWEB(consulta.getString("SenhaWEB"));
                retorno.setRamoAtiv(consulta.getString("RamoAtiv"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrdApoio(consulta.getString("PrdApoio"));
                retorno.setRisco(consulta.getString("Risco"));
                retorno.setMalotes(consulta.getInt("Malotes"));
                retorno.setNroChave(consulta.getString("NroChave"));
                retorno.setGrpChave(consulta.getInt("GrpChave"));
                retorno.setCGC(consulta.getString("CGC"));
                retorno.setIE(consulta.getString("IE"));
                retorno.setInsc_Munic(consulta.getString("Insc_Munic"));
                retorno.setCPF(consulta.getString("CPF"));
                retorno.setRG(consulta.getString("RG"));
                retorno.setRateioFat(consulta.getString("RateioFat"));
                retorno.setRateioTes(consulta.getString("RateioTes"));
                retorno.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                retorno.setDiaVencNF(consulta.getInt("DiaVencNF"));
                retorno.setRetencoesFat(consulta.getString("RetencoesFat"));
                retorno.setMarcaATM(consulta.getString("MarcaATM"));
                retorno.setRetorno(consulta.getString("Retorno"));
                retorno.setCheque(consulta.getString("Cheque"));
                retorno.setVr_A(consulta.getString("Vr_A"));
                retorno.setCed_A(consulta.getString("Ced_A"));
                retorno.setCed_AP(consulta.getString("Ced_AP"));
                retorno.setVr_B(consulta.getString("Vr_B"));
                retorno.setCed_B(consulta.getString("Ced_B"));
                retorno.setCed_BP(consulta.getString("Ced_BP"));
                retorno.setVr_C(consulta.getString("Vr_C"));
                retorno.setCed_C(consulta.getString("Ced_C"));
                retorno.setCed_CP(consulta.getString("Ced_CP"));
                retorno.setVr_D(consulta.getString("Vr_D"));
                retorno.setCed_D(consulta.getString("Ced_D"));
                retorno.setCed_DP(consulta.getString("Ced_DP"));
                retorno.setVr_E(consulta.getString("Vr_E"));
                retorno.setCed_E(consulta.getString("Ced_E"));
                retorno.setCed_EP(consulta.getString("Ced_EP"));
                retorno.setEndCob(consulta.getString("EndCob"));
                retorno.setCodCidCod(consulta.getString("CodCidCod"));
                retorno.setCidCob(consulta.getString("CidCob"));
                retorno.setUFCob(consulta.getString("UFCob"));
                retorno.setCEPCob(consulta.getString("CEPCob"));
                retorno.setEmailCob(consulta.getString("EmailCob"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setInterfExt(consulta.getString("InterfExt"));
                retorno.setCodExt(consulta.getString("CodExt"));
                retorno.setCodIntCli(consulta.getString("CodIntCli"));
                retorno.setCodPtoCli(consulta.getString("CodPtoCli"));
                retorno.setCercaElet(consulta.getString("CercaElet"));
                retorno.setDtSituacao(consulta.getString("DtSituacao"));
                retorno.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                retorno.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                retorno.setOper_Inc(consulta.getString("Oper_Inc"));
                retorno.setOper_Alt(consulta.getString("Oper_Alt"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setTipoPagto(consulta.getString("TipoPagto"));
                retorno.setLimite(consulta.getString("Limite"));
                retorno.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                retorno.setLimiteColeta(consulta.getString("LimiteColeta"));
                retorno.setProprietario(consulta.getString("Proprietario"));
                retorno.setRepresentante(consulta.getString("Representante"));
                retorno.setAtivEconomica(consulta.getString("AtivEconomica"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
                retorno.setEnvelope(consulta.getString("Envelope"));
                retorno.setPatrimonio(consulta.getString("Patrimonio"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClienteCPNJ - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1* FROM Clientes WHERE CGC = " + cnpj + " AND NRed = " + nred + " AND CodFil = " + codfil);
        }
    }

    public Clientes buscarClienteImportacao(String codExt, String codfil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1* FROM Clientes WHERE codExt = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codExt);
            consulta.setString(codfil);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setBanco(consulta.getString("Banco"));
                retorno.setTpCli(consulta.getString("TpCli"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
                retorno.setLote(consulta.getString("Lote"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setNome(consulta.getString("Nome"));
                retorno.setEnde(consulta.getString("Ende"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCodCidade(consulta.getString("CodCidade"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setEstado(consulta.getString("Estado"));
                retorno.setCEP(consulta.getString("CEP"));
                retorno.setFone1(consulta.getString("Fone1"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setFax(consulta.getString("Fax"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setSenhaWEB(consulta.getString("SenhaWEB"));
                retorno.setRamoAtiv(consulta.getString("RamoAtiv"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrdApoio(consulta.getString("PrdApoio"));
                retorno.setRisco(consulta.getString("Risco"));
                retorno.setMalotes(consulta.getInt("Malotes"));
                retorno.setNroChave(consulta.getString("NroChave"));
                retorno.setGrpChave(consulta.getInt("GrpChave"));
                retorno.setCGC(consulta.getString("CGC"));
                retorno.setIE(consulta.getString("IE"));
                retorno.setInsc_Munic(consulta.getString("Insc_Munic"));
                retorno.setCPF(consulta.getString("CPF"));
                retorno.setRG(consulta.getString("RG"));
                retorno.setRateioFat(consulta.getString("RateioFat"));
                retorno.setRateioTes(consulta.getString("RateioTes"));
                retorno.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                retorno.setDiaVencNF(consulta.getInt("DiaVencNF"));
                retorno.setRetencoesFat(consulta.getString("RetencoesFat"));
                retorno.setMarcaATM(consulta.getString("MarcaATM"));
                retorno.setRetorno(consulta.getString("Retorno"));
                retorno.setCheque(consulta.getString("Cheque"));
                retorno.setVr_A(consulta.getString("Vr_A"));
                retorno.setCed_A(consulta.getString("Ced_A"));
                retorno.setCed_AP(consulta.getString("Ced_AP"));
                retorno.setVr_B(consulta.getString("Vr_B"));
                retorno.setCed_B(consulta.getString("Ced_B"));
                retorno.setCed_BP(consulta.getString("Ced_BP"));
                retorno.setVr_C(consulta.getString("Vr_C"));
                retorno.setCed_C(consulta.getString("Ced_C"));
                retorno.setCed_CP(consulta.getString("Ced_CP"));
                retorno.setVr_D(consulta.getString("Vr_D"));
                retorno.setCed_D(consulta.getString("Ced_D"));
                retorno.setCed_DP(consulta.getString("Ced_DP"));
                retorno.setVr_E(consulta.getString("Vr_E"));
                retorno.setCed_E(consulta.getString("Ced_E"));
                retorno.setCed_EP(consulta.getString("Ced_EP"));
                retorno.setEndCob(consulta.getString("EndCob"));
                retorno.setCodCidCod(consulta.getString("CodCidCod"));
                retorno.setCidCob(consulta.getString("CidCob"));
                retorno.setUFCob(consulta.getString("UFCob"));
                retorno.setCEPCob(consulta.getString("CEPCob"));
                retorno.setEmailCob(consulta.getString("EmailCob"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setInterfExt(consulta.getString("InterfExt"));
                retorno.setCodExt(consulta.getString("CodExt"));
                retorno.setCodIntCli(consulta.getString("CodIntCli"));
                retorno.setCodPtoCli(consulta.getString("CodPtoCli"));
                retorno.setCercaElet(consulta.getString("CercaElet"));
                retorno.setDtSituacao(consulta.getString("DtSituacao"));
                retorno.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                retorno.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                retorno.setOper_Inc(consulta.getString("Oper_Inc"));
                retorno.setOper_Alt(consulta.getString("Oper_Alt"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setTipoPagto(consulta.getString("TipoPagto"));
                retorno.setLimite(consulta.getString("Limite"));
                retorno.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                retorno.setLimiteColeta(consulta.getString("LimiteColeta"));
                retorno.setProprietario(consulta.getString("Proprietario"));
                retorno.setRepresentante(consulta.getString("Representante"));
                retorno.setAtivEconomica(consulta.getString("AtivEconomica"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
                retorno.setEnvelope(consulta.getString("Envelope"));
                retorno.setPatrimonio(consulta.getString("Patrimonio"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClienteImportacao - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1* FROM Clientes WHERE codExt = " + codExt + " AND CodFil = " + codfil);
        }
    }

    public Clientes buscarClienteCPF(String cpf, String nred, String codfil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1* FROM Clientes WHERE CPF = ? AND NRed = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpf);
            consulta.setString(nred);
            consulta.setString(codfil);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setBanco(consulta.getString("Banco"));
                retorno.setTpCli(consulta.getString("TpCli"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
                retorno.setLote(consulta.getString("Lote"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setNome(consulta.getString("Nome"));
                retorno.setEnde(consulta.getString("Ende"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCodCidade(consulta.getString("CodCidade"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setEstado(consulta.getString("Estado"));
                retorno.setCEP(consulta.getString("CEP"));
                retorno.setFone1(consulta.getString("Fone1"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setFax(consulta.getString("Fax"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setSenhaWEB(consulta.getString("SenhaWEB"));
                retorno.setRamoAtiv(consulta.getString("RamoAtiv"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrdApoio(consulta.getString("PrdApoio"));
                retorno.setRisco(consulta.getString("Risco"));
                retorno.setMalotes(consulta.getInt("Malotes"));
                retorno.setNroChave(consulta.getString("NroChave"));
                retorno.setGrpChave(consulta.getInt("GrpChave"));
                retorno.setCGC(consulta.getString("CGC"));
                retorno.setIE(consulta.getString("IE"));
                retorno.setInsc_Munic(consulta.getString("Insc_Munic"));
                retorno.setCPF(consulta.getString("CPF"));
                retorno.setRG(consulta.getString("RG"));
                retorno.setRateioFat(consulta.getString("RateioFat"));
                retorno.setRateioTes(consulta.getString("RateioTes"));
                retorno.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                retorno.setDiaVencNF(consulta.getInt("DiaVencNF"));
                retorno.setRetencoesFat(consulta.getString("RetencoesFat"));
                retorno.setMarcaATM(consulta.getString("MarcaATM"));
                retorno.setRetorno(consulta.getString("Retorno"));
                retorno.setCheque(consulta.getString("Cheque"));
                retorno.setVr_A(consulta.getString("Vr_A"));
                retorno.setCed_A(consulta.getString("Ced_A"));
                retorno.setCed_AP(consulta.getString("Ced_AP"));
                retorno.setVr_B(consulta.getString("Vr_B"));
                retorno.setCed_B(consulta.getString("Ced_B"));
                retorno.setCed_BP(consulta.getString("Ced_BP"));
                retorno.setVr_C(consulta.getString("Vr_C"));
                retorno.setCed_C(consulta.getString("Ced_C"));
                retorno.setCed_CP(consulta.getString("Ced_CP"));
                retorno.setVr_D(consulta.getString("Vr_D"));
                retorno.setCed_D(consulta.getString("Ced_D"));
                retorno.setCed_DP(consulta.getString("Ced_DP"));
                retorno.setVr_E(consulta.getString("Vr_E"));
                retorno.setCed_E(consulta.getString("Ced_E"));
                retorno.setCed_EP(consulta.getString("Ced_EP"));
                retorno.setEndCob(consulta.getString("EndCob"));
                retorno.setCodCidCod(consulta.getString("CodCidCod"));
                retorno.setCidCob(consulta.getString("CidCob"));
                retorno.setUFCob(consulta.getString("UFCob"));
                retorno.setCEPCob(consulta.getString("CEPCob"));
                retorno.setEmailCob(consulta.getString("EmailCob"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setInterfExt(consulta.getString("InterfExt"));
                retorno.setCodExt(consulta.getString("CodExt"));
                retorno.setCodIntCli(consulta.getString("CodIntCli"));
                retorno.setCodPtoCli(consulta.getString("CodPtoCli"));
                retorno.setCercaElet(consulta.getString("CercaElet"));
                retorno.setDtSituacao(consulta.getString("DtSituacao"));
                retorno.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                retorno.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                retorno.setOper_Inc(consulta.getString("Oper_Inc"));
                retorno.setOper_Alt(consulta.getString("Oper_Alt"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setTipoPagto(consulta.getString("TipoPagto"));
                retorno.setLimite(consulta.getString("Limite"));
                retorno.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                retorno.setLimiteColeta(consulta.getString("LimiteColeta"));
                retorno.setProprietario(consulta.getString("Proprietario"));
                retorno.setRepresentante(consulta.getString("Representante"));
                retorno.setAtivEconomica(consulta.getString("AtivEconomica"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
                retorno.setEnvelope(consulta.getString("Envelope"));
                retorno.setPatrimonio(consulta.getString("Patrimonio"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClienteCPF - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1* FROM Clientes WHERE CPF = " + cpf + " AND NRed = " + nred + " AND CodFil = " + codfil);
        }
    }

    public Clientes buscarClienteNome(String nome, String nred, String codfil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1* FROM Clientes WHERE nome = ? AND nred = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nome);
            consulta.setString(nred);
            consulta.setString(codfil);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setBanco(consulta.getString("Banco"));
                retorno.setTpCli(consulta.getString("TpCli"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
                retorno.setLote(consulta.getString("Lote"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setNome(consulta.getString("Nome"));
                retorno.setEnde(consulta.getString("Ende"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCodCidade(consulta.getString("CodCidade"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setEstado(consulta.getString("Estado"));
                retorno.setCEP(consulta.getString("CEP"));
                retorno.setFone1(consulta.getString("Fone1"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setFax(consulta.getString("Fax"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setSenhaWEB(consulta.getString("SenhaWEB"));
                retorno.setRamoAtiv(consulta.getString("RamoAtiv"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrdApoio(consulta.getString("PrdApoio"));
                retorno.setRisco(consulta.getString("Risco"));
                retorno.setMalotes(consulta.getInt("Malotes"));
                retorno.setNroChave(consulta.getString("NroChave"));
                retorno.setGrpChave(consulta.getInt("GrpChave"));
                retorno.setCGC(consulta.getString("CGC"));
                retorno.setIE(consulta.getString("IE"));
                retorno.setInsc_Munic(consulta.getString("Insc_Munic"));
                retorno.setCPF(consulta.getString("CPF"));
                retorno.setRG(consulta.getString("RG"));
                retorno.setRateioFat(consulta.getString("RateioFat"));
                retorno.setRateioTes(consulta.getString("RateioTes"));
                retorno.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                retorno.setDiaVencNF(consulta.getInt("DiaVencNF"));
                retorno.setRetencoesFat(consulta.getString("RetencoesFat"));
                retorno.setMarcaATM(consulta.getString("MarcaATM"));
                retorno.setRetorno(consulta.getString("Retorno"));
                retorno.setCheque(consulta.getString("Cheque"));
                retorno.setVr_A(consulta.getString("Vr_A"));
                retorno.setCed_A(consulta.getString("Ced_A"));
                retorno.setCed_AP(consulta.getString("Ced_AP"));
                retorno.setVr_B(consulta.getString("Vr_B"));
                retorno.setCed_B(consulta.getString("Ced_B"));
                retorno.setCed_BP(consulta.getString("Ced_BP"));
                retorno.setVr_C(consulta.getString("Vr_C"));
                retorno.setCed_C(consulta.getString("Ced_C"));
                retorno.setCed_CP(consulta.getString("Ced_CP"));
                retorno.setVr_D(consulta.getString("Vr_D"));
                retorno.setCed_D(consulta.getString("Ced_D"));
                retorno.setCed_DP(consulta.getString("Ced_DP"));
                retorno.setVr_E(consulta.getString("Vr_E"));
                retorno.setCed_E(consulta.getString("Ced_E"));
                retorno.setCed_EP(consulta.getString("Ced_EP"));
                retorno.setEndCob(consulta.getString("EndCob"));
                retorno.setCodCidCod(consulta.getString("CodCidCod"));
                retorno.setCidCob(consulta.getString("CidCob"));
                retorno.setUFCob(consulta.getString("UFCob"));
                retorno.setCEPCob(consulta.getString("CEPCob"));
                retorno.setEmailCob(consulta.getString("EmailCob"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setInterfExt(consulta.getString("InterfExt"));
                retorno.setCodExt(consulta.getString("CodExt"));
                retorno.setCodIntCli(consulta.getString("CodIntCli"));
                retorno.setCodPtoCli(consulta.getString("CodPtoCli"));
                retorno.setCercaElet(consulta.getString("CercaElet"));
                retorno.setDtSituacao(consulta.getString("DtSituacao"));
                retorno.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                retorno.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                retorno.setOper_Inc(consulta.getString("Oper_Inc"));
                retorno.setOper_Alt(consulta.getString("Oper_Alt"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setTipoPagto(consulta.getString("TipoPagto"));
                retorno.setLimite(consulta.getString("Limite"));
                retorno.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                retorno.setLimiteColeta(consulta.getString("LimiteColeta"));
                retorno.setProprietario(consulta.getString("Proprietario"));
                retorno.setRepresentante(consulta.getString("Representante"));
                retorno.setAtivEconomica(consulta.getString("AtivEconomica"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
                retorno.setEnvelope(consulta.getString("Envelope"));
                retorno.setPatrimonio(consulta.getString("Patrimonio"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClienteCPF - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1* FROM Clientes WHERE nome = " + nome + " AND nred = " + nred + " AND CodFil = " + codfil);
        }
    }

    public Clientes buscarClienteNomeEndereco(String nome, String nred, String endereco, String codfil, Persistencia persistencia) throws Exception {
        try {
            Clientes retorno = null;
            String sql = " SELECT TOP 1* FROM Clientes WHERE (nome = ? OR nred = ?) AND Ende = ? AND CodFil = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nome);
            consulta.setString(nred);
            consulta.setString(endereco);
            consulta.setString(codfil);
            consulta.select();
            if (consulta.Proximo()) {
                retorno = new Clientes();
                retorno.setCodFil(consulta.getBigDecimal("CodFil"));
                retorno.setBanco(consulta.getString("Banco"));
                retorno.setTpCli(consulta.getString("TpCli"));
                retorno.setCodCli(consulta.getString("CodCli"));
                retorno.setAgencia(consulta.getString("Agencia"));
                retorno.setSubAgencia(consulta.getString("SubAgencia"));
                retorno.setLote(consulta.getString("Lote"));
                retorno.setNRed(consulta.getString("NRed"));
                retorno.setNome(consulta.getString("Nome"));
                retorno.setEnde(consulta.getString("Ende"));
                retorno.setBairro(consulta.getString("Bairro"));
                retorno.setCodCidade(consulta.getString("CodCidade"));
                retorno.setCidade(consulta.getString("Cidade"));
                retorno.setEstado(consulta.getString("Estado"));
                retorno.setCEP(consulta.getString("CEP"));
                retorno.setFone1(consulta.getString("Fone1"));
                retorno.setFone2(consulta.getString("Fone2"));
                retorno.setFax(consulta.getString("Fax"));
                retorno.setContato(consulta.getString("Contato"));
                retorno.setEmail(consulta.getString("Email"));
                retorno.setSenhaWEB(consulta.getString("SenhaWEB"));
                retorno.setRamoAtiv(consulta.getString("RamoAtiv"));
                retorno.setRegiao(consulta.getString("Regiao"));
                retorno.setLatitude(consulta.getString("Latitude"));
                retorno.setLongitude(consulta.getString("Longitude"));
                retorno.setPrdApoio(consulta.getString("PrdApoio"));
                retorno.setRisco(consulta.getString("Risco"));
                retorno.setMalotes(consulta.getInt("Malotes"));
                retorno.setNroChave(consulta.getString("NroChave"));
                retorno.setGrpChave(consulta.getInt("GrpChave"));
                retorno.setCGC(consulta.getString("CGC"));
                retorno.setIE(consulta.getString("IE"));
                retorno.setInsc_Munic(consulta.getString("Insc_Munic"));
                retorno.setCPF(consulta.getString("CPF"));
                retorno.setRG(consulta.getString("RG"));
                retorno.setRateioFat(consulta.getString("RateioFat"));
                retorno.setRateioTes(consulta.getString("RateioTes"));
                retorno.setDiaFechaFat(consulta.getInt("DiaFechaFat"));
                retorno.setDiaVencNF(consulta.getInt("DiaVencNF"));
                retorno.setRetencoesFat(consulta.getString("RetencoesFat"));
                retorno.setMarcaATM(consulta.getString("MarcaATM"));
                retorno.setRetorno(consulta.getString("Retorno"));
                retorno.setCheque(consulta.getString("Cheque"));
                retorno.setVr_A(consulta.getString("Vr_A"));
                retorno.setCed_A(consulta.getString("Ced_A"));
                retorno.setCed_AP(consulta.getString("Ced_AP"));
                retorno.setVr_B(consulta.getString("Vr_B"));
                retorno.setCed_B(consulta.getString("Ced_B"));
                retorno.setCed_BP(consulta.getString("Ced_BP"));
                retorno.setVr_C(consulta.getString("Vr_C"));
                retorno.setCed_C(consulta.getString("Ced_C"));
                retorno.setCed_CP(consulta.getString("Ced_CP"));
                retorno.setVr_D(consulta.getString("Vr_D"));
                retorno.setCed_D(consulta.getString("Ced_D"));
                retorno.setCed_DP(consulta.getString("Ced_DP"));
                retorno.setVr_E(consulta.getString("Vr_E"));
                retorno.setCed_E(consulta.getString("Ced_E"));
                retorno.setCed_EP(consulta.getString("Ced_EP"));
                retorno.setEndCob(consulta.getString("EndCob"));
                retorno.setCodCidCod(consulta.getString("CodCidCod"));
                retorno.setCidCob(consulta.getString("CidCob"));
                retorno.setUFCob(consulta.getString("UFCob"));
                retorno.setCEPCob(consulta.getString("CEPCob"));
                retorno.setEmailCob(consulta.getString("EmailCob"));
                retorno.setObs(consulta.getString("Obs"));
                retorno.setSituacao(consulta.getString("Situacao"));
                retorno.setInterfExt(consulta.getString("InterfExt"));
                retorno.setCodExt(consulta.getString("CodExt"));
                retorno.setCodIntCli(consulta.getString("CodIntCli"));
                retorno.setCodPtoCli(consulta.getString("CodPtoCli"));
                retorno.setCercaElet(consulta.getString("CercaElet"));
                retorno.setDtSituacao(consulta.getString("DtSituacao"));
                retorno.setDt_Cad(consulta.getLocalDate("Dt_Cad"));
                retorno.setDt_Alter(consulta.getLocalDate("Dt_Alter"));
                retorno.setHr_Alter(consulta.getString("Hr_Alter"));
                retorno.setDt_UltMov(consulta.getLocalDate("Dt_UltMov"));
                retorno.setOper_Inc(consulta.getString("Oper_Inc"));
                retorno.setOper_Alt(consulta.getString("Oper_Alt"));
                retorno.setCodigo(consulta.getString("Codigo"));
                retorno.setCCusto(consulta.getString("CCusto"));
                retorno.setTipoPagto(consulta.getString("TipoPagto"));
                retorno.setLimite(consulta.getString("Limite"));
                retorno.setLimiteSeguro(consulta.getString("LimiteSeguro"));
                retorno.setLimiteColeta(consulta.getString("LimiteColeta"));
                retorno.setProprietario(consulta.getString("Proprietario"));
                retorno.setRepresentante(consulta.getString("Representante"));
                retorno.setAtivEconomica(consulta.getString("AtivEconomica"));
                retorno.setCodCofre(consulta.getString("CodCofre"));
                retorno.setEnvelope(consulta.getString("Envelope"));
                retorno.setPatrimonio(consulta.getString("Patrimonio"));
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarClienteCPF - " + e.getMessage() + "\r\n"
                    + " SELECT TOP 1* FROM Clientes WHERE nome = " + nome + " AND nred = " + nred + " AND Ende = " + endereco + " AND CodFil = " + codfil);
        }
    }

    public List<GTVeAcesso> buscarChaveAcesso(String codCli, String codFil, Persistencia persistenciaLocal, Persistencia persistenciaCentral) throws Exception {
        String sql = "";
        GTVeAcesso gtveAcesso;
        List<GTVeAcesso> retorno = new ArrayList<>();
        PessoaDao pessoaDao = new PessoaDao();

        try {
            sql = "SELECT \n"
                    + "*\n"
                    + "FROM GTVeAcesso\n"
                    + "WHERE Parametro = ?\n"
                    + "AND   CodCli    = ?\n"
                    + "AND   CodFil    = ?\n"
                    + "AND   (flag_excl IS NULL OR flag_excl = '')";

            Consulta consulta = new Consulta(sql, persistenciaCentral);
            consulta.setString(persistenciaLocal.getEmpresa());
            consulta.setString(codCli);
            consulta.setString(codFil);
            consulta.select();

            while (consulta.Proximo()) {
                gtveAcesso = new GTVeAcesso();

                gtveAcesso.setCodPessoa(consulta.getString("CodPessoa"));
                gtveAcesso.setChave(consulta.getString("Chave"));

                Pessoa pessoa = pessoaDao.buscarPessoaCodigo(consulta.getBigDecimal("CodPessoa"), persistenciaLocal);
                gtveAcesso.setNomePessoa(pessoa.getNome());
                gtveAcesso.setPwweb(pessoa.getPWWeb());

                retorno.add(gtveAcesso);
            }

            consulta.close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesDao.buscarChaveAcesso - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void criarChaveAcesso(String codCli, String codPessoa, String codFil, String operador, Persistencia persistenciaLocal, Persistencia persistenciaCentral) throws Exception {
        String sql = "";
        try {
            sql = "DECLARE @ChaveAcesso VARCHAR(255);\n"
                    + " SET @ChaveAcesso = (SELECT\n"
                    + "                     'S0100' + CONVERT(VARCHAR, (MAX(RIGHT(Chave,3)) + 1)) Chave\n"
                    + "                     FROM GTVeAcesso);";

            sql += " INSERT INTO GTVeAcesso(Chave, Parametro, Codfil, Codcli, codPessoa, Oper_Inc, Dt_Inc, Hr_Inc, dt_valid, Operador, Dt_Alter, Hr_Alter) VALUES("
                    + "@ChaveAcesso, ?,?,?,?,?,CONVERT(DATE, GETDATE()),LEFT(CONVERT(TIME, GETDATE()),5),CONVERT(DATE, DATEADD(year, 2, GETDATE())),?,CONVERT(DATE, GETDATE()),LEFT(CONVERT(TIME, GETDATE()),5))";

            Consulta consulta = new Consulta(sql, persistenciaCentral);
            consulta.setString(persistenciaLocal.getEmpresa());
            consulta.setString(codFil);
            consulta.setString(codCli);
            consulta.setString(codPessoa);
            consulta.setString(operador);
            consulta.setString(operador);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.criarChaveAcesso - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public void excluirChaveAcesso(String codCli, String codPessoa, String codFil, String operador, Persistencia persistenciaLocal, Persistencia persistenciaCentral) throws Exception {
        String sql = "";
        try {
            sql = " UPDATE GTVeAcesso SET flag_excl = '*', Oper_Excl = ?, Dt_Excl = CONVERT(DATE, GETDATE()), Hr_Excl = LEFT(CONVERT(TIME, GETDATE()),5)  WHERE codPessoa = ? AND Parametro = ? AND codFil = ? AND codCli = ?";

            Consulta consulta = new Consulta(sql, persistenciaCentral);
            consulta.setString(operador);
            consulta.setString(codPessoa);
            consulta.setString(persistenciaLocal.getEmpresa());
            consulta.setString(codFil);
            consulta.setString(codCli);

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesDao.excluirChaveAcesso - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
