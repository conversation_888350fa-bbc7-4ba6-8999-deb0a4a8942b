/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * AND open the template in the editor.
 */
package SasDaos;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.Paramet;
import SasBeans.Rt_Guias;
import SasBeansCompostas.EGtv;
import SasBeansCompostas.GuiasPortal;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class EGtvDao {

    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    /**
     * Insere uma nova entrada na tabela EGtv
     *
     * @param guia
     * @param serie
     * @param data
     * @param hora
     * @param persistencia
     * @throws Exception
     */
    public void inserirEGtv(String guia, String serie, String data, String hora, Persistencia persistencia) throws Exception {
        try {
            String sql = " INSERT INTO EGTV VALUES (?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(data);
            consulta.setString(hora);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("EGtvDAo.inserirEGtv - " + e.getMessage() + "\r\n"
                    + "INSERT INTO EGTV VALUES (" + guia + ", " + serie + ", " + data + ", " + hora + ")");
        }
    }

    public List<EGtv> lista(Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY Rotas.Data DESC, Rt_Perc.HrCheg DESC) AS RowNum, \n"
                    + " Rotas.Data, CONVERT(BigInt,Pedido.PedidoCliente) Pedido, CONVERT(BigInt,RPV.Guia) Guia, RPV.Serie, \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN 'Alívio' WHEN Rt_Perc.ER = 'E' THEN 'Alívio' \n"
                    + " WHEN Rt_Perc.ER = 'A' THEN 'A.Tecnica' ELSE '' END Operacao, \n"
                    + " OS_Vig.NRedFat, Rt_Perc.CodCli1, Clientes.NRed LocalParada, Clientes.Agencia AgenciaParada, rt_perc.HrCheg, Rt_Perc.Hrsaida, \n"
                    + " CASE WHEN OS_Vig.Cliente = Rt_Perc.CodCli1 THEN CASE WHEN Rt_Perc.ER = 'E' THEN '' ELSE OS_Vig.NRedDst END  ELSE \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN OS_Vig.NRed ELSE '' END END AS Destino, \n"
                    + " Case when OS_Vig.Cliente = Rt_Perc.CodCli1 then case when Rt_Perc.ER = 'E' then '' else OS_Vig.CliDst end  else \n"
                    + " Case when Rt_Perc.ER = 'R' then OS_Vig.Cliente else '' end end as CodCliDestino,  \n"
                    + " (SELECT top 1 RTP.HrCheg +' - '+ RTP.HrSaida FROM RPV RPVD \n"
                    + "   LEFT JOIN Rt_Perc RTP ON  RTP.Sequencia = RPVD.SeqRota \n"
                    + "                         AND RTP.Parada    = RPVD.Parada \n"
                    + "   LEFT JOIN Rotas RT ON Rt.Sequencia = RPVD.SeqRota  \n"
                    + "   WHERE RPVD.Guia = RPV.Guia AND RPVD.Serie = RPV.Serie  \n"
                    + "     AND RTP.ER = 'E' \n"
                    + "     AND Rt_Perc.ER = 'R'  \n"
                    + "  ORDER BY Rotas.Data DESC, RTP.Hora1 DESC) Entrega, Round(RPV.Valor, 2) Valor,  \n"
                    + " (SELECT COUNT(*) FROM CxfGuiasVol WHERE CxfGuiasVol.Guia = RPV.Guia AND CxfGuiasVol.Serie = RPV.Serie) Volumes,  \n"
                    + " (SELECT TOP 1 Lacre FROM CxfGuiasVol WHERE CxfGuiasVol.Guia = RPV.Guia AND CxfGuiasVol.Serie = RPV.Serie) Volume1, \n"
                    + " CASE WHEN Pessoa.Matr > 0 THEN 'Chefe Equipe: '+Pessoa.Nome ELSE Pessoa.Nome END Assinatura,  \n"
                    + " (SELECT top 1 CASE WHEN PessoaD.Matr > 0 THEN 'Chefe Equipe: '+PessoaD.Nome ELSE PessoaD.Nome END FROM RPV RPVD \n"
                    + "   LEFT JOIN Rt_Perc RTP ON  RTP.Sequencia = RPVD.SeqRota \n"
                    + "                         AND RTP.Parada    = RPVD.Parada \n"
                    + "   LEFT JOIN Rotas RT ON Rt.Sequencia = RPVD.SeqRota  \n"
                    + "   LEFT JOIN Pessoa PessoaD ON PessoaD.Codigo = RPVD.CodPessoaAut \n"
                    + "   WHERE RPVD.Guia = RPV.Guia AND RPVD.Serie = RPV.Serie  \n"
                    + "     AND RTP.ER = 'E' \n"
                    + "     AND Rt_Perc.ER = 'R'  \n"
                    + "  ORDER BY Rotas.Data DESC, RTP.Hora1 DESC) AssinaturaDestino,  \n"
                    + " (SELECT Rt_Perc.Parada FROM RPV RPVD  \n"
                    + "   LEFT JOIN Rt_Perc RTP ON  RTP.Sequencia = RPVD.SeqRota  \n"
                    + "                          AND RTP.Parada    = RPVD.Parada  \n"
                    + " LEFT JOIN Rotas RT ON Rt.Sequencia = RPVD.SeqRota   \n"
                    + " LEFT JOIN Pessoa PessoaD ON PessoaD.Codigo = RPVD.CodPessoaAut \n"
                    + "    WHERE RPVD.Guia = RPV.Guia AND RPVD.Serie = RPV.Serie   \n"
                    + "     AND RTP.ER = 'E'  \n"
                    + "     AND Rt_Perc.ER = 'R') ParadaD, \n"
                    + " OS_Vig.OS, OS_Vig.Viacxf, CxFGuias.DtEnt DtEntCxf, CxFGuias.HrEnt HrEntCxf, \n"
                    + " CxfGuias.DtSai DtSaiCxF, CxfGuias.Hrsai HrSaiCxf, TesEntrada.Dt_incl DtEntTes, tesEntrada.Hr_Incl HrEntTes, TesSaidasCxf.Dt_Alter DtSaiTes,  \n"
                    + " TesSaidasCxf.hr_Alter HrSaiTes  \n"
                    + " FROM RPV \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = RPV.seqRota \n"
                    + " LEFT JOIN Rt_Guias ON  Rt_Guias.Guia  = RPV.Guia \n"
                    + "                    AND Rt_Guias.Serie = RPV.Serie \n"
                    + " LEFT JOIN OS_Vig ON OS_Vig.OS = Rt_Guias.OS \n"
                    + "                  AND OS_Vig.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Rt_Perc ON  rt_perc.Sequencia = rpv.SeqRota \n"
                    + "                   AND rt_Perc.Parada   = RPV.Parada \n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = Rt_Perc.codcli1 \n"
                    + "                    AND Clientes.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN TesEntrada ON  TesEntrada.CodFil  = Rotas.CodFil"
                    + "                      AND TesEntrada.Guia  = RPV.Guia \n"
                    + "                      AND TesEntrada.Serie = RPV.Serie \n"
                    + " LEFT JOIN TesSaidas ON TesSaidas.Guia = RPV.Guia \n"
                    + "                     AND TesSaidas.Serie = RPV.Serie \n"
                    + " LEFT JOIN TesSaidasCxf ON  TesSaidas.Guia  = TesSaidasCxF.Guia  \n"
                    + "                        AND TesSaidas.Serie = TesSaidasCxF.Serie \n"
                    + " LEFT JOIN CxfGuias ON cxfguias.Guia = RPV.Guia \n"
                    + "                    AND CxfGuias.Serie = RPV.Serie \n"
                    + " LEFT JOIN Pedido ON Pedido.Numero = TesSaidas.Pedido \n"
                    + "                  AND Pedido.CodFil = TesSaidas.CodFil \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = RPV.CodPessoaAut \n"
                    + " WHERE ((Rt_Perc.Er = 'R') OR ";
            if (isTranspCacamba(persistencia.getEmpresa())) {
                sql = sql + "(Rt_Perc.ER = 'E')) AND ";
            } else {
                sql = sql + "(Rt_Perc.ER = 'E' AND CxfGuias.DtSai IS NOT NULL)) AND ";
            }

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " rpv.flag_excl != '*') "
                    + " AS RowConstrainedResult "
                    //                    + " WHERE RowNum >= ? "
                    //                    + "   AND RowNum < ? "
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }
//            
//            consulta.setInt(primeiro+1);
//            consulta.setInt(primeiro+1 + linhas);
            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
                eGtv.setNRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCli1(consulta.getString("CodCli1"));
                eGtv.setLocalParada(consulta.getString("LocalParada"));
                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
                eGtv.setVolumes(consulta.getString("Volumes"));
                eGtv.setVolume1(consulta.getString("Volume1"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
//                eGtv.setParadaD(consulta.getString("ParadaD"));
                eGtv.setOS(consulta.getString("OS"));
                eGtv.setViacxf(consulta.getString("Viacxf"));
                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.lista - " + e.getMessage());
        }
    }

    public List<EGtv> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            Map<String, String> filtro = filtros;
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * from (\n"
                    + "(Select \n"
                    + " Rt_Perc.ER,\n"
                    //+ " Rt_perc.DPar ParadaD,\n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " Rotas.Rota,\n"
                    + " Escala.Veiculo VeiculoOri,\n"
                    + " Escala.Veiculo VeiculoDst,\n"
                    /*+ " \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    Escala.Veiculo\n"
                    + "else \n"
                    + "    Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%' then \n"
                    + "        Escala.Veiculo\n"
                    + "    else \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "    end \n"
                    + "end VeiculoDst,\n"*/
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Parada \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n "
                    + "               then Rt_perc.DPar\n"
                    + "               else RtP_Parada_Cfx.Parada \n"
                    + "          end \n"
                    + "end Parada_E,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Sequencia \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then Rt_Perc.Sequencia\n"
                    + "               else RtP_Parada_Cfx.Sequencia \n"
                    + "          end \n"
                    + "end Sequencia_E,\n"
                    + "Rt_Perc.Parada, \n"
                    + " Rt_Perc.Sequencia, \n"
                    + "cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Vol, \n"
                    + " CxfGuiasVol.Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data      else TesSaidas.Dt_Alter end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg  else TesSaidas.Hr_Alter end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else TesSaidas.Hr_Alter end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not NULL then OS_Vig.NRed else OSPre.NRedDst end end NRedOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Ende else Case when Rt_Perc.ER is not NULL then CliOri.Ende else CliDstPre.Ende  end end EndOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Cidade else Case when Rt_Perc.ER is not NULL then CliOri.Cidade else CliDstPre.Cidade  end end CidadeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Bairro else Case when Rt_Perc.ER is not NULL then CliOri.Bairro else CliDstPre.Bairro  end end BairroOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Estado else Case when Rt_Perc.ER is not NULL then CliOri.Estado else CliDstPre.Estado  end end EstadoOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.email else Case when Rt_Perc.ER is not NULL then CliOri.email else CliDstPre.email  end end EmailOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Codigo else Case when Rt_Perc.ER is not NULL then CliOri.Codigo else CliDstPre.Codigo  end end CodCliOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Nome else Case when Rt_Perc.ER is not NULL then CliOri.Nome else CliDstPre.Nome  end end NomeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "        case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "    else Case when Rt_Perc.ER is not NULL then \n"
                    + "        case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end \n"
                    + "    else \n"
                    + "        case when (CliDstPre.CGC is null or CliDstPre.CGC = '') then CliDstPre.CPF else CliDstPre.CGC end \n"
                    + "    end end RegistroOri, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + " RotasCfxG.Data end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino,  \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end NRedDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Ende else CliDst.Ende end EndDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cidade else CliDst.Cidade end CidadeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Bairro else CliDst.Bairro end BairroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Estado else CliDst.Estado end EstadoDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.email else CliDst.email end EmailDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Codigo else CliDst.Codigo end CodCliDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cep else CliDst.Cep end cepDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Nome else CliDst.Nome end NomeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "else \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "end RegistroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       RtP_Parada.Hora1 else \n"
                    + "   RtP_Parada_Cfx.Hora1 end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       RtP_Parada.HrCheg else \n"
                    + "   RtP_Parada_Cfx.HrCheg end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       RtP_Parada.HrSaida else \n"
                    + "   RtP_Parada_Cfx.HrSaida end end HrCheg_S, \n"
                    + "RPV.Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino,  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino, \n"                                        
                    + "Case when Rt_PercSLA.HrSaidaVei IS NOT NULL AND Rt_PercSLA.HrSaidaVei <> '' AND Rt_Perc.hrCheg IS NOT NULL AND Rt_Perc.hrCheg <> '' then 'F' else 'P' end StatusGuia, \n"
                    + "   (SELECT TOP 1\n"
                    + "    MIN(Teste.Hora1) AS Hora1\n"
                    + "    FROM rt_perc       AS Teste\n"
                    + "    WHERE Teste.sequencia = Rt_Perc.Sequencia\n"
                    + "    AND   Teste.HrCheg  IS NULL\n"
                    + "    AND   Teste.HrSaida IS NULL\n"
                    + "    AND   Teste.flag_excl <> '*'  \n"
                    + "    GROUP BY Teste.sequencia\n"
                    + "    ORDER BY Hora1) AS ProximaHora, Rt_Perc.Hora1 AS Hora1Comparacao, Rt_GuiasMoeda.Moeda, \n"
                    + " CASE WHEN Rt_guias.Sequencia IS NOT NULL THEN 'E' ELSE 'P' END statusGuiaEmissao,\n"
                    + " Rt_guias.Operador\n"
                    + " from RPV\n"
                    + " LEFT JOIN TesSaidas\n"
                    + "                   ON TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "LEFT JOIN Rt_perc RtP_Parada on RtP_Parada.Sequencia = Rt_Perc.Sequencia\n"
                    + "                            AND RtP_Parada.Parada    = Rt_perc.DPar\n"
                    + "                            AND RtP_Parada.Flag_Excl <> '*'\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + " LEFT JOIN Escala ON Escala.SeqRota = Rotas.Sequencia\n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "LEFT JOIN Rt_perc RtP_Parada_Cfx on RtP_Parada_Cfx.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                                AND RtP_Parada_Cfx.Hora1     = CxfGuias.Hora1D\n"
                    + "                                AND RtP_Parada_Cfx.Flag_Excl <> '*'\n"
                    + " left join Rotas RotasCfxG on  RotasCfxG.Sequencia = CxfGuias.SeqRotaSai \n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                         AND CliFat.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "left join Rt_PercSLA on Rt_PercSLA.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND Rt_PercSLA.Parada    = Rt_guias.Parada\n"
                    + "left join CxfGuiasVol on CxfGuiasVol.Guia = RPV.Guia \n"
                    + "                    and CxfGuiasVol.Serie = RPV.Serie\n"
                    + "                    and CxfGuiasVol.lacre = ";
            if (filtro.get("Lacre").equals("")) {
                sql += "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia and Serie = RPV.Serie) \n";
            } else {
                sql += "? \n";
            }
            sql += "where RPV.Data BETWEEN ? AND ?\n"
                    + " AND RPV.flag_excl <> '*'";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  ("
                        + "(OS_Vig.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OS_Vig.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil))\n"
                        + " OR (OSPre.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OSPre.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil))\n"
                        + ") \n";
            } else {
                sql += " AND  ((OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) OR (OSPre.Cliente = ? or OSPre.CliDst = ?))\n";
            }
            if (!filtro.get("Guia").equals("")) {
                sql += " AND RPV.Guia = ? \n";
            }
            if (!filtro.get("Lacre").equals("")) {
                sql += " AND CxfGuiasVol.Lacre = ?";
            }
            sql += " AND (Rt_perc.Sequencia is not null or RtEnt.Sequencia is null)\n"
                    + " AND (PreOrder.Guia is null OR RtEnt.Sequencia is null)\n"
                    + " )\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT \n"
                    + " Rt_PercPro.ER,\n"
                    //+ " Rt_perc.DPar ParadaD, \n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " Rotas.Rota,\n"
                    + " Escala.Veiculo VeiculoOri,\n"
                    + " \n"
                    + " Escala.Veiculo VeiculoDst,\n"
                    /*+ "Case when Rt_PercPro.ER = 'E' then \n"
                    + "    (Select veiculo from escala WHERE Escala.SeqRota = Rt_PercPro.Sequencia and Rt_PercPro.Flag_Excl <> '*')\n"
                    + "else \n"
                    + "    Case When Rt_PercPro.CodCli2 not like '641%' and Rt_PercPro.CodCli2 not like '999%' then \n"
                    + "        Escala.Veiculo\n"
                    + "    else \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "    end \n"
                    + "end VeiculoDst,\n"*/
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "Case when Rt_PercPro.ER = 'E' \n"
                    + "     then Rt_PercPro.Parada \n"
                    + "     else Case When Rt_PercPro.CodCli2 not like '641%' and Rt_PercPro.CodCli2 not like '999%'\n"
                    + "               then Rt_PercPro.DPar\n"
                    + "               else RtP_Parada_Cfx.Parada \n"
                    + "          end \n"
                    + "end Parada_E,\n"
                    + "Case when Rt_PercPro.ER = 'E' \n"
                    + "     then Rt_PercPro.Sequencia \n"
                    + "     else Case When Rt_PercPro.CodCli2 not like '641%' and Rt_PercPro.CodCli2 not like '999%'\n"
                    + "               then Rt_PercPro.Sequencia\n"
                    + "               else RtP_Parada_Cfx.Sequencia \n"
                    + "          end \n"
                    + "end Sequencia_E,\n"
                    + "Rt_PercPro.Parada, \n"
                    + " Rt_PercPro.Sequencia, \n"
                    + "cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, CONVERT(BigInt, Rt_guias.Guia) Guia, Rt_guias.Serie, \n"
                    + "(Select Count(*) from CxfGuiasVol where guia = Rt_guias.Guia AND Serie = Rt_guias.Serie) Vol, \n"
                    + " CxfGuiasVol.Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.Os OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + " case when rt_guias.guia IS NOT NULL AND rt_guias.guia <> '' then Rotas.Data else null end DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Rt_guias.Guia AND Serie = Rt_guias.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Rt_guias.Guia AND Serie = Rt_guias.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end NRedOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Ende else CliOri.Ende end EndOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Cidade else CliOri.Cidade end CidadeOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Bairro else CliOri.Bairro end BairroOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Estado else  CliOri.Estado end EstadoOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.email else  CliOri.email end EmailOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Codigo else CliOri.Codigo end CodCliOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Nome else CliOri.Nome end NomeOri, \n"
                    + "Case when Rt_PercPro.ER = 'E' then \n"
                    + "        case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "    else \n"
                    + "        case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "    end RegistroOri, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino,  \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end NRedDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Ende else CliDst.Ende end EndDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Cidade else CliDst.Cidade end CidadeDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Bairro else CliDst.Bairro end BairroDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Estado else CliDst.Estado end EstadoDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.email else CliDst.email end EmailDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Codigo else CliDst.Codigo end CodCliDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Cep else CliDst.Cep end cepDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Nome else CliDst.Nome end NomeDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "else \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "end RegistroDst, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else Rt_PercPro.Hora1 end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else Rt_PercPro.HrCheg end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else Rt_PercPro.HRSaida end HrSaida_E, Rt_guias.Valor, Rt_PercPro.CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino,  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino,\n"                                        
                    + "Case when Rt_PercSLA.HrSaidaVei IS NOT NULL then 'F' else 'P' end StatusGuia, \n"
                    + "   (SELECT TOP 1 \n"
                    + "    MIN(X.Hora1) AS Hora1\n"
                    + "    FROM rt_perc       AS X\n"
                    + "    WHERE X.sequencia = Rt_PercPro.Sequencia\n"
                    + "    AND   X.HrCheg  IS NULL\n"
                    + "    AND   X.HrCheg  <> ''\n"
                    + "    AND   X.HrSaida IS NULL\n"
                    + "    AND   X.HrSaida <> ''\n"
                    + "    AND   X.flag_excl <> '*'  \n"
                    + "    GROUP BY X.sequencia\n"
                    + "    ORDER BY Hora1) AS ProximaHora, Rt_PercPro.Hora1 AS Hora1Comparacao, Rt_GuiasMoeda.Moeda, \n"
                    + " CASE WHEN Rt_guias.Sequencia IS NOT NULL THEN 'E' ELSE 'P' END statusGuiaEmissao,\n"
                    + " Rt_guias.Operador\n"
                    + "from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + " LEFT JOIN Escala ON Escala.SeqRota = Rotas.Sequencia\n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    /*+ "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"*/
                    + "left join OS_Vig   on  OS_Vig.OS     = Rt_PercPro.OS\n" // Alterado de PreOrder para RT_Perc, após análise com Carlos em 13/04/21
                    + "                   AND OS_VIg.CodFil = Rt_PercPro.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                         AND CliFat.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.sequencia = Rt_PercPro.sequencia  \n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rt_guias.Guia \n"
                    + "                   AND CxfGuias.Serie = Rt_guias.Serie\n"
                    + "LEFT JOIN Rt_perc RtP_Parada_Cfx on RtP_Parada_Cfx.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                                AND RtP_Parada_Cfx.Hora1     = CxfGuias.Hora1D\n"
                    + "                                AND RtP_Parada_Cfx.Flag_Excl <> '*'\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Rt_PercSLA on Rt_PercSLA.Sequencia = Rt_PercPro.Sequencia\n"
                    + "                    AND Rt_PercSLA.Parada    = Rt_PercPro.Parada\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = Rt_guias.Guia AND RPVX.Serie = Rt_guias.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = Rt_guias.Guia AND RPVX.Serie = Rt_guias.Serie AND Rt_Perc.ER = 'E') \n"
                    + "\n"
                    + "LEFT JOIn RPV ON RPV.Guia  = Rt_guias.Guia \n"
                    + "             AND RPV.Serie = Rt_guias.Serie\n"
                    + "left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_guias.Guia \n"
                    + "                    and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "                    and CxfGuiasVol.lacre = ";
            if (filtro.get("Lacre").equals("")) {
                sql += "(Select Min(Lacre)from CxfGuiasVol where guia = Rt_guias.Guia and Serie = Rt_guias.Serie) \n";
            } else {
                sql += "? \n";
            }
            sql += "where Rotas.Data BETWEEN ? AND ?\n"
                    + " AND Rt_PercPro.Flag_Excl <> '*'"
                    + " AND RPV.seqrota is null AND Rotas.Rota <> '090'";

            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OS_Vig.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) \n";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            if (!filtro.get("Guia").equals("")) {
                sql += " AND Rt_guias.Guia = ? \n";
            }
            if (!filtro.get("Lacre").equals("")) {
                sql += " AND CxfGuiasVol.Lacre = ?";
            }
            sql += "  AND Rt_PercPro.Codcli1 NOT IN(select CodCli FROM cxforte WHERE CodFil = Rotas.CodFil) /*AND  Rt_guias.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)*/\n"
                    + " )\n"
                    + ")a) AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? \n"
                    + "   AND RowNum < ?\n";
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            sql += " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            if (!filtro.get("Lacre").equals("")) {
                consulta.setString(filtro.get("Lacre"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            if (!filtro.get("Guia").equals("")) {
                consulta.setString(filtro.get("Guia"));
            }
            if (!filtro.get("Lacre").equals("")) {
                consulta.setString(filtro.get("Lacre"));
            }

            // UNION
            if (!filtro.get("Lacre").equals("")) {
                consulta.setString(filtro.get("Lacre"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            if (!filtro.get("Guia").equals("")) {
                consulta.setString(filtro.get("Guia"));
            }
            if (!filtro.get("Lacre").equals("")) {
                consulta.setString(filtro.get("Lacre"));
            }
            /*consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));*/

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();
            CxFGuiasVolDao daoCxForte = new CxFGuiasVolDao();
            String Lacres = "";
            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
                eGtv.setOperador(consulta.getString("Operador"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));
                eGtv.setOS(consulta.getString("OSPreOrder"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));

                Lacres = "";

                if (null != consulta.getString("Guia") && !consulta.getString("Guia").equals("") && !consulta.getString("Guia").equals("0")) {
                    for (CxFGuiasVol guiaItem : daoCxForte.listaDeVolumesPreOrder(consulta.getString("Guia"), consulta.getString("Serie"), persistencia)) {
                        if (guiaItem.getLacre() != null && !guiaItem.getLacre().equals("")) {
                            if (!Lacres.equals("")) {
                                Lacres += " / ";
                            }

                            Lacres += guiaItem.getLacre();
                        }
                    }
                }
                eGtv.setLacre(Lacres);
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));

                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                eGtv.setnRedDst(consulta.getString("NRedDst"));
                eGtv.setEndDst(consulta.getString("EndDst"));
                eGtv.setNomeDst(consulta.getString("NomeDst"));
                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                eGtv.setBairroDst(consulta.getString("BairroDst"));
                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                eGtv.setEmailDst(consulta.getString("EmailDst"));

                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                eGtv.setMoeda(consulta.getString("Moeda"));

                eGtv.setStatusGuia(consulta.getString("statusGuiaEmissao"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuias(String sequencia, String parada, Persistencia persistencia) throws Exception, Exception, Exception, Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * from \n"
                    + "(Select \n"
                    + " Rt_Perc.ER, Rt_Perc.TipoSrv, \n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + " Rotas.Rota,\n"
                    + "(SELECT TOP 1 Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + " \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    (Select TOP 1 veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*')\n"
                    + "else \n"
                    + "    Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%' then \n"
                    + "        (Select TOP 1 veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "    else \n"
                    + "        (Select TOP 1 veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "    end \n"
                    + "end VeiculoDst,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Parada \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select TOP 1 RtP.Parada from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select TOP 1 RtP.Parada from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Parada_E,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Sequencia \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select TOP 1 RtP.Sequencia from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select TOP 1 RtP.Sequencia from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Sequencia_E,\n"
                    + "Rt_Perc.Parada, \n"
                    //+ "Rt_perc.DPar ParadaD, \n" 
                    + " Rt_Perc.Sequencia, \n"
                    + "cxfguias.DtEnt, Pedido.PedidoCliente Pedido, Pedido2.PedidoCliente, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Vol, \n"
                    + " CxfGuiasVol.Lacre, Pedido.Numero, ISNULL(CASE WHEN Rt_Guias.OS <> 0 THEN Rt_Guias.OS ELSE NULL END,Rt_Perc.OS) OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TOP 1 TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else ISNULL((select TOP 1 TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie),Rt_Perc.HrCheg) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else ISNULL((Select TOP 1 TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie),Rt_Perc.HrSaida) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not NULL then OS_Vig.NRed else OSPre.NRedDst end end NRedOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Ende else Case when Rt_Perc.ER is not NULL then CliOri.Ende else CliDstPre.Ende  end end EndOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Cidade else Case when Rt_Perc.ER is not NULL then CliOri.Cidade else CliDstPre.Cidade  end end CidadeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Bairro else Case when Rt_Perc.ER is not NULL then CliOri.Bairro else CliDstPre.Bairro  end end BairroOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Estado else Case when Rt_Perc.ER is not NULL then CliOri.Estado else CliDstPre.Estado  end end EstadoOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.email else Case when Rt_Perc.ER is not NULL then CliOri.email else CliDstPre.email  end end EmailOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Codigo else Case when Rt_Perc.ER is not NULL then CliOri.Codigo else CliDstPre.Codigo  end end CodCliOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Nome else Case when Rt_Perc.ER is not NULL then CliOri.Nome else CliDstPre.Nome  end end NomeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "else Case when Rt_Perc.ER is not NULL then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end \n"
                    + "else \n"
                    + "    case when (CliDstPre.CGC is null or CliDstPre.CGC = '') then CliDstPre.CPF else CliDstPre.CGC end \n"
                    + "end end RegistroOri, \n"
                    + "\n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select TOP 1 RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end NRedDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Ende else CliDst.Ende end EndDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cidade else CliDst.Cidade end CidadeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Bairro else CliDst.Bairro end BairroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Estado else CliDst.Estado end EstadoDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.email else CliDst.email end EmailDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Codigo else CliDst.Codigo end CodCliDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cep else CliDst.Cep end cepDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Nome else CliDst.Nome end NomeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "else \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "end RegistroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select TOP 1 RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select TOP 1 RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select TOP 1 RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select TOP 1 RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select TOP 1 RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select TOP 1 RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + " CASE WHEN Rt_Perc.valor IS NOT NULL AND Rt_Perc.valor > 0 THEN COALESCE(Rt_guias.Valor, Rt_Perc.valor) ELSE COALESCE(Rt_guias.Valor,RPV.Valor) END Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "ISNULL(Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end, CASE WHEN Rt_Perc.ER = 'E' THEN 'Ch.Eq: '+PessoaH.Nome ELSE '' END) Assinatura, \n"
                    + "ISNULL(Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end, CASE WHEN (Rt_Perc.ER = 'R' and PessoaEnt.Nome is null) THEN ' ' ELSE '' END) AssinaturaDestino, \n"
                    //+ "ISNULL(Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E')  then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end, CASE WHEN Rt_Perc.ER = 'R' THEN 'Ch.Eq: '+PessoaH.Nome ELSE '' END) AssinaturaDestino, \n"                                                             
                    + "Case when Rt_PercSLA.HrSaidaVei IS NOT NULL then 'F' else 'P' end StatusGuia, \n"
                    + "   (SELECT TOP 1\n"
                    + "    MIN(Teste.Hora1) AS Hora1\n"
                    + "    FROM rt_perc       AS Teste\n"
                    + "    WHERE Teste.sequencia = Rt_Perc.Sequencia\n"
                    + "    AND   Teste.HrCheg  IS NULL\n"
                    + "    AND   Teste.HrSaida IS NULL\n"
                    + "    AND   Teste.flag_excl <> '*'  \n"
                    + "    GROUP BY Teste.sequencia\n"
                    + "    ORDER BY Hora1) AS ProximaHora, Rt_Perc.Hora1 AS Hora1Comparacao, Rt_GuiasMoeda.Moeda, Rt_Perc.Observ Obs, \n"
                    + " XMLGTVE.ChaveGTVE ChaveGTVE, \n"
                    + " XMLGTVE.Protocolo ProtocoloGTVE, \n"
                    + " XMLGTVE.Link LinkGTVE, \n"
                    + " XMLGTVE.Dt_Retorno DataGTVE, \n"
                    + " XMLGTVE.Hr_Retorno HoraGTVE, OS_Vig.CCUSTO CCUSTOOS \n"
                    + " from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS     = (Case when Rt_Guias.OS is not null and Rt_Guias.OS <> 0 then Rt_Guias.OS     else ISNULL(CxfGUias.OS,Rt_Perc.OS)         end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null and Rt_Guias.OS <> 0 THEN Rt_Guias.CodFil else ISNULL(CxfGUias.CodFil,Rt_Perc.CodFil) end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                         AND CliFat.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Pedido Pedido2 on Rt_Perc.pedido = Pedido2.numero\n"
                    + "                  AND Rt_Perc.Codfil = Pedido2.CodFil\n"
                    + "                  AND Pedido2.Flag_Excl <> '*'\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   ISNULL((Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R'),(SELECT TOP 1 Y.codigo FROM escala as X join pessoa AS Y ON X.matrche = Y.matr where X.seqRota = Rt_Perc.Sequencia))\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "left join escala on escala.Rota = Rotas.rota and escala.codfil = Rotas.codfil and escala.data = rotas.data\n"
                    + "left join pessoa AS PessoaH on escala.MatrChe = PessoaH.Matr\n"
                    + "left join Rt_PercSLA on Rt_PercSLA.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND Rt_PercSLA.Parada    = Rt_guias.Parada\n"
                    + "left join CxfGuiasVol on CxfGuiasVol.Guia = RPV.Guia \n"
                    + "                    and CxfGuiasVol.Serie = RPV.Serie\n"
                    + "                    and CxfGuiasVol.lacre = (Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia and Serie = RPV.Serie) \n"
                    + " LEFT JOIN XMLGTVE on RPV.Guia = XMLGTVE.Guia AND RPV.Serie = XMLGTVE.Serie \n"
                    + " where (Rt_Guias.Sequencia = ? AND Rt_Guias.Parada IN(?, (SELECT TOP 1 Rt_PercTesSAidas.parada\n"
                    + "                                                          FROM tessaidas \n"
                    + "                                                          JOIN pedido pedidoTesSaidas\n"
                    + "                                                            on tessaidas.pedido = pedidoTesSaidas.numero\n"
                    + "                                                           AND tessaidas.codfil = pedidoTesSaidas.codfil\n"
                    + "                                                          JOIN Rt_Perc Rt_PercTesSAidas\n"
                    + "                                                            ON Rt_PercTesSAidas.Sequencia = pedidoTesSaidas.seqrota\n"
                    + "                                                           AND pedidoTesSaidas.parada     = Rt_PercTesSAidas.parada\n"
                    + "                                                          WHERE pedidoTesSaidas.seqrota = ?\n"
                    + "                                                          AND   Rt_PercTesSAidas.Dpar   = ?\n"
                    + "                                                          AND   Rt_PercTesSAidas.Dpar IS NOT NULL)) )\n"
                    + ")a) AS RowConstrainedResult \n"
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.setString(parada);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                               
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));
                eGtv.setCCustoOS(consulta.getString("CCustoOS"));

                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                eGtv.setnRedDst(consulta.getString("NRedDst"));
                eGtv.setEndDst(consulta.getString("EndDst"));
                eGtv.setNomeDst(consulta.getString("NomeDst"));
                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                eGtv.setBairroDst(consulta.getString("BairroDst"));
                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                eGtv.setEmailDst(consulta.getString("EmailDst"));

                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                eGtv.setMoeda(consulta.getString("Moeda"));
                eGtv.setObs(consulta.getString("Obs"));
                eGtv.setTipoSrv(consulta.getString("TipoSrv"));

                eGtv.setGTVeChave(consulta.getString("ChaveGTVE"));
                eGtv.setGTVeLink(consulta.getString("LinkGTVE"));
                eGtv.setGTVeProtocolo(consulta.getString("ProtocoloGTVE"));

                eGtv.setGTVeData(consulta.getString("DataGTVE"));
                eGtv.setGTVeHora(consulta.getString("HoraGTVE"));

                eGtv.setPedidoCli(consulta.getString("PedidoCliente"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuiasEcovisao(String sequencia, String parada, boolean pedido, Persistencia persistencia) throws Exception {
        List<EGtv> guias = new ArrayList<>();
        try {
            String sql = " SELECT Rt_GuiasMoeda.Moeda, EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, \n"
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, \n"
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, \n"
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, \n"
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, \n"
                    + "(CliOri.Agencia) AgenciaOri, (CliOri.SubAgencia) SubAgenciaOri,"
                    + "         CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, \n"
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, case when (CliOri.CGC is null or Cliori.CGC = '') then CliOri.CPF else CliOri.CGC end  RegistroOri, \n"
                    + "(CliDst.Agencia) AgenciaDst, (CliDst.SubAgencia) SubAgenciaDst, \n"
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, \n"
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, \n"
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "         Rt_Guias.*, Rotas.rota, Rotas.Data DtColeta, percOrigem.ER, percOrigem.TipoSrv, percOrigem.Observ,  \n"
                    + " (filiais.razaoSocial) razaoSocial,  \n"
                    + " (filiais.endereco) enderecoFilial, \n"
                    + " (filiais.bairro) bairroFilial, \n"
                    + " (filiais.cidade) cidadeFilial, \n"
                    + " (filiais.UF) ufFilial, \n"
                    + " (filiais.CEP) cepFilial, \n"
                    + " (filiais.CNPJ) cnpjFilial, \n"
                    + " (filiais.fone) foneFilial, \n";
            if (pedido) {
                sql += " Pedido.Solicitante \n";
            } else {
                sql += " '' Solicitante \n";
            }
            sql += " FROM Rt_Guias \n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada \n"
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia \n"
                    + "                           AND percDst.Parada    = percOrigem.Dpar \n"
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia \n"
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia \n";
            if (pedido) {
                sql += " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido \n"
                        + "                /*AND Pedido.SeqRota = percOrigem.Sequencia*/ \n"
                        + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS \n"
                        + "                       AND OS_Vig.codfil = Pedido.CodFil \n";
            } else {
                sql += " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil \n"
                        + "                  AND OS_Vig.OS    = Rt_Guias.OS \n";
            }
            sql += " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil \n"
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil \n"
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo \n"
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia \n"
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil \n"
                    + " LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + " WHERE percOrigem.Sequencia = ? AND percOrigem.Parada = ? AND percOrigem.Flag_Excl != '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.select();

            EGtv rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new EGtv();
                rt_Guias.setSequencia(consulta.getString("Sequencia"));
                rt_Guias.setParada(consulta.getString("parada"));
                rt_Guias.setCodCli1(consulta.getString("CodCli1"));
//                rt_Guias.setCodCli2(consulta.getString("CodCli2"));
                rt_Guias.setGuia(consulta.getBigDecimal("guia").toPlainString());
                rt_Guias.setSerie(consulta.getString("serie"));
//                rt_Guias.setSerieAnt(consulta.getString("serieant"));
                rt_Guias.setCodFil(consulta.getString("codfil"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setOS(consulta.getString("OS"));
//                rt_Guias.setKM(consulta.getString("km"));
//                rt_Guias.setKMTerra(consulta.getString("KMterra"));
//                rt_Guias.setOperador(consulta.getString("operador"));

                rt_Guias.setRazaoSocial(consulta.getString("razaoSocial"));
                rt_Guias.setEnderecoFilial(consulta.getString("enderecoFilial"));
                rt_Guias.setBairroFilial(consulta.getString("bairroFilial"));
                rt_Guias.setCidadeFilial(consulta.getString("cidadeFilial"));
                rt_Guias.setUfFilial(consulta.getString("ufFilial"));
                rt_Guias.setCepFilial(consulta.getString("cepFilial"));
                rt_Guias.setCnpjFilial(consulta.getString("cnpjFilial"));
                rt_Guias.setFoneFilial(consulta.getString("foneFilial"));

//                rt_Guias.setAgenciaOri(consulta.getString("AgenciaOri"));
//                rt_Guias.setSubAgenciaOri(consulta.getString("SubAgenciaOri"));
                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setNomeOri(consulta.getString("NomeOri"));
                rt_Guias.setRegistroOri(consulta.getString("RegistroOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));
                rt_Guias.setVeiculoOri(consulta.getString("veiculoOri"));
                rt_Guias.setRotaOri(consulta.getString("rotaOri"));
                rt_Guias.setColetaOri(consulta.getString("coletaOri"));

//                rt_Guias.setAgenciaDst(consulta.getString("AgenciaDst"));
//                rt_Guias.setSubAgenciaDst(consulta.getString("SubAgenciaDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
//                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setVeiculoDst(consulta.getString("veiculoDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));
                rt_Guias.setColetaDst(consulta.getString("coletaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setNomeFat(consulta.getString("NomeFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

//                rt_Guias.setHoraChegada(consulta.getString("horaChegada"));
//                rt_Guias.setHoraSaida(consulta.getString("horaSaida"));
//                rt_Guias.setHora1(consulta.getString("hora1"));
//                rt_Guias.setHora2(consulta.getString("hora2"));
//                rt_Guias.setEr(consulta.getString("er"));
//                rt_Guias.setTipoSrv(consulta.getString("tipoSrv"));
//                rt_Guias.setObserv(consulta.getString("Observ"));
//                rt_Guias.setSolicitante(consulta.getString("Solicitante"));
                rt_Guias.setMoeda(consulta.getString("Moeda"));
                rt_Guias.setER(consulta.getString("ER"));;
                rt_Guias.setDtColeta(consulta.getString("DtColeta"));
                rt_Guias.setHrCheg(consulta.getString("horaChegada"));
                rt_Guias.setHrSaida(consulta.getString("horaSaida"));
                rt_Guias.setRota(consulta.getString("Rota"));
                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.listaGuias - " + e.getMessage() + "\r\n"
                    + "SELECT EscalaDst.Veiculo veiculoDst, EscalaDst.Rota rotaDst, percDst.hrcheg hora1, "
                    + "         percDst.hrsaida hora2, convert(DATE,percDst.Dt_Fech) coletaDst, "
                    + "         EscalaOri.Veiculo veiculoOri, EscalaOri.Rota rotaOri, percOrigem.hrcheg horaChegada, "
                    + "         percOrigem.hrsaida horaSaida, convert(DATE,percOrigem.Dt_Fech) coletaOri, "
                    + "         percOrigem.codcli2 CodCli2, percOrigem.codcli1 CodCli1, "
                    + "         CliOri.NRed+' - '+Convert(Varchar,CliOri.Agencia) NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "         CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CodCliOri, "
                    + "         CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "         CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "         CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "         CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, "
                    + "         Rt_Guias.*, Rotas.rota, percOrigem.ER "
                    + " FROM Rt_Guias "
                    + " LEFT JOIN Rt_Perc percOrigem ON percOrigem.Sequencia = Rt_Guias.Sequencia "
                    + "                              AND percOrigem.Parada   = Rt_Guias.Parada "
                    + " LEFT JOIN Rt_Perc percDst ON percDst.sequencia = percOrigem.sequencia "
                    + "                           AND percDst.Parada    = percOrigem.Dpar "
                    + " LEFT JOIN Escala EscalaOri ON EscalaOri.SeqRota = percOrigem.Sequencia "
                    + " LEFT JOIN Escala EscalaDst ON EscalaDst.SeqRota = percDst.Sequencia "
                    + (pedido ? " LEFT JOIN Pedido ON Pedido.numero = percOrigem.pedido "
                            + "      LEFT JOIN OS_Vig ON OS_Vig.OS      = Pedido.OS "
                            + "                       AND OS_Vig.codfil = Pedido.CodFil "
                            : " LEFT JOIN OS_Vig ON OS_Vig.CodFil = Rt_Guias.CodFil "
                            + "                  AND OS_Vig.OS    = Rt_Guias.OS ")
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = percOrigem.Sequencia "
                    + "                 AND Rotas.CodFil = OS_Vig.Codfil "
                    + " WHERE percOrigem.Sequencia = " + sequencia + " AND percOrigem.Parada = " + parada);
        }
        return guias;
    }

    public List<EGtv> listaGuiasRefeicao(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = "Select Rt_Perc.ER, \n"
                    + "Filiais.RazaoSocial,\n"
                    + "Filiais.Endereco EnderecoFilial,\n"
                    + "Filiais.Bairro BairroFilial, \n"
                    + "Filiais.Cidade CidadeFilial,\n"
                    + "Filiais.UF UFFilial,\n"
                    + "Filiais.CEP CEPFilial,\n"
                    + "Filiais.CNPJ CNPJFilial,\n"
                    + "Filiais.Fone FoneFilial,\n"
                    + "CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + "CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + "CliFat.Nome NomeOri,CliFat.NRed NRedOri, CliFat.Ende EndOri, CliFat.Cidade CidadeOri, CliFat.Bairro BairroOri, CliFat.Estado EstadoOri, \n"
                    + "CliFat.email EmailOri, CliFat.CGC RegistroOri, CliFat.IE IEOri, CliFat.Codigo CodOri, \n"
                    + "Rotas.Rota, \n"
                    + "(SELECT Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + "(Select veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*') VeiculoDst,\n"
                    + "Rt_Perc.Parada Parada_E, \n"
                    + "Rt_Perc.Sequencia Sequencia_E,\n"
                    + "Rt_Perc.Parada,\n"
                    + "Rotas.Sequencia,\n"
                    + "Rotas.Data DtEnt, \n"
                    + "Pedido.Numero Pedido,\n"
                    + "'TesSaidas.Guia' Guia,\n"
                    + "'TesSaidas.Serie' Serie,\n"
                    + "'0' Vol, \n"
                    + "'TesSaidasLacres.Lacre' Lacre, \n"
                    + "Pedido.Numero, Pedido.OS OSRtg, '' OSPreOrder, 'Reforço' Operacao, \n"
                    + "'TesSaidas.Dt_Alter' DtColeta,\n"
                    + "'TesSaidas.Hr_Alter' HrCheg, \n"
                    + "'TesSaidas.Hr_Alter' HrSaida, \n"
                    + "Rotas.Data DtEntrega,\n"
                    + "Rt_Perc.Hora1 HrPrg, \n"
                    + "Rt_Perc.HrCheg HrCheg_E, \n"
                    + "Rt_Perc.HrCheg HrCheg_S, \n"
                    + "'0' Valor, \n"
                    + "Rt_Perc.Codfil CodFil, \n"
                    + "'' Assinatura, \n"
                    + "'' AssinaturaDestino, '' ParadaD, \n"
                    + "'P' StatusGuia, '' ProximaHora, '' Hora1Comparacao,\n"
                    + "PedidoRefeicao.Solicitante Origem\n"
                    + "\n"
                    + "FROM PedidoRefeicao\n"
                    + "\n"
                    + "Left Join Filiais         on  Filiais.CodFil    = PedidoRefeicao.CodFil      \n"
                    + "Left Join Clientes CliFat ON PedidoRefeicao.Codcli = CliFat.Codigo\n"
                    + "LEFT JOIN Pedido\n"
                    + "        ON PedidoRefeicao.CodFil = Pedido.CodFil\n"
                    + "       AND PedidoRefeicao.Data   = Pedido.Data\n"
                    + "       AND PedidoRefeicao.CodCli = Pedido.CodCli2\n"
                    + "LEFT JOIN Rotas \n"
                    + "    ON Rotas.Sequencia = Pedido.SeqRota\n"
                    + "   AND Rotas.CodFil    = Pedido.CodFil\n"
                    + "LEFT JOIN Rt_perc \n"
                    + "        ON Rt_perc.Sequencia = Rotas.Sequencia\n"
                    + "       AND Rt_perc.Parada    = Pedido.Parada\n"
                    + "       AND Rt_perc.Flag_Excl <> '*' \n"
                    + "\n"
                    + "Left Join Escala          on  Escala.SeqRota    = Rotas.Sequencia        \n"
                    + "                          and Escala.CodFil     = Rotas.CodFil\n"
                    + " where  Rotas.Sequencia = ?"
                    + " AND (Rt_Perc.Parada = ? or Rt_Perc.Parada = ?-1)";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(parada);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
//                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
//                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                                
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
//                eGtv.setVol(consulta.getString("Vol"));
//                eGtv.setLacre(consulta.getString("Lacre"));
//                eGtv.setNumero(consulta.getString("Numero"));
//                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
//                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
//                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));
//
//                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
//                eGtv.setnRedDst(consulta.getString("NRedDst"));
//                eGtv.setEndDst(consulta.getString("EndDst"));
//                eGtv.setNomeDst(consulta.getString("NomeDst"));
//                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
//                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
//                eGtv.setBairroDst(consulta.getString("BairroDst"));
//                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
//                eGtv.setEmailDst(consulta.getString("EmailDst"));
                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                eGtv.setGuia(PreencheEsquerda(eGtv.getSequencia().replace(".0", ""), 8, "0")
                        + " " + PreencheEsquerda(eGtv.getParada().replace(".0", ""), 3, "0"));
                eGtv.setSerie("1");

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaGuiasTesouraria - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuiasTesouraria(String sequencia, String parada, String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = "\n"
                    + "Select Rt_Perc.ER, \n"
                    + " Filiais.RazaoSocial,\n"
                    + " Filiais.Endereco EnderecoFilial,\n"
                    + " Filiais.Bairro BairroFilial, \n"
                    + " Filiais.Cidade CidadeFilial,\n"
                    + " Filiais.UF UFFilial,\n"
                    + " Filiais.CEP CEPFilial,\n"
                    + " Filiais.CNPJ CNPJFilial,\n"
                    + " Filiais.Fone FoneFilial,\n"
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + " Rotas.Rota, \n"
                    + " (SELECT Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + " (Select veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*') VeiculoDst,\n"
                    + " Rt_Perc.Parada Parada_E, \n"
                    + " Rt_Perc.Sequencia Sequencia_E,\n"
                    + " Rt_Perc.Parada,\n"
                    + " Rotas.Sequencia,\n"
                    + " Rotas.Data DtEnt, \n"
                    + " TesSaidas.Pedido,\n"
                    + " TesSaidas.Guia,\n"
                    + " TesSaidas.Serie,\n"
                    + " (Select Count(*) from TesSaidasLacres where guia = TesSaidas.Guia AND Serie = TesSaidas.Serie and TesSaidasLacres.Malote = 001) Vol, \n"
                    + " TesSaidasLacres.Lacre, Pedido.Numero, Pedido.OS OSRtg, '' OSPreOrder, 'Reforço' Operacao, \n"
                    + "\n"
                    + " TesSaidas.Dt_Alter DtColeta,\n"
                    + " TesSaidas.Hr_Alter HrCheg, \n"
                    + " TesSaidas.Hr_Alter HrSaida, \n"
                    + " CliDst.Agencia+' '+CliDst.Subagencia 'Ag/SB', \n"
                    + " OS_Vig.NRedDst Origem, \n"
                    + " OS_Vig.NRedDst NRedOri, \n"
                    + " CliOri.Ende EndOri, \n"
                    + " CliOri.Cidade CidadeOri, \n"
                    + " CliOri.Bairro BairroOri, \n"
                    + " CliOri.Estado EstadoOri, \n"
                    + " CliOri.email EmailOri, \n"
                    + " CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, \n"
                    + " case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end RegistroOri, \n"
                    + " Rotas.Data DtEntrega,\n"
                    + "\n"
                    + " CliOri.Agencia+' '+CliOri.Subagencia 'Ag/SB D', \n"
                    + " OS_Vig.NRed Destino, \n"
                    + " OS_Vig.NRed NRedDst, \n"
                    + " CliDst.Ende EndDst,\n"
                    + " CliDst.Cidade CidadeDst, \n"
                    + " CliDst.Bairro BairroDst, \n"
                    + " CliDst.Estado EstadoDst, \n"
                    + " CliDst.email EmailDst, \n"
                    + " CliDst.Codigo CodCliDst, \n"
                    + " CliDst.Cep cepDst, \n"
                    + " CliDst.Nome NomeDst, \n"
                    + " case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end RegistroDst, \n"
                    + " Rt_Perc.Hora1 HrPrg, \n"
                    + " Rt_Perc.HrCheg HrCheg_E, \n"
                    + " Rt_Perc.HrCheg HrCheg_S, \n"
                    + " TesSaidas.TotalGeral Valor, \n"
                    + " Rt_Perc.Codfil CodFil, \n"
                    + " '' Assinatura, \n"
                    + " '' AssinaturaDestino, '' ParadaD,  \n"
                    + " 'P' StatusGuia, '' ProximaHora, '' Hora1Comparacao\n"
                    + " from TesSaidas\n"
                    + " left Join TesSaidasLacres on  TesSaidasLacres.Guia   = TesSaidas.Guia    \n"
                    + "                           and TesSaidasLacres.Serie  = TesSaidas.Serie  \n"
                    + "                           and TesSaidasLacres.Malote = 001    \n"
                    + " Left Join Filiais         on  Filiais.CodFil    = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliOri on  CliOri.Codigo     = TesSaidas.CodCli1      \n"
                    + "                           and CliOri.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliDst on  CliDst.Codigo     = TesSaidas.CodCli2      \n"
                    + "                           and CliDst.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliFat on  CliFat.Codigo     = TesSaidas.CodCli3      \n"
                    + "                           and CliFat.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Rt_Perc         on  Rt_Perc.Pedido    = TesSaidas.Pedido      \n"
                    + "                           and Rt_Perc.CodCli1   = TesSaidas.CodCli2      \n"
                    + "                           and Rt_Perc.ER        =  'E'\n"
                    + "                           and Rt_Perc.Flag_Excl <> '*'\n"
                    + " left join Rotas           on  Rotas.Sequencia   = Rt_Perc.Sequencia      \n"
                    + "\n"
                    + " Left Join Pedido          on  Pedido.Numero     = TesSaidas.Pedido      \n"
                    + "                           and Pedido.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join OS_Vig          on  Os_Vig.Os         = Pedido.OS  \n"
                    + "                           and OS_vig.CodFil     = Pedido.CodFil          \n"
                    + " Left Join Clientes CliDstOS on  CliDstOS.Codigo  = OS_Vig.CliDst    \n"
                    + "                             and CliDstOS.CodFil  = Rotas.CodFil      \n"
                    + " Left Join Escala          on  Escala.SeqRota    = Rotas.Sequencia        \n"
                    + "                           and Escala.CodFil     = Rotas.CodFil\n"
                    + "  where  Rt_Perc.Parada = ? \n"
                    + "  AND Rotas.Sequencia = ?\n"
                    + "    and TesSaidas.CodGTV is not null \n"
                    + "     AND TesSaidas.Guia = ? \n"
                    + "     AND TesSaidas.Serie = ?;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                                
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));

                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                eGtv.setnRedDst(consulta.getString("NRedDst"));
                eGtv.setEndDst(consulta.getString("EndDst"));
                eGtv.setNomeDst(consulta.getString("NomeDst"));
                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                eGtv.setBairroDst(consulta.getString("BairroDst"));
                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                eGtv.setEmailDst(consulta.getString("EmailDst"));

                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaGuiasTesouraria - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuiasTesSaidas(String sequencia, String parada, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<EGtv> Retorno = new ArrayList<>();

            sql = "Select\n"
                    + " Rt_Perc.ER, Rt_Perc.TipoSrv, \n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + " Rotas.Rota,\n"
                    + "(SELECT Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + " \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    (Select veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*')\n"
                    + "else \n"
                    + "    Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%' then \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "    else \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "    end \n"
                    + "end VeiculoDst,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Parada \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select RtP.Parada from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select RtP.Parada from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Parada_E,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Sequencia \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select RtP.Sequencia from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select RtP.Sequencia from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Sequencia_E,\n"
                    + "Rt_Perc.Parada, \n"
                    + " Rt_Perc.Sequencia, \n"
                    + " '' ParadaD, \n"
                    + "cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, TesSaidas.Guia) Guia, TesSaidas.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = TesSaidas.Guia AND Serie = TesSaidas.Serie) Vol, \n"
                    + " CxfGuiasVol.Lacre, Pedido.Numero, PedidoRef.OS OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = TesSaidas.Guia AND TesSaidas.Serie = TesSaidas.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = TesSaidas.Guia AND TesSaidas.Serie = TesSaidas.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = TesSaidas.Guia AND TesSaidas.Serie = TesSaidas.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not NULL then OS_Vig.NRed else OSPre.NRedDst end end NRedOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Ende else Case when Rt_Perc.ER is not NULL then CliOri.Ende else CliDstPre.Ende  end end EndOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Cidade else Case when Rt_Perc.ER is not NULL then CliOri.Cidade else CliDstPre.Cidade  end end CidadeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Bairro else Case when Rt_Perc.ER is not NULL then CliOri.Bairro else CliDstPre.Bairro  end end BairroOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Estado else Case when Rt_Perc.ER is not NULL then CliOri.Estado else CliDstPre.Estado  end end EstadoOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.email else Case when Rt_Perc.ER is not NULL then CliOri.email else CliDstPre.email  end end EmailOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Codigo else Case when Rt_Perc.ER is not NULL then CliOri.Codigo else CliDstPre.Codigo  end end CodCliOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Nome else Case when Rt_Perc.ER is not NULL then CliOri.Nome else CliDstPre.Nome  end end NomeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "else Case when Rt_Perc.ER is not NULL then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end \n"
                    + "else \n"
                    + "    case when (CliDstPre.CGC is null or CliDstPre.CGC = '') then CliDstPre.CPF else CliDstPre.CGC end \n"
                    + "end end RegistroOri, \n"
                    + "\n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end NRedDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Ende else CliDst.Ende end EndDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cidade else CliDst.Cidade end CidadeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Bairro else CliDst.Bairro end BairroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Estado else CliDst.Estado end EstadoDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.email else CliDst.email end EmailDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Codigo else CliDst.Codigo end CodCliDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cep else CliDst.Cep end cepDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Nome else CliDst.Nome end NomeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "else \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "end RegistroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "TesSaidas.TotalGeral Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino,  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino, \n"                    
                    + "Case when Rt_PercSLA.HrSaidaVei IS NOT NULL then 'F' else 'P' end StatusGuia, \n"
                    + "   (SELECT TOP 1\n"
                    + "    MIN(Teste.Hora1) AS Hora1\n"
                    + "    FROM rt_perc       AS Teste\n"
                    + "    WHERE Teste.sequencia = Rt_Perc.Sequencia\n"
                    + "    AND   Teste.HrCheg  IS NULL\n"
                    + "    AND   Teste.HrSaida IS NULL\n"
                    + "    AND   Teste.flag_excl <> '*'  \n"
                    + "    GROUP BY Teste.sequencia\n"
                    + "    ORDER BY Hora1) AS ProximaHora, Rt_Perc.Hora1 AS Hora1Comparacao, 'BRL' Moeda, Rt_Perc.Observ Obs, \n"
                    + " XMLGTVE.ChaveGTVE ChaveGTVE, \n"
                    + " XMLGTVE.Protocolo ProtocoloGTVE, \n"
                    + " XMLGTVE.Link LinkGTVE, \n"
                    + " XMLGTVE.Dt_Retorno DataGTVE, \n"
                    + " XMLGTVE.Hr_Retorno HoraGTVE \n"
                    + " \n"
                    + " from TesSaidas\n"
                    + " JOIN Pedido PedidoRef\n"
                    + "   ON TesSaidas.Pedido = PedidoRef.Numero\n"
                    + "  AND TesSaidas.CodFil = PedidoRef.CodFil\n"
                    + "\n"
                    + "left join Rt_Perc  on Rt_Perc.Sequencia = PedidoRef.SeqRota\n"
                    + "                  AND Rt_Perc.Parada   = PedidoRef.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = PedidoRef.SeqRota\n"
                    + "LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + "left join CxfGuias on  CxfGuias.Guia = TesSaidas.Guia\n"
                    + "                   AND CxfGuias.Serie = TesSaidas.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when PedidoRef.OS is not null then PedidoRef.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when PedidoRef.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                         AND CliFat.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = TesSaidas.Guia\n"
                    + "                   AND PreOrder.Serie = TesSaidas.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   ISNULL((Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = TesSaidas.Guia AND RPVX.Serie = TesSaidas.Serie AND Rt_Perc.ER = 'R'),(SELECT TOP 1 Y.codigo FROM escala as X join pessoa AS Y ON X.matrche = Y.matr where X.seqRota = Rt_Perc.Sequencia))\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = TesSaidas.Guia AND RPVX.Serie = TesSaidas.Serie AND Rt_Perc.ER = 'E') \n"
                    + "left join Rt_PercSLA on Rt_PercSLA.Sequencia = PedidoRef.SeqRota\n"
                    + "                    AND Rt_PercSLA.Parada    = PedidoRef.Parada\n"
                    + "left join CxfGuiasVol on CxfGuiasVol.Guia = TesSaidas.Guia \n"
                    + "                    and CxfGuiasVol.Serie = TesSaidas.Serie\n"
                    + "                    and CxfGuiasVol.lacre = (Select Min(Lacre)from CxfGuiasVol where guia = TesSaidas.Guia and Serie = TesSaidas.Serie) \n"
                    + " LEFT JOIN XMLGTVE on TesSaidas.Guia = XMLGTVE.Guia AND TesSaidas.Serie = XMLGTVE.Serie \n"
                    + " where (PedidoRef.SeqRota = ? AND PedidoRef.Parada IN(?, (SELECT Rt_PercTesSAidas.parada\n"
                    + "                                                          FROM tessaidas \n"
                    + "                                                          JOIN pedido pedidoTesSaidas\n"
                    + "                                                            on tessaidas.pedido = pedidoTesSaidas.numero\n"
                    + "                                                           AND tessaidas.codfil = pedidoTesSaidas.codfil\n"
                    + "                                                          JOIN Rt_Perc Rt_PercTesSAidas\n"
                    + "                                                            ON Rt_PercTesSAidas.Sequencia = pedidoTesSaidas.seqrota\n"
                    + "                                                           AND pedidoTesSaidas.parada     = Rt_PercTesSAidas.parada\n"
                    + "                                                          WHERE pedidoTesSaidas.seqrota = ?\n"
                    + "                                                          AND   Rt_PercTesSAidas.Dpar   = ?\n"
                    + "                                                          AND   Rt_PercTesSAidas.Dpar IS NOT NULL)) )\n"
                    + "ORDER BY DtColeta Desc, Rt_Perc.HrCheg Desc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.setString(parada);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setAssinaturaDestino(consulta.getString("ParadaD"));                                
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));

                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                eGtv.setnRedDst(consulta.getString("NRedDst"));
                eGtv.setEndDst(consulta.getString("EndDst"));
                eGtv.setNomeDst(consulta.getString("NomeDst"));
                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                eGtv.setBairroDst(consulta.getString("BairroDst"));
                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                eGtv.setEmailDst(consulta.getString("EmailDst"));

                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                eGtv.setMoeda(consulta.getString("Moeda"));
                eGtv.setObs(consulta.getString("Obs"));
                eGtv.setTipoSrv(consulta.getString("TipoSrv"));

                eGtv.setGTVeChave(consulta.getString("ChaveGTVE"));
                eGtv.setGTVeLink(consulta.getString("LinkGTVE"));
                eGtv.setGTVeProtocolo(consulta.getString("ProtocoloGTVE"));

                eGtv.setGTVeData(consulta.getString("DataGTVE"));
                eGtv.setGTVeHora(consulta.getString("HoraGTVE"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                Retorno.add(eGtv);
            }
            consulta.Close();

            return Retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaGuiasTesSaidas - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuiasTesouraria(String sequencia, String parada, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = "\n"
                    + "Select Rt_Perc.ER, \n"
                    + " Filiais.RazaoSocial,\n"
                    + " Filiais.Endereco EnderecoFilial,\n"
                    + " Filiais.Bairro BairroFilial, \n"
                    + " Filiais.Cidade CidadeFilial,\n"
                    + " Filiais.UF UFFilial,\n"
                    + " Filiais.CEP CEPFilial,\n"
                    + " Filiais.CNPJ CNPJFilial,\n"
                    + " Filiais.Fone FoneFilial,\n"
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + " Rotas.Rota, \n"
                    + " (SELECT Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + " (Select veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*') VeiculoDst,\n"
                    + " Rt_Perc.Parada Parada_E, \n"
                    + " Rt_Perc.Sequencia Sequencia_E,\n"
                    + " Rt_Perc.Parada,\n"
                    + " Rotas.Sequencia,\n"
                    + " Rotas.Data DtEnt, \n"
                    + " TesSaidas.Pedido,\n"
                    + " TesSaidas.Guia,\n"
                    + " TesSaidas.Serie,\n"
                    + " (Select Count(*) from TesSaidasLacres where guia = TesSaidas.Guia AND Serie = TesSaidas.Serie and TesSaidasLacres.Malote = 001) Vol, \n"
                    + " TesSaidasLacres.Lacre, Pedido.Numero, Pedido.OS OSRtg, '' OSPreOrder, 'Reforço' Operacao, \n"
                    + "\n"
                    + " TesSaidas.Dt_Alter DtColeta,\n"
                    + " TesSaidas.Hr_Alter HrCheg, \n"
                    + " TesSaidas.Hr_Alter HrSaida, \n"
                    + " CliDst.Agencia+' '+CliDst.Subagencia 'Ag/SB', \n"
                    + " OS_Vig.NRedDst Origem, \n"
                    + " OS_Vig.NRedDst NRedOri, \n"
                    + " CliOri.Ende EndOri, \n"
                    + " CliOri.Cidade CidadeOri, \n"
                    + " CliOri.Bairro BairroOri, \n"
                    + " CliOri.Estado EstadoOri, \n"
                    + " CliOri.email EmailOri, \n"
                    + " CliOri.Codigo CodCliOri, \n"
                    + " CliOri.Nome NomeOri, \n"
                    + " case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end RegistroOri, \n"
                    + " Rotas.Data DtEntrega,\n"
                    + "\n"
                    + " CliOri.Agencia+' '+CliOri.Subagencia 'Ag/SB D', \n"
                    + " OS_Vig.NRed Destino, \n"
                    + " OS_Vig.NRed NRedDst, \n"
                    + " CliDst.Ende EndDst,\n"
                    + " CliDst.Cidade CidadeDst, \n"
                    + " CliDst.Bairro BairroDst, \n"
                    + " CliDst.Estado EstadoDst, \n"
                    + " CliDst.email EmailDst, \n"
                    + " CliDst.Codigo CodCliDst, \n"
                    + " CliDst.Cep cepDst, \n"
                    + " CliDst.Nome NomeDst, \n"
                    + " case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end RegistroDst, \n"
                    + " Rt_Perc.Hora1 HrPrg, \n"
                    + " Rt_Perc.HrCheg HrCheg_E, \n"
                    + " Rt_Perc.HrCheg HrCheg_S, \n"
                    + " TesSaidas.TotalGeral Valor, \n"
                    + " Rt_Perc.Codfil CodFil, \n"
                    + " '' Assinatura, \n"
                    + " '' AssinaturaDestino, '' ParadaD,  \n"
                    + " 'P' StatusGuia, '' ProximaHora, '' Hora1Comparacao\n"
                    + " from TesSaidas\n"
                    + " left Join TesSaidasLacres on  TesSaidasLacres.Guia   = TesSaidas.Guia    \n"
                    + "                           and TesSaidasLacres.Serie  = TesSaidas.Serie  \n"
                    + "                           and TesSaidasLacres.Malote = 001    \n"
                    + " Left Join Filiais         on  Filiais.CodFil    = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliOri on  CliOri.Codigo     = TesSaidas.CodCli1      \n"
                    + "                           and CliOri.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliDst on  CliDst.Codigo     = TesSaidas.CodCli2      \n"
                    + "                           and CliDst.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Clientes CliFat on  CliFat.Codigo     = TesSaidas.CodCli3      \n"
                    + "                           and CliFat.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join Rt_Perc         on  Rt_Perc.Pedido    = TesSaidas.Pedido      \n"
                    + "                           and Rt_Perc.CodCli1   = TesSaidas.CodCli2      \n"
                    + "                           and Rt_Perc.ER        =  'E'\n"
                    + "                           and Rt_Perc.Flag_Excl <> '*'\n"
                    + " left join Rotas           on  Rotas.Sequencia   = Rt_Perc.Sequencia      \n"
                    + "\n"
                    + " Left Join Pedido          on  Pedido.Numero     = TesSaidas.Pedido      \n"
                    + "                           and Pedido.CodFil     = TesSaidas.CodFil      \n"
                    + " Left Join OS_Vig          on  Os_Vig.Os         = Pedido.OS  \n"
                    + "                           and OS_vig.CodFil     = Pedido.CodFil          \n"
                    + " Left Join Clientes CliDstOS on  CliDstOS.Codigo  = OS_Vig.CliDst    \n"
                    + "                             and CliDstOS.CodFil  = Rotas.CodFil      \n"
                    + " Left Join Escala          on  Escala.SeqRota    = Rotas.Sequencia        \n"
                    + "                           and Escala.CodFil     = Rotas.CodFil\n"
                    + "  where  Rt_Perc.Parada = ? \n"
                    + "  AND Rotas.Sequencia = ?\n"
                    + "    and TesSaidas.CodGTV is not null  ;";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(sequencia);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                                
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                eGtv.setnRedOri(consulta.getString("NRedOri"));
                eGtv.setEndOri(consulta.getString("EndOri"));
                eGtv.setNomeOri(consulta.getString("NomeOri"));
                eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                eGtv.setBairroOri(consulta.getString("BairroOri"));
                eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                eGtv.setEmailOri(consulta.getString("EmailOri"));

                eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                eGtv.setnRedDst(consulta.getString("NRedDst"));
                eGtv.setEndDst(consulta.getString("EndDst"));
                eGtv.setNomeDst(consulta.getString("NomeDst"));
                eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                eGtv.setBairroDst(consulta.getString("BairroDst"));
                eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                eGtv.setEmailDst(consulta.getString("EmailDst"));

                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaGuiasTesouraria - " + e.getMessage());
        }
    }

    public List<EGtv> listaGuias(String sequencia, String parada, String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = "Declare @seqRota float = ?; \n"
                    + "Declare @parada int = ?; \n"
                    + "Declare @guia float = ?; \n"
                    + "Declare @serie Varchar(03) = ?; \n"
                    + "Declare @qtde int; \n"
                    //                    + "Select @qtde = Isnull(Count(*),0) from RPV (Nolock) where guia = @guia and serie = @serie;\n"
                    //                    + "If(@qtde = 0) begin\n"
                    //                    + "	Insert into RPV (Guia, Serie, Parada, Valor, CodPessoaAut)	\n"
                    //                    + "	Select Rt_Guias.Sequencia, Rt_Guias.Serie, Rt_Guias.Parada, Rt_Guias.Valor, Pessoa.Codigo\n"
                    //                    + "	from Rt_Guias (nolock)\n"
                    //                    + "	Left Join Escala (nolock) on Escala.SeqRota = Rt_Guias.Sequencia\n"
                    //                    + "	Left join Pessoa (nolock) on escala.MatrChe = Pessoa.Matr \n"
                    //                    + "	where guia = @guia and serie = @serie ;\n"
                    //                    + "end"
                    + "\n"
                    + "SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * from \n"
                    + "(Select \n"
                    + " Rt_Perc.ER,\n"
                    + " Rt_Perc.TipoSrv,\n"
                    + " Rt_Perc.Observ,\n"
                    + " filiais.razaoSocial razaoSocial,  \n"
                    + " filiais.endereco enderecoFilial, \n"
                    + " filiais.bairro bairroFilial, \n"
                    + " filiais.cidade cidadeFilial, \n"
                    + " filiais.UF ufFilial, \n"
                    + " filiais.CEP cepFilial, \n"
                    + " filiais.CNPJ cnpjFilial, \n"
                    + " filiais.fone foneFilial, \n"
                    + " CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, \n"
                    + " CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Nome NomeFat, CliFat.Codigo CodCliFat, \n"
                    + " Rotas.Rota,\n"
                    + "(SELECT Veiculo FROM Escala WHERE Escala.SeqRota = Rotas.Sequencia) VeiculoOri,\n"
                    + " \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    (Select veiculo from escala WHERE Escala.SeqRota = Rt_Perc.Sequencia and Rt_Perc.Flag_Excl <> '*')\n"
                    + "else \n"
                    + "    Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%' then \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "    else \n"
                    + "        (Select veiculo \n"
                    + "        from escala \n"
                    + "        left join Rt_perc RtP on RtP.Sequencia = Escala.SeqRota\n"
                    + "        where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "    end \n"
                    + "end VeiculoDst,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Parada \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select RtP.Parada from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select RtP.Parada from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Parada_E,\n"
                    + "Case when Rt_Perc.ER = 'E' \n"
                    + "     then Rt_Perc.Sequencia \n"
                    + "     else Case When Rt_Perc.CodCli2 not like '641%' and Rt_Perc.CodCli2 not like '999%'\n"
                    + "               then (Select RtP.Sequencia from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia and RtP.Parada = Rt_perc.DPar and RtP.Flag_Excl <> '*')\n"
                    + "               else (Select RtP.Sequencia from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai and RtP.Hora1 = CxfGuias.Hora1D and RtP.Flag_Excl <> '*') \n"
                    + "          end \n"
                    + "end Sequencia_E,\n"
                    + "Rt_Perc.Parada, \n"
                    + " Rt_Perc.Sequencia, \n"
                    + " Pedido2.PedidoCliente,  \n"
                    + "cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Vol, \n"
                    + " CxfGuiasVol.Lacre, Pedido.Numero, ISNULL(Rt_Guias.OS,Rt_Perc.OS) OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else ISNULL((select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie),Rt_Perc.HrCheg) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else ISNULL((Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie),Rt_Perc.HrSaida) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not NULL then OS_Vig.NRed else OSPre.NRedDst end end NRedOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Ende else Case when Rt_Perc.ER is not NULL then CliOri.Ende else CliDstPre.Ende  end end EndOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Cidade else Case when Rt_Perc.ER is not NULL then CliOri.Cidade else CliDstPre.Cidade  end end CidadeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Bairro else Case when Rt_Perc.ER is not NULL then CliOri.Bairro else CliDstPre.Bairro  end end BairroOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Estado else Case when Rt_Perc.ER is not NULL then CliOri.Estado else CliDstPre.Estado  end end EstadoOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.email else Case when Rt_Perc.ER is not NULL then CliOri.email else CliDstPre.email  end end EmailOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Codigo else Case when Rt_Perc.ER is not NULL then CliOri.Codigo else CliDstPre.Codigo  end end CodCliOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Nome else Case when Rt_Perc.ER is not NULL then CliOri.Nome else CliDstPre.Nome  end end NomeOri, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "else Case when Rt_Perc.ER is not NULL then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end \n"
                    + "else \n"
                    + "    case when (CliDstPre.CGC is null or CliDstPre.CGC = '') then CliDstPre.CPF else CliDstPre.CGC end \n"
                    + "end end RegistroOri, \n"
                    + "\n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end NRedDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Ende else CliDst.Ende end EndDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cidade else CliDst.Cidade end CidadeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Bairro else CliDst.Bairro end BairroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Estado else CliDst.Estado end EstadoDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.email else CliDst.email end EmailDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Codigo else CliDst.Codigo end CodCliDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Cep else CliDst.Cep end cepDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Nome else CliDst.Nome end NomeDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then \n"
                    + "    case when (CliOri.CGC is null or CliOri.CGC = '') then CliOri.CPF else CliOri.CGC end\n"
                    + "else \n"
                    + "    case when (CliDst.CGC is null or CliDst.CGC = '') then CliDst.CPF else CliDst.CGC end\n"
                    + "end RegistroDst, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "ISNULL(Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end, CASE WHEN Rt_Perc.ER = 'E' THEN 'Ch.Eq: '+PessoaH.Nome ELSE '' END) Assinatura, \n"
                    + "ISNULL(Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end, CASE WHEN (Rt_Perc.ER = 'R' and PessoaEnt.Nome is null) THEN '' END) AssinaturaDestino,  \n"
                    //+ "ISNULL(Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E')  then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end, CASE WHEN Rt_Perc.ER = 'R' THEN 'Ch.Eq: '+PessoaH.Nome ELSE '' END) AssinaturaDestino, \n"                                        
                    + "Case when Rt_PercSLA.HrSaidaVei IS NOT NULL then 'F' else 'P' end StatusGuia, \n"
                    + "   (SELECT TOP 1\n"
                    + "    MIN(Teste.Hora1) AS Hora1\n"
                    + "    FROM rt_perc       AS Teste\n"
                    + "    WHERE Teste.sequencia = Rt_Perc.Sequencia\n"
                    + "    AND   Teste.HrCheg  IS NULL\n"
                    + "    AND   Teste.HrSaida IS NULL\n"
                    + "    AND   Teste.flag_excl <> '*'  \n"
                    + "    GROUP BY Teste.sequencia\n"
                    + "    ORDER BY Hora1) AS ProximaHora, Rt_Perc.Hora1 AS Hora1Comparacao, Rt_GuiasMoeda.Moeda \n"
                    + " from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                 AND Rt_Guias.Parada   = Rt_Perc.Parada\n"
                    + " LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia \n"
                    + "                              AND Rt_GuiasMoeda.Parada   = Rt_Guias.Parada\n"
                    + "                              AND Rt_GuiasMoeda.Guia   = Rt_Guias.Guia\n"
                    + "                              AND Rt_GuiasMoeda.Serie   = Rt_Guias.Serie \n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "LEFT JOIN Filiais on Filiais.Codfil = Rotas.Codfil \n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1  = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "LEFT JOIN Clientes CliFat ON CliFat.Codigo = OS_Vig.CliFat\n"
                    + "                         AND CliFat.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Pedido Pedido2 on Rt_Perc.pedido = Pedido2.numero\n"
                    + "                  AND Rt_Perc.Codfil = Pedido2.CodFil\n"
                    + "                  AND Pedido2.Flag_Excl <> '*'\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "left join escala on escala.Rota = Rotas.rota and escala.codfil = Rotas.codfil and escala.data = rotas.data\n"
                    + "left join pessoa AS PessoaH on escala.MatrChe = PessoaH.Matr\n"
                    + "left join Rt_PercSLA on Rt_PercSLA.Sequencia = Rt_Guias.Sequencia\n"
                    + "                    AND Rt_PercSLA.Parada    = Rt_guias.Parada\n"
                    + "left join CxfGuiasVol on CxfGuiasVol.Guia = RPV.Guia \n"
                    + "                    and CxfGuiasVol.Serie = RPV.Serie\n"
                    + "                    and CxfGuiasVol.lacre = (Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia and Serie = RPV.Serie) \n"
                    + " where (Rt_Guias.Sequencia = @seqRota /*AND (Rt_Perc.Parada = @parada or Rt_Perc.dPar = @parada) */ AND Rt_Guias.Guia = @guia  AND Rt_Guias.Serie like '%'+@serie AND RPV.Parada <> 0 )\n"
                    + ")a) AS RowConstrainedResult \n"
                    + " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                
                eGtv.setOS(consulta.getString("OSRtg"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setParada_E(consulta.getString("Parada_E"));
                eGtv.setSequencia_E(consulta.getString("Sequencia_E"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                eGtv.setER(consulta.getString("ER"));
                eGtv.setRota(consulta.getString("Rota"));

                eGtv.setRazaoSocial(consulta.getString("razaoSocial"));
                eGtv.setEnderecoFilial(consulta.getString("enderecoFilial"));
                eGtv.setBairroFilial(consulta.getString("bairroFilial"));
                eGtv.setCidadeFilial(consulta.getString("cidadeFilial"));
                eGtv.setUfFilial(consulta.getString("ufFilial"));
                eGtv.setCepFilial(consulta.getString("cepFilial"));
                eGtv.setCnpjFilial(consulta.getString("cnpjFilial"));
                eGtv.setFoneFilial(consulta.getString("foneFilial"));

                if (consulta.getString("ER").equals("R")) {
                    eGtv.setnRedOri(consulta.getString("NRedDst"));
                    eGtv.setEndOri(consulta.getString("EndDst"));
                    eGtv.setNomeOri(consulta.getString("NomeDst"));
                    eGtv.setRegistroOri(consulta.getString("RegistroDst"));
                    eGtv.setCidadeOri(consulta.getString("CidadeDST"));
                    eGtv.setBairroOri(consulta.getString("BairroDsT"));
                    eGtv.setEstadoOri(consulta.getString("EstadoDST"));
                    eGtv.setEmailOri(consulta.getString("EmailDSt"));

                    eGtv.setCodCliDst(consulta.getString("CodCliOri"));
                    eGtv.setnRedDst(consulta.getString("NRedOri"));
                    eGtv.setEndDst(consulta.getString("EndOri"));
                    eGtv.setNomeDst(consulta.getString("NomeOri"));
                    eGtv.setRegistroDst(consulta.getString("RegistroOri"));
                    eGtv.setCidadeDst(consulta.getString("CidadeOri"));
                    eGtv.setBairroDst(consulta.getString("BairroOri"));
                    eGtv.setEstadoDst(consulta.getString("EstadoOri"));
                    eGtv.setEmailDst(consulta.getString("EmailOri"));
                } else {
                    eGtv.setnRedOri(consulta.getString("NRedOri"));
                    eGtv.setEndOri(consulta.getString("EndOri"));
                    eGtv.setNomeOri(consulta.getString("NomeOri"));
                    eGtv.setRegistroOri(consulta.getString("RegistroOri"));
                    eGtv.setCidadeOri(consulta.getString("CidadeOri"));
                    eGtv.setBairroOri(consulta.getString("BairroOri"));
                    eGtv.setEstadoOri(consulta.getString("EstadoOri"));
                    eGtv.setEmailOri(consulta.getString("EmailOri"));

                    eGtv.setCodCliDst(consulta.getString("CodCliDst"));
                    eGtv.setnRedDst(consulta.getString("NRedDst"));
                    eGtv.setEndDst(consulta.getString("EndDst"));
                    eGtv.setNomeDst(consulta.getString("NomeDst"));
                    eGtv.setRegistroDst(consulta.getString("RegistroDst"));
                    eGtv.setCidadeDst(consulta.getString("CidadeDst"));
                    eGtv.setBairroDst(consulta.getString("BairroDst"));
                    eGtv.setEstadoDst(consulta.getString("EstadoDst"));
                    eGtv.setEmailDst(consulta.getString("EmailDst"));
                }
                eGtv.setnRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCliFat(consulta.getString("CodCliFat"));
                eGtv.setNomeFat(consulta.getString("NomeFat"));
                eGtv.setEndFat(consulta.getString("EndFat"));
                eGtv.setCidadeFat(consulta.getString("CidadeFat"));
                eGtv.setBairroFat(consulta.getString("BairroFat"));
                eGtv.setEstadoFat(consulta.getString("EstadoFat"));
                eGtv.setCgcFat(consulta.getString("CGCFat"));
                eGtv.setIeFat(consulta.getString("IEFat"));
                eGtv.setEmailFat(consulta.getString("EmailFat"));

                eGtv.setVeiculoOri(consulta.getString("VeiculoOri"));
                eGtv.setVeiculoDst(consulta.getString("VeiculoDst"));

                eGtv.setMoeda(consulta.getString("Moeda"));

                eGtv.setTipoSrv(consulta.getString("TipoSrv"));

                eGtv.setObs(consulta.getString("Observ"));

                eGtv.setPedidoCli(consulta.getString("PedidoCliente"));

                if (consulta.getString("Hora1Comparacao").equals(consulta.getString("ProximaHora"))) {
                    eGtv.setClassCelula("Transito");
                    eGtv.setClassIcon("fa fa-clock-o");
                    eGtv.setStatus("T");
                } else {
                    switch (consulta.getString("StatusGuia")) {
                        case "P":
                            eGtv.setClassCelula("Pendente");
                            eGtv.setClassIcon("fa fa-warning");
                            eGtv.setStatus("P");
                            break;

                        case "T":
                            eGtv.setClassCelula("Transito");
                            eGtv.setClassIcon("fa fa-clock-o");
                            eGtv.setStatus("T");
                            break;

                        case "F":
                            eGtv.setClassCelula("Finalizado");
                            eGtv.setClassIcon("fa fa-check-square-o");
                            eGtv.setStatus("F");
                            break;
                    }
                }

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<EGtv> listaPaginadaSPM(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * from (\n"
                    + "(Select Rotas.Rota, Rt_Perc.Parada, cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino \n"                                        
                    + " from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "where (   RPV.Data >= ?  AND RPV.Data <= ?)\n"
                    + " AND RPV.Parada <> 0  \n"
                    + " AND (Rt_perc.Sequencia is not null or RtEnt.Sequencia is null)\n"
                    + " AND (PreOrder.Guia is null OR RtEnt.Sequencia is null)\n"
                    + " AND RPV.flag_excl <> '*')\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT Rotas.Rota, Rt_PercPro.Parada, cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, CONVERT(BigInt, PreOrder.Guia) Guia, PreOrder.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.Os OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "PreOrder.DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else '' end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else '' end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else '' end HrSaida_E, PreOrder.Valor, Rt_PercPro.CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino   \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino\n"                    
                    + "from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    + "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = PreOrder.Guia \n"
                    + "                   AND Rt_guias.Serie = PreOrder.Serie\n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + "left join CxfGuias on  CxfGuias.Guia = PreOrder.Guia \n"
                    + "                   AND CxfGuias.Serie = PreOrder.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'E') \n"
                    + "\n"
                    + "where (   (Rotas.Data >= ? AND Rotas.Data <=  ?))\n"
                    + " AND  PreOrder.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)\n"
                    + " AND Rt_PercPro.Flag_Excl <> '*')\n"
                    + ")a) AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? \n"
                    + "   AND RowNum < ?\n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty() && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            sql += " ORDER BY RowNum ";
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());
            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());
            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        if (!entry.equals("") && entrada.getKey().contains("?")) {
                            consulta.setString(entry);
                        }
                    }
                }
            }

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                
                eGtv.setOS(consulta.getString("OSPreOrder"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setRota(consulta.getString("Rota"));
//                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaPaginada - " + e.getMessage());
        }
    }

    public List<EGtv> listaSPM(String seqRota, String parada, String data, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM (\n"
                    + "(Select Rotas.Rota, Rotas.Sequencia, Rt_Perc.Parada, cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino \n"                   
                    + " from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "where (   RPV.Data >= ?  AND RPV.Data <= ?)\n"
                    + " AND (Rt_perc.Sequencia is not null or RtEnt.Sequencia is null)\n"
                    + " AND (PreOrder.Guia is null OR RtEnt.Sequencia is null)\n"
                    + " AND RPV.Parada <> 0 \n"
                    + " AND RPV.flag_excl <> '*')\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT Rotas.Rota, Rotas.Sequencia, Rt_PercPro.Parada, cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, CONVERT(BigInt, PreOrder.Guia) Guia, PreOrder.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.Os OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "PreOrder.DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else '' end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else '' end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else '' end HrSaida_E, PreOrder.Valor, Rt_PercPro.CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino\n"                    
                    + "from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    + "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = PreOrder.Guia \n"
                    + "                   AND Rt_guias.Serie = PreOrder.Serie\n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + "left join CxfGuias on  CxfGuias.Guia = PreOrder.Guia \n"
                    + "                   AND CxfGuias.Serie = PreOrder.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'E') \n"
                    + "\n"
                    + "where (   (Rotas.Data >= ? AND Rotas.Data <=  ?))\n"
                    + " AND  PreOrder.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)\n"
                    + " AND Rt_PercPro.Flag_Excl <> '*')\n"
                    + ")a \n"
                    + " WHERE Sequencia = ? AND Parada = ?\n";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(data);
            consulta.setString(seqRota);
            consulta.setString(parada);

            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
//                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
//                eGtv.setNRedFat(consulta.getString("NRedFat"));
//                eGtv.setCodCli1(consulta.getString("CodCli1"));
//                eGtv.setLocalParada(consulta.getString("LocalParada"));
//                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
//                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
//                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
//                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
//                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
//                eGtv.setVolumes(consulta.getString("Volumes"));
//                eGtv.setVolume1(consulta.getString("Volume1"));
//                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                
                eGtv.setOS(consulta.getString("OSPreOrder"));
//                eGtv.setViacxf(consulta.getString("Viacxf"));
//                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
//                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
//                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
//                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
//                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
//                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
//                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setRota(consulta.getString("Rota"));
//                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setCodFil(consulta.getString("CodFil"));

                eGtv.setDtEnt(consulta.getString("DtEnt"));
                eGtv.setVol(consulta.getString("Vol"));
                eGtv.setLacre(consulta.getString("Lacre"));
                eGtv.setNumero(consulta.getString("Numero"));
                eGtv.setOSRtg(consulta.getString("OSRtg"));
                eGtv.setDtColeta(consulta.getString("DtColeta"));
                eGtv.setAgSB(consulta.getString("Ag/SB"));
                eGtv.setOrigem(consulta.getString("Origem"));
                eGtv.setDtEntrega(consulta.getString("DtEntrega"));
                eGtv.setAgSBD(consulta.getString("Ag/SB D"));
                eGtv.setHrPrg(consulta.getString("HrPrg"));
                eGtv.setHrCheg_E(consulta.getString("HrCheg_E"));
                eGtv.setHrCheg_S(consulta.getString("HrCheg_S"));

                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaSPM - " + e.getMessage());
        }
    }

    /**
     * Lista todas as guias de forma paginada.
     *
     * @param primeiro
     * @param linhas
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    @Deprecated
    public List<EGtv> listaPaginada2(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<EGtv> retorno = new ArrayList<>();
            String sql = " SELECT * FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY Rotas.Data DESC, Rt_Perc.HrCheg DESC) AS RowNum,"
                    + " Rt_Perc.Sequencia, Rt_Perc.Parada, Rotas.CodFil, \n"
                    + " Rotas.Data, CONVERT(BigInt,Pedido.PedidoCliente) Pedido, CONVERT(BigInt,RPV.Guia) Guia, RPV.Serie, \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN 'Alívio' WHEN Rt_Perc.ER = 'E' THEN 'Reforço' \n"
                    + " WHEN Rt_Perc.ER = 'A' THEN 'A.Tecnica' ELSE '' END Operacao, Clientes.Agencia AgenciaCliente, Clientes.SubAgencia SubAgenciaParada, \n"
                    + " OS_Vig.NRedFat, Rt_Perc.CodCli1, Clientes.NRed LocalParada, Clientes.Agencia AgenciaParada, rt_perc.HrCheg, Rt_Perc.Hrsaida, \n"
                    + " CASE WHEN OS_Vig.Cliente = Rt_Perc.CodCli1 THEN CASE WHEN Rt_Perc.ER = 'E' THEN '' ELSE OS_Vig.NRedDst END  ELSE \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN OS_Vig.NRed ELSE '' END END AS Destino, \n"
                    + " Case when OS_Vig.Cliente = Rt_Perc.CodCli1 then case when Rt_Perc.ER = 'E' then '' else OS_Vig.CliDst end  else \n"
                    + " Case when Rt_Perc.ER = 'R' then OS_Vig.Cliente else '' end end as CodCliDestino,  \n"
                    + " CASE WHEN OS_Vig.Cliente = Rt_Perc.CodCli1 THEN CASE WHEN Rt_Perc.ER = 'E' THEN '' ELSE CliOSDST.Agencia END  ELSE \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN OS_Vig.NRed ELSE '' END END AS AgenciaDestino, \n"
                    + " CASE WHEN OS_Vig.Cliente = Rt_Perc.CodCli1 THEN CASE WHEN Rt_Perc.ER = 'E' THEN '' ELSE CliOSDST.SubAgencia END  ELSE \n"
                    + " CASE WHEN Rt_Perc.ER = 'R' THEN OS_Vig.NRed ELSE '' END END AS SubAgenciaDestino, \n"
                    + " PreOrder.Obs PreOrderOBS, "
                    + " (SELECT top 1 RTP.HrCheg +' - '+ RTP.HrSaida FROM RPV RPVD \n"
                    + "   LEFT JOIN Rt_Perc RTP ON  RTP.Sequencia = RPVD.SeqRota \n"
                    + "                         AND RTP.Parada    = RPVD.Parada \n"
                    + "   LEFT JOIN Rotas RT ON Rt.Sequencia = RPVD.SeqRota  \n"
                    + "   WHERE RPVD.Guia = RPV.Guia AND RPVD.Serie = RPV.Serie  \n"
                    + "     AND RTP.ER = 'E' \n"
                    + "     AND Rt_Perc.ER = 'R'  \n"
                    + "  ORDER BY Rotas.Data DESC, RTP.Hora1 DESC) Entrega, Round(RPV.Valor, 2) Valor,  \n"
                    + " (SELECT COUNT(*) FROM CxfGuiasVol WHERE CxfGuiasVol.Guia = RPV.Guia AND CxfGuiasVol.Serie = RPV.Serie) Volumes,  \n"
                    + " (SELECT TOP 1 Lacre FROM CxfGuiasVol WHERE CxfGuiasVol.Guia = RPV.Guia AND CxfGuiasVol.Serie = RPV.Serie) Volume1, \n"
                    + " CASE WHEN Pessoa.Matr > 0 THEN 'Chefe Equipe: '+Pessoa.Nome ELSE Pessoa.Nome END Assinatura,  \n"
                    + " (SELECT top 1 CASE WHEN PessoaD.Matr > 0 THEN 'Chefe Equipe: '+PessoaD.Nome ELSE PessoaD.Nome END FROM RPV RPVD \n"
                    + "   LEFT JOIN Rt_Perc RTP ON  RTP.Sequencia = RPVD.SeqRota \n"
                    + "                         AND RTP.Parada    = RPVD.Parada \n"
                    + "   LEFT JOIN Rotas RT ON Rt.Sequencia = RPVD.SeqRota  \n"
                    + "   LEFT JOIN Pessoa PessoaD ON PessoaD.Codigo = RPVD.CodPessoaAut \n"
                    + "   WHERE RPVD.Guia = RPV.Guia AND RPVD.Serie = RPV.Serie  \n"
                    + "     AND RTP.ER = 'E' \n"
                    + "     AND Rt_Perc.ER = 'R'  \n"
                    + "  ORDER BY Rotas.Data DESC, RTP.Hora1 DESC) AssinaturaDestino, OS_Vig.OS, OS_Vig.Viacxf, \n"
                    + " CxFGuias.DtEnt DtEntCxf, CxFGuias.HrEnt HrEntCxf, \n"
                    + " CxfGuias.DtSai DtSaiCxF, CxfGuias.Hrsai HrSaiCxf, TesEntrada.Dt_incl DtEntTes, \n"
                    + " tesEntrada.Hr_Incl HrEntTes, TesSaidasCxf.Dt_Alter DtSaiTes,  \n"
                    + " TesSaidasCxf.hr_Alter HrSaiTes  \n"
                    + " FROM RPV \n"
                    + " LEFT JOIN Rotas ON Rotas.Sequencia = RPV.seqRota \n"
                    + " LEFT JOIN Rt_Guias ON  Rt_Guias.Guia  = RPV.Guia \n"
                    + "                    AND Rt_Guias.Serie = RPV.Serie \n"
                    + " LEFT JOIN OS_Vig ON OS_Vig.OS = Rt_Guias.OS \n"
                    + "                  AND OS_Vig.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN Rt_Perc ON  rt_perc.Sequencia = rpv.SeqRota \n"
                    + "                   AND rt_Perc.Parada   = RPV.Parada \n"
                    + " LEFT JOIN Clientes ON Clientes.Codigo = Rt_Perc.codcli1 \n"
                    + "                    AND Clientes.CodFil = Rotas.CodFil \n"
                    + " LEFT JOIN TesEntrada ON  TesEntrada.CodFil  = Rotas.CodFil\n"
                    + "                      AND TesEntrada.Guia  = RPV.Guia \n"
                    + "                      AND TesEntrada.Serie = RPV.Serie \n"
                    + " LEFT JOIN TesSaidas ON TesSaidas.Guia = RPV.Guia \n"
                    + "                     AND TesSaidas.Serie = RPV.Serie \n"
                    + " LEFT JOIN TesSaidasCxf ON  TesSaidas.Guia  = TesSaidasCxF.Guia  \n"
                    + "                        AND TesSaidas.Serie = TesSaidasCxF.Serie \n"
                    + " LEFT JOIN CxfGuias ON cxfguias.Guia = RPV.Guia \n"
                    + "                    AND CxfGuias.Serie = RPV.Serie \n"
                    + " LEFT JOIN Pedido ON Pedido.Numero = TesSaidas.Pedido \n"
                    + "                  AND Pedido.CodFil = TesSaidas.CodFil \n"
                    + " LEFT JOIN Clientes CliOSDST  on CliOSDST.Codigo = OS_Vig.CliDST \n"
                    + "                             AND CliOSDST.CodFil = OS_Vig.CodFil \n"
                    + " LEFT JOIN PreOrder  on PreOrder.RPV = RPV.RPV \n"
                    + " LEFT JOIN Pessoa ON Pessoa.Codigo = RPV.CodPessoaAut \n"
                    + " WHERE ((Rt_Perc.Er = 'R') OR ";
            if (isTranspCacamba(persistencia.getEmpresa())) {
                sql = sql + "(Rt_Perc.ER = 'E')) AND ";
            } else {
                sql = sql + "(Rt_Perc.ER = 'E' AND CxfGuias.DtSai IS NOT NULL)) AND ";
            }

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + " rpv.flag_excl != '*') \n"
                    + " AS RowConstrainedResult \n"
                    + " WHERE RowNum >= ? \n"
                    + "   AND RowNum < ? \n"
                    + " ORDER BY RowNum \n";
            Consulta consulta = new Consulta(sql, persistencia);

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        consulta.setString(entry);
                    }
                }
            }

            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            EGtv eGtv;
            while (consulta.Proximo()) {
                eGtv = new EGtv();
                eGtv.setData(consulta.getString("Data"));
                eGtv.setPedido(consulta.getString("Pedido"));
                eGtv.setGuia(consulta.getString("Guia"));
                eGtv.setSerie(consulta.getString("Serie"));
                eGtv.setOperacao(consulta.getString("Operacao"));
                eGtv.setNRedFat(consulta.getString("NRedFat"));
                eGtv.setCodCli1(consulta.getString("CodCli1"));
                eGtv.setLocalParada(consulta.getString("LocalParada"));
                eGtv.setAgenciaParada(consulta.getString("AgenciaParada"));
                eGtv.setSubAgenciaParada(consulta.getString("SubAgenciaParada"));
                eGtv.setHrCheg(consulta.getString("HrCheg"));
                eGtv.setHrSaida(consulta.getString("HrSaida"));
                eGtv.setDestino(consulta.getString("Destino"));
                eGtv.setAgenciaDestino(consulta.getString("AgenciaDestino"));
                eGtv.setSubAgenciaDestino(consulta.getString("SubAgenciaDestino"));
                eGtv.setEntrega(consulta.getString("Entrega"));
                eGtv.setValor(consulta.getString("Valor"));
                eGtv.setVolumes(consulta.getString("Volumes"));
                eGtv.setVolume1(consulta.getString("Volume1"));
                eGtv.setPreOrderOBS(consulta.getString("PreOrderOBS"));
                eGtv.setAssinatura(consulta.getString("Assinatura"));
                eGtv.setAssinaturaDestino(consulta.getString("AssinaturaDestino"));
                //eGtv.setParadaD(consulta.getString("ParadaD"));                                
                eGtv.setOS(consulta.getString("OS"));
                eGtv.setViacxf(consulta.getString("Viacxf"));
                eGtv.setDtEntCxf(consulta.getString("DtEntCxf"));
                eGtv.setHrEntCxf(consulta.getString("HrEntCxf"));
                eGtv.setDtSaiCxf(consulta.getString("DtSaiCxf"));
                eGtv.setDtEntTes(consulta.getString("DtEntTes"));
                eGtv.setHrEntTes(consulta.getString("HrEntTes"));
                eGtv.setDtSaiTes(consulta.getString("DtSaiTes"));
                eGtv.setHrSaiTes(consulta.getString("HrSaiTes"));
                eGtv.setParada(consulta.getString("Parada"));
                eGtv.setSequencia(consulta.getString("Sequencia"));
                eGtv.setCodFil(consulta.getString("CodFil"));
                retorno.add(eGtv);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.listaPaginada - " + e.getMessage());
        }
    }

    public Integer qtdTotalGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            Integer retorno = 0;
            Map<String, String> filtro = filtros;
            String sql = " SELECT COUNT(*) qtde FROM (SELECT * FROM (\n"
                    + "(Select \n"
                    + " Rotas.Rota,\n"
                    + " Rt_Perc.Parada, \n"
                    + " Rt_Perc.Sequencia, \n"
                    + " Rt_guias.CodFil\n"
                    + " from RPV\n"
                    + " LEFT JOIN TesSaidas\n"
                    + "                   ON TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n";

            sql += "where RPV.Data BETWEEN ? AND ?\n"
                    + " AND RPV.flag_excl <> '*'";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  ("
                        + "(OS_Vig.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OS_Vig.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil))\n"
                        + " OR (OSPre.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OSPre.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil))\n"
                        + ") \n";
            } else {
                sql += " AND  ((OS_Vig.Cliente = ? or OS_Vig.CliDst = ?) OR (OSPre.Cliente = ? or OSPre.CliDst = ?))\n";
            }
            if (!filtro.get("Guia").equals("")) {
                sql += " AND RPV.Guia = ? \n";
            }

            sql += " AND (Rt_perc.Sequencia is not null or RtEnt.Sequencia is null)\n"
                    + " AND (PreOrder.Guia is null OR RtEnt.Sequencia is null)\n"
                    + " )\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT \n"
                    + " Rotas.Rota,\n"
                    + " Rt_PercPro.Parada, \n"
                    + " Rt_PercPro.Sequencia, \n"
                    + " Rotas.CodFil\n"
                    + "from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    /*+ "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"*/
                    + "left join OS_Vig   on  OS_Vig.OS     = Rt_PercPro.OS\n"
                    + "                   AND OS_VIg.CodFil = Rt_PercPro.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.Sequencia = Rt_PercPro.Sequencia \n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n";

            sql += "where Rotas.Data BETWEEN ? AND ?\n"
                    + " AND Rt_PercPro.Flag_Excl <> '*' and Rotas.rota <> '090'";

            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) \n"
                        + " OR OS_Vig.clidst in \n"
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) \n";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            if (!filtro.get("Guia").equals("")) {
                sql += " AND Rt_guias.Guia = ? \n";
            }

            sql += " \n"
                    + " )\n"
                    + ")a \n"
                    + " WHERE CodFil >= 0 \n";

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }

            sql += ") x";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            if (!filtro.get("Guia").equals("")) {
                consulta.setString(filtro.get("Guia"));
            }

            // UNION
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            if (!filtro.get("Guia").equals("")) {
                consulta.setString(filtro.get("Guia"));
            }

            //consulta.setString(filtro.get("DtInicio"));
            //consulta.setString(filtro.get("DtFim"));
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            while (consulta.Proximo()) {
                retorno = consulta.getInt("qtde");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.qtdTotalGuias - " + e.getMessage());
        }
    }

    public Integer qtdTotalGuiasSPM(Map filtros, Persistencia persistencia) throws Exception {
        try {
            String sql = " SELECT COUNT(*) Total FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * FROM(\n"
                    + "(Select Rotas.Rota, Rt_Perc.Parada, cxfguias.DtEnt, Pedido.PedidoCliente Pedido, RPV.Guia, RPV.Serie,\n"
                    + "(Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '' OSPreOrder,\n"
                    + " Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else Case when Rt_Perc.ER is not null then CliOri.Agencia+' '+CliOri.Subagencia else\n"
                    + "CliDstPre.Agencia+' '+CliDstPre.Subagencia end end 'Ag/SB', \n"
                    + "\n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else Case when Rt_Perc.ER is not nUll then OS_Vig.NRed else OSPre.NRedDst end end Origem, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else OS_Vig.NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, CASE When Rt_Perc.Codfil is not null then Rt_Perc.Codfil else OSPre.CodFil end CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino \n"                    
                    + " from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Rt_Perc RtEnt   on  RtEnt.Pedido = Pedido.Numero\n"
                    + "                          AND RtEnt.CodFil = Pedido.CodFil\n"
                    + "left join OS_Vig OSPre on  OSPre.OS     = PreOrder.OS\n"
                    + "                       AND OSPre.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliDstPre on  CliDstPre.Codigo = OSPre.CliDst\n"
                    + "                             AND CliDstPre.CodFil = OSPre.CodFil\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E') \n"
                    + "where (   RPV.Data >= ?  AND RPV.Data <= ?)\n"
                    + " AND (Rt_perc.Sequencia is not null or RtEnt.Sequencia is null)\n"
                    + " AND (PreOrder.Guia is null OR RtEnt.Sequencia is null)\n"
                    + " AND RPV.flag_excl <> '*')\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT Rotas.Rota, Rt_PercPro.Parada, cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, PreOrder.Guia, PreOrder.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.Os OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "PreOrder.DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else '' end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else '' end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else '' end HrSaida_E, PreOrder.Valor, Rt_PercPro.CodFil, \n"
                    + "Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + "Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"
                    //+ "Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino\n"                     
                    + "from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    + "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = PreOrder.Guia \n"
                    + "                   AND Rt_guias.Serie = PreOrder.Serie\n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + "left join CxfGuias on  CxfGuias.Guia = PreOrder.Guia \n"
                    + "                   AND CxfGuias.Serie = PreOrder.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'E') \n"
                    + "\n"
                    + "where (   (Rotas.Data >= ? AND Rotas.Data <=  ?))\n"
                    + " AND  PreOrder.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)\n"
                    + " AND Rt_PercPro.Flag_Excl <> '*')\n"
                    + ")a) AS RowConstrainedResult \n"
                    + " WHERE CodFil IS NOT NULL \n";

            Map<String, List> filtro = filtros;
            List<String> entries;
            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty() && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey() + " \n";
                }
            }

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());
            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());
            consulta.setString(filtro.get("DtInicio").get(0).toString());
            consulta.setString(filtro.get("DtFim").get(0).toString());

            for (Map.Entry<String, List> entrada : filtro.entrySet()) {
                entries = entrada.getValue();
                if (!entries.isEmpty()) {
                    for (String entry : entries) {
                        if (!entry.equals("") && entrada.getKey().contains("?")) {
                            consulta.setString(entry);
                        }
                    }
                }
            }

            consulta.select();

            consulta.select();

            Integer retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.qtdTotalGuias - " + e.getMessage());
        }
    }

    public BigDecimal valorTotalGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = BigDecimal.ZERO;
            Map<String, String> filtro = filtros;
            String sql = " SELECT ISNULL(SUM(Valor),0) Valor FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * FROM(\n"
                    + "\n"
                    + "( SELECT cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '0' OSPreOrder, Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, Rotas.CodFil, Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + " Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  from RPV\n"
                    //+ " Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  from RPV\n"                    
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E')\n"
                    + "where (   RPV.Data >= ?  AND RPV.Data <= ?)\n";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) ";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            sql += "AND Rt_perc.Sequencia is not null\n"
                    + "AND PreOrder.Guia is null)\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, CONVERT(BigInt, PreOrder.Guia) Guia, PreOrder.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.OS OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "PreOrder.DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else '' end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else '' end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else '' end HrSaida_E, PreOrder.Valor, Rt_PercPro.CodFil,"
                    + " Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + " Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino   \n"
                    // + " Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"                    
                    + " from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    + "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = PreOrder.Guia \n"
                    + "                   AND Rt_guias.Serie = PreOrder.Serie\n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + "left join CxfGuias on  CxfGuias.Guia = PreOrder.Guia \n"
                    + "                   AND CxfGuias.Serie = PreOrder.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'E')\n"
                    + "where (   (Rotas.Data >= ? AND Rotas.Data <= ?))\n";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) ";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            sql += "AND  PreOrder.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)\n"
                    + "AND Rt_PercPro.Flag_Excl <> '*')\n"
                    + ") a ) AS RowConstrainedResult \n"
                    + " WHERE RowNum > 0 \n";
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            if (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("Valor");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.valorTotalGuias - " + e.getMessage());
        }
    }

    public BigDecimal qtdTotalVolumes(Map filtros, Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = BigDecimal.ZERO;
            Map<String, String> filtro = filtros;
            String sql = " SELECT ISNULL(SUM(Vol),0) Vol FROM ( SELECT ROW_NUMBER() OVER ( ORDER BY DtColeta Desc, HrCheg Desc) AS RowNum, * FROM(\n"
                    + "\n"
                    + "( SELECT cxfguias.DtEnt, Pedido.PedidoCliente Pedido, CONVERT(BigInt, RPV.Guia) Guia, RPV.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = RPV.Guia AND Serie = RPV.Serie) Lacre, Pedido.Numero, Rt_Guias.OS OSRtg, '0' OSPreOrder, Case When Rt_Perc.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "Case When Rt_perc.ER = 'R' then Rotas.Data else (Select TesSaidas.Dt_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end DtColeta,\n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrCheg else (select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrCheg, \n"
                    + "Case when Rt_Perc.ER = 'R' then Rt_Perc.HrSaida else (Select TesSaidas.Hr_Alter from TesSaidas where TesSaidas.Guia = RPV.Guia AND TesSaidas.Serie = RPV.Serie) end HrSaida, \n"
                    + "Case when Rt_Perc.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Case When Rt_Perc.ER = 'R' then Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then Rotas.Data else \n"
                    + "(Select RtX.Data from Rotas RtX where RtX.Sequencia = CxfGuias.SeqRotaSai) end else Rotas.Data end DtEntrega,\n"
                    + "Case when Rt_Perc.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_Perc.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.Hora1 else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.Hora1 from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.Hora1 from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end  HrPrg, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrCheg from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrCheg from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_E, \n"
                    + "Case when Rt_Perc.ER = 'E' then Rt_Perc.HrCheg else Case When Rt_Perc.CodCli2 not like '641%' AND Rt_Perc.CodCli2 not like '999%' then \n"
                    + "       (Select RtP.HrSaida from  Rt_perc RtP where RtP.Sequencia = Rt_Perc.Sequencia AND RtP.Parada = Rt_perc.DPar AND RtP.Flag_Excl <> '*') else \n"
                    + "   (Select RtP.HrSaida from Rt_perc RtP where RtP.Sequencia = CxfGuias.SeqRotaSai AND RtP.Hora1 = CxfGuias.Hora1D AND RtP.Flag_Excl <> '*') end end HrCheg_S, \n"
                    + "RPV.Valor, Rotas.CodFil, Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + " Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino   from RPV\n"
                    //+ " Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E')  then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  from RPV\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = Rpv.Guia \n"
                    + "                   AND Rt_guias.Serie = RPV.Serie\n"
                    + "                   AND Rt_Guias.Parada = RPV.Parada\n"
                    + "left join Rt_Perc on Rt_Perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                  AND Rt_Perc.Parada   = Rt_guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join CxfGuias on  CxfGuias.Guia = Rpv.Guia \n"
                    + "                   AND CxfGuias.Serie = RPV.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join OS_Vig on  OS_Vig.OS = (Case when Rt_Guias.OS is not null then Rt_Guias.OS else CxfGUias.OS end)\n"
                    + "                 AND OS_VIg.CodFil = (Case when Rt_Guias.OS is not null then Rotas.CodFil else CxfGUias.CodFil end)\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "\n"
                    + "left join PreOrder on Preorder.Guia = RPV.Guia\n"
                    + "                   AND PreOrder.Serie = RPV.Serie\n"
                    + "                   AND Preorder.Flag_Excl <> '*'\n"
                    + "left join Pedido  on Pedido.PedidoCliente = Preorder.PedidoCliente\n"
                    + "                  AND Pedido.Codfil = Preorder.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "                  AND Pedido.Obs Like '%'+Preorder.Obs\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = RPV.Guia AND RPVX.Serie = RPV.Serie AND Rt_Perc.ER = 'E')\n"
                    + "where (   RPV.Data >= ?  AND RPV.Data <= ?)\n";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) ";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            sql += "AND Rt_perc.Sequencia is not null\n"
                    + "AND PreOrder.Guia is null)\n"
                    + "\n"
                    + "Union\n"
                    + "\n"
                    + "(SELECT cxfguias.DtEnt, PreOrder.PedidoCliente Pedido, CONVERT(BigInt, PreOrder.Guia) Guia, PreOrder.Serie, (Select Count(*) from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Vol, \n"
                    + "(Select Min(Lacre)from CxfGuiasVol where guia = PreOrder.Guia AND Serie = PreOrder.Serie) Lacre, Pedido.Numero, \n"
                    + "Rt_guias.OS OSRtg, PreOrder.OS OSPreOrder, Case When Rt_PercPro.ER = 'E' then 'Reforço' else 'Alívio' end Operacao, \n"
                    + "PreOrder.DtColeta, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HrCheg else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrCheg, \n"
                    + "Case when Rt_PercPro.ER = 'R' then Rt_PercPro.HRSaida else (select top 1 Hora from rpv where Guia = Preorder.Guia AND Serie = PreOrder.Serie order by data) end HrSaida, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliDst.Agencia+' '+CliDst.Subagencia else CliOri.Agencia+' '+CliOri.Subagencia end 'Ag/SB', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRedDst else OS_Vig.NRed end Origem, \n"
                    + "Rotas.Data DtEntrega, \n"
                    + "Case when Rt_PercPro.ER = 'E' then CliOri.Agencia+' '+CliOri.Subagencia else CliDst.Agencia+' '+CliDst.Subagencia end 'Ag/SB D', \n"
                    + "Case when Rt_PercPro.ER = 'E' then OS_Vig.NRed else NRedDst end Destino, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.Hora1 else '' end HrPrg, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HrCheg else '' end HrCheg_E, \n"
                    + "Case when Rt_PercPro.ER = 'E' then Rt_PercPro.HRSaida else '' end HrSaida_E, PreOrder.Valor, Rt_PercPro.CodFil,"
                    + " Case when PessoaRec.Matr > 0 then 'Ch.Eq: '+PessoaRec.Nome else PessoaRec.Nome end Assinatura, \n"
                    + " Case when PessoaEnt.Matr > 0 then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino \n"
                    //+ " Case when PessoaEnt.Matr is null then ' ' when (PessoaEnt.Matr > 0 and Rt_Perc.ER = 'E') then 'Ch.Eq: '+PessoaEnt.Nome else PessoaEnt.Nome end AssinaturaDestino  \n"                    
                    + " from Rt_Perc Rt_PercPro\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_PercPro.Sequencia\n"
                    + "left join Pedido  on Pedido.Numero = Rt_PercPro.Pedido\n"
                    + "                  AND Pedido.Codfil = Rt_PercPro.CodFil\n"
                    + "                  AND Pedido.Flag_Excl <> '*'\n"
                    + "left join PreOrder on Preorder.PedidoCliente = Pedido.PedidoCliente\n"
                    + "                   AND PreOrder.Obs = SubString(Pedido.Obs, 19,100)\n"
                    + "                   AND PreOrder.Flag_Excl <> '*'\n"
                    + "left join OS_Vig   on  OS_Vig.OS     = PreOrder.OS\n"
                    + "                   AND OS_VIg.CodFil = PreOrder.CodFil\n"
                    + "left join Clientes CliOri on  CliOri.Codigo = OS_Vig.Cliente\n"
                    + "                          AND CliOri.CodFil = OS_Vig.CodFil\n"
                    + "left join Clientes CliDst on  CliDst.Codigo = OS_Vig.CliDst\n"
                    + "                          AND CliDst.CodFil = OS_Vig.CodFil\n"
                    + "left join Rt_guias on  Rt_Guias.GUia = PreOrder.Guia \n"
                    + "                   AND Rt_guias.Serie = PreOrder.Serie\n"
                    + "                   AND Rt_Guias.Parada = Rt_percPro.Parada\n"
                    + "left join CxfGuias on  CxfGuias.Guia = PreOrder.Guia \n"
                    + "                   AND CxfGuias.Serie = PreOrder.Serie\n"
                    + "left join Rt_Perc Rt_PercRec on  Rt_PercRec.Sequencia = CxfGuias.SeqRota\n"
                    + "                                AND Rt_PercRec.Hora1     = CxfGuias.Hora1\n"
                    + "left join Pessoa PessoaRec on PessoaRec.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'R')\n"
                    + "left join Pessoa PessoaEnt on PessoaEnt.Codigo = \n"
                    + "   (Select Top 1 RPVX.CodPessoaAut from RPV RPVX \n"
                    + "    left join Rt_Perc on Rt_perc.Sequencia = RPVX.SeqRota AND Rt_Perc.Parada = RPVX.Parada\n"
                    + "    where RPVX.Guia = PreOrder.Guia AND RPVX.Serie = PreOrder.Serie AND Rt_Perc.ER = 'E')\n"
                    + "where (   (Rotas.Data >= ? AND Rotas.Data <= ?))\n";
            if (filtro.get("CodCli").equals("")) {
                sql += " AND  (OS_Vig.Cliente in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil) "
                        + " OR OS_Vig.clidst in "
                        + " (Select CodCli from PessoaCliAut where PessoaCliAut.Codigo = ? AND PessoaCliAut.CodFil = Rotas.CodFil)) ";
            } else {
                sql += " AND  (OS_Vig.Cliente = ? or OS_Vig.CliDst = ?)\n";
            }
            sql += "AND  PreOrder.Guia >= (Select Min(Guia) from PreOrder where DtColeta >= ? AND DtColeta <= ?)\n"
                    + "AND Rt_PercPro.Flag_Excl <> '*')\n"
                    + ") a ) AS RowConstrainedResult \n"
                    + " WHERE RowNum > 0 \n";
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));
            if (filtro.get("CodCli").equals("")) {
                consulta.setString(filtro.get("CodPessoa"));
                consulta.setString(filtro.get("CodPessoa"));
            } else {
                consulta.setString(filtro.get("CodCli"));
                consulta.setString(filtro.get("CodCli"));
            }
            consulta.setString(filtro.get("DtInicio"));
            consulta.setString(filtro.get("DtFim"));

            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("") && entrada.getKey().contains("?")) {
                    consulta.setString(entrada.getValue());
                }
            }

            consulta.select();

            if (consulta.Proximo()) {
                retorno = consulta.getBigDecimal("Vol");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("EGtvDao.qtdTotalVolumes - " + e.getMessage());
        }
    }

    public List<GuiasPortal> editarGuiaLancada(String seqRota, String parada, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<GuiasPortal> guiasPortal = new ArrayList<>();
            GuiasPortal guia = new GuiasPortal();

            sql = "SELECT\n"
                    + " CONVERT(BIGINT, RPV.RPV) rpv,\n"
                    + " RPV.serie,\n"
                    + " RPV.volumes qtdeVolumes,\n"
                    + " RPV.guia,\n"
                    + " RPV.valor,\n"
                    + " Rt_GuiasMoeda.Moeda\n"
                    + " FROM RPV \n"
                    + " LEFT JOIN Rt_GuiasMoeda\n"
                    + "   ON RPV.guia = Rt_GuiasMoeda.guia\n"
                    + "  AND RPV.parada = Rt_GuiasMoeda.parada\n"
                    + " WHERE RPV.SeqRota = " + seqRota + "\n"
                    + " AND   RPV.Parada  = " + parada + "\n"
                    + " AND   RPV.Flag_excl <> '*'";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                guia = new GuiasPortal();

                guia.setGuia(consulta.getString("guia"));
                guia.setMoeda(consulta.getString("Moeda"));
                guia.setQtdeVolumes(consulta.getString("qtdeVolumes"));
                guia.setRPV(consulta.getString("rpv"));
                guia.setSerie(consulta.getString("serie"));
                guia.setValor(consulta.getString("valor"));

                guiasPortal.add(guia);
            }

            // Se não houver guia com RPV, busca em Rt_Guias
            if (guiasPortal.size() == 0) {
                sql = "SELECT\n"
                        + "Rt_Guias.guia rpv,\n"
                        + "Rt_Guias.serie,\n"
                        + "0 qtdeVolumes,\n"
                        + "Rt_Guias.guia,\n"
                        + "Rt_Guias.valor,\n"
                        + "COALESCE(Rt_GuiasMoeda.Moeda,'BRL') Moeda\n"
                        + "FROM Rt_Guias \n"
                        + "LEFT JOIN Rt_GuiasMoeda\n"
                        + "  ON Rt_Guias.guia = Rt_GuiasMoeda.guia\n"
                        + " AND Rt_Guias.parada = Rt_GuiasMoeda.parada\n"
                        + "WHERE Rt_Guias.Sequencia = " + seqRota + "\n"
                        + "AND   Rt_Guias.Parada  = " + parada;

                consulta = new Consulta(sql, persistencia);
                consulta.select();

                while (consulta.Proximo()) {
                    guia = new GuiasPortal();

                    guia.setGuia(consulta.getString("guia"));
                    guia.setMoeda(consulta.getString("Moeda"));
                    guia.setQtdeVolumes(consulta.getString("qtdeVolumes"));
                    guia.setRPV(consulta.getString("rpv"));
                    guia.setSerie(consulta.getString("serie"));
                    guia.setValor(consulta.getString("valor"));

                    guiasPortal.add(guia);
                }
            }

            // Carregar volumes
            if (guiasPortal.size() > 0) {
                for (int I = 0; I < guiasPortal.size(); I++) {
                    sql = "SELECT \n"
                            + " *\n"
                            + " FROM cxfGuiasVol\n"
                            + " WHERE Guia  = '" + guiasPortal.get(I).getGuia() + "'\n"
                            + " AND   Serie = '" + guiasPortal.get(I).getSerie() + "'\n"
                            + " ORDER BY ordem";

                    consulta = new Consulta(sql, persistencia);
                    consulta.select();

                    CxFGuiasVol cxfGuiasVol;
                    guiasPortal.get(I).volumes = new ArrayList<>();

                    while (consulta.Proximo()) {
                        cxfGuiasVol = new CxFGuiasVol();

                        cxfGuiasVol.setCodCli(consulta.getString("CodCli"));
                        cxfGuiasVol.setCodFil(consulta.getString("CodFil"));
                        cxfGuiasVol.setGuia(consulta.getString("Guia"));
                        cxfGuiasVol.setLacre(consulta.getString("Lacre"));
                        cxfGuiasVol.setObs(consulta.getString("Obs"));
                        cxfGuiasVol.setOrdem(consulta.getString("Ordem"));
                        cxfGuiasVol.setQtde(consulta.getString("Qtde"));
                        cxfGuiasVol.setSerie(consulta.getString("Serie"));
                        cxfGuiasVol.setTipo(consulta.getString("Tipo"));
                        cxfGuiasVol.setValor(consulta.getString("Valor"));

                        guiasPortal.get(I).getVolumes().add(cxfGuiasVol);
                    }
                }
            }

            return guiasPortal;

        } catch (Exception e) {
            throw new Exception("EGtvDao.editarGuiaLancada - " + sql + "\n" + e.getMessage());
        }
    }

    public void excluirGuiaPortal(String rpv, String serie, Persistencia persistencia) throws Exception {
        ArquivoLog logerro;
        String caminho = "";
        String sql = "";
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\EgtvDAO\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";

        try {
            sql = "DELETE FROM CxFGuiasVol \n"
                    + "WHERE Guia = (select Guia\n"
                    + "              FROM RPV \n"
                    + "              WHERE rpv = '" + rpv + "'\n"
                    + "              and serie = '" + serie + "')\n"
                    + "and serie = '" + serie + "'";
            logerro.Grava(sql + " - Persistencia-" + persistencia.getEmpresa(), caminho);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.delete();
            consulta.Close();
            sql = "DELETE FROM rt_guias \n"
                    + "WHERE Guia = (select CONVERT(bigint, Guia)\n"
                    + "             FROM RPV \n"
                    + "             WHERE rpv = '" + rpv + "'\n"
                    + "             and serie = '" + serie + "')\n"
                    + "and serie = '" + serie + "'";
            logerro.Grava(sql + " - Persistencia-" + persistencia.getEmpresa(), caminho);
            Consulta consulta2 = new Consulta(sql, persistencia);
            consulta2.delete();
            consulta2.Close();

            sql = "DELETE FROM RPV \n"
                    + "WHERE rpv = '" + rpv + "'\n"
                    + "and serie = '" + serie + "'";
            logerro.Grava(sql + " - Persistencia-" + persistencia.getEmpresa(), caminho);
            Consulta consulta3 = new Consulta(sql, persistencia);
            consulta3.delete();
            consulta3.Close();
        } catch (Exception e) {
            throw new Exception("EGtvDao.excluirGuiaPortal - " + sql + "\n" + e.getMessage());
        }
    }

    public List<Rt_Guias> listaGuiasParaExclusao(String seqRota, String parada, Persistencia persistencia) throws Exception {
        String sql = "";

        try {
            List<Rt_Guias> Retorno = new ArrayList<>();
            Rt_Guias guia;

            sql = "select RPV.*\n"
                    + "from rt_guias\n"
                    + "JOIN RPV\n"
                    + "  ON rt_guias.guia  = RPV.guia\n"
                    + " AND rt_guias.serie = RPV.serie \n"
                    + "WHERE rt_guias.Sequencia = " + seqRota + "\n"
                    + "AND   rt_guias.Parada    = " + parada;

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                guia = new Rt_Guias();

                guia.setGuia(consulta.getString("rpv"));
                guia.setSerie(consulta.getString("serie"));

                Retorno.add(guia);
            }

            consulta.Close();

            return Retorno;

        } catch (Exception e) {
            throw new Exception("EGtvDao.listaGuiasParaExclusao - " + sql + "\n" + e.getMessage());
        }
    }
    
    public String buscarMotoristaGuia(Persistencia persistencia, String guia, String serie) throws Exception{
        String sql = "";
        try {
            String retorno = null;
            sql = "select f.Nome from RPV rpv LEFT JOIN Escala e ON e.SeqRota = rpv.SeqRota LEFT JOIN Funcion f ON f.Matr = e.MatrMot WHERE rpv.Guia = ':guia' AND rpv.Serie = ':serie'";
            sql = sql.replace(":guia", guia);
            sql = sql.replace(":serie", serie);
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            if(consulta.Proximo()){
                retorno = consulta.getString("Nome");
            }
            consulta.Close();
            return retorno;
        }
        catch (Exception e) {
            throw new Exception("EGtvDao.buscarMotoristaGuia - " + sql + "\n" + e.getMessage());
        }
        
    }
}
