package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.Sysdef;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AcessosDao {

    /**
     * Calcula a quantidade de usuarios de uma filial
     *
     * @param codfil codigo da filial
     * @param persistencia
     * @return lista contendo registros do usuários
     * @throws java.lang.Exception
     */
    public Integer contaUsuarios(BigDecimal codfil, Persistencia persistencia) throws Exception {
        Integer acessos = 0;
        try {
            String query = "SELECT count(*) total FROM saspw WHERE codfil = ? and situacao = 'A'";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(codfil);
            consulta.select();

            while (consulta.Proximo()) {
                acessos = consulta.getInt("total");
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.contaUsuarios - " + e.getMessage() + "\r\n"
                    + "SELECT count(*) total FROM saspw WHERE codfil = " + codfil + " and situacao = 'A'");
        }
        return acessos;
    }

    /**
     * Carrega lista de usuarios
     *
     * @param codfil codigo da filial
     * @param persistencia
     * @return lista contendo registros do usuários
     * @throws java.lang.Exception
     */
    public List<Saspw> listaUsuarios(BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            String query = "SELECT top 30 * FROM saspw WHERE codfil = ? and situacao = 'A'";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(codfil);
            consulta.select();

            Saspw acesso = null;
            while (consulta.Proximo()) {
                acesso = new Saspw();

                acesso.setNome(consulta.getString("Nome"));
                acesso.setSituacao(consulta.getString("Situacao"));
                acesso.setMotivo(consulta.getString("Motivo"));
                acesso.setAcessos(consulta.getInt("Acessos"));
                acesso.setCodFil(consulta.getString("Codfil"));
                acesso.setCodigo(consulta.getString("Codigo"));
                acesso.setNomeCompleto(consulta.getString("NomeCompleto"));
                acesso.setDescricao(consulta.getString("Descricao"));
                acesso.setCodGrupo(consulta.getInt("CodGrupo"));
                acesso.setNivelx(consulta.getString("NivelX"));
                acesso.setNivel(consulta.getString("Nivel"));
                acesso.setNivelOP(consulta.getString("NivelOp"));
                acesso.setPW(consulta.getString("PW"));
                acesso.setCodPessoa(consulta.getString("CodPessoa"));
                acesso.setCodPessoaWeb(consulta.getString("CodPessoaWEB"));
                acesso.setCodModulo(consulta.getString("CodModulo"));
                acesso.setModulo(consulta.getString("Modulo"));
                acesso.setOperador(consulta.getString("Operador"));
                acesso.setDt_Alter(consulta.getString("Dt_Alter"));
                acesso.setHr_Alter(consulta.getString("Hr_Alter"));

                acessos.add(acesso);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaUsuarios - " + e.getMessage() + "\r\n"
                    + "SELECT top 30 * FROM saspw WHERE codfil = " + codfil + " and situacao = 'A'");
        }
        return acessos;
    }

    /**
     * Carrega lista de usuarios
     *
     * @param usuario
     * @param CodPessoa
     * @param persistencia
     * @return lista contendo registros do usuários
     * @throws java.lang.Exception
     */
    public List<Saspw> pesquisaUsuarios(Saspw usuario, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            String query = "SELECT * FROM saspw WHERE codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?)";

            if (!usuario.getCodFil().equals(new BigDecimal("-1"))) {
                query = query + " and codfil = ?";
            }
            if (null != usuario.getNivelx()) {
                query = query + " and nivelx = ?";
            }
            if (null != usuario.getSituacao()) {
                query = query + " and situacao = ?";
            }
            if (null != usuario.getNomeCompleto()) {
                query = query + " and nomecompleto like ?";
            }

            query = query + " order by codfil";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(CodPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
                consulta.setString(persistencia.getEmpresa());
//            }
            if (!usuario.getCodFil().equals(new BigDecimal("-1"))) {
                consulta.setBigDecimal(usuario.getCodFil());
            }
            if (null != usuario.getNivelx()) {
                consulta.setString(usuario.getNivelx());
            }
            if (null != usuario.getSituacao()) {
                consulta.setString(usuario.getSituacao());
            }
            if (null != usuario.getNomeCompleto()) {
                consulta.setString("%" + usuario.getNomeCompleto().toUpperCase() + "%");
            }

            consulta.select();

            Saspw acesso;
            while (consulta.Proximo()) {
                acesso = new Saspw();

                acesso.setNome(consulta.getString("Nome"));
                acesso.setSituacao(consulta.getString("Situacao"));
                acesso.setMotivo(consulta.getString("Motivo"));
                acesso.setAcessos(consulta.getInt("Acessos"));
                acesso.setCodFil(consulta.getString("Codfil"));
                acesso.setCodigo(consulta.getString("Codigo"));
                acesso.setNomeCompleto(consulta.getString("NomeCompleto"));
                acesso.setDescricao(consulta.getString("Descricao"));
                acesso.setCodGrupo(consulta.getInt("CodGrupo"));
                acesso.setNivelx(consulta.getString("NivelX"));
                acesso.setNivel(consulta.getString("Nivel"));
                acesso.setNivelOP(consulta.getString("NivelOp"));
                acesso.setPW(consulta.getString("PW"));
                acesso.setCodPessoa(consulta.getString("CodPessoa"));
                acesso.setCodPessoaWeb(consulta.getString("CodPessoaWEB"));
                acesso.setCodModulo(consulta.getString("CodModulo"));
                acesso.setModulo(consulta.getString("Modulo"));
                acesso.setOperador(consulta.getString("Operador"));
                acesso.setDt_Alter(consulta.getString("Dt_Alter"));
                acesso.setHr_Alter(consulta.getString("Hr_Alter"));

                acessos.add(acesso);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.pesquisaUsuarios - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM saspw WHERE codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = " + CodPessoa + " and paramet.path = " + persistencia.getEmpresa() + ")"
                    + " and codfil = " + usuario.getCodFil() + " and nivelx = " + usuario.getNivelx() + " and situacao = " + usuario.getSituacao()
                    + " and nomecompleto like " + usuario.getNomeCompleto() + " order by codfil");
        }
        return acessos;
    }

    /**
     * Busca usuário pelo código de pessoa
     *
     * @param CodPessoa
     * @param persistencia
     * @return lista contendo registros do usuários
     * @throws java.lang.Exception
     */
    public Saspw getUsuario(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        Saspw acesso = new Saspw();
        try {
            String query = "SELECT * FROM saspw WHERE codpessoa = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(CodPessoa);
            consulta.select();

            while (consulta.Proximo()) {
                acesso.setNome(consulta.getString("Nome"));
                acesso.setSituacao(consulta.getString("Situacao"));
                acesso.setMotivo(consulta.getString("Motivo"));
                acesso.setAcessos(consulta.getInt("Acessos"));
                acesso.setCodFil(consulta.getString("Codfil"));
                acesso.setCodigo(consulta.getString("Codigo"));
                acesso.setNomeCompleto(consulta.getString("NomeCompleto"));
                acesso.setDescricao(consulta.getString("Descricao"));
                acesso.setCodGrupo(consulta.getInt("CodGrupo"));
                acesso.setNivelx(consulta.getString("NivelX"));
                acesso.setNivel(consulta.getString("Nivel"));
                acesso.setNivelOP(consulta.getString("NivelOp"));
                acesso.setPW(consulta.getString("PW"));
                acesso.setCodPessoa(consulta.getString("CodPessoa"));
                acesso.setCodPessoaWeb(consulta.getString("CodPessoaWEB"));
                acesso.setCodModulo(consulta.getString("CodModulo"));
                acesso.setModulo(consulta.getString("Modulo"));
                acesso.setOperador(consulta.getString("Operador"));
                acesso.setDt_Alter(consulta.getString("Dt_Alter"));
                acesso.setHr_Alter(consulta.getString("Hr_Alter"));
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.getUsuario - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM saspw WHERE codpessoa = " + CodPessoa);
        }
        return acesso;
    }

    /**
     * Busca usuário pelo código web de pessoa
     *
     * @param CodPessoaWeb
     * @param persistencia
     * @return lista contendo registros do usuários
     * @throws java.lang.Exception
     */
    public Saspw getUsuarioWeb(BigDecimal CodPessoaWeb, Persistencia persistencia) throws Exception {
        Saspw acesso = null;
        try {
            String query = "SELECT * FROM saspw WHERE codpessoaweb = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(CodPessoaWeb);
            consulta.select();

            while (consulta.Proximo()) {
                acesso = new Saspw();
                acesso.setNome(consulta.getString("Nome"));
                acesso.setSituacao(consulta.getString("Situacao"));
                acesso.setMotivo(consulta.getString("Motivo"));
                acesso.setAcessos(consulta.getInt("Acessos"));
                acesso.setCodFil(consulta.getString("Codfil"));
                acesso.setCodigo(consulta.getString("Codigo"));
                acesso.setNomeCompleto(consulta.getString("NomeCompleto"));
                acesso.setDescricao(consulta.getString("Descricao"));
                acesso.setCodGrupo(consulta.getInt("CodGrupo"));
                acesso.setNivelx(consulta.getString("NivelX"));
                acesso.setNivel(consulta.getString("Nivel"));
                acesso.setNivelOP(consulta.getString("NivelOp"));
                acesso.setPW(consulta.getString("PW"));
                acesso.setCodPessoa(consulta.getString("CodPessoa"));
                acesso.setCodPessoaWeb(consulta.getString("CodPessoaWEB"));
                acesso.setCodModulo(consulta.getString("CodModulo"));
                acesso.setModulo(consulta.getString("Modulo"));
                acesso.setOperador(consulta.getString("Operador"));
                acesso.setDt_Alter(consulta.getString("Dt_Alter"));
                acesso.setHr_Alter(consulta.getString("Hr_Alter"));
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.getUsuarioWeb - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM saspw WHERE codpessoaweb = " + CodPessoaWeb);
        }
        return acesso;
    }

    /**
     * Listagem da permissões do usuário
     *
     * @param nome nome do usuário
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<SaspwacSysdef> listaPermissoes(String nome, Persistencia persistencia) throws Exception {
        List<SaspwacSysdef> permissoes = new ArrayList();
        try {
            String query = "select sysdef.codigo sysdefcodigo, * from saspwac left join sysdef on saspwac.sistema = sysdef.codigo where saspwac.nome = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.select();

            SaspwacSysdef permissao;
            Saspwac saspwac;
            Sysdef sysdef;
            while (consulta.Proximo()) {
                permissao = new SaspwacSysdef();
                saspwac = new Saspwac();
                saspwac.setNome(consulta.getString("nome"));
                saspwac.setSistema(consulta.getString("sistema"));
                saspwac.setCodFil(consulta.getString("codfil"));
                saspwac.setCodigo(consulta.getString("codigo"));
                saspwac.setInclusao(consulta.getInt("inclusao"));
                saspwac.setAlteracao(consulta.getInt("alteracao"));
                saspwac.setExclusao(consulta.getInt("exclusao"));
                saspwac.setOperador(consulta.getString("operador"));
                saspwac.setDt_Alter(consulta.getString("dt_alter"));
                saspwac.setHr_Alter(consulta.getString("hr_alter"));
                sysdef = new Sysdef();
                sysdef.setSubSistema(consulta.getString("subsistema"));
                sysdef.setCodigo(consulta.getString("sysdefcodigo"));
                permissao.setSaspwac(saspwac);
                permissao.setSysdef(sysdef);
                permissoes.add(permissao);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaPermissoes - " + e.getMessage() + "\r\n"
                    + "select * from saspwac left join sysdef on saspwac.sistema = sysdef.codigo where saspwac.nome = " + nome);
        }
        return permissoes;
    }
    
    public List<SaspwacSysdef> listaPermissoesRotas(BigDecimal codPessoaBD, Persistencia persistencia) throws Exception {
        List<SaspwacSysdef> permissoes = new ArrayList();
        try {
            String query = "select sysdef.codigo sysdefcodigo, saspwac.*, sysdef.* from saspwac JOIN saspw ON saspwac.nome = saspw.nome left join sysdef on saspwac.sistema = sysdef.codigo where saspw.codpessoa = ? AND saspw.CodPessoaWEB IS NOT NULL";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(codPessoaBD);
            consulta.select();

            SaspwacSysdef permissao;
            Saspwac saspwac;
            Sysdef sysdef;
            while (consulta.Proximo()) {
                permissao = new SaspwacSysdef();
                saspwac = new Saspwac();
                saspwac.setNome(consulta.getString("nome"));
                saspwac.setSistema(consulta.getString("sistema"));
                saspwac.setCodFil(consulta.getString("codfil"));
                saspwac.setCodigo(consulta.getString("codigo"));
                saspwac.setInclusao(consulta.getInt("inclusao"));
                saspwac.setAlteracao(consulta.getInt("alteracao"));
                saspwac.setExclusao(consulta.getInt("exclusao"));
                saspwac.setOperador(consulta.getString("operador"));
                saspwac.setDt_Alter(consulta.getString("dt_alter"));
                saspwac.setHr_Alter(consulta.getString("hr_alter"));
                sysdef = new Sysdef();
                sysdef.setSubSistema(consulta.getString("subsistema"));
                sysdef.setCodigo(consulta.getString("sysdefcodigo"));
                permissao.setSaspwac(saspwac);
                permissao.setSysdef(sysdef);
                permissoes.add(permissao);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaPermissoes - " + e.getMessage() + "\r\n"
                    + "select * from saspwac left join sysdef on saspwac.sistema = sysdef.codigo where saspwac.nome = " + codPessoaBD.toPlainString());
        }
        return permissoes;
    }

    /**
     * Listagem da permissões do usuário
     *
     * @param subsistema nome da permissão
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<SaspwacSysdef> buscarPermissoes(String subsistema, Persistencia persistencia) throws Exception {
        List<SaspwacSysdef> permissoes = new ArrayList();
        try {
            String query = "select top 20 * from sysdef where subsistema like ? or codigo like ? ";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString("%" + subsistema + "%");
            consulta.setString("%" + subsistema + "%");
            consulta.select();

            SaspwacSysdef permissao;
            Saspwac saspwac;
            Sysdef sysdef;
            while (consulta.Proximo()) {
                permissao = new SaspwacSysdef();
                saspwac = new Saspwac();
                sysdef = new Sysdef();
                sysdef.setDescricao(consulta.getString("codigo").replace(".0", "") + ": " + consulta.getString("subsistema"));
                sysdef.setSubSistema(consulta.getString("subsistema"));
                sysdef.setCodigo(consulta.getString("codigo"));
                permissao.setSaspwac(saspwac);
                permissao.setSysdef(sysdef);
                permissoes.add(permissao);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaPermissoes - " + e.getMessage() + "\r\n"
                    + "select * from saspwac left join sysdef on saspwac.sistema = sysdef.codigo where saspwac.nome = " + subsistema);
        }
        return permissoes;
    }

    /**
     * Listagem de todas as permissões do sistema
     *
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<Sysdef> listaTodasPermissoes(Persistencia persistencia) throws Exception {
        List<Sysdef> retorno = new ArrayList();
        try {
            String query = "SELECT * FROM sysdef";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.select();

            Sysdef permissao;
            while (consulta.Proximo()) {
                permissao = new Sysdef();
                permissao.setCodigo(consulta.getString("codigo").replace(".0", ""));
                permissao.setSubSistema(consulta.getString("subsistema"));
                retorno.add(permissao);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaTodasPermissoes - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM sysdef");
        }
        return retorno;
    }

    /**
     * Listagem de todas as permissões de um grupo
     *
     * @param codGrupo
     * @param persistencia Conexão com a base de dados
     * @return lista contendo os registros
     * @throws Exception
     */
    public List<Sysdef> listaPermissoesGrupo(BigDecimal codGrupo, Persistencia persistencia) throws Exception {
        List<Sysdef> retorno = new ArrayList();
        try {
            String query = "SELECT sysdef.codigo, sysdef.subsistema FROM sysdefgrp"
                    + " inner join sysdef on sysdef.codigo = sysdefgrp.codigo"
                    + " where codgrupo = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(codGrupo);
            consulta.select();

            Sysdef permissao;
            while (consulta.Proximo()) {
                permissao = new Sysdef();
                permissao.setCodigo(consulta.getString("codigo").replace(".0", ""));
                permissao.setSubSistema(consulta.getString("subsistema"));
                retorno.add(permissao);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaPermissoesGrupo - " + e.getMessage() + "\r\n"
                    + "SELECT sysdef.codigo, sysdef.subsistema FROM sysdefgrp"
                    + " inner join sysdef on sysdef.codigo = sysdefgrp.codigo"
                    + " where codgrupo = " + codGrupo);
        }
        return retorno;
    }

    /**
     * Lista registros da filiias
     *
     * @param nome nome do usuario do sistema
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<SasPWFill> listaFiliais(String nome, Persistencia persistencia) throws Exception {
        List<SasPWFill> permissoes = new ArrayList();
        try {
            String query = "SELECT * FROM saspwfil WHERE nome = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.select();

            SasPWFill permissao;
            while (consulta.Proximo()) {
                permissao = new SasPWFill();
                permissao.setNome(consulta.getString("nome"));
                permissao.setCodfilAc(consulta.getString("Codfilac"));
                permissao.setCodFil(consulta.getString("codfil"));
                permissao.setOperador(consulta.getString("operador"));
                permissao.setDt_Alter(consulta.getString("Dt_Alter"));
                permissao.setHr_Alter(consulta.getString("Hr_Alter"));
                permissoes.add(permissao);
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaFiliais - " + e.getMessage() + "\r\n"
                    + "SELECT * FROM saspwfil WHERE nome = " + nome);
        }
        return permissoes;
    }

    /**
     * Lista clientes das filiais liberadas para o usuário. SALVA A FILIAL JUNTO
     * AO CÓDIGO codigo}codfil
     *
     * @param nome nome do usuario do sistema
     * @param query restricoes de busca
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<Clientes> BuscaClientes(String nome, String query, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select * \n" //top 10 * \n"
                    + " from clientes "
                    + " where codfil in (select saspwfil.codfilac from saspwfil"
                    + "                  left join saspw on saspw.codfil = saspwfil.codfilac"
                    + "                                 and saspw.nome =  saspwfil.nome"
                    + "                  where saspwfil.nome = ?)"
                    + " and (clientes.nome like ? or clientes.nred like ?) and clientes.situacao = 'A'";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(nome);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }
            consult.Close();

            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n"
                    + "select top 10 * from clientes "
                    + " where codfil in (select saspwfil.codfilac from saspwfil"
                    + "                  left join saspw on saspw.codfil = saspwfil.codfilac"
                    + "                                 and saspw.nome =  saspwfil.nome"
                    + "                  where saspwfil.nome = " + nome + ")"
                    + " and (clientes.nome like " + query.toUpperCase() + " or clientes.nred "
                    + "like " + query.toUpperCase() + ") and clientes.situacao = 'A'");
        }
    }

    /**
     * Lista clientes das filiais liberadas para o usuário
     *
     * @param filiais filiais que o usuário tem acesso
     * @param query busca
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<Clientes> buscaClientes(List<SasPWFill> filiais, String query, Persistencia persistencia) throws Exception {
        String sql = "select top 20 * from clientes where codfil in ( ";
        try {
            List<Clientes> retorno = new ArrayList();
            for (SasPWFill f : filiais) {
                sql = sql + " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + ") "
                    + " and (clientes.nome like ? or clientes.nred like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            for (SasPWFill f : filiais) {
                consult.setBigDecimal(f.getCodfilAc());
            }
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }

            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n" + sql);
        }
    }

    public List<PessoaCliAut> buscaClientesPermissao(List<SasPWFill> filiais, String query, Persistencia persistencia) throws Exception {
        String sql = "select top 20 * from clientes where codfil in ( ";
        List<PessoaCliAut> retorno = new ArrayList<>();
        try {
            for (SasPWFill f : filiais) {
                sql = sql + " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + ") "
                    + " and (clientes.nome like ? or clientes.nred like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            for (SasPWFill f : filiais) {
                consult.setString(f.getCodfilAc());
            }
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();
            PessoaCliAut cliente;
            while (consult.Proximo()) {
                cliente = new PessoaCliAut();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodCli(consult.getString("codigo"));
                cliente.setNomeCli(consult.getString("NRed"));
                retorno.add(cliente);
            }

            consult.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n" + sql);
        }
        return retorno;
    }

    public List<PessoaCliAut> buscaClientesPermissaoServico(List<SasPWFill> filiais, String query, String codigo, Persistencia persistencia) throws Exception {
        String sql = "select top 20 Clientes.* from Clientes\n"
                + " LEFT JOIN OS_Vig ON OS_Vig.Cliente = Clientes.Codigo \n"
                + "                   AND OS_Vig.CodFil = Clientes.CodFil \n"
                + " where Clientes.CodFil in ( ";
        List<PessoaCliAut> retorno = new ArrayList<>();
        try {
            for (SasPWFill f : filiais) {
                sql = sql + " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + ") "
                    + " AND (Clientes.nome like ? or Clientes.nred like ?) \n"
                    + "  AND OS_Vig.Cliente in ( Select b.Cliente from PessoaCliAut a \n"
                    + "                                           Left Join OS_Vig b   on a.CodCli = b.CliFat \n"
                    + "                                                               AND a.CodFil = b.CodFil \n"
                    + "                                           where a.CodFil = OS_Vig.CodFil \n"
                    + "                                             AND a.Codigo = ? \n"
                    + "                                             AND a.Flag_Excl <> '*') \n";
            Consulta consult = new Consulta(sql, persistencia);
            for (SasPWFill f : filiais) {
                consult.setString(f.getCodfilAc());
            }
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString(codigo);
            consult.select();
            PessoaCliAut cliente;
            while (consult.Proximo()) {
                cliente = new PessoaCliAut();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodCli(consult.getString("codigo"));
                cliente.setNomeCli(consult.getString("NRed"));
                retorno.add(cliente);
            }

            consult.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n" + sql);
        }
        return retorno;
    }

    /**
     * Lista clientes das filiais liberadas para o usuário
     *
     * @param filiais filiais que o usuário tem acesso
     * @param codPessoa
     * @param query busca
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<Clientes> buscaClientes(List<SasPWFill> filiais, String codPessoa, String query, Persistencia persistencia) throws Exception {
        String sql = "select * \n"// top 20 * "
                + " from clientes "
                + " left join pessoacliaut on pessoacliaut.codcli = clientes.codigo "
                + "                       and pessoacliaut.codfil = clientes.codfil "
                + " where pessoacliaut.codigo = ? and clientes.situacao = 'A' and clientes.codfil in ( ";
        try {
            List<Clientes> retorno = new ArrayList();
            for (SasPWFill f : filiais) {
                sql = sql + " ?,";
            }
            sql = sql.substring(0, sql.length() - 1) + ") "
                    + " and (clientes.nome like ? or clientes.nred like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codPessoa);
            for (SasPWFill f : filiais) {
                consult.setBigDecimal(f.getCodfilAc());
            }
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }

            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n" + sql);
        }
    }

    /**
     * Lista clientes das filiais liberadas para o usuário. SALVA A FILIAL JUNTO
     * AO CÓDIGO
     *
     * @param query busca
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<Clientes> buscaClientes(String query, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList();
            String sql = "select top 20 * from clientes where (clientes.nome like ? or clientes.nred like ?)";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString("%" + query.toUpperCase() + "%");
            consult.setString("%" + query.toUpperCase() + "%");
            consult.select();
            Clientes cliente;
            while (consult.Proximo()) {
                cliente = new Clientes();
                cliente.setCodigo(consult.getString("codigo"));
                cliente.setCodFil(consult.getString("codfil"));
                cliente.setNome(consult.getString("nome"));
                cliente.setNRed(consult.getString("nred"));
                cliente.setCGC(consult.getString("cgc"));
                cliente.setCPF(consult.getString("cpf"));
                cliente.setCEP(consult.getString("cep"));
                cliente.setEstado(consult.getString("estado"));
                cliente.setCidade(consult.getString("cidade"));
                cliente.setBairro(consult.getString("bairro"));
                cliente.setEnde(consult.getString("ende"));
                retorno.add(cliente);
            }

            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.BuscaClientes - " + e.getMessage() + "\r\n"
                    + "select top 20 * from clientes where (clientes.nome like ? or clientes.nred like ?)");
        }
    }

    /**
     * Lista registros de clientes autorizados
     *
     * @param codPessoa
     * @param excluido
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<PessoaCliAut> listaClientes(BigDecimal codPessoa, Boolean excluido, Persistencia persistencia) throws Exception {
        List<PessoaCliAut> clientes = new ArrayList();
        try {
            String query = "Select PessoaCliAut.*, Clientes.Nred nred from PessoaCliAut"
                    + " left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo"
                    + " left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil"
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli"
                    + " where PessoaCliAut.Codigo = ?";
            if (!excluido) {
                query = query + " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*')";
            }
            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(codPessoa);
            consulta.select();

            PessoaCliAut cliente;
            while (consulta.Proximo()) {
                cliente = new PessoaCliAut();
                cliente.setFlag_Excl(consulta.getString("flag_excl"));
                cliente.setCodCli(consulta.getString("codcli"));
                cliente.setCodFil(consulta.getString("codfil"));
                cliente.setCodigo(consulta.getString("codigo"));
                cliente.setDt_Alter(consulta.getString("dt_alter"));
                cliente.setHr_Alter(consulta.getString("hr_alter"));
                cliente.setOperador(consulta.getString("operador"));
                cliente.setNomeCli(consulta.getString("nred"));
                clientes.add(cliente);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaClientes - " + e.getMessage() + "\n\r"
                    + "Select PessoaCliAut.*, Clientes.Nred nred from PessoaCliAut"
                    + " left join Pessoa on Pessoa.Codigo = PessoaCliaut.Codigo"
                    + " left join Clientes on  Clientes.CodFil  = PEssoaCliAut.CodFil"
                    + "                   and Clientes.Codigo = PessoaCliAut.CodCli"
                    + " where PessoaCliAut.Codigo = " + codPessoa
                    + (excluido ? "" : " and (PessoaCliAut.Flag_Excl is null or PessoaCliAut.Flag_Excl != '*')"));
        }
        return clientes;
    }

    /**
     * Lista todos os usuários por cliente
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Saspw> listarUsuariosCliente(Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Saspw> retorno = new ArrayList<>();
            String sql = " select clientes.ende + ' ' + clientes.cidade + '/' +clientes.Estado ende, "
                    + " clientes.nred, clientes.agencia, clientes.subagencia, saspw.nomecompleto, pessoa.email, saspw.situacao, "
                    + " case when (select count(*) from saspwac where nome = saspw.nome and sistema = '60113') >= 1 "
                    + " then 'Sim' "
                    + " else 'Não' end Adm "
                    + " from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa "
                    + " left join pessoacliaut on pessoacliaut.codigo = saspw.codpessoa "
                    + " left join clientes on clientes.codigo = pessoacliaut.codcli "
                    + "                   and clientes.codfil = saspw.codfil "
                    + " where saspw.nomecompleto is not null and clientes.codigo is not null ";
//                    + " --and saspw.codgrupo = 1001 "

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + " and pessoacliaut.flag_excl <> '*' "
                    + " order by clientes.codigo, saspw.nomecompleto ";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    if (entrada.getKey().contains("like")) {
                        consult.setString("%" + entrada.getValue() + "%");
                    } else {
                        consult.setString(entrada.getValue());
                    }
                }
            }
            consult.select();
            Saspw saspw;
            while (consult.Proximo()) {
                saspw = new Saspw();
                saspw.setNomeCodFil(consult.getString("ende"));
                saspw.setCliente(consult.getString("nred"));
                saspw.setAgencia(consult.getString("agencia"));
                saspw.setSubAgencia(consult.getString("subagencia"));
                saspw.setEmail(consult.getString("email"));
                saspw.setSituacao(consult.getString("situacao"));
                saspw.setNomeCompleto(consult.getString("nomecompleto"));
                saspw.setMotivo(consult.getString("Adm"));
                retorno.add(saspw);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listarUsuariosCliente");
        }
    }

    public List<Saspw> listarUsuariosGrupo(Map filtros, Persistencia persistencia) throws Exception {
        try {
            List<Saspw> retorno = new ArrayList<>();
            String sql = " select saspw.nomecompleto, pessoa.email, saspw.situacao, "
                    + " case when (select count(*) from saspwac where nome = saspw.nome and sistema = '60113') >= 1 "
                    + " then 'Sim' "
                    + " else 'Não' end Adm "
                    + " from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa "
                    + " left join pessoacliaut on pessoacliaut.codigo = saspw.codpessoa "
                    + " where saspw.nomecompleto is not null ";
//                    + " --and saspw.codgrupo = 1001 "

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql += " group by saspw.nomecompleto, pessoa.email, saspw.situacao, saspw.nome";
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    if (entrada.getKey().contains("like")) {
                        consult.setString("%" + entrada.getValue() + "%");
                    } else {
                        consult.setString(entrada.getValue());
                    }
                }
            }
            consult.select();
            Saspw saspw;
            while (consult.Proximo()) {
                saspw = new Saspw();
                saspw.setEmail(consult.getString("email"));
                saspw.setSituacao(consult.getString("situacao"));
                saspw.setNomeCompleto(consult.getString("nomecompleto"));
                saspw.setMotivo(consult.getString("Adm"));
                retorno.add(saspw);
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listarUsuariosCliente");
        }
    }

    /**
     * Lista todos os registros de filial
     *
     * @param persistencia conexão com o banco de dados
     * @return lista contendo registros
     * @throws Exception
     */
    public List<SasPWFill> listaTodasFiliais(Persistencia persistencia) throws Exception {
        List<SasPWFill> retorno = new ArrayList<>();
        try {
            String query = "SELECT Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+Convert(Varchar,Filiais.Codfil) codfil,"
                    + " descricao FROM filiais order by codfil";
            Consulta consulta = new Consulta(query, persistencia);
            consulta.select();

            SasPWFill filiais;
            while (consulta.Proximo()) {
                filiais = new SasPWFill();
                filiais.setCodFil(consulta.getString("codfil"));
                filiais.setCodfilAc(consulta.getString("codfil"));
                filiais.setDescricao(consulta.getString("descricao"));
                retorno.add(filiais);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.listaTodasFiliais - " + e.getMessage() + "\r\n"
                    + "SELECT Replicate('0',4-Len(Convert(Varchar,Filiais.Codfil)))+Convert(Varchar,Filiais.Codfil) codfil,"
                    + " descricao FROM filiais order by codfil");
        }
        return retorno;
    }

    /**
     * Inserir registros da filiais
     *
     * @param filial - Objeto contendo informações de filiais
     * @param persistencia Conexão com banco de dados
     * @exception Exception
     */
    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            String query = "INSERT INTO saspwfil (Nome, CodfilAc, CodFil, Codigo,"
                    + "Operador, Dt_Alter, Hr_Alter) VALUES (?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(filial.getNome());
            consulta.setBigDecimal(filial.getCodfilAc());
            consulta.setBigDecimal(filial.getCodFil());
            consulta.setBigDecimal(filial.getCodigo());
            consulta.setString(filial.getOperador());
            consulta.setString(filial.getDt_Alter());
            consulta.setString(filial.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.inserirFilial - " + e.getMessage() + "\r\n"
                    + "INSERT INTO saspwfil (Nome, CodfilAc, CodFil, Codigo,"
                    + "Operador, Dt_Alter, Hr_Alter) VALUES (" + filial.getNome() + "," + filial.getCodfilAc() + "," + filial.getCodFil() + ","
                    + filial.getCodigo() + "," + filial.getOperador() + "," + filial.getDt_Alter() + "," + filial.getHr_Alter() + ")");
        }
    }

    public Boolean existeCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            String query = "SELECT * from pessoacliaut "
                    + "where codfil = ? and codigo = ? and codcli = ?";
            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(cliente.getCodFil());
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.setString(cliente.getCodCli());
            consulta.select();

            List<PessoaCliAut> cs = new ArrayList<>();
            PessoaCliAut c;
            while (consulta.Proximo()) {
                c = new PessoaCliAut();
                c.setCodigo(consulta.getString("codigo"));
                cs.add(c);
            }

            consulta.Close();
            if (cs.isEmpty()) {
                return false;
            }
            return true;

        } catch (Exception e) {
            throw new Exception("AcessosDAO.existeCliente - " + e.getMessage() + "\r\n"
                    + "SELECT * from pessoacliaut "
                    + "where codfil = " + cliente.getCodFil() + " and codigo = " + cliente.getCodigo() + " and codcli = " + cliente.getCodCli());
        }
    }

    public void atualizaCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            String query = "UPDATE PessoaCliAut set flag_excl = ?, operador = ?,"
                    + " Dt_Alter = ?, Hr_Alter = ?"
                    + " where codfil = ? and codigo = ? and codcli = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(cliente.getFlag_Excl());
            consulta.setString(cliente.getOperador());
            consulta.setString(cliente.getDt_Alter());
            consulta.setString(cliente.getHr_Alter());
            consulta.setBigDecimal(cliente.getCodFil());
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.setString(cliente.getCodCli());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.atualizaCliente - " + e.getMessage() + "\r\n"
                    + "UPDATE PessoaCliAut set flag_excl = " + cliente.getFlag_Excl() + ", operador = " + cliente.getOperador() + ","
                    + " Dt_Alter = " + cliente.getDt_Alter() + ", Hr_Alter = " + cliente.getHr_Alter()
                    + " where codfil = " + cliente.getCodFil() + " and codigo = " + cliente.getCodigo() + " and codcli =" + cliente.getCodCli());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            String query = "INSERT INTO PessoaCliAut (CodFil, Codigo, CodCli, "
                    + "Operador, Dt_Alter, Hr_Alter, flag_excl) VALUES (?,?,?,?,?,?,'')";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(cliente.getCodFil());
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.setString(cliente.getCodCli());
            consulta.setString(cliente.getOperador());
            consulta.setString(cliente.getDt_Alter());
            consulta.setString(cliente.getHr_Alter());
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.inserirCliente - " + e.getMessage() + "\r\n"
                    + "INSERT INTO PessoaCliAut (CodFil, Codigo, CodCli, "
                    + "Operador, Dt_Alter, Hr_Alter) VALUES (" + cliente.getCodFil() + "," + cliente.getCodigo() + ","
                    + cliente.getCodCli() + "," + cliente.getOperador() + "," + cliente.getDt_Alter() + "," + cliente.getHr_Alter() + ")");
        }
    }

    /**
     * Cria a permissão em SASWPW
     *
     * @param saspw - Objeto de SASWPw - Campos Necessários: Nome, Situacao,
     * Codfil, Codigo, NomeCompleto, CodGroup, Nivelx, NivelOP, PW, CodPessoa
     * @param persistencia Conexao com o banco de dados
     */
    public void criarUsuario(Saspw saspw, Persistencia persistencia) throws Exception {
        try {
            String query = "INSERT INTO saspw (nome, situacao, codfil, codigo, nomecompleto,"
                    + "codgrupo, nivelx, nivelop, pw, codpessoa, codpessoaweb, Operador, Dt_Alter, Hr_Alter, motivo, descricao) "
                    + " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(query, persistencia);

            consulta.setString(saspw.getNome());
            consulta.setString(saspw.getSituacao());
            consulta.setBigDecimal(saspw.getCodFil());
            consulta.setBigDecimal(saspw.getCodigo());
            consulta.setString(saspw.getNomeCompleto());
            consulta.setInt(saspw.getCodGrupo());
            consulta.setString(saspw.getNivelx());
            consulta.setString(saspw.getNivelOP());
            consulta.setString(saspw.getPW());
            consulta.setBigDecimal(saspw.getCodPessoa());
            consulta.setBigDecimal(saspw.getCodPessoaWeb());
            consulta.setString(saspw.getOperador());
            consulta.setString(saspw.getDt_Alter());
            consulta.setString(saspw.getHr_Alter());
            consulta.setString(saspw.getMotivo());
            consulta.setString(saspw.getDescricao());

            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.criarUsuario - " + e.getMessage() + "\r\n"
                    + "INSERT INTO saspw (nome, situacao, codfil, codigo, nomecompleto,"
                    + "codgrupo, nivelx, nivelop, pw, codpessoa, codpessoaweb, Operador, Dt_Alter, Hr_Alter, motivo, descricao) "
                    + " VALUES(" + saspw.getNome() + "," + saspw.getSituacao() + "," + saspw.getCodFil() + "," + saspw.getCodigo() + ","
                    + saspw.getNomeCompleto() + "," + saspw.getCodGrupo() + "," + saspw.getNivelx() + "," + saspw.getNivelOP() + ","
                    + saspw.getPW() + "," + saspw.getCodPessoa() + "," + saspw.getCodPessoaWeb() + "," + saspw.getOperador() + ","
                    + saspw.getDt_Alter() + "," + saspw.getHr_Alter() + "," + saspw.getMotivo() + "," + saspw.getDescricao() + ")");

        }
    }

    /**
     * Edita acesso acesso do usuário
     *
     * @param acesso - Objeto de SASWPw - Campos Necessários: Nome, Situacao,
     * Codfil, Codigo, NomeCompleto, CodGroup, Nivelx, NivelOP, PW, CodPessoa
     * @param persistencia Conexao com o banco de dados
     * @throws java.lang.Exception
     */
    public void editarUsuario(Saspw acesso, Persistencia persistencia) throws Exception {
        try {
            String query = " UPDATE saspw SET situacao = ?, codfil = ?,"
                    + " codgrupo = ?, nivelx = ?, nivelop = ?, motivo = ?, descricao = ?, "
                    + " Operador = ?, Dt_Alter = ?, Hr_Alter = ?, codpessoaweb = ?, "
                    + " NomeCompleto = ? "
                    + " WHERE nome = ?";

            Consulta consulta = new Consulta(query, persistencia);

            consulta.setString(acesso.getSituacao());
            consulta.setBigDecimal(acesso.getCodFil());
            consulta.setInt(acesso.getCodGrupo());
            consulta.setString(acesso.getNivelx());
            consulta.setString(acesso.getNivelOP());
            consulta.setString(acesso.getMotivo());
            consulta.setString(acesso.getDescricao());
            consulta.setString(acesso.getOperador());
            consulta.setString(acesso.getDt_Alter());
            consulta.setString(acesso.getHr_Alter());
            consulta.setBigDecimal(acesso.getCodPessoaWeb());
            consulta.setString(acesso.getNomeCompleto());
            consulta.setString(acesso.getNome());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.editarUsuario - " + e.getMessage() + "\r\n"
                    + "UPDATE saspw SET situacao = " + acesso.getSituacao() + ", codfil = " + acesso.getCodFil() + ","
                    + " codgrupo = " + acesso.getCodGrupo() + ", nivelx = " + acesso.getNivelx() + ", nivelop = " + acesso.getNivelOP() + ", "
                    + " motivo = " + acesso.getMotivo() + ", descricao = " + acesso.getDescricao() + ", "
                    + " Operador = " + acesso.getOperador() + ", Dt_Alter = " + acesso.getDt_Alter() + ", Hr_Alter = " + acesso.getHr_Alter()
                    + " WHERE nome = " + acesso.getNome());
        }
    }

    /**
     * Cria as permissões individuais para usuário
     *
     * @param permissao Objeto contendo informações sobre pormissões
     * @param persistencia Conexão com o banco de dados
     * @exception Exception
     */
    public void editarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            //Verifica se a permissão individual já foi criada
            if (existeSasPwAc(permissao.getNome(), permissao.getSistema(), persistencia)) {
                String sql = "UPDATE saspwac SET CodFil = ?, Codigo = ?, "
                        + "Inclusao = ?, Alteracao = ?, Exclusao = ?, Operador = ?, Dt_alter = ?,"
                        + " Hr_alter = ? WHERE Nome = ? AND Sistema = ?";

                Consulta consulta = new Consulta(sql, persistencia);

                consulta.setBigDecimal(permissao.getCodFil());
                consulta.setBigDecimal(permissao.getCodigo());
                consulta.setInt(permissao.getInclusao());
                consulta.setInt(permissao.getAlteracao());
                consulta.setInt(permissao.getExclusao());
                consulta.setString(permissao.getOperador());
                consulta.setString(permissao.getDt_Alter());
                consulta.setString(permissao.getHr_Alter());
                consulta.setString(permissao.getNome());
                consulta.setBigDecimal(permissao.getSistema());

                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.editarPermissoesIndividuais - " + e.getMessage() + "\r\n"
                    + "UPDATE saspwac SET CodFil = " + permissao.getCodFil() + ", Codigo = " + permissao.getCodigo() + ", "
                    + "Inclusao = " + permissao.getInclusao() + ", Alteracao = " + permissao.getAlteracao() + ", Exclusao = " + permissao.getExclusao()
                    + ", Operador = " + permissao.getOperador() + ", Dt_alter = " + permissao.getDt_Alter() + ","
                    + " Hr_alter = " + permissao.getHr_Alter() + " WHERE Nome = " + permissao.getNome() + " AND Sistema = " + permissao.getSistema());
        }
    }

    /**
     * Cria as permissões individuais para usuário
     *
     * @param permissao Objeto contendo informações sobre pormissões
     * @param persistencia Conexão com o banco de dados
     * @exception Exception
     */
    public void criarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            //Verifica se a permissão individual já foi criada
            if (!existeSasPwAc(permissao.getNome(), permissao.getSistema(), persistencia)) {
                String sql = "INSERT INTO saspwac (Nome, Sistema, CodFil, Codigo, "
                        + "Inclusao, Alteracao, Exclusao, Operador, Dt_alter, Hr_alter) "
                        + "VALUES(?,?,?,?,?,?,?,?,?,?)";

                Consulta consulta = new Consulta(sql, persistencia);

                consulta.setString(permissao.getNome());
                consulta.setBigDecimal(permissao.getSistema());
                consulta.setBigDecimal(permissao.getCodFil());
                consulta.setBigDecimal(permissao.getCodigo());
                consulta.setInt(permissao.getInclusao());
                consulta.setInt(permissao.getAlteracao());
                consulta.setInt(permissao.getExclusao());
                consulta.setString(permissao.getOperador());
                consulta.setString(permissao.getDt_Alter());
                consulta.setString(permissao.getHr_Alter());

                consulta.insert();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.criarPermissoesIndividuais - " + e.getMessage() + "\r\n"
                    + "INSERT INTO saspwac (Nome, Sistema, CodFil, Codigo, "
                    + "Inclusao, Alteracao, Exclusao, Operador, Dt_alter, Hr_alter) "
                    + "VALUES(" + permissao.getNome() + "," + permissao.getSistema() + "," + permissao.getCodFil() + ","
                    + permissao.getCodigo() + "," + permissao.getInclusao() + "," + permissao.getAlteracao() + "," + permissao.getExclusao() + ","
                    + permissao.getOperador() + "," + permissao.getDt_Alter() + "," + permissao.getHr_Alter() + ")");
        }
    }

    /**
     * Realiza a contagem de registros
     *
     * @param nome Nome do usuario a receber as permissões
     * @param sistema Codigo do sistema
     * @param persistencia Conexão com a base de dados
     * @exception Exception;
     * @return se existe codigo
     */
    public boolean existeSasPwAc(String nome, BigDecimal sistema, Persistencia persistencia) throws Exception {
        try {
            String query = "SELECT count(*) quantidade FROM saspwac WHERE nome = ? AND sistema = ?";

            //Realiza a consulta
            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.setBigDecimal(sistema);
            consulta.select();

            //Retona a quantidade solicitada na query
            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("quantidade");
            }
            consulta.Close();
            if (quantidade > 0) {
                return true;
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.existeSasPwAc - " + e.getMessage() + "\r\n"
                    + "SELECT count(*) quantidade FROM saspwac WHERE nome = " + nome + " AND sistema = " + sistema);
        }
        return false;
    }

    /**
     * Verifica a existencia da liberação da filial para o usuário
     *
     * @param nome Nome do usuario
     * @param codfil Códigil da filial
     * @param codfilAc
     * @param persistencia Conexão com a base de dados
     * @return se exsite ou não a liberação
     * @throws Exception
     */
    public boolean existeFilialUsuario(String nome, String codfil, String codfilAc, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT COUNT(*) quantidade FROM saspwfil WHERE nome = ? AND codfilac = ?";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(nome);
            consulta.setString(codfilAc);
            consulta.select();

            int quantidade = 0;
            while (consulta.Proximo()) {
                quantidade = consulta.getInt("quantidade");
            }

            consulta.Close();
            if (quantidade > 0) {
                return true;
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.existeFilialUsuario - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) quantidade FROM saspwfil WHERE nome = " + nome + " AND codfil = " + codfil + " AND codfilac = " + codfilAc);
        }
        return false;
    }

    /**
     * Apaga registros da filial
     *
     * @param nome Nome do usuario
     * @param codfilAc
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarRegistroFilial(String nome, String codfilAc, Persistencia persistencia) throws Exception {
        try {
            String query = "DELETE FROM saspwfil WHERE nome = ? AND codfilac = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.setString(codfilAc);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.apagarRegistroFilial - " + e.getMessage() + "\r\n"
                    + "DELETE FROM saspwfil WHERE nome = " + nome + " AND codfilac = " + codfilAc);
        }
    }

    public void apagarRegistroCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            String query = "UPDATE PessoaCliAut SET Flag_Excl = '*' WHERE Codigo = ? and CodFil = ? and CodCli  = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setBigDecimal(cliente.getCodigo());
            consulta.setBigDecimal(cliente.getCodFil());
            consulta.setString(cliente.getCodCli());

            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.apagarRegistroCliente - " + e.getMessage() + "\r\n"
                    + "UPDATE PessoaCliAut SET Flag_Excl = '*' WHERE Codigo = " + cliente.getCodigo() + " and CodFil = " + cliente.getCodFil()
                    + " and CodCli  = " + cliente.getCodCli());
        }
    }

    /**
     * Apaga todos os registros das filiais vinculadas ao nome do acesso
     *
     * @param nome Nome do acesso
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarRegistrosFilial(String nome, Persistencia persistencia) throws Exception {
        try {
            String query = "DELETE FROM saspwfil WHERE nome = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.apagarRegistrosFilial - " + e.getMessage() + "\r\n"
                    + "DELETE FROM saspwfil WHERE nome = " + nome);
        }
    }

    /**
     * Apaga um registro de permissoes
     *
     * @param nome Nome do acesso do sistema
     * @param sistema numero do sistema
     * @param persistencia Conexao com base de dados
     * @throws Exception
     */
    public void apagarRegistroPermissao(String nome, String sistema, Persistencia persistencia) throws Exception {
        try {
            String query = "DELETE FROM saspwac WHERE nome = ? AND sistema = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.setString(sistema);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.apagarRegistroPermissao - " + e.getMessage() + "\r\n"
                    + "DELETE FROM saspwac WHERE nome = " + nome + " AND sistema = " + sistema);
        }
    }

    /**
     * Apaga todos os registros das permissões relacionados ao nome do acesso
     *
     * @param nome Nome do acesso
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarRegistrosPermissao(String nome, Persistencia persistencia) throws Exception {
        try {
            String query = "DELETE FROM saspwac WHERE nome = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(nome);
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.apagarRegistrosPermissao - " + e.getMessage() + "\r\n"
                    + "DELETE FROM saspwac WHERE nome = " + nome);
        }
    }

    /**
     * Bloqueia o usuario
     *
     * @param nome nome do usuario
     * @param persistencia Conexao com a base de dados
     * @throws Exception
     */
    public void bloquearUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            String query = "UPDATE saspw SET situacao = ? WHERE nome = ?";

            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString("B");
            consulta.setString(nome);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.bloquearUsuario - " + e.getMessage() + "\r\n"
                    + "UPDATE saspw SET situacao = B WHERE nome = " + nome);
        }
    }

    /**
     * Carrega Senha do Dia
     *
     * @param senhas
     * @param tipo
     * @param item
     * @param ano
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Integer> senhaDia(List<Integer> senhas, String tipo, int item, int ano, Persistencia persistencia) throws Exception {
        String sql = "Select Valor, SenhaCli from Senhas "
                + " where Tipo = ? "
                + " and Item = ? "
                + " and Ano  = ? ";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(tipo);
            consult.setInt(item);
            consult.setInt(ano);
            consult.select();
            while (consult.Proximo()) {
                senhas.set(0, senhas.get(0) + consult.getInt("valor"));
                senhas.set(1, senhas.get(1) + consult.getInt("senhacli"));
            }
            consult.Close();
            return senhas;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.senhaDia - " + e.getMessage() + "\r\n"
                    + "Select Valor, SenhaCli from Senhas "
                    + " where Tipo = " + tipo
                    + " and Item = " + item
                    + " and Ano  = " + ano);
        }
    }

    /**
     * Retorna a situacao do usuario
     *
     * @param email email do usuario
     * @param persistencia Conexao com a base de dados
     * @return
     * @throws Exception
     */
    public String situacaoUsuario(String email, Persistencia persistencia) throws Exception {
        try {
            String query = "select saspw.situacao from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " where pessoa.email = ?";
            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(email);
            consulta.select();
            String retorno = new String();
            while (consulta.Proximo()) {
                retorno = consulta.getString("situacao");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.situacaoUsuario - " + e.getMessage() + "\r\n"
                    + "Select saspw.situacao from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " where pessoa.email = " + email);
        }
    }
    
     /**
     * Retorna a situacao do usuario
     *
     * @param email email do usuario
     * @param persistencia Conexao com a base de dados
     * @return
     * @throws Exception
     */
    public String situacaoUsuario(String codFil, String email, Persistencia persistencia) throws Exception {
        try {
            String query = "select saspw.situacao from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " where saspw.codFil = ? and pessoa.email = ?";
            Consulta consulta = new Consulta(query, persistencia);
            consulta.setString(codFil);
            consulta.setString(email);
            consulta.select();
            String retorno = new String();
            while (consulta.Proximo()) {
                retorno = consulta.getString("situacao");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.situacaoUsuario - " + e.getMessage() + "\r\n"
                    + "Select saspw.situacao from saspw "
                    + " left join pessoa on pessoa.codigo = saspw.codpessoa"
                    + " where pessoa.email = " + email);
        }
    }

    public Boolean existeUsuario(Saspw usuario, Persistencia persistencia) throws Exception {
        String sql = "SELECT COUNT(*) quantidade FROM saspw WHERE nome = ?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(usuario.getNome());
            consulta.select();

            int quantidade = 0;
            if (consulta.Proximo()) {
                quantidade = consulta.getInt("quantidade");
            }
            consulta.Close();
            if (quantidade > 0) {
                return true;
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.existeUsuario - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) quantidade FROM saspw WHERE nome = " + usuario.getNome());
        }
        return false;
    }

    public Boolean existeNomeUsuario(Saspw usuario, Persistencia persistencia) throws Exception {
        try {
            String sql = "SELECT codpessoaweb, nome FROM saspw WHERE nome = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(usuario.getNome());
            consulta.select();
            String codpessoaweb = null, nome = null;
            while (consulta.Proximo()) {
                codpessoaweb = consulta.getString("codpessoaweb");
                nome = consulta.getString("nome");
            }
            consulta.Close();
            if (null == nome || (null != codpessoaweb
                    && codpessoaweb.replace(".0", "").equals(usuario.getCodPessoaWeb().toPlainString().replace(".0", "")))) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            throw new Exception("AcessosDAO.existeNomeUsuario - " + e.getMessage() + "\r\n"
                    + "SELECT COUNT(*) quantidade FROM saspw WHERE nome = " + usuario.getNome());
        }
    }

    /* CONSULTAS PAGINADAS */
    /**
     * Conta o número de usuarios cadastradas no banco
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer TotalUsuariosMobWeb(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            String sql = "select count(*) total from saspw";
            Map<String, String> filtro = filtros;

            sql = sql + " WHERE  codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consult.setString("SatCVB");
//            } else {
                consult.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    if (entrada.getKey().contains("like")) {
                        consult.setString("%" + entrada.getValue() + "%");
                    } else {
                        consult.setString(entrada.getValue());
                    }
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.TotalUsuariosMobWeb - " + e.getMessage());
        }
    }

    /**
     * Listagem paginada de pessoas para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Saspw> ListaPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY nomecompleto ) AS RowNum, * "
                    + "          FROM      Saspw ";
            Map<String, String> filtro = filtros;
            sql = sql + " WHERE codfil in (select filiais.codfil"
                    + "                          from saspw"
                    + "                          inner join saspwfil on saspwfil.nome = saspw.nome"
                    + "                          inner join filiais on filiais.codfil = saspwfil.codfilac"
                    + "                          inner join paramet on paramet.filial_pdr = filiais.codfil"
                    + "                          where saspw.codpessoa = ? and paramet.path = ?) AND ";
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + entrada.getKey() + " AND ";
                }
            }
            sql = sql + "codigo IS NOT null) AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setBigDecimal(codPessoa);
//            if (persistencia.getEmpresa().equals("SATCONFEDERALBSB")) {
//                consulta.setString("SatCVB");
//            } else {
                consulta.setString(persistencia.getEmpresa());
//            }
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    if (entrada.getKey().contains("like")) {
                        consulta.setString("%" + entrada.getValue() + "%");
                    } else {
                        consulta.setString(entrada.getValue());
                    }
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            Saspw acesso;
            while (consulta.Proximo()) {
                acesso = new Saspw();
                acesso.setNome(consulta.getString("Nome"));
                acesso.setSituacao(consulta.getString("Situacao"));
                acesso.setMotivo(consulta.getString("Motivo"));
                acesso.setAcessos(consulta.getInt("Acessos"));
                acesso.setCodFil(consulta.getString("Codfil"));
                acesso.setCodigo(consulta.getString("Codigo"));
                acesso.setNomeCompleto(consulta.getString("NomeCompleto"));
                acesso.setDescricao(consulta.getString("Descricao"));
                acesso.setCodGrupo(consulta.getInt("CodGrupo"));
                acesso.setNivelx(consulta.getString("NivelX"));
                acesso.setNivel(consulta.getString("Nivel"));
                acesso.setNivelOP(consulta.getString("NivelOp"));
                acesso.setPW(consulta.getString("PW"));
                acesso.setCodPessoa(consulta.getString("CodPessoa"));
                acesso.setCodPessoaWeb(consulta.getString("CodPessoaWEB"));
                acesso.setCodModulo(consulta.getString("CodModulo"));
                acesso.setModulo(consulta.getString("Modulo"));
                acesso.setOperador(consulta.getString("Operador"));
                acesso.setDt_Alter(consulta.getString("Dt_Alter"));
                acesso.setHr_Alter(consulta.getString("Hr_Alter"));

                acessos.add(acesso);
            }

            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.ListaPaginada - " + e.getMessage());
        }
        return acessos;
    }

    public UsuarioSatMobWeb buscarUsuario(BigDecimal codPessoaWeb, Persistencia persistencia, Persistencia satellite) throws Exception {
        UsuarioSatMobWeb retorno = null;
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? and Codigo = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.setString(codPessoaWeb.toPlainString());
            consultaSatellite.select();

            PessoaLogin login = new PessoaLogin();
            if (consultaSatellite.Proximo()) {
                login.setCodigo(new BigDecimal(consultaSatellite.getString("Codigo")));
                login.setCodPessoa(consultaSatellite.getString("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
            }
            consultaSatellite.Close();
            satellite.FechaConexao();

            String sql = " SELECT s.CodFil, s.nivelx, s.nivelop, s.Situacao, s.Descricao sdesc, s.dt_alter, s.hr_alter, s.operador, "
                    + " s.motivo, s.nome snome, s.codgrupo, g.Descricao gdesc, g.codigo gcod, p.nome pnome, p.email, "
                    + " p.pwweb, p.codigo, p.codpessoaweb, p.cpf, p.RG, p.RGOrgEmis, p.situacao situacaoPessoa"
                    + " from saspw s "
                    + " left join SASGrupos g on g.Codigo = s.CodGrupo "
                    + " left join pessoa p on p.codpessoaweb = s.codpessoaweb "
                    + " WHERE s.codpessoaweb = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoaWeb.toEngineeringString());
            consulta.select();
            Pessoa pessoa;
            Saspw saspw;
            SASGrupos sasgrupos;
            if (consulta.Proximo()) {
                retorno = new UsuarioSatMobWeb();
                pessoa = new Pessoa();
                pessoa.setPWWeb(consulta.getString("PWWEB"));
                pessoa.setEmail(consulta.getString("email"));
                pessoa.setNome(consulta.getString("pnome"));
                pessoa.setCPF(consulta.getString("CPF"));
                pessoa.setCodigo(consulta.getString("codigo"));
                pessoa.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                pessoa.setRG(consulta.getString("RG"));
                pessoa.setRGOrgEmis(consulta.getString("RGOrgEmis"));
                pessoa.setSituacao(consulta.getString("situacaoPessoa"));

                saspw = new Saspw();
                saspw.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                saspw.setCodFil(consulta.getString("codfil"));
                saspw.setSituacao(consulta.getString("situacao"));
                saspw.setDescricao(consulta.getString("sdesc"));
                saspw.setNivelx(consulta.getString("Nivelx"));
                saspw.setNivelOP(consulta.getString("NivelOP"));
                saspw.setCodGrupo(consulta.getInt("codgrupo"));
                saspw.setNome(consulta.getString("snome"));
                saspw.setMotivo(consulta.getString("motivo"));
                saspw.setOperador(consulta.getString("operador"));
                saspw.setDt_Alter(consulta.getString("dt_alter"));
                saspw.setHr_Alter(consulta.getString("hr_alter"));

                sasgrupos = new SASGrupos();
                sasgrupos.setDescricao(consulta.getString("gdesc"));
                sasgrupos.setCodigo(consulta.getString("gcod"));

                retorno.setGrupo(sasgrupos);
                retorno.setPessoa(pessoa);
                retorno.setPessoalogin(login);
                retorno.setSaspw(saspw);
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.buscarUsuario - " + e.getMessage());
        }
    }

    /* FIM CONSULTAS PAGINADAS */
    /**
     * Listagem paginada de usuarios para o SatMobWeb
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> ListaUsuarios(int primeiro, int linhas, Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        List<UsuarioSatMobWeb> usuarios = new ArrayList();
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY p.nome ) AS RowNum,"
                    + "          s.CodFil, s.nivelx, s.nivelop, s.Situacao, s.Descricao sdesc, s.dt_alter, s.hr_alter, s.operador,"
                    + "          s.motivo, s.nome snome, s.codgrupo, g.Descricao gdesc, g.codigo gcod, p.nome pnome, p.email,"
                    + "          p.pwweb, p.codigo, p.codpessoaweb, p.cpf"
                    + "          from saspw s"
                    + "          left join SASGrupos g on g.Codigo = s.CodGrupo"
                    + "          left join pessoa p on p.codigo = s.CodPessoa"
                    //+ "                      and p.codpessoaweb = s.codpessoaweb"
                    + " WHERE p.codpessoaweb is not null";
                    //+ " WHERE s.codpessoaweb is not null";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult "
                    + "WHERE   RowNum >= ?"
                    + "    AND RowNum < ? "
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            UsuarioSatMobWeb usuario;
            Pessoa p;
            Saspw s;
            PessoaLogin pl;
            SASGrupos g;
            while (consulta.Proximo()) {
                usuario = new UsuarioSatMobWeb();
                p = new Pessoa();
                pl = new PessoaLogin();
                s = new Saspw();
                g = new SASGrupos();
                s.setCodFil(consulta.getString("codfil"));
                s.setSituacao(consulta.getString("situacao"));
                s.setDescricao(consulta.getString("sdesc"));
                g.setDescricao(consulta.getString("gdesc"));
                p.setCPF(consulta.getString("CPF"));
                p.setNome(consulta.getString("pnome"));
                s.setNivelx(consulta.getString("Nivelx"));
                s.setNivelOP(consulta.getString("NivelOP"));
                s.setCodGrupo(consulta.getInt("codgrupo"));
                g.setCodigo(consulta.getString("gcod"));
                p.setPWWeb(consulta.getString("PWWEB"));
                s.setNome(consulta.getString("snome"));
                p.setEmail(consulta.getString("email"));
                s.setMotivo(consulta.getString("motivo"));
                s.setOperador(consulta.getString("operador"));
                s.setDt_Alter(consulta.getString("dt_alter"));
                s.setHr_Alter(consulta.getString("hr_alter"));
                p.setCodigo(consulta.getString("codigo"));
                s.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                p.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                for (PessoaLogin l : logins) {
                    if (l.getCodPessoaBD().equals(p.getCodigo())) {
                        pl.setBancoDados(l.getBancoDados());
                        pl.setCodPessoaBD(l.getCodPessoaBD());
                        pl.setNivel(l.getNivel());
                        pl.setCodigo(l.getCodigo());
                        //logins.remove(l);
                        break;
                    }
                }
                usuario.setGrupo(g);
                usuario.setPessoa(p);
                usuario.setPessoalogin(pl);
                usuario.setSaspw(s);

                usuarios.add(usuario);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.ListaUsuarios - " + e.getMessage());
        }
        return usuarios;
    }

    /**
     * Listagem paginada de usuarios para o SatMobWeb
     *
     * @param codCli
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> listaUsuarios(String codCli, Persistencia persistencia, Persistencia satellite) throws Exception {
        List<UsuarioSatMobWeb> usuarios = new ArrayList();
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "SELECT s.CodFil, s.nivelx, s.nivelop, s.Situacao, s.Descricao sdesc, s.dt_alter, s.hr_alter, s.operador, \n"
                    + "          s.motivo, s.nome snome, s.codgrupo, g.Descricao gdesc, g.codigo gcod, p.nome pnome, p.email, \n"
                    + "          p.pwweb, p.codigo, p.codpessoaweb, p.cpf, p.RG, p.RGOrgEmis, p.situacao situacaoPessoa \n"
                    + "          from saspw s \n"
                    + "          left join SASGrupos g on g.Codigo = s.CodGrupo \n"
                    + "          left join pessoa p on p.codigo = s.CodPessoa \n"
                    + "                      and p.codpessoaweb = s.codpessoaweb "
                    + "          left join pessoacliaut pa on pa.codigo = p.codigo \n"
                    + " WHERE s.codpessoaweb is not null AND pa.codcli = ? AND pa.Flag_Excl <> '*' ";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.select();
            UsuarioSatMobWeb usuario;
            Pessoa p;
            Saspw s;
            PessoaLogin pl;
            SASGrupos g;
            while (consulta.Proximo()) {
                usuario = new UsuarioSatMobWeb();
                p = new Pessoa();
                pl = new PessoaLogin();
                s = new Saspw();
                g = new SASGrupos();
                s.setCodFil(consulta.getString("codfil"));
                s.setSituacao(consulta.getString("situacao"));
                s.setDescricao(consulta.getString("sdesc"));
                g.setDescricao(consulta.getString("gdesc"));
                p.setCPF(consulta.getString("CPF"));
                p.setRG(consulta.getString("RG"));
                p.setRGOrgEmis(consulta.getString("RGOrgEmis"));
                p.setSituacao(consulta.getString("situacaoPessoa"));
                p.setNome(consulta.getString("pnome"));
                s.setNivelx(consulta.getString("Nivelx"));
                s.setNivelOP(consulta.getString("NivelOP"));
                s.setCodGrupo(consulta.getInt("codgrupo"));
                g.setCodigo(consulta.getString("gcod"));
                p.setPWWeb(consulta.getString("PWWEB"));
                s.setNome(consulta.getString("snome"));
                p.setEmail(consulta.getString("email"));
                s.setMotivo(consulta.getString("motivo"));
                s.setOperador(consulta.getString("operador"));
                s.setDt_Alter(consulta.getString("dt_alter"));
                s.setHr_Alter(consulta.getString("hr_alter"));
                p.setCodigo(consulta.getString("codigo"));
                s.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                p.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                for (PessoaLogin l : logins) {
                    if (l.getCodPessoaBD().equals(p.getCodigo())) {
                        pl.setBancoDados(l.getBancoDados());
                        pl.setCodPessoaBD(l.getCodPessoaBD());
                        pl.setNivel(l.getNivel());
                        pl.setCodigo(l.getCodigo());
                        //logins.remove(l);
                        break;
                    }
                }
                usuario.setGrupo(g);
                usuario.setPessoa(p);
                usuario.setPessoalogin(pl);
                usuario.setSaspw(s);

                usuarios.add(usuario);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.ListaUsuarios - " + e.getMessage());
        }
        return usuarios;
    }

    public List<UsuarioSatMobWeb> listaUsuariosGetLock(String codCofre, Persistencia persistencia, Persistencia satellite) throws Exception {
        List<UsuarioSatMobWeb> usuarios = new ArrayList();
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "SELECT s.CodFil, s.nivelx, s.nivelop, s.Situacao, s.Descricao sdesc, s.dt_alter, s.hr_alter, s.operador, \n"
                    + "          s.motivo, s.nome snome, s.codgrupo, g.Descricao gdesc, g.codigo gcod, p.nome pnome, p.email, \n"
                    + "          p.pwweb, p.codigo, p.codpessoaweb, p.cpf, p.RG, p.RGOrgEmis, p.situacao situacaoPessoa \n"
                    + "          from saspw s \n"
                    + "          left join SASGrupos g on g.Codigo = s.CodGrupo \n"
                    + "          left join pessoa p on p.codigo = s.CodPessoa \n"
                    + "                      and p.codpessoaweb = s.codpessoaweb "
                    + "          left join pessoacliaut pa on pa.codigo = p.codigo \n"
                    + "          left join clientes on clientes.codigo = pa.codcli \n"
                    + " WHERE s.codpessoaweb is not null AND clientes.codcofre = ? AND pa.Flag_Excl <> '*' ";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCofre);
            consulta.select();
            UsuarioSatMobWeb usuario;
            Pessoa p;
            Saspw s;
            PessoaLogin pl;
            SASGrupos g;
            while (consulta.Proximo()) {
                usuario = new UsuarioSatMobWeb();
                p = new Pessoa();
                pl = new PessoaLogin();
                s = new Saspw();
                g = new SASGrupos();
                s.setCodFil(consulta.getString("codfil"));
                s.setSituacao(consulta.getString("situacao"));
                s.setDescricao(consulta.getString("sdesc"));
                g.setDescricao(consulta.getString("gdesc"));
                p.setCPF(consulta.getString("CPF"));
                p.setRG(consulta.getString("RG"));
                p.setRGOrgEmis(consulta.getString("RGOrgEmis"));
                p.setSituacao(consulta.getString("situacaoPessoa"));
                p.setNome(consulta.getString("pnome"));
                s.setNivelx(consulta.getString("Nivelx"));
                s.setNivelOP(consulta.getString("NivelOP"));
                s.setCodGrupo(consulta.getInt("codgrupo"));
                g.setCodigo(consulta.getString("gcod"));
                p.setPWWeb(consulta.getString("PWWEB"));
                s.setNome(consulta.getString("snome"));
                p.setEmail(consulta.getString("email"));
                s.setMotivo(consulta.getString("motivo"));
                s.setOperador(consulta.getString("operador"));
                s.setDt_Alter(consulta.getString("dt_alter"));
                s.setHr_Alter(consulta.getString("hr_alter"));
                p.setCodigo(consulta.getString("codigo"));
                s.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                p.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                for (PessoaLogin l : logins) {
                    if (l.getCodPessoaBD().equals(p.getCodigo())) {
                        pl.setBancoDados(l.getBancoDados());
                        pl.setCodPessoaBD(l.getCodPessoaBD());
                        pl.setNivel(l.getNivel());
                        pl.setCodigo(l.getCodigo());
                        //logins.remove(l);
                        break;
                    }
                }
                usuario.setGrupo(g);
                usuario.setPessoa(p);
                usuario.setPessoalogin(pl);
                usuario.setSaspw(s);

                usuarios.add(usuario);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.ListaUsuarios - " + e.getMessage());
        }
        return usuarios;
    }

    public Integer TotalAcessosMobWeb(Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "select count(*) total"
                    + " from saspw s"
                    + " left join SASGrupos g on g.Codigo = s.CodGrupo"
                    + " left join pessoa p on p.codigo = s.CodPessoa"
                    //+ "             and p.codpessoaweb = s.codpessoaweb"
                    + " WHERE p.codpessoaweb is not null \n";
                    //+ " WHERE s.codpessoaweb is not null \n";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.TotalAcessosMobWeb - " + e.getMessage());
        }
    }

    public List<UsuarioSatMobWeb> listaUsuariosServicos(int primeiro, int linhas, Map filtros, String codPessoa,
            Persistencia persistencia, Persistencia satellite) throws Exception {
        List<UsuarioSatMobWeb> usuarios = new ArrayList();
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "SELECT  * "
                    + "FROM    ( SELECT    ROW_NUMBER() OVER ( ORDER BY p.nome ) AS RowNum,\n"
                    + "          s.CodFil, s.nivelx, s.nivelop, s.Situacao, s.Descricao sdesc, s.dt_alter, s.hr_alter, s.operador,\n"
                    + "          s.motivo, s.nome snome, s.codgrupo, g.Descricao gdesc, g.codigo gcod, p.nome pnome, p.email,\n"
                    + "          p.pwweb, p.codigo, p.cpf, p.codpessoaweb\n"
                    + "          from saspw s\n"
                    + "          left join SASGrupos g on g.Codigo = s.CodGrupo\n"
                    + "          left join pessoa p on p.codigo = s.CodPessoa\n"
                    + "                      and p.codpessoaweb = s.codpessoaweb\n"
                    + "          left join pessoacliaut on pessoacliaut.codigo = p.codigo\n"
                    + " WHERE s.codpessoaweb is not null \n"
                    + " AND pessoacliaut.codcli in ( Select b.Cliente from PessoaCliAut a\n"
                    + "                                       Left Join OS_Vig b   on a.CodCli = b.CliFat\n"
                    + "                                                           AND a.CodFil = b.CodFil\n"
                    + "                                       where a.CodFil = b.CodFil\n"
                    + "                                         AND a.Codigo = ?\n"
                    + "                                         AND a.Flag_Excl <> '*')\n";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            sql = sql + ") AS RowConstrainedResult \n"
                    + "WHERE   RowNum >= ?\n"
                    + "    AND RowNum < ?\n"
                    + "ORDER BY RowNum";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codPessoa);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consulta.setString(entrada.getValue());
                }
            }
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();
            UsuarioSatMobWeb usuario;
            Pessoa p;
            Saspw s;
            PessoaLogin pl;
            SASGrupos g;
            while (consulta.Proximo()) {
                usuario = new UsuarioSatMobWeb();
                p = new Pessoa();
                pl = new PessoaLogin();
                s = new Saspw();
                g = new SASGrupos();
                p.setCPF(consulta.getString("CPF"));
                s.setCodFil(consulta.getString("codfil"));
                s.setSituacao(consulta.getString("situacao"));
                s.setDescricao(consulta.getString("sdesc"));
                g.setDescricao(consulta.getString("gdesc"));
                p.setNome(consulta.getString("pnome"));
                s.setNivelx(consulta.getString("Nivelx"));
                s.setNivelOP(consulta.getString("NivelOP"));
                s.setCodGrupo(consulta.getInt("codgrupo"));
                g.setCodigo(consulta.getString("gcod"));
                p.setPWWeb(consulta.getString("PWWEB"));
                s.setNome(consulta.getString("snome"));
                p.setEmail(consulta.getString("email"));
                s.setMotivo(consulta.getString("motivo"));
                s.setOperador(consulta.getString("operador"));
                s.setDt_Alter(consulta.getString("dt_alter"));
                s.setHr_Alter(consulta.getString("hr_alter"));
                p.setCodigo(consulta.getString("codigo"));
                s.setCodPessoaWeb(consulta.getString("codpessoaweb"));
                p.setCodPessoaWEB(consulta.getBigDecimal("codpessoaweb"));
                for (PessoaLogin l : logins) {
                    if (l.getCodPessoaBD().equals(p.getCodigo())) {
                        pl.setBancoDados(l.getBancoDados());
                        pl.setCodPessoaBD(l.getCodPessoaBD());
                        pl.setNivel(l.getNivel());
                        pl.setCodigo(l.getCodigo());
                        //logins.remove(l);
                        break;
                    }
                }
                usuario.setGrupo(g);
                usuario.setPessoa(p);
                usuario.setPessoalogin(pl);
                usuario.setSaspw(s);

                usuarios.add(usuario);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("AcessosDAO.ListaUsuarios - " + e.getMessage());
        }
        return usuarios;
    }

    public Integer totalAcessosMobWeb(Map filtros, String codPessoa, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            String pessoaslogin = "Select * from pessoalogin "
                    + " where bancodados = ? ";
            Consulta consultaSatellite = new Consulta(pessoaslogin, satellite);
            consultaSatellite.setString(persistencia.getEmpresa());
            consultaSatellite.select();

            List<PessoaLogin> logins = new ArrayList();
            PessoaLogin login;
            while (consultaSatellite.Proximo()) {
                login = new PessoaLogin();
                login.setCodigo(consultaSatellite.getBigDecimal("Codigo"));
                login.setCodPessoaBD(consultaSatellite.getBigDecimal("CodPessoaBD"));
                login.setBancoDados(consultaSatellite.getString("BancoDados"));
                login.setNivel(consultaSatellite.getString("nivel"));
                logins.add(login);
            }

            consultaSatellite.Close();

            String sql = "select count(*) total"
                    + " from saspw s"
                    + " left join SASGrupos g on g.Codigo = s.CodGrupo"
                    + " left join pessoa p on p.codigo = s.CodPessoa"
                    + "             and p.codpessoaweb = s.codpessoaweb"
                    + " left join pessoacliaut on pessoacliaut.codigo = p.codigo\n"
                    + " WHERE s.codpessoaweb is not null \n"
                    + " AND pessoacliaut.codcli in ( Select b.Cliente from PessoaCliAut a\n"
                    + "                                       Left Join OS_Vig b   on a.CodCli = b.CliFat\n"
                    + "                                                           AND a.CodFil = b.CodFil\n"
                    + "                                       where a.CodFil = b.CodFil\n"
                    + "                                         AND a.Codigo = ?\n"
                    + "                                         AND a.Flag_Excl <> '*')\n";

            if (!logins.isEmpty()) {
                sql = sql + " AND p.Codigo in (";
            }
            for (PessoaLogin l : logins) {
                sql = sql + l.getCodPessoaBD().toPlainString().substring(0, l.getCodPessoaBD().toPlainString().lastIndexOf(".0")) + ",";
            }
            if (!logins.isEmpty()) {
                sql = sql.substring(0, sql.lastIndexOf(",")) + ")";
            }

            Map<String, String> filtro = filtros;
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    sql = sql + " AND " + entrada.getKey();
                }
            }
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codPessoa);
            for (Map.Entry<String, String> entrada : filtro.entrySet()) {
                if (!entrada.getValue().equals("")) {
                    consult.setString(entrada.getValue());
                }
            }
            consult.select();
            int retorno = 0;
            while (consult.Proximo()) {
                retorno = consult.getInt("total");
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("AcessosDAO.TotalAcessosMobWeb - " + e.getMessage());
        }
    }

    public void alterarSenhaSimples(BigDecimal codPessoa, String senha, Persistencia persistencia) throws Exception {
        try {
            String sql = "";

            sql = "UPDATE saspw \n"
                    + " SET PW = ?\n"
                    + " WHERE CodPessoa = ?;";
            
            sql += " UPDATE Pessoa \n"
                    + " SET PWWeb = ?\n"
                    + " WHERE Codigo = ?";
            
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(senha);
            consult.setBigDecimal(codPessoa);consult.setString(senha);
            
            consult.setBigDecimal(codPessoa);
            consult.update();

        } catch (Exception e) {
            throw new Exception("AcessosDAO.alterarSenhaSimples - " + e.getMessage());
        }
    }
    
    public void alterarSenhaSimplesCentral(BigDecimal codPessoaWeb, String senha, Persistencia persistencia) throws Exception {
        try {
            String sql = "";

            sql += "UPDATE Pessoa \n"
                    + " SET PWWeb = ?\n"
                    + " WHERE Codigo = ?";
            
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(senha);
            consult.setBigDecimal(codPessoaWeb);
            consult.update();

        } catch (Exception e) {
            throw new Exception("AcessosDAO.alterarSenhaSimplesCentral - " + e.getMessage());
        }
    }
}
