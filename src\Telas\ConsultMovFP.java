package Telas;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import java.awt.Toolkit;
import java.awt.event.KeyEvent;
import javax.swing.table.DefaultTableModel;
import pacotesuteis.Main;

/**
 *
 * <AUTHOR>
 */
public class ConsultMovFP extends javax.swing.JFrame {

    /**
     * Creates new form ConsultFilial
     */
    private Persistencia_OLD persistfilial;
    private String param;

    public ConsultMovFP() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        jScrollPane1 = new javax.swing.JScrollPane();
        ConsultMovFP = new javax.swing.JTable();
        TitPesq = new javax.swing.JLabel();
        jButton1 = new javax.swing.JButton();
        Tconsult = new javax.swing.JLabel();
        TitPesq1 = new javax.swing.JLabel();
        DtIni = new javax.swing.JFormattedTextField();
        DtFim = new javax.swing.JFormattedTextField();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Movimentação de Folha de Pagamento");
        setAlwaysOnTop(true);
        setBackground(new java.awt.Color(20, 91, 155));
        setResizable(false);
        addWindowListener(new java.awt.event.WindowAdapter() {
            public void windowOpened(java.awt.event.WindowEvent evt) {
                formWindowOpened(evt);
            }
            public void windowClosing(java.awt.event.WindowEvent evt) {
                formWindowClosing(evt);
            }
        });

        ConsultMovFP.setAutoCreateRowSorter(true);
        ConsultMovFP.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        ConsultMovFP.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {

            },
            new String [] {
                "CodFil", "CodMovFP", "TipoFP", "DtPagto"
            }
        ));
        ConsultMovFP.setAutoResizeMode(javax.swing.JTable.AUTO_RESIZE_OFF);
        ConsultMovFP.setRowHeight(30);
        ConsultMovFP.setShowVerticalLines(false);
        ConsultMovFP.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultMovFPKeyPressed(evt);
            }
        });
        jScrollPane1.setViewportView(ConsultMovFP);

        TitPesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TitPesq.setText("Data:");

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/Figuras/icone_pesquisar.png"))); // NOI18N
        jButton1.setToolTipText("Toque para pesquisar");
        jButton1.setBorderPainted(false);
        jButton1.setContentAreaFilled(false);
        jButton1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jButton1MouseClicked(evt);
            }
        });
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        TitPesq1.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TitPesq1.setHorizontalAlignment(javax.swing.SwingConstants.CENTER);
        TitPesq1.setText("a");

        try {
            DtIni.setFormatterFactory(new javax.swing.text.DefaultFormatterFactory(new javax.swing.text.MaskFormatter("##/##/####")));
        } catch (java.text.ParseException ex) {
            ex.printStackTrace();
        }
        DtIni.setHorizontalAlignment(javax.swing.JTextField.CENTER);
        DtIni.setFocusable(false);
        DtIni.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        DtIni.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusLost(java.awt.event.FocusEvent evt) {
                DtIniFocusLost(evt);
            }
        });
        DtIni.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                DtIniKeyPressed(evt);
            }
        });

        try {
            DtFim.setFormatterFactory(new javax.swing.text.DefaultFormatterFactory(new javax.swing.text.MaskFormatter("##/##/####")));
        } catch (java.text.ParseException ex) {
            ex.printStackTrace();
        }
        DtFim.setHorizontalAlignment(javax.swing.JTextField.CENTER);
        DtFim.setFocusable(false);
        DtFim.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        DtFim.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusLost(java.awt.event.FocusEvent evt) {
                DtFimFocusLost(evt);
            }
        });
        DtFim.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                DtFimKeyPressed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addComponent(Tconsult)
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addComponent(TitPesq)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(DtIni, javax.swing.GroupLayout.PREFERRED_SIZE, 109, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(20, 20, 20)
                .addComponent(TitPesq1, javax.swing.GroupLayout.PREFERRED_SIZE, 33, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(18, 18, 18)
                .addComponent(DtFim, javax.swing.GroupLayout.PREFERRED_SIZE, 109, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addGap(31, 31, 31)
                .addComponent(jButton1, javax.swing.GroupLayout.PREFERRED_SIZE, 52, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addContainerGap())
            .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 0, Short.MAX_VALUE)
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addComponent(DtIni)
                    .addComponent(jButton1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(TitPesq, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(TitPesq1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(DtFim))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 558, javax.swing.GroupLayout.PREFERRED_SIZE)
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(Tconsult))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void formWindowOpened(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowOpened
        ConsultMovFP.requestFocus();
        DefaultTableModel modelo = (DefaultTableModel) ConsultMovFP.getModel();
        setIconImage(Toolkit.getDefaultToolkit().getImage(getClass().getResource("/Figuras/icone_satellite.png")));
        try {
            persistfilial = new Persistencia_OLD(param, "/Dados/mapconect.txt", "DESK");
            Consulta consult = new Consulta("select top 20 CodFil,CodMovFP,TipoFP,DtPagto "
                    + " from FPMensalOP "
                    //+" where DtPagto between '20130101' and '20130313'" 
                    + " group by CodMovFP,Codfil,TipoFP,DtPagto "
                    + " order by CodMovFP,Codfil,TipoFP,DtPagto desc", persistfilial);
            consult.select();
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2).substring(0, consult.getString(2).indexOf(".")),
                    consult.getString(3),
                    consult.getString(4),});
            }
            consult.Close();
            for (int i = 0; i < 4; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultMovFP, i);
            }
            ConsultMovFP.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowOpened

    private void jButton1MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_jButton1MouseClicked
        Consulta();
    }//GEN-LAST:event_jButton1MouseClicked

    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_jButton1ActionPerformed

    private void ConsultMovFPKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultMovFPKeyPressed
        if (evt.getKeyCode() == 27) {
            String movfp;
            try {
                movfp = (String) ConsultMovFP.getValueAt(ConsultMovFP.getSelectedRow(), 0);
            } catch (Exception e) {
                movfp = "0";
            }
            Main.DadosConsultas.setCodMovFP(movfp);
            this.dispose();
        } else if (evt.getKeyCode() == KeyEvent.VK_P) {
            DtIni.setFocusable(true);
            DtIni.setText("");
            DtIni.requestFocus();
        }
    }//GEN-LAST:event_ConsultMovFPKeyPressed

    private void formWindowClosing(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowClosing
        try {
            persistfilial.FechaConexao();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowClosing

    private void DtIniFocusLost(java.awt.event.FocusEvent evt) {//GEN-FIRST:event_DtIniFocusLost
        if (!DtIni.getText().equals("  /  /    ")) {
            try {
                if (!br.com.sasw.pacotesuteis.utilidades.DataAtual.isDate(DtIni.getText())) {
                    javax.swing.JOptionPane.showMessageDialog(null, "Data inválida", "Falha", 1);
                    DtIni.requestFocus();
                }
            } catch (Exception e) {
                javax.swing.JOptionPane.showMessageDialog(null, "Data inválida", "Falha", 1);
                DtIni.requestFocus();
            }
        }
    }//GEN-LAST:event_DtIniFocusLost

    private void DtIniKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_DtIniKeyPressed
        if (evt.getKeyCode() == KeyEvent.VK_ENTER) {
            DtFim.setFocusable(true);
            DtFim.requestFocus();
        }
    }//GEN-LAST:event_DtIniKeyPressed

    private void DtFimFocusLost(java.awt.event.FocusEvent evt) {//GEN-FIRST:event_DtFimFocusLost
        if (!DtFim.getText().equals("  /  /    ")) {
            try {
                if (!br.com.sasw.pacotesuteis.utilidades.DataAtual.isDate(DtFim.getText())) {
                    javax.swing.JOptionPane.showMessageDialog(null, "Data inválida", "Falha", 1);
                    DtFim.requestFocus();
                }
            } catch (Exception e) {
                javax.swing.JOptionPane.showMessageDialog(null, "Data inválida", "Falha", 1);
                DtFim.requestFocus();
            }
        }
    }//GEN-LAST:event_DtFimFocusLost

    private void DtFimKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_DtFimKeyPressed
        if (evt.getKeyCode() == KeyEvent.VK_ENTER) {
            Consulta();
        }
    }//GEN-LAST:event_DtFimKeyPressed

    private void Consulta() {
        DefaultTableModel modelo = (DefaultTableModel) ConsultMovFP.getModel();
        try {
            Consulta consult;
            if (DtIni.getText().equals("  /  /    ")) {
                consult = new Consulta("select top 20 CodFil,CodMovFP,TipoFP,DtPagto "
                        + " from FPMensalOP "
                        //+" where DtPagto between '20130101' and '20130313'" 
                        + " group by CodMovFP,Codfil,TipoFP,DtPagto "
                        + " order by CodMovFP,Codfil,TipoFP,DtPagto desc", persistfilial);
            } else {
                consult = new Consulta("select top 20 CodFil,CodMovFP,TipoFP,DtPagto "
                        + " from FPMensalOP "
                        + " where DtPagto between ? and ?"
                        + " group by CodMovFP,Codfil,TipoFP,DtPagto "
                        + " order by CodMovFP,Codfil,TipoFP,DtPagto desc", persistfilial);
                consult.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(DtIni.getText()));
                consult.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.inverteData(DtFim.getText()));
            }
            consult.select();
            modelo.setNumRows(0);
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2).substring(0, consult.getString(2).indexOf(".")),
                    consult.getString(3),
                    consult.getString(4),});
            }
            consult.Close();
            for (int i = 0; i < 4; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultMovFP, i);
            }
            ConsultMovFP.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(ConsultMovFP.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(ConsultMovFP.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(ConsultMovFP.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(ConsultMovFP.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new ConsultMovFP().setVisible(true);
            }
        });
    }
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTable ConsultMovFP;
    private javax.swing.JFormattedTextField DtFim;
    private javax.swing.JFormattedTextField DtIni;
    private javax.swing.JLabel Tconsult;
    private javax.swing.JLabel TitPesq;
    private javax.swing.JLabel TitPesq1;
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.JButton jButton1;
    private javax.swing.JScrollPane jScrollPane1;
    // End of variables declaration//GEN-END:variables
}
