/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasLibrary;

import Dados.Persistencia;
import SasBeans.Rh_Seman;
import SasDaos.Rh_SemanDao;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Rh_Semans {

    public static String CarregaSeman(String sCodFil, String sMatr, Persistencia persistencia) throws Exception {
        String ret = "<?xml version=\"1.0\"?>";
        try {

            //carrega postos de serviços
            List<Rh_Seman> list_Rh_Seman;
            Rh_SemanDao oRh_Seman = new Rh_SemanDao();
            try {
                list_Rh_Seman = oRh_Seman.getRhSeman(sMatr, sCodFil, persistencia);
            } catch (Exception e) {
                throw new Exception("Falha ao carregar dados semana - " + e.getMessage());
            }
            if (list_Rh_Seman.isEmpty()) {
                //return "<f>" + list_lrota.get(0).getFuncion().getNome_Guer() + ", Fim de Rota";
                ret += "<resp>Rh_Seman_2</resp>";//sem registros
                return ret;
            }

            String xml = "<?xml version=\"1.0\"?><nreg>" + list_Rh_Seman.size() + "</nreg>";

            for (int i = 0; i < list_Rh_Seman.size(); i++) {
                xml += "<rhseman>";
                xml += "<matr>" + list_Rh_Seman.get(i).getMatr() + "</matr>";
                xml += "<codfil>" + list_Rh_Seman.get(i).getCodFil() + "</codfil>";
                xml += "<semana>" + list_Rh_Seman.get(i).getSemana() + "</semana>";
                xml += "<hr50>" + list_Rh_Seman.get(i).getHr50() + "</hr50>";
                xml += "<hr100>" + list_Rh_Seman.get(i).getHr100() + "</hr100>";
                xml += "<hr3>" + list_Rh_Seman.get(i).getHr3() + "</hr3>";
                xml += "<hr50i>" + list_Rh_Seman.get(i).getHr50i() + "</hr50i>";
                xml += "<hr100i>" + list_Rh_Seman.get(i).getHr100i() + "</hr100i>";
                xml += "<hr3i>" + list_Rh_Seman.get(i).getHr3i() + "</hr3i>";
                xml += "<adnot>" + list_Rh_Seman.get(i).getAdNot() + "</adnot>";
                xml += "<horastrab>" + list_Rh_Seman.get(i).getHorasTrab() + "</horastrab>";
                xml += "<hsabnfalta>" + list_Rh_Seman.get(i).getHsAbnFalta() + "</hsabnfalta>";
                xml += "<hsabnferiado>" + list_Rh_Seman.get(i).getHsAbnFeriado() + "</hsabnferiado>";
                xml += "<hsprojecao>" + list_Rh_Seman.get(i).getHsProjecao() + "</hsprojecao>";
                xml += "<faltas>" + list_Rh_Seman.get(i).getFaltas() + "</faltas>";
                xml += "<faltasjust>" + list_Rh_Seman.get(i).getFaltasJust() + "</faltasjust>";
                xml += "<reciclagem>" + list_Rh_Seman.get(i).getReciclagem() + "</reciclagem>";
                xml += "<sindicato>" + list_Rh_Seman.get(i).getSindicato() + "</sindicato>";
                xml += "<transito>" + list_Rh_Seman.get(i).getTransito() + "</transito>";
                xml += "<heinc>" + list_Rh_Seman.get(i).getHEInc() + "</heinc>";
                xml += "<horasextr>" + list_Rh_Seman.get(i).getHorasExtr() + "</horasextr>";
                xml += "<hsacomp>" + list_Rh_Seman.get(i).getHsAComp() + "</hsacomp>";
                xml += "<diasfolga>" + list_Rh_Seman.get(i).getDiasFolga() + "</diasfolga>";
                xml += "<diasfertrab>" + list_Rh_Seman.get(i).getDiasFerTrab() + "</diasfertrab>";
                xml += "<heferiado>" + list_Rh_Seman.get(i).getHEFeriado() + "</heferiado>";
                xml += "<operador>" + list_Rh_Seman.get(i).getOperador() + "</operador>";
                xml += "<dtalter>" + list_Rh_Seman.get(i).getDt_Alter() + "</dtalter>";
                xml += "<hralter>" + list_Rh_Seman.get(i).getHr_Alter() + "</hralter>";
                xml += "</rhseman>";
            }
            return xml = xml.replaceAll("&", "&amp;");

        } // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd // trata possíveis erros de comunicacao com sgbd
        catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
