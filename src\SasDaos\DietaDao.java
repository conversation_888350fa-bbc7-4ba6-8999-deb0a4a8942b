/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeansCompostas.Dieta;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DietaDao {

    String sql = "";

    public List<Dieta> listaDieta(Persistencia persistencia) throws Exception {
        try {
            List<Dieta> retorno = new ArrayList<>();

            sql = "SELECT * FROM Dieta ORDER BY CASE WHEN Sequencia = 1 THEN 0 ELSE 1 END, Descricao";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            Dieta dieta;

            while (consulta.Proximo()) {
                dieta = new Dieta();
                dieta.setDescricao(consulta.getString("Descricao"));
                dieta.setDt_alter(consulta.getString("Dt_alter"));
                dieta.setEspecificacao(consulta.getString("Especificacao"));
                dieta.setHr_Alter(consulta.getString("Hr_Alter"));
                dieta.setOperador(consulta.getString("Operador"));
                dieta.setSequencia(consulta.getString("Sequencia"));

                retorno.add(dieta);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("DietaDao.listaDieta - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public void salvarDieta(Persistencia persistencia, SasBeansCompostas.Dieta dieta) throws Exception {
        try {
            List<Dieta> retorno = new ArrayList<>();

            if (new Float(dieta.getSequencia()) == 0) {
                sql = "INSERT INTO Dieta (Sequencia,Descricao,Especificacao,Operador,Dt_alter, Hr_Alter) VALUES(";
                sql += "(SELECT ISNULL((MAX(Sequencia)+1),1) FROM Dieta),?,?,?,?,?);";

                Consulta consulta = new Consulta(sql, persistencia);
                consulta.setString(dieta.getDescricao());
                consulta.setString(dieta.getEspecificacao());
                consulta.setString(dieta.getOperador());
                consulta.setString(dieta.getDt_alter());
                consulta.setString(dieta.getHr_Alter());

                consulta.insert();
                consulta.close();
            } else {
                sql = "UPDATE Dieta SET";
                sql += " Descricao     = ?,";
                sql += " Especificacao = ?,";
                sql += " Operador      = ?,";
                sql += " Dt_alter      = ?,";
                sql += " Hr_Alter      = ?";
                sql += " WHERE Sequencia = ?;";

                Consulta consulta = new Consulta(sql, persistencia);

                consulta.setString(dieta.getDescricao());
                consulta.setString(dieta.getEspecificacao());
                consulta.setString(dieta.getOperador());
                consulta.setString(dieta.getDt_alter());
                consulta.setString(dieta.getHr_Alter());
                consulta.setString(dieta.getSequencia());

                consulta.update();
                consulta.close();
            }
        } catch (Exception e) {
            throw new Exception("DietaDao.listaDieta - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

}
