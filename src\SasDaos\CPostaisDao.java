/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CPostais;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CPostaisDao {

    // create
    /**
     * gravaCPostais
     *
     * @param cpostais
     * @param persistencia
     * @return
     */
    public boolean gravaCPostais(CPostais cpostais, Persistencia persistencia) {
        boolean retorno;
        String sql = "insert into cpostais(Pais,CP,Assentamento,TipoAssenta,Cidade,"
                + "Estado,CidadeDesc,CodEstado,CodMunicipio,IDCP) "
                + "Values (?,?,?,?,?,?,?,?,?,?,?)";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpostais.getPais());
            consulta.setString(cpostais.getCP());
            consulta.setString(cpostais.getAssentamento());
            consulta.setString(cpostais.getTipoAssenta());
            consulta.setString(cpostais.getCidade());
            consulta.setString(cpostais.getEstado());
            consulta.setString(cpostais.getCidadeDesc());
            consulta.setBigDecimal(cpostais.getCodEstado());
            consulta.setBigDecimal(cpostais.getCodMunicipio());
            consulta.setBigDecimal(cpostais.getIDCP());
            consulta.insert();
            consulta.close();
            retorno = true;
        } catch (Exception e) {
            retorno = false;
        }
        return retorno;
    }

    // read
    /**
     * buscaCPostais
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CPostais> buscaCPostais(Persistencia persistencia) throws Exception {
        List<CPostais> listCPostais;
        try {
            CPostais cpostais;
            Consulta consult = new Consulta("select Pais,CP,Assentamento,TipoAssenta,Cidade,"
                    + "Estado,CidadeDesc,CodEstado,CodMunicipio,IDCP "
                    + "from cpostais", persistencia);
            consult.select();
            listCPostais = new ArrayList();
            while (consult.Proximo()) {
                cpostais = new CPostais();
                cpostais.setPais(consult.getString("Sequencia"));
                cpostais.setCP(consult.getString("CP"));
                cpostais.setAssentamento(consult.getString("Assentamento"));
                cpostais.setTipoAssenta(consult.getString("TipoAssenta"));
                cpostais.SetCidade(consult.getString("Cidade"));
                cpostais.setEstado(consult.getString("Estado"));
                cpostais.setCidadeDesc(consult.getString("CidadeDesc"));
                cpostais.setCodEstado(consult.getString("CodEstado"));
                cpostais.setCodMunicipio(consult.getString("CodMunicipio"));
                cpostais.setIDCP(consult.getString("IDCP"));
                listCPostais.add(cpostais);
            }
            consult.Close();
        } catch (Exception e) {
            listCPostais = null;
            throw new Exception("CPostaisDao.buscaCPostais - " + e.getMessage() + "\r\n"
                    + "select Pais,CP,Assentamento,TipoAssenta,Cidade,"
                    + "Estado,CidadeDesc,CodEstado,CodMunicipio,IDCP "
                    + "from cpostais");
        }
        return listCPostais;
    }

    // update
    /**
     * atualizarCPostais
     *
     * @param cpostais
     * @param persistencia
     * @throws Exception
     */
    public void atualizarCPostais(CPostais cpostais, Persistencia persistencia) throws Exception {
        String sql = "update cpostais set Pais=?, CP=?, Assentamento=?, TipoAssenta=?, Cidade=?, "
                + "Estado=?, CidadeDesc=?, CodEstado=?, CodMunicipio=?, IDCP=? "
                + "where Pais=? and CP=? and CodEstado=? and IDCP=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpostais.getPais());
            consulta.setString(cpostais.getCP());
            consulta.setString(cpostais.getAssentamento());
            consulta.setString(cpostais.getTipoAssenta());
            consulta.setString(cpostais.getCidade());
            consulta.setString(cpostais.getEstado());
            consulta.setString(cpostais.getCidadeDesc());
            consulta.setBigDecimal(cpostais.getCodEstado());
            consulta.setBigDecimal(cpostais.getCodMunicipio());
            consulta.setBigDecimal(cpostais.getIDCP());
            consulta.setString(cpostais.getPais());
            consulta.setString(cpostais.getCP());
            consulta.setBigDecimal(cpostais.getCodEstado());
            consulta.setBigDecimal(cpostais.getIDCP());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CPostaisDao.atualizarCPostais - " + e.getMessage() + "\r\n"
                    + "update cpostais set Pais=" + cpostais.getPais() + ", CP=" + cpostais.getCP() + ", Assentamento=" + cpostais.getAssentamento() + ", "
                    + "TipoAssenta=" + cpostais.getTipoAssenta() + ", Cidade=" + cpostais.getCidade() + ", "
                    + "Estado=" + cpostais.getEstado() + ", CidadeDesc=" + cpostais.getCidadeDesc() + ", CodEstado=" + cpostais.getCodEstado() + ", "
                    + "CodMunicipio=" + cpostais.getCodMunicipio() + ", IDCP=" + cpostais.getIDCP() + " "
                    + "where Pais=" + cpostais.getPais() + " and CP=" + cpostais.getCP() + " and CodEstado=" + cpostais.getCodEstado() + " and IDCP=" + cpostais.getIDCP());
        }
    }

    // delete
    /**
     * excluirCPostais
     *
     * @param cpostais
     * @param persistencia
     * @throws Exception
     */
    public void excluirCPostais(CPostais cpostais, Persistencia persistencia) throws Exception {
        String sql = "delete from cpostais "
                + "where Pais=? and CP=? and CodEstado=? and IDCP=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(cpostais.getPais());
            consulta.setString(cpostais.getCP());
            consulta.setBigDecimal(cpostais.getCodEstado());
            consulta.setBigDecimal(cpostais.getIDCP());
            consulta.delete();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CPostaisDao.excluirCPostais - " + e.getMessage() + "\r\n"
                    + "delete from cpostais "
                    + "where Pais=" + cpostais.getPais() + " and CP=" + cpostais.getCP() + ""
                    + " and CodEstado=" + cpostais.getCodEstado() + " and IDCP=" + cpostais.getIDCP());
        }
    }
}
