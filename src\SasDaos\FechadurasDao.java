package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.Fechaduras;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class FechadurasDao {

    /**
     * Retorna a lista de fechaduras me conjunto, cliente veiculo
     *
     * @param Codfil - código da filial
     * @param codcli - Código cliente
     * @param carro - Chave carro
     * @param persistencia - conexão com o banco
     * @return - retorna dados fechadura
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<Fechaduras> listaFechaduras(String codfil, String codcli, String carro, String seqRota, Persistencia persistencia) throws Exception {
        Fechaduras fechadura;
        List<Fechaduras> listFechaduras = new ArrayList();
        String vSQLAdic = "";
        if (persistencia.getEmpresa().equals("SATCORPVS")  ||  persistencia.getEmpresa().equals("SATCORPVSPE")||
            persistencia.getEmpresa().equals("SATCORPVS2") ||  persistencia.getEmpresa().equals("SATCORPVSPE2")){
           vSQLAdic =  "and FechaDuras.Tipo <> '22'\n";
        }
        String sql = "Select \n"
                + "ISNULL((select top 1 Comando_Ref from queuefech WHERE SeqRota = ? AND CodFech = Fechaduras.Codigo order by sequencia desc), '') Status,\n"
                + "ISNULL((select top 1 Sequencia from queuefech WHERE SeqRota = ? AND CodFech = Fechaduras.Codigo order by sequencia desc), '') QueueFech,\n"
                + "Fechaduras.Tipo, Fechaduras.Codigo, Fechaduras.Identif, Fechaduras.TipoInst,\n"
                + "Fechaduras.Veiculo, Fechaduras.Codcli, Fechaduras.ModTecBan\n"
                + "from Fechaduras \n"
                + "Left Join Veiculos on  Veiculos.Numero  = Fechaduras.Veiculo \n"
                + "       and Veiculos.CodFil  = Fechaduras.CodFil \n"
                + "       and Veiculos.Situacao <> 'D'\n"
                + "where Fechaduras.CodFil = ?\n"
                + "and Fechaduras.Flag_excl <> '*'\n"
                + "and FechaDuras.Status = 'A'\n"                
                + "and (Fechaduras.Veiculo = ? or Fechaduras.CodCli = ?)\n"
                +vSQLAdic
                + "order by Fechaduras.TipoInst";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(seqRota);
            consult.setString(seqRota);
            consult.setString(codfil);
            consult.setString(carro);
            consult.setString(codcli);
            consult.select();
            while (consult.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setCodigo(consult.getString("Codigo"));
                fechadura.setTipo(consult.getInt("Tipo"));
                fechadura.setIdentif(consult.getString("Identif"));
                fechadura.setTipoInst(consult.getInt("TipoInst"));
                fechadura.setVeiculo(consult.getString("Veiculo"));
                fechadura.setCodCli(consult.getString("Codcli"));
                fechadura.setStatus(consult.getString("Status"));
                fechadura.setModTecban(consult.getString("ModTecBan"));
                fechadura.setObs(consult.getString("QueueFech"));
                listFechaduras.add(fechadura);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("FechadurasDao.listaFechaduras  - " + e.getMessage() + "\r\n"
                    + "Select \n"
                    + "ISNULL((select top 1 Comando_Ref from queuefech WHERE SeqRota = " + seqRota + " AND CodFech = Fechaduras.Codigo order by sequencia desc), '') Status,\n"
                    + "ISNULL((select top 1 Sequencia from queuefech WHERE SeqRota = " + seqRota + " AND CodFech = Fechaduras.Codigo order by sequencia desc), '') QueueFech,\n"
                    + "Fechaduras.Tipo, Fechaduras.Codigo, Fechaduras.Identif, Fechaduras.TipoInst,\n"
                    + "Fechaduras.Veiculo, Fechaduras.Codcli\n"
                    + "from Fechaduras \n"
                    + "Left Join Veiculos on  Veiculos.Numero  = Fechaduras.Veiculo \n"
                    + "       and Veiculos.CodFil  = Fechaduras.CodFil \n"
                    + "       and Veiculos.Situacao <> 'D'\n"
                    + "where Fechaduras.CodFil = " + codfil + "\n"
                    + "and Fechaduras.Flag_excl <> '*'\n"
                    + "and FechaDuras.Status = 'A'\n"                                             
                    + "and (Fechaduras.Veiculo = " + carro + " or Fechaduras.CodCli = " + codcli + ")\n"
                    +vSQLAdic  
                    + "order by Fechaduras.TipoInst");
        }
        return listFechaduras;
    }

    /**
     * Retorna a lista de fechaduras me conjunto, cliente veiculo
     *
     * @param Codfil - código da filial
     * @param codcli - Código cliente
     * @param carro - Chave carro
     * @param persistencia - conexão com o banco
     * @return - retorna dados fechadura
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<Fechaduras> listaFechaduras(String Codfil, String codcli, String carro, Persistencia persistencia) throws Exception {
        Fechaduras fechadura;
        List<Fechaduras> listFechaduras = new ArrayList();
        String vSQLAdic = "";
        if (persistencia.getEmpresa().equals("SATCORPVS") || persistencia.getEmpresa().equals("SATCORPVSPE") ||
                persistencia.getEmpresa().equals("SATCORPVS2") || persistencia.getEmpresa().equals("SATCORPVSPE2")){
           vSQLAdic =  "and FechaDuras.Tipo <> '22'\n";
        }

        String sql = "Select Fechaduras.Tipo, Fechaduras.Codigo, Fechaduras.Identif, Fechaduras.TipoInst,"
                + " Fechaduras.Veiculo, Fechaduras.Codcli, Fechaduras.PK_Fechadura,"
                + " Fechaduras.ModTecBan "
                + " from Fechaduras "
                + " Left Join Veiculos on  Veiculos.Numero  = Fechaduras.Veiculo "
                + "        and Veiculos.CodFil  = Fechaduras.CodFil "
                + "        and Veiculos.Situacao <> 'D'"
                + " where Fechaduras.CodFil = ?"
                + " and Fechaduras.Flag_excl <> '*'"
                + " and FechaDuras.Status = 'A'"                
                + " and (Fechaduras.Veiculo = ? or Fechaduras.CodCli = ?)"
                +vSQLAdic
                + " order by Fechaduras.TipoInst";
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setBigDecimal(new BigDecimal(Codfil));
            consult.setString(carro);
            consult.setString(codcli);
            consult.select();
            while (consult.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setCodigo(consult.getString("Codigo"));
                fechadura.setTipo(consult.getInt("Tipo"));
                fechadura.setIdentif(consult.getString("Identif"));
                fechadura.setTipoInst(consult.getInt("TipoInst"));
                fechadura.setVeiculo(consult.getString("Veiculo"));
                fechadura.setCodCli(consult.getString("Codcli"));
                fechadura.setPK_Fechadura(consult.getInt("PK_Fechadura"));
                fechadura.setModTecban(consult.getString("ModTecBan"));
                fechadura.setStatus("");
                fechadura.setObs("");
                listFechaduras.add(fechadura);
            }
            consult.Close();
        } catch (Exception e) {
            throw new Exception("FechadurasDao.listaFechaduras  - " + e.getMessage() + "\r\n"
                    + "Select Fechaduras.Tipo, Fechaduras.Codigo, sFechaduras.Identif, Fechaduras.TipoInst,"
                    + " Fechaduras.Veiculo, Fechaduras.Codcli"
                    + " from Fechaduras "
                    + " Left Join Veiculos on  Veiculos.Numero  = Fechaduras.Veiculo "
                    + "        and Veiculos.CodFil  = Fechaduras.CodFil "
                    + "        and Veiculos.Situacao <> 'D'"
                    + " where Fechaduras.CodFil = " + Codfil
                    + " and Fechaduras.Flag_excl <> '*'"
                    + " and FechaDuras.Status = 'A'"                       
                    + " and (Fechaduras.Veiculo = " + carro + " or Fechaduras.CodCli = " + codcli + ")"
                    +vSQLAdic
                    + " order by Fechaduras.TipoInst");
        }
        return listFechaduras;
    }

    /**
     * Lista a fechadura do veiculo
     *
     * @param codfil - Codigo da filial
     * @param TipoInst - tipo de instalação
     * @param Veiculo - número do veículo
     * @param persistencia - conexão com o banco
     * @return - Lista contendo as fechaduras
     * @throws Exception
     */
    public List<Fechaduras> listFechaduraVeiculo(String codfil, String TipoInst, String Veiculo, Persistencia persistencia) throws Exception {
        String sql = "select status, codigo from fechaduras where codfil = ? AND veiculo = ? and status='A' and tipoinst=?";
        Fechaduras fechadura;
        List<Fechaduras> listFechaduras = new ArrayList();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codfil);
            consult.setString(Veiculo);
            consult.setString(TipoInst);
            consult.select();
            while (consult.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setCodigo(consult.getString("codigo"));
                fechadura.setStatus(consult.getString("status"));
                listFechaduras.add(fechadura);
            }
            consult.Close();
            return listFechaduras;
        } catch (Exception e) {
            throw new Exception("FechadurasDao.listFechaduraVeiculo  - " + e.getMessage() + "\r\n"
                    + "select status, codigo from fechaduras where codfil = " + codfil + " AND "
                    + " veiculo = " + Veiculo + " and status='A' and tipoinst=" + TipoInst);
        }
    }

    /**
     * Lista a fechadura do veiculo
     *
     * @param codFil - Codigo da filial
     * @param TipoInst - tipo de instalação
     * @param CodCli - código do cliente
     * @param persistencia - conexão com o banco
     * @return - Lista contendo as fechaduras
     * @throws Exception
     */
    public List<Fechaduras> listFechaduraCliente(String codFil, String TipoInst, String CodCli, Persistencia persistencia) throws Exception {
        String sql = "select status, codigo from fechaduras where codfil = ? and codcli = ? and status='A' and tipoinst=?";
        Fechaduras fechadura;
        List<Fechaduras> listFechaduras = new ArrayList();
        try {
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(codFil);
            consult.setString(CodCli);
            consult.setString(TipoInst);
            consult.select();
            while (consult.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setCodigo(consult.getString("codigo"));
                fechadura.setStatus(consult.getString("status"));
                listFechaduras.add(fechadura);
            }
            consult.Close();
            return listFechaduras;
        } catch (Exception e) {
            throw new Exception("FechadurasDao.listFechaduraCliente  - " + e.getMessage() + "\r\n"
                    + "select status, codigo from fechaduras where codfil = " + codFil + " and codcli = " + CodCli + " and status='A' and tipoinst=" + TipoInst);
        }
    }

    /**
     * Retorna permissão de solicitação fechadura 2
     *
     * @param codigo
     * @param persistencia - conexão com o banco
     * @return - retorna dados fechadura
     * @throws java.lang.Exception - pode gerar exception
     */
    public Fechaduras pegaTipoFechadura(String codigo, Persistencia persistencia) throws Exception {
        String sql = "select codigo, tipo, ModTecban from fechaduras where status='A' "
                + "and codigo = ?";
        Fechaduras fechadura = new Fechaduras();
        try {
            Consulta rs = new Consulta(sql, persistencia);
            rs.setString(codigo);
            rs.select();
            while (rs.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setTipo(rs.getInt("tipo"));
                fechadura.setCodigo(rs.getString("codigo"));
                fechadura.setModTecban(rs.getString("ModTecban"));
            }
            rs.Close();
            return fechadura;
        } catch (Exception e) {
            throw new Exception("FechadurasDao.pegaTipoFechadura  - " + e.getMessage() + "\r\n"
                    + "select codigo, tipo from fechaduras where status='A' "
                    + "and codigo = " + codigo);
        }
    }

    /**
     * Retorna permissão de solicitação fechadura 2
     *
     * @param codigo
     * @param codFil
     * @param persistencia - conexão com o banco
     * @return - retorna fechadura do cliente
     * @throws java.lang.Exception - pode gerar exception
     */
    public List<Fechaduras> listaFechadurasCliente(String codigo, String codFil, Persistencia persistencia) throws Exception {
        String vSQLAdic = "";
        if (persistencia.getEmpresa().equals("SATCORPVS") || persistencia.getEmpresa().equals("SATCORPVSPE") ||
                persistencia.getEmpresa().equals("SATCORPVS2") || persistencia.getEmpresa().equals("SATCORPVSPE2")){
           vSQLAdic =  "and FechaDuras.Tipo <> '22'\n";
        }        
        String sql = "SELECT * FROM Fechaduras\n"
                + " WHERE CodCli = ?\n"
                + " AND   CodFil = ?\n"
                + " AND   Fechaduras.Flag_excl <> '*'\n"
                + " AND   FechaDuras.Status = 'A'\n"
                +vSQLAdic;
        
        List<Fechaduras> Retorno = new ArrayList<>();
        
        Fechaduras fechadura = new Fechaduras();
        
        try {
            Consulta rs = new Consulta(sql, persistencia);
            
            rs.setString(codigo);
            rs.setString(codFil);
            
            rs.select();
            while (rs.Proximo()) {
                fechadura = new Fechaduras();
                fechadura.setCodigo(rs.getString("Codigo"));
                fechadura.setCodFil(rs.getString("CodFil"));
                fechadura.setTipo(rs.getInt("Tipo"));
                fechadura.setTipoInst(rs.getInt("TipoInst"));
                fechadura.setIdentif(rs.getString("Identif"));
                fechadura.setCodCli(rs.getString("CodCli"));
                fechadura.setStatus(rs.getString("Status"));
                fechadura.setObs(rs.getString("Obs"));
                fechadura.setSenhaManager(rs.getString("SenhaManager"));
                fechadura.setSenhaUsuario1(rs.getString("SenhaUsuario1"));
                fechadura.setSenhaUsuario2(rs.getString("SenhaUsuario2"));
                fechadura.setPK_Fechadura(rs.getString("PK_Fechadura").replace(".0", ""));
                fechadura.setVeiculo(rs.getString("Veiculo"));
                fechadura.setOperador(rs.getString("Operador"));
                fechadura.setDt_Alter(rs.getString("Dt_Alter"));
                fechadura.setHr_alter(rs.getString("Hr_Alter"));
                fechadura.setOperador(rs.getString("Operador"));
                
                Retorno.add(fechadura);
            }
            rs.Close();
            return Retorno;
        } catch (Exception e) {
            throw new Exception("FechadurasDao.pegaTipoFechadura  - " + e.getMessage() + "\r\n"
                    + "select codigo, tipo from fechaduras where status='A' "
                    + "and codigo = " + codigo);
        }
    }
    
    public void inserirFechadura(Fechaduras fechadura, Persistencia persistencia) throws Exception {
        String sql = "INSERT INTO Fechaduras (Codigo, CodFil, Tipo, TipoInst, Identif, CodCli, Status, Obs, SenhaManager, SenhaUsuario1, SenhaUsuario2, PK_Fechadura, Veiculo, Operador, Dt_Alter, Hr_alter, Flag_Excl) VALUES("
                + " (SELECT (ISNULL(MAX(Codigo),0)+1) FROM Fechaduras),?,?,?,?,?,'A',?,?,?,?,?,0,?,?,?,'')";
        
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setBigDecimal(fechadura.getCodFil());
            consulta.setInt(fechadura.getTipo());
            consulta.setInt(fechadura.getTipoInst());
            consulta.setString(fechadura.getIdentif());
            consulta.setString(fechadura.getCodCli());
            consulta.setString(fechadura.getObs());
            consulta.setString(fechadura.getSenhaManager());
            consulta.setString(fechadura.getSenhaUsuario1());
            consulta.setString(fechadura.getSenhaUsuario2());
            consulta.setString(fechadura.getPK_Fechadura());
            consulta.setString(fechadura.getOperador());
            consulta.setString(fechadura.getDt_Alter());
            consulta.setString(fechadura.getHr_alter());
            
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("FechadurasDao.inserirFechadura  - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
    
    public void atualizarFechadura(Fechaduras fechadura, Persistencia persistencia) throws Exception {
        String sql = "UPDATE Fechaduras"
                + " SET     Tipo = ?,"
                +"      TipoInst = ?,"
                +"       Identif = ?,"
                +"           Obs = ?,"
                +"  SenhaManager = ?,"
                +" SenhaUsuario1 = ?,"
                +" SenhaUsuario2 = ?,"
                +"  PK_Fechadura = ?,"
                +"      Operador = ?,"
                +"      Dt_Alter = ?,"
                +"      Hr_alter = ?"
                +" WHERE Codigo = ?";
        
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setInt(fechadura.getTipo());
            consulta.setInt(fechadura.getTipoInst());
            consulta.setString(fechadura.getIdentif());
            consulta.setString(fechadura.getObs());
            consulta.setString(fechadura.getSenhaManager());
            consulta.setString(fechadura.getSenhaUsuario1());
            consulta.setString(fechadura.getSenhaUsuario2());
            consulta.setString(fechadura.getPK_Fechadura());
            consulta.setString(fechadura.getOperador());
            consulta.setString(fechadura.getDt_Alter());
            consulta.setString(fechadura.getHr_alter());
            consulta.setBigDecimal(fechadura.getCodigo());
            
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("FechadurasDao.atualizarFechadura  - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
    
    public void excluirFechadura(Fechaduras fechadura, Persistencia persistencia) throws Exception {
        String sql = "UPDATE Fechaduras"
                + " SET Flag_Excl = '*',"
                +"       OperExcl = ?,"
                +"        Dt_Excl = ?,"
                +"        Hr_Excl = ?"
                +" WHERE Codigo = ?";
        
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            
            consulta.setString(fechadura.getOperExcl());
            consulta.setString(fechadura.getDt_Excl());
            consulta.setString(fechadura.getHr_Excl());
            consulta.setBigDecimal(fechadura.getCodigo());
            
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("FechadurasDao.excluirFechadura  - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }
}
