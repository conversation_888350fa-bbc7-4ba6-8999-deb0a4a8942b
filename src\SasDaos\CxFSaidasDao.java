package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFSaidas;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 */
public class CxFSaidasDao {

    public void atualizaCxFSaidas(String qtde, String valor,
            String codFil, String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " Qtde = ?,  \n"
                    + " Valor = ?, \n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(qtde);
            consulta.setString(valor);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.atualizaCxFSaidas - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " Qtde = " + qtde + ", \n"
                    + " Valor = " + valor + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Remessa = " + remessa);
        }
    }

    public void atualizaCxFSaidas(String qtde, String qtdeVol, String valor,
            String codFil, String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " Qtde = ?,  \n"
                    + " QtdeVol = ?, \n"
                    + " Valor = ?, \n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(qtde);
            consulta.setString(qtdeVol);
            consulta.setString(valor);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.atualizaCxFSaidas - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " Qtde = " + qtde + ", \n"
                    + " QtdeVol = " + qtdeVol + ", \n"
                    + " Valor = " + valor + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Remessa = " + remessa);
        }
    }

    public void atualizarQtdeVol(String qtdeVol, String codFil, String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " QtdeVol = ? \n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(qtdeVol);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.atualizarQtdeVol - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " QtdeVol = " + qtdeVol + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Remessa = " + remessa);
        }
    }

    public void inserirCxFSaidas(String codFil, String seqRota, String remessa, String codRemessa, String qtde,
            String qtdeVol, String valor, String oper_Prep, String dt_Prep, String hr_Prep, Persistencia persistencia) throws Exception {
        try {
            String sql = " Insert into CxFSaidas (CodFil, SeqRota, Remessa, CodRemessa, Qtde, QtdeVol, Valor, Oper_Prep, Dt_Prep, Hr_Prep) \n"
                    + " Values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.setString(codRemessa);
            consulta.setString(qtde);
            consulta.setString(qtdeVol);
            consulta.setString(valor);
            consulta.setString(oper_Prep);
            consulta.setString(dt_Prep);
            consulta.setString(hr_Prep);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.inserirCxFSaidasDao - " + e.getMessage() + "\r\n"
                    + " Insert into CxFSaidas (CodFil, SeqRota, Remessa, CodRemessa, Qtde, QtdeVol, Valor, Oper_Prep, Dt_Prep, Hr_Prep) \n"
                    + " Values (" + codFil + ", " + seqRota + ", " + remessa + ", " + codRemessa + ", " + qtde + ", " + qtdeVol + "?, " + valor + ", " + oper_Prep + ", " + dt_Prep + ", " + hr_Prep + ") ");
        }
    }

    public CxFSaidas buscarSaida(String codFil, String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select * from CxFSaidas \n"
                    + " where CxFSaidas.CodFil  = ?\n"
                    + "   and CxFSaidas.SeqRota = ?\n"
                    + "   and CxFSaidas.Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.select();
            CxFSaidas cxFSaidas = null;
            if (consulta.Proximo()) {
                cxFSaidas = new CxFSaidas();

                cxFSaidas.setCodFil(consulta.getString("CodFil"));
                cxFSaidas.setSeqRota(consulta.getString("SeqRota"));
                cxFSaidas.setRemessa(consulta.getString("Remessa"));
                cxFSaidas.setCodRemessa(consulta.getString("CodRemessa"));
                cxFSaidas.setQtde(consulta.getString("Qtde"));
                cxFSaidas.setQtdeVol(consulta.getString("QtdeVol"));
                cxFSaidas.setValor(consulta.getString("Valor"));
                cxFSaidas.setLacre(consulta.getString("Lacre"));
                cxFSaidas.setOper_Prep(consulta.getString("Oper_Prep"));
                cxFSaidas.setDt_Prep(consulta.getString("Dt_Prep"));
                cxFSaidas.setHr_Prep(consulta.getString("Hr_Prep"));
                cxFSaidas.setOper_Saida(consulta.getString("Oper_Saida"));
                cxFSaidas.setDt_Saida(consulta.getString("Dt_Saida"));
                cxFSaidas.setHr_Saida(consulta.getString("Hr_Saida"));
            }
            consulta.close();
            return cxFSaidas;
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.buscarSaida - " + e.getMessage() + "\r\n"
                    + "Select * from CxFSaidas \n"
                    + " where CxFSaidas.CodFil  = " + codFil + "\n"
                    + "   and CxFSaidas.SeqRota = " + seqRota + "\n"
                    + "   and CxFSaidas.Remessa = " + remessa);
        }
    }

    public void saidaCxFSaidas(String codFil, String seqRota, String valor, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " Qtde         = Qtde         + 1, \n"
                    + " Valor        = Valor        + ?\n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(valor);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.saidaCxFSaidas - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " Qtde         = Qtde         + 1, \n"
                    + " Valor        = Valor        + " + valor + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Remessa = " + remessa);
        }
    }

    public void saidaCxFSaidas(String codFil, String seqRota, String dataAtual, String horaAtual, String operador, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " Dt_Saida = ?, \n"
                    + " Hr_Saida = ?, \n"
                    + " Oper_Saida = ? \n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Dt_Saida IS NULL ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(operador);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.saidaCxFSaidas - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " Dt_Saida = " + dataAtual + ", \n"
                    + " Hr_Saida = " + horaAtual + ", \n"
                    + " Oper_Saida = " + operador + " \n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Dt_Saida IS NULL ");
        }
    }

    private Persistencia persistencia;

    public CxFSaidasDao() {
    }

    public CxFSaidasDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    /**
     * Grava Saida de Caixa forte na CxfSaidas para a remessa passada com:
     * Oper_Saida: SatMob Dt_Saida: Data atual do servidor Hr_Saida: Hora atual
     * do servidor
     *
     * @param Remessa - Número da Remessa
     * @param persistencia - Conexão ao banco de dados
     * @throws Exception
     */
    public void GravaSaidaCxFSaidas(String Remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "update CxFSaidas set Oper_Saida = ?, Dt_Saida = ?, Hr_Saida = ? where CodRemessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("SatMob");
            consulta.setDate(Date.valueOf(LocalDate.now()));
            consulta.setString(DataAtual.getDataAtual("HORA"));
            consulta.setString(Remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.GravaSaidaCxFSaidas  - " + e.getMessage() + "\r\n"
                    + "update CxFSaidas set Oper_Saida = SatMob, Dt_Saida = " + LocalDate.now() + ", Hr_Saida = " + DataAtual.getDataAtual("HORA")
                    + " where CodRemessa = " + Remessa);
        }
    }

    public void GravaSaidaCxFSaidas(String Remessa, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql = "update CxFSaidas set Oper_Saida = ?, Dt_Saida = ?, Hr_Saida = ? where CodRemessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString("SatMob");
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(Remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxFSaidasDao.GravaSaidaCxFSaidas  - " + e.getMessage() + "\r\n"
                    + "update CxFSaidas set Oper_Saida = SatMob, Dt_Saida = " + dataAtual + ", Hr_Saida = " + horaAtual
                    + " where CodRemessa = " + Remessa);
        }
    }

    public BigDecimal getMaxRemessa(String codFil, String seqRota, boolean operSaidaNull) throws Exception {
        String sql = "SELECT Max(Remessa) Remessa\n"
                + "FROM CxfSaidas\n"
                + "WHERE SeqRota = ? \n"
                + "  AND CodFil = ? ";

        sql += operSaidaNull ? "\n  AND Oper_Saida IS NULL;" : ";";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(codFil);
            consulta.select();

            if (consulta.Proximo()) {
                return consulta.getBigDecimal("Remessa");
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return null;
    }

}
