package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.ClientesFech;
import java.math.BigDecimal;
import java.sql.SQLException;

/**
 *
 * <AUTHOR>
 */
public class ClientesFechDao {

    /**
     * Insere ficha clientes
     *
     * @param codfil - Código filial
     * @param seq - Número sequencia
     * @param codfechadura - Número fechadura
     * @param codcli - Código cliente
     * @param Cod_Pessoa - Número pessoa
     * @param Comando_Ref - Comando
     * @param sequencia - Número sequência
     * @param parada - Número parada
     * @param usuario - Usuário
     * @param persistencia - Conexão ao banco
     * @throws java.lang.Exception - pode gerar exception
     */
    public void insereClientesFech(String codfil, String seq, String codfechadura, String codcli,
            String Cod_Pessoa, String Comando_Ref, String sequencia, String parada,
            String usuario, Persistencia persistencia) throws Exception {
        String sql = "insert into clientesfech (codfil,sequencia,codfech,codcli,codpessoa,tipo,comando,seqrota,"
                + "parada,operador,dt_alter,hr_alter)"
                + " values(?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(seq);
            consulta.setString(codfechadura);
            consulta.setString(codcli);
            consulta.setString(Cod_Pessoa);
            consulta.setString("1");//tipofechadura
            consulta.setString(Comando_Ref);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(usuario);
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));

            consulta.insert();
            consulta.close();
            persistencia.FechaConexao();
        } catch (SQLException e) {
            throw new Exception("ClientesFechDao.insereClientesFech - " + e.getMessage() + "\r\n"
                    + "insert into clientesfech (codfil,sequencia,codfech,codcli,codpessoa,tipo,comando,seqrota,"
                    + "parada,operador,dt_alter,hr_alter)"
                    + " values(" + codfil + "," + seq + "," + codfechadura + "," + codcli + "," + Cod_Pessoa + ",1," + Comando_Ref + ","
                    + "" + sequencia + "," + parada + "," + usuario + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "," + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA") + ")");
        }
    }

    public void insereClientesFech(String codfil, String seq, String codfechadura, String codcli,
            String Cod_Pessoa, String Comando_Ref, String sequencia, String parada,
            String usuario, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        String sql = "insert into clientesfech (codfil,sequencia,codfech,codcli,codpessoa,tipo,comando,seqrota,"
                + "parada,operador,dt_alter,hr_alter)"
                + " values(?,?,?,?,?,?,?,?,?,?,?,?)";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codfil);
            consulta.setString(seq);
            consulta.setString(codfechadura);
            consulta.setString(codcli);
            consulta.setString(Cod_Pessoa);
            consulta.setString("1");//tipofechadura
            consulta.setString(Comando_Ref);
            consulta.setString(sequencia);
            consulta.setString(parada);
            consulta.setString(usuario);
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);

            consulta.insert();
            consulta.close();
            persistencia.FechaConexao();
        } catch (SQLException e) {
            throw new Exception("ClientesFechDao.insereClientesFech - " + e.getMessage() + "\r\n"
                    + "insert into clientesfech (codfil,sequencia,codfech,codcli,codpessoa,tipo,comando,seqrota,"
                    + "parada,operador,dt_alter,hr_alter)"
                    + " values(" + codfil + "," + seq + "," + codfechadura + "," + codcli + "," + Cod_Pessoa + ",1," + Comando_Ref + ","
                    + "" + sequencia + "," + parada + "," + usuario + "," + dataAtual + "," + horaAtual + ")");
        }
    }

    /**
     * Seleciona maior valor da sequencia ClienteFech
     *
     * @param codFil - Código filial
     * @param persistencia - Conexão ao banco
     * @return - Código filial
     * @throws java.lang.Exception - pode gerar exception
     */
    public String pegaSeqMax(String codFil, Persistencia persistencia) throws Exception {
        String sql = "select MAX(sequencia)+1 sequencia from ClientesFech "
                + "where CodFil=?";
        BigDecimal sequencia = BigDecimal.ZERO;
        try {
            Consulta rs = new Consulta(sql, persistencia);

            rs.setString(codFil);
            rs.select();
            while (rs.Proximo()) {
                try {
                    sequencia = new BigDecimal(rs.getString("sequencia"));
                } catch (Exception e) {
                    sequencia = new BigDecimal("1");
                }
            }
            rs.Close();
            return sequencia.toString();
        } catch (Exception e) {
            throw new Exception("ClientesFechDao.pegaSeqMax - " + e.getMessage() + "\r\n"
                    + "select MAX(sequencia)+1 sequencia from ClientesFech "
                    + "where CodFil=" + codFil);
        }
    }

    /**
     * Atualiza o aceite de senha em um pedido feito pelo mobile
     *
     * @param clifech
     * @param persistencia
     * @throws Exception
     */
    public void atualiza(ClientesFech clifech, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "update clientesfech set codfech = ?, codcli = ?, codpessoa = ?, tipo = ?, comandoret = ?, "
                    + " seqrota = ?, parada = ?, dt_alter = ?, hr_alter = ?"
                    + " where queueseq = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(clifech.getCodFech().toString());
            consulta.setString(clifech.getCodCli());
            consulta.setString(clifech.getCodPessoa().toString());
            consulta.setString(clifech.getTipo());
            consulta.setString(clifech.getComandoRet());
            consulta.setString(clifech.getSeqRota().toString());
            consulta.setInt(clifech.getParada());
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL"));
            consulta.setString(br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA"));
            consulta.setString(clifech.getQueueSeq().toString());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesFechDao.atualiza - " + e.getMessage() + "\r\n"
                    + "update clientesfech set codfech = " + clifech.getCodFech().toString() + ", codcli = " + clifech.getCodCli() + ", "
                    + " codpessoa = " + clifech.getCodPessoa().toString() + ", tipo = " + clifech.getTipo() + ", comandoret = " + clifech.getComandoRet() + ", "
                    + " seqrota = " + clifech.getSeqRota().toString() + ", parada = " + clifech.getParada() + ", dt_alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + ", "
                    + "hr_alter = " + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("HORA")
                    + " where queueseq = " + clifech.getQueueSeq().toString());
        }
    }

    public void atualiza(ClientesFech clifech, String dataAtual, String horaAtual, Persistencia persistencia) throws Exception {
        try {
            String sql;
            sql = "update clientesfech set codfech = ?, codcli = ?, codpessoa = ?, tipo = ?, comandoret = ?, "
                    + " seqrota = ?, parada = ?, dt_alter = ?, hr_alter = ?"
                    + " where queueseq = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(clifech.getCodFech().toString());
            consulta.setString(clifech.getCodCli());
            consulta.setString(clifech.getCodPessoa().toString());
            consulta.setString(clifech.getTipo());
            consulta.setString(clifech.getComandoRet());
            consulta.setString(clifech.getSeqRota().toString());
            consulta.setInt(clifech.getParada());
            consulta.setString(dataAtual);
            consulta.setString(horaAtual);
            consulta.setString(clifech.getQueueSeq().toString());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("ClientesFechDao.atualiza - " + e.getMessage() + "\r\n"
                    + "update clientesfech set codfech = " + clifech.getCodFech().toString() + ", codcli = " + clifech.getCodCli() + ", "
                    + " codpessoa = " + clifech.getCodPessoa().toString() + ", tipo = " + clifech.getTipo() + ", comandoret = " + clifech.getComandoRet() + ", "
                    + " seqrota = " + clifech.getSeqRota().toString() + ", parada = " + clifech.getParada() + ", dt_alter = " + dataAtual + ", "
                    + "hr_alter = " + horaAtual
                    + " where queueseq = " + clifech.getQueueSeq().toString());
        }
    }
}
