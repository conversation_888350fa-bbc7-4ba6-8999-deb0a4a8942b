package Telas;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import java.awt.Toolkit;
import java.awt.event.KeyEvent;
import javax.swing.table.DefaultTableModel;
import pacotesuteis.Main;

/**
 *
 * <AUTHOR>
 */
public class ConsultFilial extends javax.swing.JFrame {

    /**
     * Creates new form ConsultFilial
     */
    private Persistencia_OLD persistfilial;
    private String param;

    public ConsultFilial() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        jScrollPane1 = new javax.swing.JScrollPane();
        ConsultFilial = new javax.swing.JTable();
        nomePesq = new javax.swing.JTextField();
        TitPesq = new javax.swing.JLabel();
        jButton1 = new javax.swing.JButton();
        ConsultaDesc = new javax.swing.JRadioButton();
        ConsultaCod = new javax.swing.JRadioButton();
        Tconsult = new javax.swing.JLabel();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Filial");
        setAlwaysOnTop(true);
        setBackground(new java.awt.Color(20, 91, 155));
        setResizable(false);
        addWindowListener(new java.awt.event.WindowAdapter() {
            public void windowOpened(java.awt.event.WindowEvent evt) {
                formWindowOpened(evt);
            }
            public void windowClosing(java.awt.event.WindowEvent evt) {
                formWindowClosing(evt);
            }
        });

        ConsultFilial.setAutoCreateRowSorter(true);
        ConsultFilial.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        ConsultFilial.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {

            },
            new String [] {
                "CodFil", "Descrição", "Razão Social", "Endereco", "Bairro", "Cidade", "UF", "CEP", "Fone", "Fone 2", "Fax", "Contato", "Email", "CNPJ", "InscEst", "InscMunic", "ISS", "Praça", "Operador", "Dt_Alter", "Hr_ALter"
            }
        ));
        ConsultFilial.setAutoResizeMode(javax.swing.JTable.AUTO_RESIZE_OFF);
        ConsultFilial.setRowHeight(30);
        ConsultFilial.setShowVerticalLines(false);
        ConsultFilial.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultFilialKeyPressed(evt);
            }
        });
        jScrollPane1.setViewportView(ConsultFilial);

        nomePesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        nomePesq.setFocusable(false);
        nomePesq.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                nomePesqMouseClicked(evt);
            }
        });
        nomePesq.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusGained(java.awt.event.FocusEvent evt) {
                nomePesqFocusGained(evt);
            }
        });
        nomePesq.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                nomePesqKeyPressed(evt);
            }
        });

        TitPesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TitPesq.setText("Descr.:");

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/Figuras/icone_pesquisar.png"))); // NOI18N
        jButton1.setToolTipText("Toque para pesquisar");
        jButton1.setBorderPainted(false);
        jButton1.setContentAreaFilled(false);
        jButton1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jButton1MouseClicked(evt);
            }
        });
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        buttonGroup1.add(ConsultaDesc);
        ConsultaDesc.setSelected(true);
        ConsultaDesc.setText("Descrição");
        ConsultaDesc.setFocusable(false);
        ConsultaDesc.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                ConsultaDescMouseClicked(evt);
            }
        });

        buttonGroup1.add(ConsultaCod);
        ConsultaCod.setText("Código");
        ConsultaCod.setFocusable(false);
        ConsultaCod.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                ConsultaCodMouseClicked(evt);
            }
        });
        ConsultaCod.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                ConsultaCodActionPerformed(evt);
            }
        });
        ConsultaCod.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultaCodKeyPressed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jScrollPane1)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(Tconsult)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(ConsultaDesc)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(ConsultaCod))
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(TitPesq)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                        .addComponent(nomePesq, javax.swing.GroupLayout.PREFERRED_SIZE, 323, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                        .addComponent(jButton1, javax.swing.GroupLayout.PREFERRED_SIZE, 52, javax.swing.GroupLayout.PREFERRED_SIZE)))
                .addContainerGap(218, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(ConsultaDesc)
                    .addComponent(ConsultaCod)
                    .addComponent(Tconsult))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                    .addComponent(jButton1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(nomePesq)
                    .addComponent(TitPesq, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 558, javax.swing.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void formWindowOpened(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowOpened
        ConsultFilial.requestFocus();
        DefaultTableModel modelo = (DefaultTableModel) ConsultFilial.getModel();
        setIconImage(Toolkit.getDefaultToolkit().getImage(getClass().getResource("/Figuras/icone_satellite.png")));
        try {
            persistfilial = new Persistencia_OLD(param, "/Dados/mapconect.txt", "DESK");
            Consulta consult = new Consulta("select top 20 "
                    + " CodFil,Descricao,RazaoSocial,Endereco,Bairro,Cidade,UF,CEP,"
                    + " Fone,Fone2,Fax,Contato,Email,CNPJ,InscEst,InscMunic,ISS,"
                    + " Praca,Operador,Dt_alter,Hr_alter"
                    + " from filiais ", persistfilial);
            consult.select();
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4),
                    consult.getString(5),
                    consult.getString(6),
                    consult.getString(7),
                    consult.getString(8),
                    consult.getString(9),
                    consult.getString(10),
                    consult.getString(11),
                    consult.getString(12),
                    consult.getString(13),
                    consult.getString(14),
                    consult.getString(15),
                    consult.getString(16),
                    consult.getString(17),
                    consult.getString(18),
                    consult.getString(19),
                    consult.getString(20),
                    consult.getString(21),});
            }
            consult.Close();
            for (int i = 0; i < 21; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFilial, i);
            }
            ConsultFilial.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowOpened

    private void jButton1MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_jButton1MouseClicked
        Consulta();
    }//GEN-LAST:event_jButton1MouseClicked

    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_jButton1ActionPerformed

    private void nomePesqKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_nomePesqKeyPressed
        if (evt.getKeyCode() == KeyEvent.VK_ENTER) {
            Consulta();
        } else if (evt.getKeyCode() == KeyEvent.VK_F2) {
            if (ConsultaDesc.isSelected()) {
                ConsultaCod.setSelected(true);
                TitPesq.setText("Código:");
                nomePesq.requestFocus();
            } else {
                ConsultaDesc.setSelected(true);
                TitPesq.setText("Descr.:");
                nomePesq.requestFocus();
            }
        }
    }//GEN-LAST:event_nomePesqKeyPressed

    private void ConsultFilialKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultFilialKeyPressed
        if (evt.getKeyCode() == 27) {
            String codfil;
            try {
                codfil = (String) ConsultFilial.getValueAt(ConsultFilial.getSelectedRow(), 0);
            } catch (Exception e) {
                codfil = "0";
            }
            Main.DadosConsultas.setCodfil(codfil);
            this.dispose();
        } else if (evt.getKeyCode() == KeyEvent.VK_P) {
            nomePesq.setFocusable(true);
            nomePesq.setText("");
            nomePesq.requestFocus();
        }
    }//GEN-LAST:event_ConsultFilialKeyPressed

    private void ConsultaDescMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_ConsultaDescMouseClicked
        ConsultaDesc.setSelected(true);
        TitPesq.setText("Descr.:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");

    }//GEN-LAST:event_ConsultaDescMouseClicked

    private void ConsultaCodKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultaCodKeyPressed

    }//GEN-LAST:event_ConsultaCodKeyPressed

    private void ConsultaCodMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_ConsultaCodMouseClicked
        ConsultaCod.setSelected(true);
        TitPesq.setText("Código:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_ConsultaCodMouseClicked

    private void nomePesqFocusGained(java.awt.event.FocusEvent evt) {//GEN-FIRST:event_nomePesqFocusGained
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_nomePesqFocusGained

    private void nomePesqMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_nomePesqMouseClicked
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
    }//GEN-LAST:event_nomePesqMouseClicked

    private void formWindowClosing(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowClosing
        try {
            persistfilial.FechaConexao();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowClosing

    private void ConsultaCodActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_ConsultaCodActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_ConsultaCodActionPerformed

    private void Consulta() {
        DefaultTableModel modelo = (DefaultTableModel) ConsultFilial.getModel();
        try {
            Consulta consult;
            if (nomePesq.getText().equals("")) {
                consult = new Consulta("select "
                        + " CodFil,Descricao,RazaoSocial,Endereco,Bairro,Cidade,UF,CEP,"
                        + " Fone,Fone2,Fax,Contato,Email,CNPJ,InscEst,InscMunic,ISS,"
                        + " Praca,Operador,Dt_alter,Hr_alter"
                        + " from filiais ", persistfilial);
            } else {
                if (ConsultaDesc.isSelected()) {
                    consult = new Consulta("select top 20 "
                            + " CodFil,Descricao,RazaoSocial,Endereco,Bairro,Cidade,UF,CEP,"
                            + " Fone,Fone2,Fax,Contato,Email,CNPJ,InscEst,InscMunic,ISS,"
                            + " Praca,Operador,Dt_alter,Hr_alter"
                            + " from filiais "
                            + " where Descricao like ?", persistfilial);
                    consult.setString("%" + nomePesq.getText() + "%");
                } else {
                    consult = new Consulta("select top 20 "
                            + " CodFil,Descricao,RazaoSocial,Endereco,Bairro,Cidade,UF,CEP,"
                            + " Fone,Fone2,Fax,Contato,Email,CNPJ,InscEst,InscMunic,ISS,"
                            + " Praca,Operador,Dt_alter,Hr_alter"
                            + " from filiais "
                            + " where CodFil = ?", persistfilial);
                    consult.setString(nomePesq.getText());
                }
            }
            consult.select();
            modelo.setNumRows(0);
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4),
                    consult.getString(5),
                    consult.getString(6),
                    consult.getString(7),
                    consult.getString(8),
                    consult.getString(9),
                    consult.getString(10),
                    consult.getString(11),
                    consult.getString(12),
                    consult.getString(13),
                    consult.getString(14),
                    consult.getString(15),
                    consult.getString(16),
                    consult.getString(17),
                    consult.getString(18),
                    consult.getString(19),
                    consult.getString(20),
                    consult.getString(21),});
            }
            consult.Close();
            for (int i = 0; i < 21; i++) {
                br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFilial, i);
            }
            ConsultFilial.requestFocus();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }

    /**
     * Passa o parametro de empresa
     *
     * @param value
     */
    public void setParam(String value) {
        param = value;
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(ConsultFilial.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(ConsultFilial.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(ConsultFilial.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(ConsultFilial.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new ConsultFilial().setVisible(true);
            }
        });
    }
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTable ConsultFilial;
    private javax.swing.JRadioButton ConsultaCod;
    private javax.swing.JRadioButton ConsultaDesc;
    private javax.swing.JLabel Tconsult;
    private javax.swing.JLabel TitPesq;
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.JButton jButton1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTextField nomePesq;
    // End of variables declaration//GEN-END:variables
}
