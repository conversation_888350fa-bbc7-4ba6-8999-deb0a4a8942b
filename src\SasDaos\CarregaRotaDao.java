package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CarregaRotaVol;
import SasBeans.Clientes;
import SasBeans.CxFGuiasVol;
import SasBeans.Escala;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.GuiasList;
import SasBeans.OS_Vig;
import SasBeans.Paramet;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasBeans.Rt_PercSla;
import SasBeansCompostas.CarregaParadaRota;
import SasBeansCompostas.CarregaRota;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregaRotaDao {
    
  
    private static Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    public static void ConfirmaSupervisaoRota(String aparencia, String atribuicoes, String proativo, String materiais,
            String organizacao, String obs, String latitude, String longitude, String sequencia,
            String matr, String fmatr, Persistencia persistencia) throws Exception {

        try {
            String sql = "INSERT INTO ConfSuperv (aparencia, atribuicoes, proativo, materiais, organizacao,"
                    + " obs, latitude, longitude, sequencia, matr, fmatr) VALUES (?,?,?,?,?,?,?,?,?,?,?)";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(aparencia);
            consulta.setString(atribuicoes);
            consulta.setString(proativo);
            consulta.setString(materiais);
            consulta.setString(organizacao);
            consulta.setString(obs);
            consulta.setFloat(Float.parseFloat(latitude));
            consulta.setFloat(Float.parseFloat(longitude));
            consulta.setFloat(Float.parseFloat(sequencia));
            consulta.setFloat(Float.parseFloat(matr));
            consulta.setFloat(Float.parseFloat(fmatr));

            consulta.insert();
            consulta.close();

        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.ConfirmaSupervisaoRota - " + e.getMessage() + "\r\n"
                    + "INSERT INTO ConfSuperv (aparencia, atribuicoes, proativo, materiais, organizacao,"
                    + " obs, latitude, longitude, sequencia, matr, fmatr) VALUES (" + aparencia + "," + atribuicoes + ","
                    + proativo + "," + materiais + "," + organizacao + "," + obs + "," + latitude + "," + longitude + "," + sequencia + "," + matr + "," + fmatr + ")");
        }
    }

    public static List<CarregaRota> getRotaSuperv(String sCodPessoa, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRota> list_rota = new ArrayList<>();
            String sql = "Select Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, "
                    + " Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, substring(Rt_Perc.ER,1,1) ER, Escala.Veiculo, Cliori.Latitude, "
                    + " Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.CodFil CodFilori, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D,"
                    + " Substring(Rt_Perc.HrCheg,1,2)+':'+Substring(Rt_Perc.HrCheg,4,2) HrCheg, "
                    + " Substring(Rt_Perc.HrSaida,1,2)+':'+Substring(Rt_Perc.HrSaida,4,2) HrSaida, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Rt_Perc.TipoSrv, Funcion.Matr, Funcion.CodFil, Filiais.Descricao "
                    + " from Pessoa as Pessoa "
                    + " Left Join Funcion as Funcion on Funcion.Matr = Pessoa.Matr "
                    + " Left Join Escala as Escala on Escala.CodPessoaSup = Pessoa.Codigo and "
                    //                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Filiais on Filiais.Codfil = Rotas.CodFil "
                    //+ " Where Funcion.Matr = ? "
                    + " Where Pessoa.Codigo = ? "
                    //+ " and Rt_Perc.HrBaixa = '' "
                    + " Order by Rt_Perc.Hora1";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sCodPessoa);
            consult.select();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Filiais filiais = new Filiais();
                CarregaRota carregarota = new CarregaRota();
                Funcion funcion = new Funcion();
                rota.setRota(consult.getString("Rota"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setCodFil(consult.getString("CodFilori"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                funcion.setMatr(consult.getString("Matr"));
                //funcion.setCodFil(consult.getString("CodFil"));
                filiais.setDescricao(consult.getString("Descricao"));

                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setFuncion(funcion);
                carregarota.setFiliais(filiais);
                list_rota.add(carregarota);
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotaSuperv - " + e.getMessage() + "\r\n"
                    + "Select Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, "
                    + " Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, substring(Rt_Perc.ER,1,1) ER, Escala.Veiculo, Cliori.Latitude, "
                    + " Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.CodFil CodFilori, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D,"
                    + " Substring(Rt_Perc.HrCheg,1,2)+':'+Substring(Rt_Perc.HrCheg,4,2) HrCheg, "
                    + " Substring(Rt_Perc.HrSaida,1,2)+':'+Substring(Rt_Perc.HrSaida,4,2) HrSaida, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Rt_Perc.TipoSrv, Funcion.Matr, Funcion.CodFil, Filiais.Descricao "
                    + " from Pessoa as Pessoa "
                    + " Left Join Funcion as Funcion on Funcion.Matr = Pessoa.Matr "
                    + " Left Join Escala as Escala on Escala.CodPessoaSup = Pessoa.Codigo and "
                    //                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Filiais on Filiais.Codfil = Rotas.CodFil "
                    //+ " Where Funcion.Matr = ? "+sCodPessoa
                    + " Where Pessoa.Codigo =  " + sCodPessoa
                    //+ " and Rt_Perc.HrBaixa = '' "
                    + " Order by Rt_Perc.Hora1");
        }
    }

    /**
     * Carrega a rota de supervisão do mobile
     *
     * @param sCodPessoa
     * @param dataAtual
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static List<CarregaRota> getRotaSuperv(String sCodPessoa, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRota> list_rota = new ArrayList<>();
            String sql = "Select Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, "
                    + " Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, substring(Rt_Perc.ER,1,1) ER, Escala.Veiculo, Cliori.Latitude, "
                    + " Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.CodFil CodFilori, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D,"
                    + " Substring(Rt_Perc.HrCheg,1,2)+':'+Substring(Rt_Perc.HrCheg,4,2) HrCheg, "
                    + " Substring(Rt_Perc.HrSaida,1,2)+':'+Substring(Rt_Perc.HrSaida,4,2) HrSaida, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Rt_Perc.TipoSrv, Funcion.Matr, Funcion.CodFil, Filiais.Descricao "
                    + " from Pessoa as Pessoa "
                    + " Left Join Funcion as Funcion on Funcion.Matr = Pessoa.Matr "
                    + " Left Join Escala as Escala on Escala.CodPessoaSup = Pessoa.Codigo and "
                    //                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = ? "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Filiais on Filiais.Codfil = Rotas.CodFil "
                    //+ " Where Funcion.Matr = ? "
                    + " Where Pessoa.Codigo = ? "
                    //+ " and Rt_Perc.HrBaixa = '' "
                    + " Order by Rt_Perc.Hora1";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(dataAtual);
            consult.setString(sCodPessoa);
            consult.select();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Filiais filiais = new Filiais();
                CarregaRota carregarota = new CarregaRota();
                Funcion funcion = new Funcion();
                rota.setRota(consult.getString("Rota"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setCodFil(consult.getString("CodFilori"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                funcion.setMatr(consult.getString("Matr"));
                //funcion.setCodFil(consult.getString("CodFil"));
                filiais.setDescricao(consult.getString("Descricao"));

                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setFuncion(funcion);
                carregarota.setFiliais(filiais);
                list_rota.add(carregarota);
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotaSuperv - " + e.getMessage() + "\r\n"
                    + "Select Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, "
                    + " Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, substring(Rt_Perc.ER,1,1) ER, Escala.Veiculo, Cliori.Latitude, "
                    + " Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.CodFil CodFilori, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D,"
                    + " Substring(Rt_Perc.HrCheg,1,2)+':'+Substring(Rt_Perc.HrCheg,4,2) HrCheg, "
                    + " Substring(Rt_Perc.HrSaida,1,2)+':'+Substring(Rt_Perc.HrSaida,4,2) HrSaida, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Rt_Perc.TipoSrv, Funcion.Matr, Funcion.CodFil, Filiais.Descricao "
                    + " from Pessoa as Pessoa "
                    + " Left Join Funcion as Funcion on Funcion.Matr = Pessoa.Matr "
                    + " Left Join Escala as Escala on Escala.CodPessoaSup = Pessoa.Codigo and "
                    //                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = " + dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Filiais on Filiais.Codfil = Rotas.CodFil "
                    //+ " Where Funcion.Matr = ? "
                    + " Where Pessoa.Codigo = " + sCodPessoa
                    //+ " and Rt_Perc.HrBaixa = '' "
                    + " Order by Rt_Perc.Hora1");
        }
    }

    /**
     * Conta o total de paradas da rota do dia para uma matrícula
     *
     * @param Matricula
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int getTotalRotas(String Matricula, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select count(*) total "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = ? "
                    + " and Rt_Perc.HrBaixa = '' ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Matricula);
            consult.select();
            int total = 0;
            while (consult.Proximo()) {
                total = consult.getInt("total");
            }
            consult.Close();
            return total;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getTotalRotas - " + e.getMessage() + "\r\n"
                    + "Select count(*) total "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = " + Matricula
                    + " and Rt_Perc.HrBaixa = '' ");
        }
    }

    public static CarregaRota getRotaPorParada(String parada, String Matricula, Persistencia persistencia) throws Exception {
        CarregaRota carregarota = new CarregaRota();
        try {
            String sql = "Select * FROM ( SELECT ROW_NUMBER() over (Order by Rt_Perc.Hora1) Iterador, "
                    + " Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1,"
                    + " Rt_perc.Nred, Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, "
                    + " Cliori.Latitude, Cliori.Longitude, Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, "
                    + " CliOri.Bairro, CliOri.Cidade, CliOri.Estado, CliOri.NroChave, "
                    + " Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, Rt_Perc.HrSaida, "
                    + " Rt_Perc.HrBaixa,Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv,Rotas.CodFil,CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat,CliFat.Estado EstadoFat,Clifat.CGC CNPJFat,Clifat.IE IEFat, OS.OS "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS"
                    + "                       and OS.CodFil = Rotas.CodFil"
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat"
                    + "                             and Clifat.CodFil = Rotas.CodFil"
                    + " Where Escala.Data = Convert(date,Getdate())"
                    + " and Funcion.Matr = ? "
                    + " and Rt_Perc.HrBaixa = '' ) AS Rotas "
                    + " WHERE Iterador = ? "
                    + " ORDER BY Iterador";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Matricula);
            consult.setString(parada);
            consult.select();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Clientes clifat = new Clientes();
                OS_Vig os = new OS_Vig();
                rota.setRota(consult.getString("Rota"));
                rota.setCodFil(consult.getString("CodFil"));
                rota.setSequencia(consult.getString("Sequencia"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setCodCli2(consult.getString("CodCli2"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setCodCli(consult.getString("CodCli1"));
                cliOri.setCodigo(consult.getString("CodCli1"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setNroChave(consult.getString("NroChave"));
                cliOri.setLatitude(consult.getString("Latitude"));
                cliOri.setLongitude(consult.getString("Longitude"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                cliDst.setEstado(consult.getString("EstadoDst"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                cliDst.setLatitude(consult.getString("Latitudedst"));
                cliDst.setLongitude(consult.getString("Longitudedst"));
                clifat.setNRed(consult.getString("NRedFat"));
                clifat.setEnde(consult.getString("EndFat"));
                clifat.setBairro(consult.getString("BairroFat"));
                clifat.setCidade(consult.getString("CidadeFat"));
                clifat.setEstado(consult.getString("EstadoFat"));
                clifat.setCGC(consult.getString("CNPJFat"));
                clifat.setIE(consult.getString("IEFat"));
                os.setOS(consult.getString("OS"));
                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setCliFat(clifat);
                carregarota.setOS(os);
            }
            consult.Close();
            return carregarota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotaPorParada - " + e.getMessage() + "\r\n"
                    + "Select * FROM ( SELECT ROW_NUMBER() over (Order by Rt_Perc.Hora1) Iterador, "
                    + " Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1,"
                    + " Rt_perc.Nred, Rt_perc.Sequencia, Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, "
                    + " Cliori.Latitude, Cliori.Longitude, Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, "
                    + " CliOri.Bairro, CliOri.Cidade, CliOri.Estado, CliOri.NroChave, "
                    + " Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, Rt_Perc.HrSaida, "
                    + " Rt_Perc.HrBaixa,Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv,Rotas.CodFil,CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat,CliFat.Estado EstadoFat,Clifat.CGC CNPJFat,Clifat.IE IEFat, OS.OS "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS"
                    + "                       and OS.CodFil = Rotas.CodFil"
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat"
                    + "                             and Clifat.CodFil = Rotas.CodFil"
                    + " Where Funcion.Matr = " + Matricula
                    + " and Rt_Perc.HrBaixa = '' ) AS Rotas "
                    + " WHERE Iterador = " + parada
                    + " ORDER BY Iterador");
        }
    }

    public static List<CarregaRota> getRotas(String param, String Matricula, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRota> list_rota = new ArrayList();

            String sql;
            if (param.equals("SATRODOBAN")) {
                sql = "Select ";
            } else {
                sql = "Select top 20 ";
            }

            sql += " Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, Rt_Perc.HrSaida, "
                    + " Rt_Perc.HrBaixa,Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv,Rotas.CodFil,CliFat.NRed NRedFat,CliFat.Ende EndFat,CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat,CliFat.Estado EstadoFat,Clifat.CGC CNPJFat,Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = ? "
                    + " and Rt_Perc.HrBaixa = '' " // Marcos 10-03-2017
                    + " Order by Rt_Perc.Hora1 ";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Matricula);
            consult.select();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Clientes clifat = new Clientes();
                OS_Vig os = new OS_Vig();
                CarregaRota carregarota = new CarregaRota();
                rota.setRota(consult.getString("Rota"));
                rota.setCodFil(consult.getString("CodFil"));
                rota.setSequencia(consult.getString("Sequencia"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setCodCli2(consult.getString("CodCli2"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setCodCli(consult.getString("CodCli1"));
                cliOri.setCodigo(consult.getString("CodCli1"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setNroChave(consult.getString("NroChave"));
                cliOri.setLatitude(consult.getString("Latitude"));
                cliOri.setLongitude(consult.getString("Longitude"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                cliDst.setEstado(consult.getString("EstadoDst"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                cliDst.setLatitude(consult.getString("Latitudedst"));
                cliDst.setLongitude(consult.getString("Longitudedst"));
                clifat.setNRed(consult.getString("NRedFat"));
                clifat.setEnde(consult.getString("EndFat"));
                clifat.setBairro(consult.getString("BairroFat"));
                clifat.setCidade(consult.getString("CidadeFat"));
                clifat.setEstado(consult.getString("EstadoFat"));
                clifat.setCGC(consult.getString("CNPJFat"));
                clifat.setIE(consult.getString("IEFat"));
                os.setOS(consult.getString("OS"));
                os.setCliente(consult.getString("OrigemPedido"));
                os.setNRed(consult.getString("NRedOrigemPedido"));
                os.setCliDst(consult.getString("DstPedido"));
                os.setNRedDst(consult.getString("NRedDstPedido"));
                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setCliFat(clifat);
                carregarota.setOS(os);
                list_rota.add(carregarota);
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotas - " + e.getMessage() + "\r\n"
                    + "Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, Rt_Perc.HrSaida, "
                    + " Rt_Perc.HrBaixa,Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv,Rotas.CodFil,CliFat.NRed NRedFat,CliFat.Ende EndFat,CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat,CliFat.Estado EstadoFat,Clifat.CGC CNPJFat,Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = " + Matricula
                    + " and Rt_Perc.HrBaixa = '' " // Marcos 10-03-2017
                    + " Order by Rt_Perc.Hora1 ");
        }
    }

    public static List<CarregaRota> getRotas(String param, String matricula, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRota> list_rota = new ArrayList();
            String sql;
            if (param.equals("SATGLOVAL")) {
                sql = "Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                        + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                        + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                        + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                        + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst, "
                        + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                        + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                        + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                        + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                        + " from Funcion as Funcion "
                        + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                        + "                               Escala.CodFil = Funcion.CodFil and "
                        + "                               Escala.Data = ? "
                        + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                        + "                             Rotas.CodFil = Escala.CodFil and "
                        + "                             Rotas.Flag_Excl <> '*' "
                        + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                        + "                                 Rt_perc.Flag_Excl <> '*' "
                        + " Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia"
                        + "                             and sla.parada = rt_perc.parada  "
                        //+ "                             and sla.codfil = rt_perc.codfil "
                        + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                        + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                        + "                                    Clientes.CodFil = Rotas.CodFil "
                        + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                        + "                                  CliOri.CodFil = Rotas.CodFil "
                        + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                        + "                 and Pedido.codfil = Rotas.codfil"
                        + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                        + "                         and OSPedido.CodFil = Pedido.CodFil "
                        + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                        + "                       and OS.CodFil = Rotas.CodFil "
                        + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                        + "                             and Clifat.CodFil = Rotas.CodFil "
                        + " Where Funcion.Matr = ? "
                        + "     and (sla.hrsaidavei = '' or sla.hrsaidavei is null) "
                        + " Order by Rt_Perc.Hora1 ";
            } else {
                sql = " Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                        + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                        + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                        + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                        + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, Clientes.NRed Destino, Clientes.Ende EndDst, "
                        + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                        + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                        + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                        + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                        + " from Funcion as Funcion "
                        + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                        + "                               Escala.CodFil = Funcion.CodFil and "
                        + "                               Escala.Data = ? "
                        + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                        + "                             Rotas.CodFil = Escala.CodFil and "
                        + "                             Rotas.Flag_Excl <> '*' "
                        + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                        + "                                 Rt_perc.Flag_Excl <> '*' "
                        + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                        + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                        + "                                    Clientes.CodFil = Rotas.CodFil "
                        + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                        + "                                  CliOri.CodFil = Rotas.CodFil "
                        + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                        + "                 and Pedido.codfil = Rotas.codfil"
                        + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                        + "                         and OSPedido.CodFil = Pedido.CodFil "
                        + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                        + "                       and OS.CodFil = Rotas.CodFil "
                        + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                        + "                             and Clifat.CodFil = Rotas.CodFil "
                        + " Where Funcion.Matr = ? "
                        + " and Rt_Perc.HrBaixa = '' " // Marcos 10-03-2017
                        + " Order by Rt_Perc.Hora1 ";
            }

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(dataAtual);
            consult.setString(matricula);
            consult.select();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Rt_PercSla rt_percsla = new Rt_PercSla();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Clientes clifat = new Clientes();
                OS_Vig os = new OS_Vig();
                CarregaRota carregarota = new CarregaRota();
                rota.setRota(consult.getString("Rota"));
                rota.setCodFil(consult.getString("CodFil"));
                rota.setSequencia(consult.getString("Sequencia"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setCodCli2(consult.getString("CodCli2"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                if (param.equals("SATGLOVAL")) {
                    rt_percsla.setHrChegVei(consult.getString("HrChegVei"));
                }
                if (param.equals("SATGLOVAL")) {
                    rt_percsla.setHrSaidaVei(consult.getString("HrSaidaVei"));
                }
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setNRed(consult.getString("Origem"));
                cliOri.setCodCli(consult.getString("CodCli1"));
                cliOri.setCodigo(consult.getString("CodCli1"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setNroChave(consult.getString("NroChave"));
                cliOri.setLatitude(consult.getString("Latitude"));
                cliOri.setLongitude(consult.getString("Longitude"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                cliDst.setEstado(consult.getString("EstadoDst"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                cliDst.setLatitude(consult.getString("Latitudedst"));
                cliDst.setLongitude(consult.getString("Longitudedst"));
                clifat.setNRed(consult.getString("NRedFat"));
                clifat.setEnde(consult.getString("EndFat"));
                clifat.setBairro(consult.getString("BairroFat"));
                clifat.setCidade(consult.getString("CidadeFat"));
                clifat.setEstado(consult.getString("EstadoFat"));
                clifat.setCGC(consult.getString("CNPJFat"));
                clifat.setIE(consult.getString("IEFat"));
                os.setOS(consult.getString("OS"));
                os.setCliente(consult.getString("OrigemPedido"));
                os.setNRed(consult.getString("NRedOrigemPedido"));
                os.setCliDst(consult.getString("DstPedido"));
                os.setNRedDst(consult.getString("NRedDstPedido"));
                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setRt_percsla(rt_percsla);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setCliFat(clifat);
                carregarota.setOS(os);
                list_rota.add(carregarota);
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotas - " + e.getMessage());
        }
    }

    public static CarregaRota getUltimaParada(String Matricula, String dataAtual, Persistencia persistencia) throws Exception {
        try {

            String sql = "Select top 1 Rotas.Rota, "
                    + " Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, Rt_perc.Parada, "
                    + " Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat ,Clifat.CGC CNPJFat ,Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = ? "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = ? "
                    + " Order by Rt_Perc.HrSaida desc";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(dataAtual);
            consult.setString(Matricula);
            consult.select();

            CarregaRota carregarota = new CarregaRota();
            while (consult.Proximo()) {
                Rotas rota = new Rotas();
                Rt_Perc rt_perc = new Rt_Perc();
                Escala escala = new Escala();
                Clientes cliOri = new Clientes();
                Clientes cliDst = new Clientes();
                Clientes clifat = new Clientes();
                OS_Vig os = new OS_Vig();
                rota.setRota(consult.getString("Rota"));
                rota.setCodFil(consult.getString("CodFil"));
                rota.setSequencia(consult.getString("Sequencia"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setCodCli2(consult.getString("CodCli2"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setNRed(consult.getString("Origem"));
                cliOri.setCodCli(consult.getString("CodCli1"));
                cliOri.setCodigo(consult.getString("CodCli1"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setNroChave(consult.getString("NroChave"));
                cliOri.setLatitude(consult.getString("Latitude"));
                cliOri.setLongitude(consult.getString("Longitude"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                cliDst.setEstado(consult.getString("EstadoDst"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                cliDst.setLatitude(consult.getString("Latitudedst"));
                cliDst.setLongitude(consult.getString("Longitudedst"));
                clifat.setNRed(consult.getString("NRedFat"));
                clifat.setEnde(consult.getString("EndFat"));
                clifat.setBairro(consult.getString("BairroFat"));
                clifat.setCidade(consult.getString("CidadeFat"));
                clifat.setEstado(consult.getString("EstadoFat"));
                clifat.setCGC(consult.getString("CNPJFat"));
                clifat.setIE(consult.getString("IEFat"));
                os.setOS(consult.getString("OS"));
                os.setCliente(consult.getString("OrigemPedido"));
                os.setNRed(consult.getString("NRedOrigemPedido"));
                os.setCliDst(consult.getString("DstPedido"));
                os.setNRedDst(consult.getString("NRedDstPedido"));
                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setCliFat(clifat);
                carregarota.setOS(os);
            }
            consult.Close();
            return carregarota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getUltimaParada - " + e.getMessage() + "\r\n"
                    + "Select top 1 Rotas.Rota, "
                    + " Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, Rt_perc.Parada, "
                    + " Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, Clientes.NRed Destino, Clientes.Ende EndDst, Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst, Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat ,Clifat.CGC CNPJFat ,Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = " + dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = " + Matricula
                    + " Order by Rt_Perc.HrSaida desc");
        }
    }

    /**
     * Carrega Rota Atualizado 08/08/2018
     *
     * @param param
     * @param matricula
     * @param dataAtual
     * @param persistencia
     * @return
     * @throws Exception
     */
    public static List<CarregaRotaVol> getRotasVol(String param, String matricula, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRotaVol> list_rota = new ArrayList();
            String sql = (persistencia.getEmpresa().toUpperCase().contains("CORPV_S")
                    || persistencia.getEmpresa().toUpperCase().contains("PRESERVE") ? " select top 05 " : "Select ")
                    + " Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, "
                    + " Case when (Select count(*) from CxForte where CxForte.CodCli in (Rt_Perc.CodCli1) and CxForte.CodFil = Rotas.CodFil) > 0 "
                    + " then 'S' else 'N' end CxForte,"
                    + " Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Rotas"
                    + " Left Join Escala as Escala on Escala.SeqRota = Rotas.Sequencia "
                    + " Left Join Funcion  on Funcion.Matr = Escala.MatrChe"
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia "
                    + " Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia"
                    + "                             and sla.parada = rt_perc.parada  "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                 Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                 CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                     and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Rotas.Data = ? "
                    + "   and Funcion.Matr = ? "
                    + "   and (sla.hrsaidavei = '' or sla.hrsaidavei is null) "
                    + "   and Rotas.Flag_Excl <> '*' "
                    + "   and Rt_perc.Flag_Excl <> '*' "
                    + " Group by Rotas.Rota,  Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + "         Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + "         Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + "         CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg, "
                    + "         Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende, "
                    + "         Clientes.Bairro, Clientes.Cidade, "
                    + "         Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed, CliFat.Ende, CliFat.Bairro, "
                    + "         CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente, "
                    + "         OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst "
                    + " Order by Rt_Perc.Hora1 ";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(dataAtual);
            consult.setString(matricula);
            consult.select();

            Rotas rota;
            Rt_Perc rt_perc;
            Rt_PercSla rt_percsla;
            Escala escala;
            Clientes cliOri;
            Clientes cliDst;
            Clientes clifat;
            OS_Vig os;
            CarregaRotaVol carregarota;

            while (consult.Proximo()) {

                rota = new Rotas();
                rt_perc = new Rt_Perc();
                rt_percsla = new Rt_PercSla();
                escala = new Escala();
                cliOri = new Clientes();
                cliDst = new Clientes();
                clifat = new Clientes();
                os = new OS_Vig();
                carregarota = new CarregaRotaVol();

                rota.setRota(consult.getString("Rota"));
                rota.setCodFil(consult.getString("CodFil"));
                rota.setSequencia(consult.getString("Sequencia"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setNRed(consult.getString("Nred"));
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("Parada"));
                rt_perc.setCodCli1(consult.getString("CodCli1"));
                rt_perc.setCodCli2(consult.getString("CodCli2"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setHora1D(consult.getString("Hora1D"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                rt_perc.setHrSaida(consult.getString("HrSaida"));
                rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                rt_percsla.setHrChegVei(consult.getString("HrChegVei"));
                rt_percsla.setHrSaidaVei(consult.getString("HrSaidaVei"));
                escala.setVeiculo(consult.getString("Veiculo"));
                cliOri.setNRed(consult.getString("Origem"));
                cliOri.setCodCli(consult.getString("CodCli1"));
                cliOri.setCodigo(consult.getString("CodCli1"));
                cliOri.setEnde(consult.getString("Ende"));
                cliOri.setBairro(consult.getString("Bairro"));
                cliOri.setCidade(consult.getString("Cidade"));
                cliOri.setEstado(consult.getString("Estado"));
                cliOri.setNroChave(consult.getString("NroChave"));
                cliOri.setLatitude(consult.getString("Latitude"));
                cliOri.setLongitude(consult.getString("Longitude"));
                cliDst.setNRed(consult.getString("Destino"));
                cliDst.setEnde(consult.getString("EndDst"));
                cliDst.setBairro(consult.getString("BairroDst"));
                cliDst.setCidade(consult.getString("CidDst"));
                cliDst.setEstado(consult.getString("EstadoDst"));
                rt_perc.setHrCheg(consult.getString("HrCheg"));
                cliDst.setLatitude(consult.getString("Latitudedst"));
                cliDst.setLongitude(consult.getString("Longitudedst"));
                clifat.setNRed(consult.getString("NRedFat"));
                clifat.setEnde(consult.getString("EndFat"));
                clifat.setBairro(consult.getString("BairroFat"));
                clifat.setCidade(consult.getString("CidadeFat"));
                clifat.setEstado(consult.getString("EstadoFat"));
                clifat.setCGC(consult.getString("CNPJFat"));
                clifat.setIE(consult.getString("IEFat"));
                os.setOS(consult.getString("OS"));
                os.setCliente(consult.getString("OrigemPedido"));
                os.setNRed(consult.getString("NRedOrigemPedido"));
                os.setCliDst(consult.getString("DstPedido"));
                os.setNRedDst(consult.getString("NRedDstPedido"));

                carregarota.setCxforte(consult.getString("CxForte"));
                carregarota.setRota(rota);
                carregarota.setRt_perc(rt_perc);
                carregarota.setRt_percsla(rt_percsla);
                carregarota.setEscala(escala);
                carregarota.setCliOri(cliOri);
                carregarota.setCliDst(cliDst);
                carregarota.setCliFat(clifat);
                carregarota.setOS(os);
                list_rota.add(carregarota);
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotasVol - " + e.getMessage() + "\r\n"
                    + "Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = " + dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia"
                    + "                             and sla.parada = rt_perc.parada  "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    + " Where Funcion.Matr = " + matricula
                    + "     and (sla.hrsaidavei = '' or sla.hrsaidavei is null) "
                    + " Group by Rotas.Rota,  Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + "          Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + "          Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + "          CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg, "
                    + "          Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende, "
                    + "          Clientes.Bairro, Clientes.Cidade, "
                    + "          Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed, CliFat.Ende, CliFat.Bairro, "
                    + "          CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente, "
                    + "          OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst "
                    + " Order by Rt_Perc.Hora1");
        }
    }

    public static List<CarregaRotaVol> getRotasVol(String sequencia, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRotaVol> list_rota = new ArrayList();
            Boolean isEmpresaCamba = isTranspCacamba(persistencia.getEmpresa());
            
            String sql = " Select * FROM  (\n"
                    + "    SELECT \n"
                    + "         case when Rt_PErc.ER = 'R' then\n"
                    + "            case when Pedido.Hora1O is null then\n"
                    + "                case when OS_VFreq.Hora1 is null then\n"
                    + "                    Rotas.HrLargada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora1\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora1O \n"
                    + "            end\n"
                    + "        else\n"
                    + "            case when Pedido.Hora1D is null then\n"
                    + "                case when OS_VFreq.Hora1 is null then\n"
                    + "                    Rotas.HrLargada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora1\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora1D \n"
                    + "            end\n"
                    + "        end Horario1,\n"
                    + "        case when Rt_PErc.ER = 'R' then\n"
                    + "            case when Pedido.Hora2O is null then\n"
                    + "                case when OS_VFreq.Hora2 is null then\n"
                    + "                    Rotas.HrChegada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora2\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora2O \n"
                    + "            end\n"
                    + "        else\n"
                    + "            case when Pedido.Hora2D is null then\n"
                    + "                case when OS_VFreq.Hora2 is null then\n"
                    + "                    Rotas.HrChegada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora2\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora2D \n"
                    + "            end\n"
                    + "        end Horario2,\n"
                    + "    CliOri.CodPtoCli CodPtoCli, CliOri.CodExt CodExt, \n"
                    + "    Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia,\n"
                    + "    Rt_perc.Observ, CliOri.Contato, CliOri.Fone1, Rt_perc.Parada, 0 ParadaOri,\n"
                    + "    Case when (Select count(*) from CxForte where CxForte.CodCli in (Rt_Perc.CodCli1) and CxForte.CodFil = Rotas.CodFil) > 0\n"
                    + "    then 'S' else 'N' end CxForte, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude,\n"
                    + "    Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado,\n"
                    + "    CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg,\n"
                    + "    Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst,\n"
                    + "    Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.Nome NRedFat,\n"
                    + "    CliFat.Ende EndFat, CliFat.Bairro BairroFat, CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat,\n"
                    + "    OS.OS, OSPedido.Cliente OrigemPedido, OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido, \n"
                    + "    -1 Guia, Replicate('X', 3) Serie, \n"
                    + "(Select top 1 Case when Rt_Perc.TipoSrv = 'R' then CtrItens.ValorRot \n"
                    + "            when Rt_Perc.TipoSrv = 'V' then CtrItens.ValorEve\n"
                    + "            else CtrItens.ValorESP end ValorFat from OS_VItens \n"
                    + "left join OS_Vig on OS_Vig.OS     = OS_VItens.OS\n"
                    + "                 and OS_Vig.CodFil = OS_VItens.CodFil\n"
                    + "left join CtrItens on  CtrItens.Contrato  = OS_Vig.Contrato\n"
                    + "                   and CtrItens.CodFil    = OS_Vig.CodFil\n"
                    + "                   and CtrItens.TipoPosto = OS_VItens.TipoPosto\n"
                    + "where OS_VItens.OS        = Rt_Perc.OS\n"
                    + "  and OS_VItens.Codfil    = Rotas.CodFil\n"
                    + "  and OS_VItens.DtInicio <= Convert(Date, GetDate())\n"
                    + "  and OS_VItens.DtFim    >= Convert(Date, GetDate())) Valor, \n"
                    + "Case when Pedido.TipoMoeda is null then paramet.MoedaPdrMobile else Pedido.TipoMoeda end Moeda,\n"
                    + " 999999 OSGuia, 999999 SequenciaOri, 999 Volumes, Replicate('X', 20) Lacre,\n"
                    + "    99999 ValorLacre, 999 QtdeLacre, 'ROTAS' OriInf, \n"
                    + "(Select count(*) from PreOrder where Preorder.DtColeta = Rotas.Data and PreOrder.CodCli1 = Rt_Perc.CodCli1 and PreOrder.CodFil = Rotas.CodFil) PreOrder\n"
                    + "from Rotas\n"
                    + "Left Join Escala as Escala on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left Join Funcion  on Funcion.Matr = Escala.MatrChe\n"
                    + "Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr\n"
                    + "Left Join Clientes as Clientes on Clientes.Codigo = Rt_Perc.CodCli2\n"
                    + "                              and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left Join Clientes as CliOri on CliOri.Codigo = Rt_Perc.CodCli1\n"
                    + "                            and CliOri.CodFil = Rotas.CodFil\n"
                    + "Left Join Pedido on Pedido.numero = Rt_Perc.pedido\n"
                    + "                and Pedido.codfil = Rotas.codfil\n"
                    + "Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS\n"
                    + "                        and OSPedido.CodFil = Pedido.CodFil\n"
                    + "Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS\n"
                    + "                  and OS.CodFil = Rotas.CodFil\n"
                    + "\n"
                    + "Left Join OS_VFreq ON OS_VFreq.OS = OS.OS\n"
                    + "                  AND OS_VFreq.CodFil = OS.CodFil\n"
                    + "                  AND OS_VFreq.DiaSem = DATEPART(WEEKDAY, GETDATE())\n"
                    + "Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat\n"
                    + "                            and Clifat.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_perc.Sequencia = ? \n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "Group by Rotas.HrLargada, OS_VFreq.Hora1, OS_VFreq.Hora2, Rotas.HrChegada, CliOri.CodPtoCli, CliOri.CodExt, \n"
                    + "    Rotas.Rota, Rotas.Data, Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia,  Rt_perc.Observ, CliOri.Contato, CliOri.Fone1, \n"
                    + "    Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_perc.OS, \n"
                    + "    Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende,\n"
                    + "    CliOri.Bairro, CliOri.Cidade, CliOri.Estado, CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg,\n"
                    + "    Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende,\n"
                    + "    Clientes.Bairro, Clientes.Cidade, Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.Nome, CliFat.Ende, CliFat.Bairro,\n"
                    + "    CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente,\n"
                    + "    OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst, Pedido.TipoMoeda, paramet.MoedaPdrMobile,\n"
                    + "    Pedido.Hora1O, Pedido.Hora1D, Pedido.Hora2D, Pedido.Hora2O \n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, \n"
                    + "    Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, Rt_Guias.Valor, \n"
                    + "    Rt_GuiasMoeda.Moeda Moeda,\n"
                    + "    Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri, \n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, '' Lacre,\n"
                    + "     99999 ValorLacre, 999 QtdeLacre, 'RT_GUIAS' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "Where Rt_guias.Sequencia = ? -- (Select SeqRota from Escala where CodFil = 2001 and Data = '20180808' and MatrChe =  61)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_perc.ER = 'E'\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "UNION  -- Parada destino vinda de outras paradas - normalmente caixa-forte\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), Rt_perc.DPar Parada, Rt_perc.Parada ParadaOri, 'X', Rt_perc.CodCli1, \n"
                    + "    Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, \n"
                    + "    Rt_Guias.Valor, \n"
                    + "    Rt_GuiasMoeda.Moeda Moeda,\n"
                    + "Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, '' Lacre, \n"
                    + "    99999 ValorLacre, 999 QtdeLacre, 'RT_GUIAS' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.dpar \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "Where Rt_guias.Sequencia = ? \n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Parada in (Select Parada from Rt_Perc where Sequencia = Rt_Guias.Sequencia and Dpar > 0)\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.DPar Parada, Rt_perc.Parada ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie,\n"
                    + "    Rt_Guias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda, \n"
                    + " Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0  \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.dpar \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_guias.Sequencia = ? \n"
                    + "    and Rt_Guias.Parada in (Select Parada from Rt_Perc where Sequencia = Rt_Guias.Sequencia and Dpar > 0)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, Rt_Guias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "    Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri, \n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, CxfGuiasVol.Lacre, \n"
                    + "    CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_guias.Sequencia = ? -- (Select SeqRota from Escala where CodFil = 2001 and Data = '20180808' and MatrChe =  61)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_perc.ER = 'E'\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "union \n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CxFGuias.Guia) Guia, CxFguias.Serie, CxFGuias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "     CxFguias.OS, CxfGuias.SeqRotaSai SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = CxFguias.Guia and Serie = CxFguias.Serie) Volumes, '' Lacre, \n"
                    + "99999 ValorLacre, 999 QtdeLacre, 'CXFGUIAS' OriInf, 0 \n"
                    + " from CxfGuias\n"
                    + "left join Rotas on Rotas.Sequencia = CxFGuias.SeqRotaSai\n"
                    + "left join Escala on Escala.Seqrota = CxFGuias.SeqRotaSai\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                             and Rt_perc.Hora1     = CxFGuias.Hora1D\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_Guias on  Rt_guias.Guia      = CxfGuias.Guia\n"
                    + "                   and Rt_guias.Serie     = CxfGUias.Serie\n"
                    + "                   and Rt_Guias.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where CxfGuias.SeqRotaSai = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Guia is null\n"
                    + "\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CxFGuias.Guia) Guia, CxFguias.Serie,\n"
                    + "     CxFGuias.Valor, \n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "CxFguias.OS, CxfGuias.SeqRotaSai SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = CxFguias.Guia and Serie = CxFguias.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 \n"
                    + "from CxfGuias\n"
                    + "left join Rotas on Rotas.Sequencia = CxFGuias.SeqRotaSai\n"
                    + "left join Escala on Escala.Seqrota = CxFGuias.SeqRotaSai\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                             and Rt_perc.Hora1     = CxFGuias.Hora1D\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_Guias on  Rt_guias.Guia      = CxfGuias.Guia\n"
                    + "                   and Rt_guias.Serie     = CxfGUias.Serie\n"
                    + "                   and Rt_Guias.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = CxFGuias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = CxFguias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where CxfGuias.SeqRotaSai = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Guia is null\n"
                    + "\n"
                    + "Union \n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,TesSaidas.Guia) Guia, TesSaidas.Serie,\n"
                    + "     TesSaidas.TotalGeral Valor, \n"
                    + "    (SELECT TOP 1 MoedaPdrMobile FROM Paramet WHERE Filial_PDR = TesSaidas.CodFil) Moeda,\n"
                    + "Pedido.OS, Pedido.SeqRota SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = TesSaidas.Guia and Serie = TesSaidas.Serie) Volumes,\n"
                    + "     Replicate('', 20) Lacre, 99999 ValorLacre, 999 QtdeLacre, 'TESSAIDAS' OriInf, 0  \n"
                    + "from TesSaidas\n"
                    + "left join Pedido on Pedido.Numero   = TesSaidas.Pedido\n"
                    + "                 and Pedido.CodFil  = TesSaidas.CodFil\n"
                    + "                 and Pedido.CodCli2 = TesSaidas.CodCli2\n"
                    + "left join Rotas on Rotas.Sequencia = Pedido.SeqRota\n"
                    + "left join Escala on Escala.Seqrota = Pedido.SeqRota\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Pedido.SeqRota\n"
                    + "                             and Rt_perc.Parada    = Pedido.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join CxfGuias on  CxfGuias.Guia = TesSaidas.Guia\n"
                    + "                   and CxfGuias.Serie = TesSaidas.Serie\n"
                    + "Where Pedido.SeqRota = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and CxfGuias.Guia is null \n"
                    + "    and TesSaidas.TotalGeral > 0\n"
                    + "\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,TesSaidas.Guia) Guia, TesSaidas.Serie,\n"
                    + "    TesSaidas.TotalGeral Valor, \n"
                    + "--    (SELECT TOP 1 MoedaPdrMobile FROM Paramet WHERE Filial_PDR = TesSaidas.CodFil) Moeda,\n"
                    + "    Case when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "Pedido.OS, Pedido.SeqRota SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = TesSaidas.Guia and Serie = TesSaidas.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 from TesSaidas\n"
                    + "left join Pedido on Pedido.Numero   = TesSaidas.Pedido\n"
                    + "                 and Pedido.CodFil  = TesSaidas.CodFil\n"
                    + "                 and Pedido.CodCli2 = TesSaidas.CodCli2\n"
                    + "left join Rotas on Rotas.Sequencia = Pedido.SeqRota\n"
                    + "left join Escala on Escala.Seqrota = Pedido.SeqRota\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Pedido.SeqRota\n"
                    + "                             and Rt_perc.Parada    = Pedido.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join CxfGuias on  CxfGuias.Guia = TesSaidas.Guia\n"
                    + "                   and CxfGuias.Serie = TesSaidas.Serie\n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = TesSaidas.Guia\n"
                    + "                      and CxfGuiasVol.Serie = TesSaidas.Serie\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Pedido.SeqRota = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and CxfGuias.Guia is null \n"
                    + "    and TesSaidas.TotalGeral > 0\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_Perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CtrOperEquip.Guia) Guia, CtrOperEquip.Serie,\n"
                    + "    '0' Valor, \n"
                    + "'' Moeda,\n"
                    + " '' OS, CtrOperEquip.SeqRota SequenciaOri, '' Volumes,\n"
                    + "    CtrOperEquip.IDEquip Lacre, '0' ValorLacre, '1' QtdeLacre, 'CONTAINER' OriInf, 0 \n"
                    + "from CtrOperEquip\n"
                    + "\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.CodCli1 = CtrOperEquip.CodCli1\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_perc.Sequencia\n"
                    + "Where Rt_perc.Sequencia = ? and Rt_Perc.ER = 'R'\n";
            if (!isEmpresaCamba
                    && !persistencia.getEmpresa().equals("SATMAXIMA")) {
                sql += "    and (sla.hrsaidavei = '' or sla.hrsaidavei is null)\n";
            }
            sql += ") a\n";
            if (isEmpresaCamba) {
                sql += " WHERE OS > 0 OR CodCli1 = '9996900' OR CodCli1 = '9996015'\n";
            }
            sql += "Order by Hora1, Parada, Guia, Lacre, ValorLacre";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.select();

            Rotas rota;
            Rt_Perc rt_perc;
            Rt_PercSla rt_percsla;
            Escala escala;
            Clientes cliOri;
            Clientes cliDst;
            Clientes clifat;
            OS_Vig os;
            CarregaRotaVol carregarota;
            GuiasList guia;
            CxFGuiasVol volume;

            while (consult.Proximo()) {
                carregarota = new CarregaRotaVol();
                carregarota.setOriInf(consult.getString("OriInf"));

                if (carregarota.getOriInf().equals("ROTAS")) {
                    rota = new Rotas();
                    rt_perc = new Rt_Perc();
                    rt_percsla = new Rt_PercSla();
                    escala = new Escala();
                    cliOri = new Clientes();
                    cliDst = new Clientes();
                    clifat = new Clientes();
                    os = new OS_Vig();

                    rota.setRota(consult.getString("Rota"));
                    rota.setCodFil(consult.getString("CodFil"));
                    rota.setSequencia(consult.getString("Sequencia"));
                    rota.setValor(consult.getString("Valor"));
                    rt_perc.setHora1(consult.getString("Hora1"));
                    rt_perc.setNRed(consult.getString("Nred"));
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    rt_perc.setCodCli1(consult.getString("CodCli1"));
                    rt_perc.setCodCli2(consult.getString("CodCli2"));
                    rt_perc.setER(consult.getString("ER"));
                    rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                    rt_perc.setHora1D(consult.getString("Hora1D"));
                    rt_perc.setHrCheg(consult.getString("HrCheg"));
                    rt_perc.setHrSaida(consult.getString("HrSaida"));
                    rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                    rt_perc.setObserv(consult.getString("Observ"));
                    rt_perc.setMoeda(consult.getString("Moeda"));
                    rt_percsla.setHrChegVei(consult.getString("HrChegVei"));
                    rt_percsla.setHrSaidaVei(consult.getString("HrSaidaVei"));
                    escala.setVeiculo(consult.getString("Veiculo"));
                    cliOri.setNRed(consult.getString("Origem"));
                    cliOri.setCodExt(consult.getString("CodExt"));
                    cliOri.setCodPtoCli(consult.getString("CodPtoCli"));
                    cliOri.setCodCli(consult.getString("CodCli1"));
                    cliOri.setCodigo(consult.getString("CodCli1"));
                    cliOri.setEnde(consult.getString("Ende"));
                    cliOri.setBairro(consult.getString("Bairro"));
                    cliOri.setCidade(consult.getString("Cidade"));
                    cliOri.setEstado(consult.getString("Estado"));
                    cliOri.setNroChave(consult.getString("NroChave"));
                    cliOri.setLatitude(consult.getString("Latitude"));
                    cliOri.setLongitude(consult.getString("Longitude"));
                    cliOri.setContato(consult.getString("contato"));
                    cliOri.setFone1(consult.getString("fone1"));
                    cliDst.setNRed(consult.getString("Destino"));
                    cliDst.setEnde(consult.getString("EndDst"));
                    cliDst.setBairro(consult.getString("BairroDst"));
                    cliDst.setCidade(consult.getString("CidDst"));
                    cliDst.setEstado(consult.getString("EstadoDst"));
                    rt_perc.setHrCheg(consult.getString("HrCheg"));
                    cliDst.setLatitude(consult.getString("Latitudedst"));
                    cliDst.setLongitude(consult.getString("Longitudedst"));
                    clifat.setNRed(consult.getString("NRedFat"));
                    clifat.setEnde(consult.getString("EndFat"));
                    clifat.setBairro(consult.getString("BairroFat"));
                    clifat.setCidade(consult.getString("CidadeFat"));
                    clifat.setEstado(consult.getString("EstadoFat"));
                    clifat.setCGC(consult.getString("CNPJFat"));
                    clifat.setIE(consult.getString("IEFat"));
                    os.setOS(consult.getString("OS"));
                    os.setCliente(consult.getString("OrigemPedido"));
                    os.setNRed(consult.getString("NRedOrigemPedido"));
                    os.setCliDst(consult.getString("DstPedido"));
                    os.setNRedDst(consult.getString("NRedDstPedido"));
                    os.setHorario1(consult.getString("Horario1"));
                    os.setHorario2(consult.getString("Horario2"));

                    carregarota.setCxforte(consult.getString("CxForte"));
                    carregarota.setPreOrder(consult.getString("PreOrder"));

                    carregarota.setRota(rota);
                    carregarota.setRt_perc(rt_perc);
                    carregarota.setRt_percsla(rt_percsla);
                    carregarota.setEscala(escala);
                    carregarota.setCliOri(cliOri);
                    carregarota.setCliDst(cliDst);
                    carregarota.setCliFat(clifat);
                    carregarota.setOS(os);
                    carregarota.setGuias(new ArrayList<>());
                    carregarota.setContainers(new ArrayList<>());
                    list_rota.add(carregarota);

                } else if (carregarota.getOriInf().equals("RT_GUIAS")
                        || carregarota.getOriInf().equals("CXFGUIAS")
                        || carregarota.getOriInf().equals("TESSAIDAS")) {
                    guia = new GuiasList();
                    guia.setGuia(consult.getString("Guia"));
                    guia.setSerie(consult.getString("Serie"));
                    guia.setValor(consult.getString("Valor"));
                    guia.setOS(consult.getString("OSGuia"));
                    guia.setSequenciaOri(consult.getBigDecimal("SequenciaOri"));
                    guia.setParadaOri(consult.getInt("ParadaOri"));
                    guia.setMoeda(consult.getString("moeda"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        list_rota.get(indice1).getGuias().add(guia);
                    }
                } else if (carregarota.getOriInf().equals("CXFGUIASVOL")) {
                    guia = new GuiasList();
                    guia.setGuia(consult.getString("Guia"));
                    guia.setSerie(consult.getString("Serie"));

                    volume = new CxFGuiasVol();
                    volume.setGuia(guia.getGuia());
                    volume.setSerie(guia.getSerie());
                    volume.setLacre(consult.getString("Lacre"));
                    volume.setValor(consult.getString("ValorLacre"));
                    volume.setQtde(consult.getString("QtdeLacre"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        int indice2 = list_rota.get(indice1).getGuias().indexOf(guia);

                        if (indice2 != -1) {
                            list_rota.get(indice1).getGuias().get(indice2).getVolumes().add(volume);
                        }
                    }

                } else if (carregarota.getOriInf().equals("CONTAINER")) {
                    volume = new CxFGuiasVol();
                    volume.setGuia(consult.getString("Guia"));
                    volume.setSerie(consult.getString("Serie"));
                    volume.setLacre(consult.getString("Lacre"));
                    volume.setValor(consult.getString("ValorLacre"));
                    volume.setQtde(consult.getString("QtdeLacre"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        list_rota.get(indice1).getContainers().add(volume);
                    }

                }
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotasVol - " + e.getMessage() + "\r\n"
                    + "Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    //                + "                               Escala.Data = "+dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia"
                    + "                             and sla.parada = rt_perc.parada  "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    //                + " Where Funcion.Matr = "+matricula
                    + "     and (sla.hrsaidavei = '' or sla.hrsaidavei is null) "
                    + " Group by Rotas.Rota,  Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + "          Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + "          Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + "          CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg, "
                    + "          Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende, "
                    + "          Clientes.Bairro, Clientes.Cidade, "
                    + "          Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed, CliFat.Ende, CliFat.Bairro, "
                    + "          CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente, "
                    + "          OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst "
                    + " Order by Rt_Perc.Hora1");
        }
    }

    public static List<CarregaRotaVol> getRotasVolData(String sequencia, Persistencia persistencia) throws Exception {
        try {
            List<CarregaRotaVol> list_rota = new ArrayList();
            String sql = " Select * FROM  (\n"
                    + "    SELECT \n"
                    + "         case when Rt_PErc.ER = 'R' then\n"
                    + "            case when Pedido.Hora1O is null then\n"
                    + "                case when OS_VFreq.Hora1 is null then\n"
                    + "                    Rotas.HrLargada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora1\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora1O \n"
                    + "            end\n"
                    + "        else\n"
                    + "            case when Pedido.Hora1D is null then\n"
                    + "                case when OS_VFreq.Hora1 is null then\n"
                    + "                    Rotas.HrLargada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora1\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora1D \n"
                    + "            end\n"
                    + "        end Horario1,\n"
                    + "        case when Rt_PErc.ER = 'R' then\n"
                    + "            case when Pedido.Hora2O is null then\n"
                    + "                case when OS_VFreq.Hora2 is null then\n"
                    + "                    Rotas.HrChegada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora2\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora2O \n"
                    + "            end\n"
                    + "        else\n"
                    + "            case when Pedido.Hora2D is null then\n"
                    + "                case when OS_VFreq.Hora2 is null then\n"
                    + "                    Rotas.HrChegada \n"
                    + "                else\n"
                    + "                    OS_VFreq.Hora2\n"
                    + "                end\n"
                    + "            else \n"
                    + "                Pedido.Hora2D \n"
                    + "            end\n"
                    + "        end Horario2,\n"
                    + "    CliOri.CodPtoCli CodPtoCli, CliOri.CodExt CodExt, \n"
                    + "    Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia,\n"
                    + "    Rt_perc.Observ, CliOri.Contato, CliOri.Fone1, Rt_perc.Parada, 0 ParadaOri,\n"
                    + "    Case when (Select count(*) from CxForte where CxForte.CodCli in (Rt_Perc.CodCli1) and CxForte.CodFil = Rotas.CodFil) > 0\n"
                    + "    then 'S' else 'N' end CxForte, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude,\n"
                    + "    Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado,\n"
                    + "    CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg,\n"
                    + "    Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst,\n"
                    + "    Clientes.Bairro BairroDst, Clientes.Cidade CidDst, Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.Nome NRedFat,\n"
                    + "    CliFat.Ende EndFat, CliFat.Bairro BairroFat, CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat,\n"
                    + "    OS.OS, OSPedido.Cliente OrigemPedido, OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido, \n"
                    + "    -1 Guia, Replicate('X', 3) Serie, \n"
                    + "(Select top 1 Case when Rt_Perc.TipoSrv = 'R' then CtrItens.ValorRot \n"
                    + "            when Rt_Perc.TipoSrv = 'V' then CtrItens.ValorEve\n"
                    + "            else CtrItens.ValorESP end ValorFat from OS_VItens \n"
                    + "left join OS_Vig on OS_Vig.OS     = OS_VItens.OS\n"
                    + "                 and OS_Vig.CodFil = OS_VItens.CodFil\n"
                    + "left join CtrItens on  CtrItens.Contrato  = OS_Vig.Contrato\n"
                    + "                   and CtrItens.CodFil    = OS_Vig.CodFil\n"
                    + "                   and CtrItens.TipoPosto = OS_VItens.TipoPosto\n"
                    + "where OS_VItens.OS        = Rt_Perc.OS\n"
                    + "  and OS_VItens.Codfil    = Rotas.CodFil\n"
                    + "  and OS_VItens.DtInicio <= Convert(Date, GetDate())\n"
                    + "  and OS_VItens.DtFim    >= Convert(Date, GetDate())) Valor, \n"
                    + "Case when Pedido.TipoMoeda is null then paramet.MoedaPdrMobile else Pedido.TipoMoeda end Moeda,\n"
                    + " 999999 OSGuia, 999999 SequenciaOri, 999 Volumes, Replicate('X', 20) Lacre,\n"
                    + "    99999 ValorLacre, 999 QtdeLacre, 'ROTAS' OriInf, \n"
                    + "(Select count(*) from PreOrder where Preorder.DtColeta = Rotas.Data and PreOrder.CodCli1 = Rt_Perc.CodCli1 and PreOrder.CodFil = Rotas.CodFil) PreOrder\n"
                    + "from Rotas\n"
                    + "Left Join Escala as Escala on Escala.SeqRota = Rotas.Sequencia\n"
                    + "Left Join Funcion  on Funcion.Matr = Escala.MatrChe\n"
                    + "Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr\n"
                    + "Left Join Clientes as Clientes on Clientes.Codigo = Rt_Perc.CodCli2\n"
                    + "                              and Clientes.CodFil = Rotas.CodFil\n"
                    + "Left Join Clientes as CliOri on CliOri.Codigo = Rt_Perc.CodCli1\n"
                    + "                            and CliOri.CodFil = Rotas.CodFil\n"
                    + "Left Join Pedido on Pedido.numero = Rt_Perc.pedido\n"
                    + "                and Pedido.codfil = Rotas.codfil\n"
                    + "Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS\n"
                    + "                        and OSPedido.CodFil = Pedido.CodFil\n"
                    + "Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS\n"
                    + "                  and OS.CodFil = Rotas.CodFil\n"
                    + "\n"
                    + "Left Join OS_VFreq ON OS_VFreq.OS = OS.OS\n"
                    + "                  AND OS_VFreq.CodFil = OS.CodFil\n"
                    + "                  AND OS_VFreq.DiaSem = DATEPART(WEEKDAY, GETDATE())\n"
                    + "Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat\n"
                    + "                            and Clifat.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_perc.Sequencia = ? \n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "Group by Rotas.HrLargada, OS_VFreq.Hora1, OS_VFreq.Hora2, Rotas.HrChegada, CliOri.CodPtoCli, CliOri.CodExt, \n"
                    + "    Rotas.Rota, Rotas.Data, Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia,  Rt_perc.Observ, CliOri.Contato, CliOri.Fone1, \n"
                    + "    Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_perc.OS, \n"
                    + "    Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, Clientes.Latitude, Clientes.Longitude, CliOri.Ende,\n"
                    + "    CliOri.Bairro, CliOri.Cidade, CliOri.Estado, CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg,\n"
                    + "    Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende,\n"
                    + "    Clientes.Bairro, Clientes.Cidade, Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.Nome, CliFat.Ende, CliFat.Bairro,\n"
                    + "    CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente,\n"
                    + "    OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst, Pedido.TipoMoeda, paramet.MoedaPdrMobile,\n"
                    + "    Pedido.Hora1O, Pedido.Hora1D, Pedido.Hora2D, Pedido.Hora2O \n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, \n"
                    + "    Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, Rt_Guias.Valor, \n"
                    + "    Rt_GuiasMoeda.Moeda Moeda,\n"
                    + "    Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri, \n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, '' Lacre,\n"
                    + "     99999 ValorLacre, 999 QtdeLacre, 'RT_GUIAS' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "Where Rt_guias.Sequencia = ? -- (Select SeqRota from Escala where CodFil = 2001 and Data = '20180808' and MatrChe =  61)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "    and Rt_perc.ER = 'E'\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "UNION  -- Parada destino vinda de outras paradas - normalmente caixa-forte\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), Rt_perc.DPar Parada, Rt_perc.Parada ParadaOri, 'X', Rt_perc.CodCli1, \n"
                    + "    Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, \n"
                    + "    Rt_Guias.Valor, \n"
                    + "    Rt_GuiasMoeda.Moeda Moeda,\n"
                    + "Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, '' Lacre, \n"
                    + "    99999 ValorLacre, 999 QtdeLacre, 'RT_GUIAS' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.dpar \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "Where Rt_guias.Sequencia = ? \n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Parada in (Select Parada from Rt_Perc where Sequencia = Rt_Guias.Sequencia and Dpar > 0)\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.DPar Parada, Rt_perc.Parada ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie,\n"
                    + "    Rt_Guias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda, \n"
                    + " Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0  \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.dpar \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_guias.Sequencia = ? \n"
                    + "    and Rt_Guias.Parada in (Select Parada from Rt_Perc where Sequencia = Rt_Guias.Sequencia and Dpar > 0)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "UNION\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint, Rt_Guias.Guia) Guia, Rt_guias.Serie, Rt_Guias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "    Rt_guias.OS, Rt_Guias.Sequencia SequenciaOri, \n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = rt_guias.Guia and Serie = Rt_guias.Serie) Volumes, CxfGuiasVol.Lacre, \n"
                    + "    CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 \n"
                    + "from Rt_Guias\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join Rotas on Rotas.Sequencia = Rt_Guias.Sequencia\n"
                    + "left join Escala on Escala.Seqrota = Rt_Guias.Sequencia\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Rt_Guias.Sequencia\n"
                    + "                             and Rt_perc.Parada    = Rt_Guias.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_perc Rt_Origem on Rt_Origem.Sequencia = Rt_Guias.Sequencia\n"
                    + "                            and Rt_Origem.Dpar     = Rt_Guias.Parada   \n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = Rt_Guias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Rt_guias.Sequencia = ? -- (Select SeqRota from Escala where CodFil = 2001 and Data = '20180808' and MatrChe =  61)\n"
                    + "    and Rt_perc.Flag_Excl <> '*'\n"
                    + "    and Rt_perc.ER = 'E'\n"
                    + "    and Rt_Perc.TipoSrv <> 'A'\n"
                    + "\n"
                    + "union \n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CxFGuias.Guia) Guia, CxFguias.Serie, CxFGuias.Valor,\n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "     CxFguias.OS, CxfGuias.SeqRotaSai SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = CxFguias.Guia and Serie = CxFguias.Serie) Volumes, '' Lacre, \n"
                    + "99999 ValorLacre, 999 QtdeLacre, 'CXFGUIAS' OriInf, 0 \n"
                    + " from CxfGuias\n"
                    + "left join Rotas on Rotas.Sequencia = CxFGuias.SeqRotaSai\n"
                    + "left join Escala on Escala.Seqrota = CxFGuias.SeqRotaSai\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                             and Rt_perc.Hora1     = CxFGuias.Hora1D\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_Guias on  Rt_guias.Guia      = CxfGuias.Guia\n"
                    + "                   and Rt_guias.Serie     = CxfGUias.Serie\n"
                    + "                   and Rt_Guias.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where CxfGuias.SeqRotaSai = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Guia is null\n"
                    + "\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CxFGuias.Guia) Guia, CxFguias.Serie,\n"
                    + "     CxFGuias.Valor, \n"
                    + "    Case when isnull(Rt_GuiasMoeda.Moeda,'') <> '' then Rt_GuiasMoeda.Moeda\n"
                    + "	     when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "CxFguias.OS, CxfGuias.SeqRotaSai SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = CxFguias.Guia and Serie = CxFguias.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 \n"
                    + "from CxfGuias\n"
                    + "left join Rotas on Rotas.Sequencia = CxFGuias.SeqRotaSai\n"
                    + "left join Escala on Escala.Seqrota = CxFGuias.SeqRotaSai\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "                             and Rt_perc.Hora1     = CxFGuias.Hora1D\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rt_Guias on  Rt_guias.Guia      = CxfGuias.Guia\n"
                    + "                   and Rt_guias.Serie     = CxfGUias.Serie\n"
                    + "                   and Rt_Guias.Sequencia = CxfGuias.SeqRotaSai\n"
                    + "LEFT JOIN Rt_GuiasMoeda ON Rt_GuiasMoeda.Guia = Rt_Guias.Guia\n"
                    + "                        AND Rt_GuiasMoeda.Serie = Rt_Guias.Serie\n"
                    + "                        AND Rt_GuiasMoeda.Sequencia = Rt_Guias.Sequencia\n"
                    + "                        AND Rt_GuiasMoeda.Parada = Rt_Guias.Parada\n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = CxFGuias.Guia\n"
                    + "                      and CxfGuiasVol.Serie = CxFguias.Serie\n"
                    + "Left Join Pedido  on Pedido.Numero = Rt_Perc.Pedido\n"
                    + "                 and Pedido.CodFil = Rotas.CodFil\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where CxfGuias.SeqRotaSai = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and Rt_Guias.Guia is null\n"
                    + "\n"
                    + "Union \n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,TesSaidas.Guia) Guia, TesSaidas.Serie,\n"
                    + "     TesSaidas.TotalGeral Valor, \n"
                    + "    (SELECT MoedaPdrMobile FROM Paramet WHERE Filial_PDR = TesSaidas.CodFil) Moeda,\n"
                    + "Pedido.OS, Pedido.SeqRota SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = TesSaidas.Guia and Serie = TesSaidas.Serie) Volumes,\n"
                    + "     Replicate('', 20) Lacre, 99999 ValorLacre, 999 QtdeLacre, 'TESSAIDAS' OriInf, 0  \n"
                    + "from TesSaidas\n"
                    + "left join Pedido on Pedido.Numero   = TesSaidas.Pedido\n"
                    + "                 and Pedido.CodFil  = TesSaidas.CodFil\n"
                    + "                 and Pedido.CodCli2 = TesSaidas.CodCli2\n"
                    + "left join Rotas on Rotas.Sequencia = Pedido.SeqRota\n"
                    + "left join Escala on Escala.Seqrota = Pedido.SeqRota\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Pedido.SeqRota\n"
                    + "                             and Rt_perc.Parada    = Pedido.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join CxfGuias on  CxfGuias.Guia = TesSaidas.Guia\n"
                    + "                   and CxfGuias.Serie = TesSaidas.Serie\n"
                    + "Where Pedido.SeqRota = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and CxfGuias.Guia is null \n"
                    + "    and TesSaidas.TotalGeral > 0\n"
                    + "\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,TesSaidas.Guia) Guia, TesSaidas.Serie,\n"
                    + "    TesSaidas.TotalGeral Valor, \n"
                    + "    Case when isnull(Pedido.TipoMoeda,'') <> '' then Pedido.TipoMoeda\n"
                    + "		 else Paramet.MoedaPdrMobile end Moeda,\n"
                    + "Pedido.OS, Pedido.SeqRota SequenciaOri,\n"
                    + "    (Select count(*) from CxfGuiasVol where Guia = TesSaidas.Guia and Serie = TesSaidas.Serie) Volumes,\n"
                    + "     CxfGuiasVol.Lacre, CxfGuiasVol.Valor ValorLacre, CxfGuiasVol.Qtde QtdeLacre, 'CXFGUIASVOL' OriInf, 0 from TesSaidas\n"
                    + "left join Pedido on Pedido.Numero   = TesSaidas.Pedido\n"
                    + "                 and Pedido.CodFil  = TesSaidas.CodFil\n"
                    + "                 and Pedido.CodCli2 = TesSaidas.CodCli2\n"
                    + "left join Rotas on Rotas.Sequencia = Pedido.SeqRota\n"
                    + "left join Escala on Escala.Seqrota = Pedido.SeqRota\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.Sequencia = Pedido.SeqRota\n"
                    + "                             and Rt_perc.Parada    = Pedido.Parada\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join CxfGuias on  CxfGuias.Guia = TesSaidas.Guia\n"
                    + "                   and CxfGuias.Serie = TesSaidas.Serie\n"
                    + "left join CxfGuiasVol on CxFGuiasVol.Guia   = TesSaidas.Guia\n"
                    + "                      and CxfGuiasVol.Serie = TesSaidas.Serie\n"
                    + "Left join Paramet  on Paramet.Filial_Pdr = Rotas.CodFil\n"
                    + "Where Pedido.SeqRota = ?\n"
                    + "    and Rotas.Flag_Excl <> '*'\n"
                    + "    and Rt_Perc.Flag_Excl <> '*'\n"
                    + "    and CxfGuias.Guia is null \n"
                    + "    and TesSaidas.TotalGeral > 0\n"
                    + "union\n"
                    + "\n"
                    + "Select '' Horario1, '' Horario2, '' CodPtoCli, '' CodExt, Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Replicate('X',20), Rt_Perc.Sequencia,\n"
                    + "    Replicate('X',20), Replicate('X',30), Replicate('X',11), \n"
                    + "    Rt_perc.Parada, 0 ParadaOri, 'X', Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2, 999999, Replicate('X', 20), Replicate('X', 20),\n"
                    + "    Replicate('X', 20), Replicate('X', 20), Replicate('X', 60), Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),\n"
                    + "    999999, Replicate('X', 20), '99:99', '99:99', '99:99', '99:99', '99:99', '99:99', Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02),  Rt_Perc.TipoSrv, Rotas.CodFil, Replicate('X', 20), Replicate('X', 60),\n"
                    + "    Replicate('X', 25), Replicate('X', 25), Replicate('X', 02), Replicate('X', 14), Replicate('X', 24), 999999 OSGeral, Replicate('X', 7),\n"
                    + "    Replicate('X', 20), Replicate('X', 07), Replicate('X', 20), Convert(bigint,CtrOperEquip.Guia) Guia, CtrOperEquip.Serie,\n"
                    + "    '0' Valor, \n"
                    + "'' Moeda,\n"
                    + " '' OS, CtrOperEquip.SeqRota SequenciaOri, '' Volumes,\n"
                    + "    CtrOperEquip.IDEquip Lacre, '0' ValorLacre, '1' QtdeLacre, 'CONTAINER' OriInf, 0 from CtrOperEquip\n"
                    + "\n"
                    + "Left Join Rt_Perc as Rt_perc on  Rt_perc.CodCli1 = CtrOperEquip.CodCli1\n"
                    + "Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia\n"
                    + "                              and sla.parada = rt_perc.parada \n"
                    + "left join Rotas on Rotas.Sequencia = Rt_perc.Sequencia\n"
                    + "Where Rt_perc.Sequencia = ? and Rt_Perc.ER = 'R'\n"
                    + ") a\n";
            if (isTranspCacamba(persistencia.getEmpresa())) {
                sql += " WHERE OS > 0 OR CodCli1 = '9996900' OR CodCli1 = '9996015'\n";
            }
            sql += "Order by Hora1, Parada, Guia, Lacre, ValorLacre";

            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.setString(sequencia);
            consult.select();

            Rotas rota;
            Rt_Perc rt_perc;
            Rt_PercSla rt_percsla;
            Escala escala;
            Clientes cliOri;
            Clientes cliDst;
            Clientes clifat;
            OS_Vig os;
            CarregaRotaVol carregarota;
            GuiasList guia;
            CxFGuiasVol volume;

            while (consult.Proximo()) {
                carregarota = new CarregaRotaVol();
                carregarota.setOriInf(consult.getString("OriInf"));

                if (carregarota.getOriInf().equals("ROTAS")) {
                    rota = new Rotas();
                    rt_perc = new Rt_Perc();
                    rt_percsla = new Rt_PercSla();
                    escala = new Escala();
                    cliOri = new Clientes();
                    cliDst = new Clientes();
                    clifat = new Clientes();
                    os = new OS_Vig();

                    rota.setRota(consult.getString("Rota"));
                    rota.setCodFil(consult.getString("CodFil"));
                    rota.setSequencia(consult.getString("Sequencia"));
                    rota.setValor(consult.getString("Valor"));
                    rt_perc.setHora1(consult.getString("Hora1"));
                    rt_perc.setNRed(consult.getString("Nred"));
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    rt_perc.setCodCli1(consult.getString("CodCli1"));
                    rt_perc.setCodCli2(consult.getString("CodCli2"));
                    rt_perc.setER(consult.getString("ER"));
                    rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                    rt_perc.setHora1D(consult.getString("Hora1D"));
                    rt_perc.setHrCheg(consult.getString("HrCheg"));
                    rt_perc.setHrSaida(consult.getString("HrSaida"));
                    rt_perc.setHrBaixa(consult.getString("HrBaixa"));
                    rt_perc.setObserv(consult.getString("Observ"));
                    rt_perc.setMoeda(consult.getString("Moeda"));
                    rt_percsla.setHrChegVei(consult.getString("HrChegVei"));
                    rt_percsla.setHrSaidaVei(consult.getString("HrSaidaVei"));
                    escala.setVeiculo(consult.getString("Veiculo"));
                    cliOri.setNRed(consult.getString("Origem"));
                    cliOri.setCodExt(consult.getString("CodExt"));
                    cliOri.setCodPtoCli(consult.getString("CodPtoCli"));
                    cliOri.setCodCli(consult.getString("CodCli1"));
                    cliOri.setCodigo(consult.getString("CodCli1"));
                    cliOri.setEnde(consult.getString("Ende"));
                    cliOri.setBairro(consult.getString("Bairro"));
                    cliOri.setCidade(consult.getString("Cidade"));
                    cliOri.setEstado(consult.getString("Estado"));
                    cliOri.setNroChave(consult.getString("NroChave"));
                    cliOri.setLatitude(consult.getString("Latitude"));
                    cliOri.setLongitude(consult.getString("Longitude"));
                    cliOri.setContato(consult.getString("contato"));
                    cliOri.setFone1(consult.getString("fone1"));
                    cliDst.setNRed(consult.getString("Destino"));
                    cliDst.setEnde(consult.getString("EndDst"));
                    cliDst.setBairro(consult.getString("BairroDst"));
                    cliDst.setCidade(consult.getString("CidDst"));
                    cliDst.setEstado(consult.getString("EstadoDst"));
                    rt_perc.setHrCheg(consult.getString("HrCheg"));
                    cliDst.setLatitude(consult.getString("Latitudedst"));
                    cliDst.setLongitude(consult.getString("Longitudedst"));
                    clifat.setNRed(consult.getString("NRedFat"));
                    clifat.setEnde(consult.getString("EndFat"));
                    clifat.setBairro(consult.getString("BairroFat"));
                    clifat.setCidade(consult.getString("CidadeFat"));
                    clifat.setEstado(consult.getString("EstadoFat"));
                    clifat.setCGC(consult.getString("CNPJFat"));
                    clifat.setIE(consult.getString("IEFat"));
                    os.setOS(consult.getString("OS"));
                    os.setCliente(consult.getString("OrigemPedido"));
                    os.setNRed(consult.getString("NRedOrigemPedido"));
                    os.setCliDst(consult.getString("DstPedido"));
                    os.setNRedDst(consult.getString("NRedDstPedido"));
                    os.setHorario1(consult.getString("Horario1"));
                    os.setHorario2(consult.getString("Horario2"));

                    carregarota.setCxforte(consult.getString("CxForte"));
                    carregarota.setPreOrder(consult.getString("PreOrder"));

                    carregarota.setRota(rota);
                    carregarota.setRt_perc(rt_perc);
                    carregarota.setRt_percsla(rt_percsla);
                    carregarota.setEscala(escala);
                    carregarota.setCliOri(cliOri);
                    carregarota.setCliDst(cliDst);
                    carregarota.setCliFat(clifat);
                    carregarota.setOS(os);
                    carregarota.setGuias(new ArrayList<>());
                    carregarota.setContainers(new ArrayList<>());
                    list_rota.add(carregarota);

                } else if (carregarota.getOriInf().equals("RT_GUIAS")
                        || carregarota.getOriInf().equals("CXFGUIAS")
                        || carregarota.getOriInf().equals("TESSAIDAS")) {
                    guia = new GuiasList();
                    guia.setGuia(consult.getString("Guia"));
                    guia.setSerie(consult.getString("Serie"));
                    guia.setValor(consult.getString("Valor"));
                    guia.setOS(consult.getString("OSGuia"));
                    guia.setSequenciaOri(consult.getBigDecimal("SequenciaOri"));
                    guia.setParadaOri(consult.getInt("ParadaOri"));
                    guia.setMoeda(consult.getString("moeda"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        list_rota.get(indice1).getGuias().add(guia);
                    }
                } else if (carregarota.getOriInf().equals("CXFGUIASVOL")) {
                    guia = new GuiasList();
                    guia.setGuia(consult.getString("Guia"));
                    guia.setSerie(consult.getString("Serie"));

                    volume = new CxFGuiasVol();
                    volume.setGuia(guia.getGuia());
                    volume.setSerie(guia.getSerie());
                    volume.setLacre(consult.getString("Lacre"));
                    volume.setValor(consult.getString("ValorLacre"));
                    volume.setQtde(consult.getString("QtdeLacre"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        int indice2 = list_rota.get(indice1).getGuias().indexOf(guia);

                        if (indice2 != -1) {
                            list_rota.get(indice1).getGuias().get(indice2).getVolumes().add(volume);
                        }
                    }

                } else if (carregarota.getOriInf().equals("CONTAINER")) {
                    volume = new CxFGuiasVol();
                    volume.setGuia(consult.getString("Guia"));
                    volume.setSerie(consult.getString("Serie"));
                    volume.setLacre(consult.getString("Lacre"));
                    volume.setValor(consult.getString("ValorLacre"));
                    volume.setQtde(consult.getString("QtdeLacre"));

                    rt_perc = new Rt_Perc();
                    rt_perc.setSequencia(consult.getString("Sequencia"));
                    rt_perc.setParada(consult.getInt("Parada"));
                    carregarota.setRt_perc(rt_perc);

                    int indice1 = list_rota.indexOf(carregarota);
                    if (indice1 != -1) {
                        list_rota.get(indice1).getContainers().add(volume);
                    }

                }
            }
            consult.Close();
            return list_rota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.getRotasVolData - " + e.getMessage() + "\r\n"
                    + "Select  Rotas.Rota, Substring(Rt_Perc.Hora1,1,2)+':'+Substring(Rt_Perc.Hora1,3,2) Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + " Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + " Clientes.Latitude Latitudedst, Clientes.Longitude Longitudedst, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + " CliOri.NroChave, CliOri.NRed Origem, Substring(Rt_Perc.Hora1D,1,2)+':'+Substring(Rt_Perc.Hora1D,3,2) Hora1D, Rt_Perc.HrCheg, "
                    + " Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed Destino, Clientes.Ende EndDst, "
                    + " Clientes.Bairro BairroDst, Clientes.Cidade CidDst, "
                    + " Clientes.Estado EstadoDst,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed NRedFat, CliFat.Ende EndFat, CliFat.Bairro BairroFat, "
                    + " CliFat.Cidade CidadeFat, CliFat.Estado EstadoFat, Clifat.CGC CNPJFat, Clifat.IE IEFat, OS.OS, OSPedido.Cliente OrigemPedido, "
                    + " OSPedido.NRed NRedOrigemPedido, OSPedido.CliDst DstPedido, OSPedido.NRedDst NRedDstPedido "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    //                + "                               Escala.Data = "+dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and "
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Rt_Perc as Rt_perc on Rt_perc.Sequencia = Rotas.Sequencia and "
                    + "                                 Rt_perc.Flag_Excl <> '*' "
                    + " Left Join Rt_PercSLA as sla on sla.sequencia = rt_perc.sequencia"
                    + "                             and sla.parada = rt_perc.parada  "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Left Join Clientes as Clientes on (Clientes.Codigo = Rt_Perc.CodCli2) and "
                    + "                                    Clientes.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliOri on (CliOri.Codigo = Rt_Perc.CodCli1) and "
                    + "                                  CliOri.CodFil = Rotas.CodFil "
                    + " Left Join Pedido on Pedido.numero = Rt_Perc.pedido "
                    + "                 and Pedido.codfil = Rotas.codfil"
                    + " Left Join OS_Vig as OSPedido on OSPedido.OS = Pedido.OS "
                    + "                         and OSPedido.CodFil = Pedido.CodFil "
                    + " Left Join OS_Vig as OS on OS.OS = Rt_Perc.OS "
                    + "                       and OS.CodFil = Rotas.CodFil "
                    + " Left Join Clientes as CliFat on CliFat.Codigo = OS.CliFat "
                    + "                             and Clifat.CodFil = Rotas.CodFil "
                    //                + " Where Funcion.Matr = "+matricula
                    + "     and (sla.hrsaidavei = '' or sla.hrsaidavei is null) "
                    + " Group by Rotas.Rota,  Rt_perc.Hora1, Rt_perc.Nred, Rt_perc.Sequencia, "
                    + "          Rt_perc.Parada, Rt_perc.CodCli1, Rt_Perc.ER, Rt_Perc.CodCli2,Escala.Veiculo, Cliori.Latitude, Cliori.Longitude, "
                    + "          Clientes.Latitude, Clientes.Longitude, CliOri.Ende, CliOri.Bairro, CliOri.Cidade, CliOri.Estado, "
                    + "          CliOri.NroChave, CliOri.NRed, Rt_perc.Hora1D, Rt_Perc.HrCheg, "
                    + "          Rt_Perc.HrSaida, Rt_Perc.HrBaixa, SLA.HrChegVei, SLA.HrSaidaVei, Clientes.NRed, Clientes.Ende, "
                    + "          Clientes.Bairro, Clientes.Cidade, "
                    + "          Clientes.Estado,  Rt_Perc.TipoSrv, Rotas.CodFil, CliFat.NRed, CliFat.Ende, CliFat.Bairro, "
                    + "          CliFat.Cidade, CliFat.Estado, Clifat.CGC, Clifat.IE, OS.OS, OSPedido.Cliente, "
                    + "          OSPedido.NRed, OSPedido.CliDst, OSPedido.NRedDst "
                    + " Order by Rt_Perc.Hora1");
        }
    }

    /**
     * Carrega os dados da Parada da Rota Selecionada
     *
     * @param Sequencia - Seqüência de Rota
     * @param Parada - Número da PArada
     * @param persistencia - Conexão ao banco
     * @return - Lista com dados da parada
     * @throws java.lang.Exception - pode gerar exception
     */
    public static List<CarregaParadaRota> CarregaDadosParada(String Sequencia, int Parada, Persistencia persistencia) throws Exception {
        try {
            List<CarregaParadaRota> list_paradaRota = new ArrayList();
            String sql = "select rt_perc.Sequencia, rt_perc.ER,rt_perc.Parada, rotas.CodFil ,"
                    + " Rt_Perc.CodCli1, Rt_Perc.Hora1,origem.Nred Origem,rt_perc.TipoSrv,"
                    + " origem.Ende oriEndereco,origem.Bairro oriBairro,origem.Cidade oriCidade,origem.nroChave oriChave,"
                    + " destino.Nred Destino, destino.Ende destEndereco, destino.Bairro destBairro, "
                    + " destino.Cidade destCidade, destino.nroChave destChave "
                    + " from rt_perc "
                    + " left join rotas on rotas.sequencia = rt_perc.sequencia"
                    + " left join clientes as origem on origem.codigo = rt_perc.codcli1"
                    + "                             and origem.codfil = rotas.codfil"
                    + " left join clientes as destino on destino.codigo = rt_perc.codcli2"
                    + "                              and destino.codfil = rotas.codfil"
                    + " where rt_perc.sequencia = ?"
                    + " and rt_perc.parada = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Sequencia);
            consult.setInt(Parada);
            consult.select();
            while (consult.Proximo()) {
                Clientes cliorigem = new Clientes();
                Clientes clidestino = new Clientes();
                CarregaParadaRota paradaRota = new CarregaParadaRota();
                Rt_Perc rt_perc = new Rt_Perc();
                rt_perc.setSequencia(consult.getString("Sequencia"));
                rt_perc.setParada(consult.getInt("parada"));
                rt_perc.setCodFil(consult.getString("CodFil"));
                rt_perc.setER(consult.getString("ER"));
                rt_perc.setHora1(consult.getString("Hora1"));
                rt_perc.setTipoSrv(consult.getString("TipoSrv"));
                rt_perc.setTipoSrv(consult.getString("CodCli1"));
                cliorigem.setNRed(consult.getString("Origem"));
                cliorigem.setEnde(consult.getString("oriEndereco"));
                cliorigem.setBairro(consult.getString("oriBairro"));
                cliorigem.setCidade(consult.getString("oriCidade"));
                cliorigem.setNroChave(consult.getString("oriChave"));
                clidestino.setNRed(consult.getString("Destino"));
                clidestino.setEnde(consult.getString("destEndereco"));
                clidestino.setBairro(consult.getString("destBairro"));
                clidestino.setCidade(consult.getString("destCidade"));
                clidestino.setNroChave(consult.getString("destChave"));
                paradaRota.setRt_perc(rt_perc);
                paradaRota.setCliorigem(cliorigem);
                if (!consult.getString("ER").equals("E")) {
                    paradaRota.setClidestino(clidestino);
                }
                list_paradaRota.add(paradaRota);
            }
            consult.Close();
            return list_paradaRota;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.CarregaDadosParada - " + e.getMessage() + "\r\n"
                    + "select rt_perc.Sequencia, rt_perc.ER,rt_perc.Parada, rotas.CodFil ,"
                    + " Rt_Perc.CodCli1, Rt_Perc.Hora1,origem.Nred Origem,rt_perc.TipoSrv,"
                    + " origem.Ende oriEndereco,origem.Bairro oriBairro,origem.Cidade oriCidade,origem.nroChave oriChave,"
                    + " destino.Nred Destino, destino.Ende destEndereco, destino.Bairro destBairro, "
                    + " destino.Cidade destCidade, destino.nroChave destChave "
                    + " from rt_perc "
                    + " left join rotas on rotas.sequencia = rt_perc.sequencia"
                    + " left join clientes as origem on origem.codigo = rt_perc.codcli1"
                    + "                             and origem.codfil = rotas.codfil"
                    + " left join clientes as destino on destino.codigo = rt_perc.codcli2"
                    + "                              and destino.codfil = rotas.codfil"
                    + " where rt_perc.sequencia = " + Sequencia
                    + " and rt_perc.parada = " + Parada);
        }
    }

    /**
     * Busca Sequencia de rota onde o funcionário está escalado como chefe de
     * equipe
     *
     * @param Codigo - Matricula do fucnionário
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public BigDecimal BuscaSeqRota(String Codigo, Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("1");
            String sql = "Select "
                    + " Rotas.Sequencia "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and"
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Where Pessoa.Codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(Codigo);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getBigDecimal("Sequencia");
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.BuscaSeqRota - " + e.getMessage() + "\r\n"
                    + "Select "
                    + " Rotas.Sequencia "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data ='" + br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual("SQL") + "' "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and"
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Where Pessoa.Codigo = " + Codigo);
        }
    }

    public BigDecimal BuscaSeqRota(String Codigo, String dataAtual, Persistencia persistencia) throws Exception {
        try {
            BigDecimal retorno = new BigDecimal("1");
            String sql = "Select "
                    + " Rotas.Sequencia "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = ? "
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and"
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Where Pessoa.Codigo = ?";
            Consulta consult = new Consulta(sql, persistencia);
            consult.setString(dataAtual);
            consult.setString(Codigo);
            consult.select();
            while (consult.Proximo()) {
                try {
                    retorno = consult.getBigDecimal("Sequencia");
                } catch (Exception e) {
                    retorno = new BigDecimal("0");
                }
            }
            consult.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CarregaRotaDao.BuscaSeqRota - " + e.getMessage() + "\r\n"
                    + "Select "
                    + " Rotas.Sequencia "
                    + " from Funcion as Funcion "
                    + " Left Join Escala as Escala on Escala.MatrChe = Funcion.Matr and "
                    + "                               Escala.CodFil = Funcion.CodFil and "
                    + "                               Escala.Data = " + dataAtual
                    + " Left Join Rotas as Rotas on Rotas.Sequencia = Escala.SeqRota and "
                    + "                             Rotas.CodFil = Escala.CodFil and"
                    + "                             Rotas.Flag_Excl <> '*' "
                    + " Left Join Pessoa as Pessoa on Pessoa.Matr = Funcion.Matr "
                    + " Where Pessoa.Codigo = " + Codigo);
        }
    }
}
