package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.CxFGuias;
import SasBeans.Rt_Guias;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CxFGuiasDao {

    private Persistencia persistencia;

    public CxFGuiasDao() {
    }

    public CxFGuiasDao(Persistencia persistencia) {
        this.persistencia = persistencia;
    }

    public List<CxFGuias> listagemPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        List<CxFGuias> retorno = new ArrayList<>();
        String codFil = (String) filtros.get("CodFil");
        String codCli = (String) filtros.get("CodCli");
        String dataInicio = (String) filtros.get("DataInicio");
        String dataFim = (String) filtros.get("DataFim");
        boolean remessa = (boolean) filtros.get("remessa");
        boolean geral = (boolean) filtros.get("geral");

        String sql = "SELECT * FROM (\n"
                + "         SELECT ROW_NUMBER() OVER ( ORDER BY CxFGuias.DtEnt DESC , CxFGuias.HrEnt DESC ) AS RowNum,\n"
                + "                Clientes.Codigo,\n"
                + "                Clientes.Nred           Nome,\n"
                + "                Clientes.Ende           Endereco,\n"
                + "                Clientes.NroChave,\n"
                + "                OS_Vig.CliFat,\n"
                + "                OS_Vig.NredFat,\n"
                + "                CxfGuias.Remessa,\n"
                + "                CXfGuias.DtEnt          Data,\n"
                + "                CxFGuias.Guia,\n"
                + "                CxFGuias.Serie,\n"
                + "                CxFGuias.CodFil,\n"
                + "                CxFGuias.CodCLi,\n"
                + "                CxFGuias.CliOri,\n"
                + "                CxFGuias.Tipo,\n"
                + "                CxFGuias.Valor,\n"
                + "                CxfGuias.RotaEnt,\n"
                + "                CxFGuias.OperEnt,\n"
                + "                CxFGuias.DtEnt,\n"
                + "                CxFGuias.HrEnt,\n"
                + "                CxfGuias.RotaSai,\n"
                + "                CxFGuias.OperSai,\n"
                + "                CxFGuias.DtSai,\n"
                + "                CxFGuias.HrSai,\n"
                + "                Clientes.NRed,\n"
                + "                Cli2.NRed               NRedDst,\n"
                + "                CxFSaidas.Hr_Saida,\n"
                + "                OS_Vig.NRedFat          NRedOS,\n"
                + "                TesSaidas.CodCli2,\n"
                + "                CxFGuias.Volumes,\n"
                + "                CxFGuias.VolDh,\n"
                + "                CxFGuias.ValorDh,\n"
                + "                CxFGuias.VolCh,\n"
                + "                CxFGuias.ValorCh,\n"
                + "                CxFGuias.VolMd,\n"
                + "                CxFGuias.ValorMd,\n"
                + "                CxFGuias.VolMet,\n"
                + "                CxFGuias.ValorMet,\n"
                + "                CxFGuias.VolMEstr,\n"
                + "                CxFGuias.ValorMEstr,\n"
                + "                CxFGuias.VolOutr,\n"
                + "                CxFGuias.ValorOutr,\n"
                + "                CxFGuias.OS,\n"
                + "                CxFGuias.Lote,\n"
                + "                OS_Vig.Nred             NRedCli\n"
                + "         from CxFGuias\n"
                + "                  left Join Clientes on Clientes.Codigo = CxFGuias.CliOri\n"
                + "             and Clientes.CodFil = CxFGuias.CodFil\n"
                + "                  left Join Clientes Cli2 on Cli2.Codigo = CxFGuias.CliDst\n"
                + "             and Cli2.CodFil = CxFGuias.CodFil\n"
                + "                  left Join TesSaidas on TesSaidas.Guia = CXFGuias.Guia\n"
                + "             and TesSaidas.Serie = CXFGuias.Serie\n"
                + "                  left join CxFSaidas on CxFSaidas.CodFil = CxFGuias.CodFil\n"
                + "             and CxFSaidas.SeqRota = CxFGuias.SeqRotaSai\n"
                + "             and CxFSaidas.Remessa = CxFGuias.Remessa\n"
                + "                  left Join OS_Vig on OS_Vig.OS = CxfGuias.OS\n"
                + "             and OS_Vig.CodFil = CxfGuias.CodFil\n"
                + "         WHERE CxfGuias.CodCli = ? \n"
                + "           AND CxFGuias.CodFil = ? \n";

        sql += remessa ? "            AND CxfSaidas.Dt_Prep IS NOT NULL\n" : "";
        sql += (geral && !remessa) ? "" : "             AND ((RotaSai is null and DtSai is null) OR (CxfSaidas.Hr_Saida IS NULL AND CxfSaidas.SeqRota IS NOT NULL))\n";
        sql += "           AND ((CxfGuias.DtEnt >= ? \n"
                + "             AND CxfGuias.DtEnt <= ? )\n"
                + "             OR (CxfGuias.DtSai >= ? \n"
                + "                 AND CxfGuias.DtSai <= ? ))\n";

        sql += ") AS RowConstrainedResult"
                + " WHERE RowNum >= ?"
                + " AND RowNum < ?"
                + " ORDER BY RowNum";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.setString(codFil);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setInt(primeiro + 1);
            consulta.setInt(primeiro + 1 + linhas);
            consulta.select();

            while (consulta.Proximo()) {
                CxFGuias cxFGuias = new CxFGuias();

                cxFGuias.setCodFil(consulta.getString("CodFil"));
                cxFGuias.setCodCli(consulta.getString("CodCli"));
                cxFGuias.setGuia(consulta.getString("Guia"));
                cxFGuias.setSerie(consulta.getString("Serie"));
                cxFGuias.setCliOri(consulta.getString("CliOri"));
                cxFGuias.setValor(consulta.getString("Valor"));
                cxFGuias.setTipo(consulta.getString("Tipo"));
                cxFGuias.setRotaEnt(consulta.getString("RotaEnt"));
                cxFGuias.setOperEnt(consulta.getString("OperEnt"));
                cxFGuias.setDtEnt(consulta.getString("DtEnt"));
                cxFGuias.setHrEnt(consulta.getString("HrEnt"));
                cxFGuias.setRotaSai(consulta.getString("RotaSai"));
                cxFGuias.setRemessa(consulta.getString("Remessa"));
                cxFGuias.setOperSai(consulta.getString("OperSai"));
                cxFGuias.setDtSai(consulta.getString("DtSai"));
                cxFGuias.setHrSai(consulta.getString("HrSai"));
                cxFGuias.setOS(consulta.getString("OS").replace(".0", ""));
                cxFGuias.setLote(consulta.getString("Lote"));
                cxFGuias.setVolumes(consulta.getInt("Volumes"));
                cxFGuias.setVolDh(consulta.getInt("VolDh"));
                cxFGuias.setValorDh(consulta.getString("ValorDh"));
                cxFGuias.setVolCh(consulta.getInt("VolCh"));
                cxFGuias.setValorCh(consulta.getString("ValorCh"));
                cxFGuias.setVolMd(consulta.getInt("VolMd"));
                cxFGuias.setValorMd(consulta.getString("ValorMd"));
                cxFGuias.setVolMet(consulta.getInt("VolMet"));
                cxFGuias.setValorMet(consulta.getString("ValorMet"));
                cxFGuias.setVolMEstr(consulta.getInt("VolMEstr"));
                cxFGuias.setValorMEstr(consulta.getString("ValorMEstr"));
                cxFGuias.setVolOutr(consulta.getInt("VolOutr"));
                cxFGuias.setValorOutr(consulta.getString("ValorOutr"));
                cxFGuias.setNRed(consulta.getString("nred"));
                cxFGuias.setNRedDst(consulta.getString("nreddst"));
                cxFGuias.setNRedOS(consulta.getString("nredos"));
                cxFGuias.setNRedCli(consulta.getString("NRedCli"));
                cxFGuias.setHr_Saida(consulta.getString("Hr_Saida"));
                retorno.add(cxFGuias);
            }
            consulta.Close();
        } catch (Exception e) {
            throw new Exception("Failed to list cxfguias - \r\n" + e.getMessage());
        }
        return retorno;
    }

    public Integer contagemCxFGuias(Map filtros, Persistencia persistencia) throws Exception {
        String codFil = (String) filtros.get("CodFil");
        String codCli = (String) filtros.get("CodCli");
        String dataInicio = (String) filtros.get("DataInicio");
        String dataFim = (String) filtros.get("DataFim");
        boolean remessa = (boolean) filtros.get("remessa");
        boolean geral = (boolean) filtros.get("geral");

        String sql = "SELECT count(*) total \n"
                + "  from CxFGuias\n"
                + "  left Join Clientes on Clientes.Codigo = CxFGuias.CliOri\n"
                + "    and Clientes.CodFil = CxFGuias.CodFil\n"
                + "  left Join Clientes Cli2 on Cli2.Codigo = CxFGuias.CliDst\n"
                + "    and Cli2.CodFil = CxFGuias.CodFil\n"
                + "  left Join TesSaidas on TesSaidas.Guia = CXFGuias.Guia\n"
                + "    and TesSaidas.Serie = CXFGuias.Serie\n"
                + "  left join CxFSaidas on CxFSaidas.CodFil = CxFGuias.CodFil\n"
                + "    and CxFSaidas.SeqRota = CxFGuias.SeqRotaSai\n"
                + "    and CxFSaidas.Remessa = CxFGuias.Remessa\n"
                + "  left Join OS_Vig on OS_Vig.OS = CxfGuias.OS\n"
                + "    and OS_Vig.CodFil = CxfGuias.CodFil\n"
                + "  WHERE CxfGuias.CodCli = ? \n"
                + "    AND CxFGuias.CodFil = ? \n";

        sql += remessa ? "  AND CxfSaidas.Dt_Prep IS NOT NULL\n" : "";
        sql += (geral && !remessa) ? "" : "  AND ((RotaSai is null and DtSai is null) OR (CxfSaidas.Hr_Saida IS NULL AND CxfSaidas.SeqRota IS NOT NULL))\n";
        sql += "  AND ((CxfGuias.DtEnt >= ? \n"
                + "    AND CxfGuias.DtEnt <= ? )\n"
                + "    OR (CxfGuias.DtSai >= ? \n"
                + "    AND CxfGuias.DtSai <= ? ))\n";

        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(codCli);
            consulta.setString(codFil);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);
            consulta.setString(dataInicio);
            consulta.setString(dataFim);

            consulta.select();
            int retorno = 0;
            while (consulta.Proximo()) {
                retorno = consulta.getInt("total");
            }
            consulta.Close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("Falha ao contar cxfguias - \r\n" + e.getMessage());
        }
    }

    public void atualizaCxfGuias(Persistencia persistencia, String sOs, String sCodFil, String sGuia, String sSerie, String sSeqRotaSai) throws Exception {
        String sql = "update CxFGuias set OS=? WHERE Codfil=? and Guia=? and Serie=? and SeqRotaSai=?";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(sOs);
            consulta.setString(sCodFil);
            consulta.setString(sGuia);
            consulta.setString(sSerie);
            consulta.setString(sSeqRotaSai);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.atualizaCxfGuias - " + e.getMessage() + "\r\n"
                    + "update CxFGuias set OS=" + sOs + " WHERE Codfil=" + sCodFil + " and Guia=" + sGuia + " and Serie=" + sSerie + " and SeqRotaSai=" + sSeqRotaSai);
        }
    }

    public List<CxFGuias> getCxfGuiasEntrega(String sequencia, String hora1, Persistencia persistencia) throws Exception {
        List<CxFGuias> listCxFGuias = new ArrayList();
        try {
            Consulta rsgtv = new Consulta("select Guia,Serie,Valor,OS,CliOri,hora1 "
                    + " from CxFGuias "
                    + " WHERE seqrotasai = ?"
                    + " and hora1d = ?", persistencia);
            rsgtv.setString(sequencia);
            rsgtv.setString(hora1.replace(":", ""));
            rsgtv.select();
            while (rsgtv.Proximo()) {
                if ((rsgtv.getFloat("Guia") > 0) && (!rsgtv.getString("hora1").equals(""))) {
                    CxFGuias temp = new CxFGuias();
                    temp.setGuia(rsgtv.getString("Guia").substring(0, rsgtv.getString("Guia").indexOf(".")));
                    temp.setSerie(rsgtv.getString("Serie"));
                    temp.setValor(rsgtv.getString("Valor"));
                    temp.setOS(rsgtv.getString("Os"));
                    temp.setCliOri(rsgtv.getString("CliOri"));
                    listCxFGuias.add(temp);
                }
            }
            rsgtv.Close();
            return listCxFGuias;
        } catch (Exception e) {
            return listCxFGuias;
            //throw new Exception("CxFGuiasDao.getCxfGuiasEntrega - " + e.getMessage());
        }
    }

    public List<CxFGuias> getCxfGuiasEntrega2(String seqRota, String hora1) throws Exception {
        String sql = "SELECT Guia, Serie, Valor, OperEnt, DtEnt, HrEnt, Tipo\n"
                + "FROM CXFGuias\n"
                + "WHERE SeqRota = ? \n"
                + "    AND hora1 = ? ;";

        List<CxFGuias> listCxFGuias = new ArrayList();
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(seqRota);
            consulta.setString(hora1);
            consulta.select();

            while (consulta.Proximo()) {
                CxFGuias cxFGuias = new CxFGuias();
                cxFGuias.setGuia(consulta.getBigDecimal("Guia").toString());
                cxFGuias.setSerie(consulta.getString("Serie"));
                cxFGuias.setValor(consulta.getString("Valor"));
                cxFGuias.setOperEnt(consulta.getString("OperEnt"));
                cxFGuias.setDtEnt(consulta.getString("DtEnt"));
                cxFGuias.setHrEnt(consulta.getString("HrEnt"));
                cxFGuias.setTipo(consulta.getString("Tipo"));

                listCxFGuias.add(cxFGuias);
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return listCxFGuias;
    }

    public List<CxFGuias> getGuiasSaida(String sequencia, Persistencia persistencia) throws Exception {
        List<CxFGuias> listCxFGuias = new ArrayList();
        try {
            Consulta consult = new Consulta(
                    "Select Guia, Serie, CxfGuias.Codfil, CliDST, CxfGuias.Valor, CxfGuias.Hora1D from CxfGuias"
                    + " left join rt_perc on rt_perc.sequencia = CxFGuias.seqrotasai"
                    + "                  and rt_perc.codfil = CxFGuias.codfil"
                    + " WHERE seqrotasai = ?"
                    + " and (rt_perc.hr_fech is null or rt_perc.hr_fech='')", persistencia);
            consult.setString(sequencia);
            consult.select();
            while (consult.Proximo()) {
                CxFGuias cxfg = new CxFGuias();
                cxfg.setSeqRotaSai(sequencia);
                cxfg.setGuia(consult.getString("Guia"));
                cxfg.setSerie(consult.getString("Serie"));
                cxfg.setCodFil(consult.getString("Codfil"));
                cxfg.setCliDst(consult.getString("CliDST"));
                cxfg.setHora1D(consult.getString("Hora1D"));
                cxfg.setValor(consult.getString("Valor"));
                listCxFGuias.add(cxfg);
            }
            consult.Close();
            return listCxFGuias;
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.getGuiasSaida - " + e.getMessage() + "\r\n"
                    + "Select Guia, Serie, CxfGuias.Codfil, CliDST, CxfGuias.Valor, CxfGuias.Hora1D from CxfGuias"
                    + " left join rt_perc on rt_perc.sequencia = CxFGuias.seqrotasai"
                    + "                  and rt_perc.codfil = CxFGuias.codfil"
                    + " WHERE seqrotasai = " + sequencia
                    + " and (rt_perc.hr_fech is null or rt_perc.hr_fech='')");
        }
    }

    /**
     * Troca a serie da Guia em Caixa Forte
     *
     * @param persistencia - conexão ao banco de dados
     * @param CxFGuias - guia de caixa forte Dados obrigatórios - guia, série,
     * codfil
     * @param novaserie - série de destino da guia
     * @throws Exception
     */
    public void TrocaSerieGuia(Persistencia persistencia, CxFGuias CxFGuias, String novaserie) throws Exception {
        try {
            String sql;
            sql = "update CxFGuias set Serie = ?"
                    + " WHERE Guia = ?"
                    + " and Serie = ?"
                    + " and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(novaserie);
            consulta.setString(CxFGuias.getGuia());
            consulta.setString(CxFGuias.getSerie());
            consulta.setString(CxFGuias.getCodFil());
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.TrocaSerieGuia - " + e.getMessage() + "\r\n"
                    + "update CxFGuias set Serie = " + novaserie
                    + " WHERE Guia = " + CxFGuias.getGuia()
                    + " and Serie = " + CxFGuias.getSerie()
                    + " and CodFil = " + CxFGuias.getCodFil());
        }
    }

    /**
     * Lista as guias de entrega no formato Rt_Guias
     *
     * @param sequencia
     * @param parada
     * @param persistencia
     * @return
     */
    public List<Rt_Guias> listaGuiasEntrega(String sequencia, String parada, Persistencia persistencia) {
        List<Rt_Guias> guias = new ArrayList<>();
        try {
            String sql = " SELECT CxFGuias.Valor, CxFGuias.Guia, CxFGuias.Serie, CxFGuias.RotaSai RotaDst, "
                    + "        Rt_Perc.codcli1 CodCli2, "
                    + "        CliOri.NRed NRedOri, CliOri.Ende EndOri, CliOri.Cidade CidadeOri, "
                    + "        CliOri.Bairro BairroOri, CliOri.Estado EstadoOri, CliOri.email EmailOri, CliOri.Codigo CliOriOS, "
                    + "        CliDst.NRed NRedDst,CliDst.Ende EndDst, CliDst.Cidade CidadeDst, CliDst.Bairro BairroDst, CliDst.Estado EstadoDst, "
                    + "        CliDst.email EmailDst, CliDst.Cep cepDst, CliDst.Codigo CodCliDst, "
                    + "        CliFat.NRed NRedFat,CliFat.Ende EndFat, CliFat.Cidade CidadeFat, CliFat.Bairro BairroFat, CliFat.Estado EstadoFat, "
                    + "        CliFat.email EmailFat, CliFat.CGC CGCFat, CliFat.IE IEFat, CliFat.Codigo CodCliFat, "
                    + "        CliCxf.Codigo CliCxf, CliCxf.email EmailCxf "
                    + " FROM CxFGuias "
                    + " LEFT JOIN Rt_Perc on Rt_Perc.Sequencia = CxFGuias.SeqRotaSai "
                    + "                   and Rt_Perc.Hora1    = CxFGuias.Hora1D "
                    + " LEFT JOIN OS_Vig ON OS_Vig.os = CxFGuias.os "
                    + " LEFT JOIN Clientes CliOri ON OS_Vig.Cliente = CliOri.Codigo "
                    + "                           AND OS_Vig.CodFil = CliOri.CodFil "
                    + " LEFT JOIN Clientes CliDst ON OS_Vig.CliDst  = CliDst.Codigo "
                    + "                           AND OS_Vig.CodFil = CliDst.CodFil "
                    + " LEFT JOIN Clientes CliFat ON OS_Vig.CliFat  = CliFat.Codigo "
                    + "                           AND OS_Vig.CodFil = CliFat.CodFil "
                    + " LEFT JOIN Clientes CliCxf ON CliCxf.Codigo  = CxFGuias.CodCli "
                    + "                           AND CliCxf.CodFil = CxFGuias.CodFil "
                    + " WHERE Rt_Perc.Parada = ? and Rt_Perc.Sequencia = ? and Rt_Perc.Flag_Excl != '*' ";
//                    + " ORDER BY codclifat ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(parada);
            consulta.setString(sequencia);
            consulta.select();

            Rt_Guias rt_Guias;
            while (consulta.Proximo()) {
                rt_Guias = new Rt_Guias();
                rt_Guias.setGuia(consulta.getString("guia"));
                rt_Guias.setSerie(consulta.getString("serie"));
                rt_Guias.setValor(consulta.getString("valor"));
                rt_Guias.setCodCli2(consulta.getString("CodCli2"));

                rt_Guias.setnRedOri(consulta.getString("NRedOri"));
                rt_Guias.setEndOri(consulta.getString("EndOri"));
                rt_Guias.setCidadeOri(consulta.getString("CidadeOri"));
                rt_Guias.setBairroOri(consulta.getString("BairroOri"));
                rt_Guias.setEstadoOri(consulta.getString("EstadoOri"));
                rt_Guias.setEmailOri(consulta.getString("EmailOri"));

                rt_Guias.setBairroDst(consulta.getString("BairroDst"));
                rt_Guias.setCodCliDst(consulta.getString("CodCliDst"));
                rt_Guias.setEstadoDst(consulta.getString("EstadoDst"));
                rt_Guias.setnRedDst(consulta.getString("NRedDst"));
                rt_Guias.setEndDst(consulta.getString("EndDst"));
                rt_Guias.setCidadeDst(consulta.getString("CidadeDst"));
                rt_Guias.setEmailDst(consulta.getString("EmailDst"));
                rt_Guias.setCepDst(consulta.getString("CepDst"));
                rt_Guias.setRotaDst(consulta.getString("rotaDst"));

                rt_Guias.setnRedFat(consulta.getString("NRedFat"));
                rt_Guias.setCodCliFat(consulta.getString("CodCliFat"));
                rt_Guias.setEndFat(consulta.getString("EndFat"));
                rt_Guias.setCidadeFat(consulta.getString("CidadeFat"));
                rt_Guias.setBairroFat(consulta.getString("BairroFat"));
                rt_Guias.setEstadoFat(consulta.getString("EstadoFat"));
                rt_Guias.setCgcFat(consulta.getString("CGCFat"));
                rt_Guias.setIeFat(consulta.getString("IEFat"));
                rt_Guias.setEmailFat(consulta.getString("EmailFat"));

                rt_Guias.setCliCxf(consulta.getString("CliCxf"));
                rt_Guias.setEmailCxf(consulta.getString("EmailCxf"));

                guias.add(rt_Guias);
            }
            consulta.Close();
        } catch (Exception e) {

        }
        return guias;
    }

    public CxFGuias getCxFGuiasById(String codFil, String guia, String serie) throws Exception {
        String sql = "SELECT TOP 1\n"
                + "cliori.nred nred, clidst.nred nreddst, os_vig.NRedFat nredos,\n"
                + "OS_Vig.Nred NRedCli, CxFSaidas.Hr_Saida,\n"
                + "CxFGuias.CliDst,\n CxFGuias.CliOri,\n CxFGuias.CodCli,\n"
                + "CxFGuias.CodFil,\n CxFGuias.DtEnt,\n CxFGuias.DtSai,\n"
                + "CxFGuias.Guia,\n CxFGuias.Hora1,\n CxFGuias.Hora1D,\n"
                + "CxFGuias.HrEnt,\n CxFGuias.HrSai,\n CxFGuias.Lote,\n"
                + "CxFGuias.OperEnt,\n CxFGuias.OperSai,\n CxFGuias.OS,\n"
                + "CxFGuias.PedidoDst,\n CxFGuias.Remessa,\n CxFGuias.RotaEnt,\n"
                + "CxFGuias.RotaSai,\n CxFGuias.SeqRota,\n CxFGuias.SeqRotaSai,\n"
                + "CxFGuias.Serie,\n CxFGuias.Tipo,\n CxFGuias.Valor,\n"
                + "CxFGuias.ValorCh,\n CxFGuias.ValorDh,\n CxFGuias.ValorMd,\n"
                + "CxFGuias.ValorMEstr,\n CxFGuias.ValorMet,\n CxFGuias.ValorOutr,\n"
                + "CxFGuias.VolCh,\n CxFGuias.VolDh,\n CxFGuias.VolMd,\n"
                + "CxFGuias.VolMEstr,\n CxFGuias.VolMet,\n"
                + "CxFGuias.VolOutr,\n CxFGuias.Volumes\n"
                + "FROM CxFGuias\n"
                + "LEFT JOIN clientes cliori ON cliori.codigo = cxfguias.cliori\n"
                + "                          AND cliori.codfil = cxfguias.codfil\n"
                + "LEFT JOIN clientes clidst ON clidst.codigo = cxfguias.clidst\n"
                + "                          AND clidst.codfil = cxfguias.codfil\n"
                + "LEFT JOIN os_vig ON os_vig.OS = cxfguias.OS\n"
                + "                 AND os_vig.codfil = cxfguias.codfil\n"
                + "LEFT JOIN CxFSaidas ON  CxFSaidas.CodFil  = CxFGuias.CodFil\n"
                + "                    AND CxFSaidas.SeqRota = CxFGuias.SeqRotaSai\n"
                + "                    AND CxFSaidas.Remessa = CxFGuias.Remessa\n"
                + " WHERE CxFGuias.guia = ? \n"
                + "    AND CxFGuias.CodFil = ? \n"
                + "    AND CxFGuias.serie = ? ;";

        CxFGuias cxFGuias = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(codFil);
            consulta.setString(serie);
            consulta.select();

            if (consulta.Proximo()) {
                cxFGuias = new CxFGuias();

                cxFGuias.setCodFil(consulta.getString("CodFil"));
                cxFGuias.setCodCli(consulta.getString("CodCli"));
                cxFGuias.setGuia(consulta.getString("Guia"));
                cxFGuias.setSerie(consulta.getString("Serie"));
                cxFGuias.setCliOri(consulta.getString("CliOri"));
                cxFGuias.setCliDst(consulta.getString("CliDst"));
                cxFGuias.setValor(consulta.getString("Valor"));
                cxFGuias.setTipo(consulta.getString("Tipo"));
                cxFGuias.setRotaEnt(consulta.getString("RotaEnt"));
                cxFGuias.setOperEnt(consulta.getString("OperEnt"));
                cxFGuias.setDtEnt(consulta.getString("DtEnt"));
                cxFGuias.setHrEnt(consulta.getString("HrEnt"));
                cxFGuias.setRotaSai(consulta.getString("RotaSai"));
                cxFGuias.setRemessa(consulta.getString("Remessa"));
                cxFGuias.setOperSai(consulta.getString("OperSai"));
                cxFGuias.setDtSai(consulta.getString("DtSai"));
                cxFGuias.setHrSai(consulta.getString("HrSai"));
                cxFGuias.setSeqRota(consulta.getString("SeqRota"));
                cxFGuias.setHora1(consulta.getString("Hora1"));
                cxFGuias.setSeqRotaSai(consulta.getString("SeqRotaSai"));
                cxFGuias.setHora1D(consulta.getString("Hora1D"));
                cxFGuias.setOS(consulta.getString("OS").replace(".0", ""));
                cxFGuias.setLote(consulta.getString("Lote"));
                cxFGuias.setVolumes(consulta.getInt("Volumes"));
                cxFGuias.setVolDh(consulta.getInt("VolDh"));
                cxFGuias.setValorDh(consulta.getString("ValorDh"));
                cxFGuias.setVolCh(consulta.getInt("VolCh"));
                cxFGuias.setValorCh(consulta.getString("ValorCh"));
                cxFGuias.setVolMd(consulta.getInt("VolMd"));
                cxFGuias.setValorMd(consulta.getString("ValorMd"));
                cxFGuias.setVolMet(consulta.getInt("VolMet"));
                cxFGuias.setValorMet(consulta.getString("ValorMet"));
                cxFGuias.setVolMEstr(consulta.getInt("VolMEstr"));
                cxFGuias.setValorMEstr(consulta.getString("ValorMEstr"));
                cxFGuias.setVolOutr(consulta.getInt("VolOutr"));
                cxFGuias.setValorOutr(consulta.getString("ValorOutr"));
                cxFGuias.setPedidoDst(consulta.getString("PedidoDst"));
                cxFGuias.setNRed(consulta.getString("nred"));
                cxFGuias.setNRedDst(consulta.getString("nreddst"));
                cxFGuias.setNRedOS(consulta.getString("nredos"));
                cxFGuias.setNRedCli(consulta.getString("NRedCli"));
                cxFGuias.setHr_Saida(consulta.getString("Hr_Saida"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }

        return cxFGuias;
    }

    public int verificaGuiaSerieExiste(String guia, String serie) throws Exception {
        String sql = "SELECT Isnull(COUNT(*),0) Qtde FROM CXFGuias\n"
                + "WHERE guia = ? AND Serie = ? ;";

        Consulta consulta = null;
        int qtde = 0;
        try {
            consulta = new Consulta(sql, persistencia);
            // consulta.setString(codFil);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();
            if (consulta.Proximo()) {
                qtde = consulta.getInt("Qtde");
            }
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return qtde;
    }

    public void inserirGuiaSerie(String codFil, String codCXF, String guia, String serie, String clienteOrigem,
            String clienteDestino, String valor, String tipo, String rota, String usuario, String data,
            String horaAtual, String sequenciaRota, String OS, String hora1Parada) throws Exception {
        String sql = "declare @CodFil float\n"
                + "declare @CodCXF varchar(7)\n"
                + "declare @Guia float\n"
                + "declare @Serie varchar(3)\n"
                + "declare @ClienteOrigem varchar(7)\n"
                + "declare @ClienteDestino varchar(7)\n"
                + "declare @Valor float\n"
                + "declare @Tipo varchar(1)\n"
                + "declare @RotaSelecionada varchar(3)\n"
                + "declare @Usuario varchar(10)\n"
                + "declare @DataSelecionada datetime\n"
                + "declare @HoraAtual varchar(5)\n"
                + "declare @SequenciaRotaSelecionada float\n"
                + "declare @OS float\n"
                + "declare @Hora1Parada varchar(5)\n"
                + "set @CodFil = ?\n"
                + "set @CodCXF = ?\n"
                + "set @Guia = ?\n"
                + "set @Serie = ?\n"
                + "set @ClienteOrigem = ?\n"
                + "set @ClienteDestino = ?\n"
                + "set @Valor = ?\n"
                + "set @Tipo = ?\n"
                + "set @RotaSelecionada = ?\n"
                + "set @Usuario = ?\n"
                + "set @DataSelecionada = ?\n"
                + "set @HoraAtual = ?\n"
                + "set @SequenciaRotaSelecionada = ?\n"
                + "set @OS = ?\n"
                + "set @Hora1Parada = ?\n\n"
                + "BEGIN TRANSACTION\n"
                + "    INSERT INTO CxFGuias (CodFil, CodCli, Guia, Serie, CliOri, CliDst, Valor,\n"
                + "                          Tipo, RotaEnt, OperEnt, DtEnt, HrEnt,\n"
                + "                          SeqRota, OS, Hora1,\n"
                + "                          Volumes, VolDh, ValorDh, VolCh, ValorCh, VolMd, ValorMd,\n"
                + "                          VolMet, ValorMet, VolMEstr, ValorMEstr, VolOutr, ValorOutr)\n"
                + "    VALUES (@CodFil, @CodCXF, @Guia, @Serie, @ClienteOrigem, @ClienteDestino, @Valor,\n"
                + "            @Tipo, @RotaSelecionada, @Usuario, @DataSelecionada, @HoraAtual,\n"
                + "            @SequenciaRotaSelecionada, @OS, @Hora1Parada,\n"
                + "            0, 0, 0, 0, 0, 0, 0,\n"
                + "            0, 0, 0, 0, 0, 0);\n"
                + "\n"
                + "    UPDATE GTV\n"
                + "    SET Situacao = '4',\n"
                + "        OS       = @OS\n"
                + "    WHERE CodFil = @CodFil\n"
                + "      AND Guia = @Guia\n"
                + "      AND Serie = @Serie\n"
                + "      AND Situacao < '4';\n"
                + "COMMIT";

        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(codFil);
            consulta.setString(codCXF);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(clienteOrigem);
            consulta.setString(clienteDestino);
            consulta.setString(valor);
            consulta.setString(tipo);
            consulta.setString(rota);
            consulta.setString(usuario);
            consulta.setString(data);
            consulta.setString(horaAtual);
            consulta.setString(sequenciaRota);
            consulta.setString(OS);
            consulta.setString(hora1Parada);

            consulta.insert();
            consulta.Close();
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
    }

    public String obterValorTotal(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            String sql = "Select Sum(Valor) TotalGeral from CxfGuias \n"
                    + " where Guia  = ? \n"
                    + "   and Serie = ? ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.select();
            String valor = null;
            if (consulta.Proximo()) {
                valor = consulta.getString("TotalGeral");
            }
            consulta.Close();
            return valor;
        } catch (Exception e) {
            throw new Exception("Rt_GuiasDao.obterValorTotal - " + e.getMessage() + "\r\n"
                    + "Select Sum(Valor) TotalGeral from Rt_guias \n"
                    + " where Guia  = " + guia + " \n"
                    + "   and Serie = " + serie);
        }
    }

    public CxFGuias buscarGuia(String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = " Select Guia, Serie, Valor, CxFGuias.CodFil, CxFGuias.CliOri, CxFGuias.CliDst, Clientes.NRed \n"
                    + " from CxFGuias \n"
                    + " left Join Clientes on Clientes.Codigo = CxFGuias.CliDst \n"
                    + "                    and Clientes.CodFil = CxFGuias.CodFil \n"
                    + " where Guia = ?\n"
                    + "   and Serie = ?\n"
                    + "   and CxFGuias.CodFil = ?\n"
                    + "   and RotaSai is Null ";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.select();
            CxFGuias cxFGuias = null;
            if (consulta.Proximo()) {
                cxFGuias = new CxFGuias();
                cxFGuias.setGuia(consulta.getString("Guia"));
                cxFGuias.setSerie(consulta.getString("Serie"));
                cxFGuias.setValor(consulta.getString("Valor"));
                cxFGuias.setCodFil(consulta.getString("CodFil"));
                cxFGuias.setCliOri(consulta.getString("CliOri"));
                cxFGuias.setCliDst(consulta.getString("CliDst"));
                cxFGuias.setNRed(consulta.getString("NRed"));
            }
            consulta.Close();
            return cxFGuias;
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.buscarGuia - " + e.getMessage() + "\r\n"
                    + " Select Guia, Serie, Valor, CxFGuias.CodFil, CxFGuias.CliOri, CxFGuias.CliDst, Clientes.NRed \n"
                    + " from CxFGuias \n"
                    + " left Join Clientes on Clientes.Codigo = CxFGuias.CliDst \n"
                    + "                    and Clientes.CodFil = CxFGuias.CodFil \n"
                    + " where Guia = " + guia + "\n"
                    + "   and Serie = " + serie + "\n"
                    + "   and CxFGuias.CodFil = " + codFil + "\n"
                    + "   and RotaSai is Null ");
        }
    }

    public void saidaCxFGuias(String rotaSai, String seqRotaSai, String remessa, String hora1D, String cliDst, String operSai, String dtSai, String hrSai,
            String OS, String lote, String guia, String serie, String codFil, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFGuias set \n"
                    + " RotaSai    = ?, \n"
                    + " SeqRotaSai = ?, \n"
                    + " Remessa    = ?, \n"
                    + " Hora1D     = ?, \n"
                    + " CliDst     = ?, \n"
                    + " OperSai    = ?, \n"
                    + " Dtsai      = ?, \n"
                    + " HrSai      = ?, \n"
                    + " OS         = ?, \n"
                    + " Lote       = ? \n"
                    + " where Guia = ? \n"
                    + "   and Serie = ? \n"
                    + "   and CodFil = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(rotaSai);
            consulta.setString(seqRotaSai);
            consulta.setString(remessa);
            consulta.setString(hora1D);
            consulta.setString(cliDst);
            consulta.setString(operSai);
            consulta.setString(dtSai);
            consulta.setString(hrSai);
            consulta.setString(OS);
            consulta.setString(lote);
            consulta.setString(guia);
            consulta.setString(serie);
            consulta.setString(codFil);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.saidaCxFGuias - " + e.getMessage() + "\r\n"
                    + "Update CxFGuias set \n"
                    + " RotaSai    = " + rotaSai + ", \n"
                    + " SeqRotaSai = " + seqRotaSai + ", \n"
                    + " Remessa    = " + remessa + ", \n"
                    + " Hora1D     = " + hora1D + ", \n"
                    + " CliDst     = " + cliDst + ", \n"
                    + " OperSai    = " + operSai + ", \n"
                    + " Dtsai      = " + dtSai + ", \n"
                    + " HrSai      = " + hrSai + ", \n"
                    + " OS         = " + OS + ", \n"
                    + " Lote       = " + lote + " \n"
                    + " where Guia = " + guia + " \n"
                    + "   and Serie = " + serie + " \n"
                    + "   and CodFil = " + codFil);
        }
    }

    public void atualizarQtdeValor(String qtde, String valor, String codFil, String seqRota, String remessa, Persistencia persistencia) throws Exception {
        try {
            String sql = "Update CxFSaidas set \n"
                    + " Qtde = ?\n"
                    + " Valor = ?\n"
                    + " where CodFil = ?\n"
                    + "   and SeqRota = ?\n"
                    + "   and Remessa = ?";
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(qtde);
            consulta.setString(valor);
            consulta.setString(codFil);
            consulta.setString(seqRota);
            consulta.setString(remessa);
            consulta.update();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("CxfguiasDao.atualizarQtdeValor - " + e.getMessage() + "\r\n"
                    + "Update CxFSaidas set \n"
                    + " Qtde = " + qtde + "\n"
                    + " Valor = " + valor + "\n"
                    + " where CodFil = " + codFil + "\n"
                    + "   and SeqRota = " + seqRota + "\n"
                    + "   and Remessa = " + remessa);
        }
    }

    public CxFGuias getSeqRotaSaiByGuiaSerie(String guia, String serie) throws Exception {
        String sql = "SELECT Isnull(SeqRotaSai, 0) SeqRotaSai, RotaSai, DtSai\n"
                + "FROM CXFGuias\n"
                + "WHERE Guia = ? \n"
                + "  AND Serie = ? ;";

        CxFGuias item = null;
        Consulta consulta = null;
        try {
            consulta = new Consulta(sql, persistencia);
            consulta.setString(guia);
            consulta.setString(serie);

            consulta.select();
            if (consulta.Proximo()) {
                item = new CxFGuias();
                item.setSeqRotaSai(consulta.getString("SeqRotaSai"));
                item.setRotaSai(consulta.getString("RotaSai"));
                item.setDtSai(consulta.getString("DtSai"));
            }
        } finally {
            if (consulta != null) {
                consulta.Close();
            }
        }
        return item;
    }

}
