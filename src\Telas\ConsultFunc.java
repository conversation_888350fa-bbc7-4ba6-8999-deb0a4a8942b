package Telas;

import Dados.Consulta;
import Dados.OLD.Persistencia_OLD;
import java.awt.Toolkit;
import java.awt.event.KeyEvent;
import javax.swing.table.DefaultTableModel;
import pacotesuteis.Main;

/**
 *
 * <AUTHOR>
 */
public class ConsultFunc extends javax.swing.JFrame {

    /**
     * Creates new form ConsultFunc
     */
    private Persistencia_OLD persistfunc;
    private String param;
    private int tipoconsult;

    public ConsultFunc() {
        initComponents();
    }

    /**
     * This method is called from within the constructor to initialize the form.
     * WARNING: Do NOT modify this code. The content of this method is always
     * regenerated by the Form Editor.
     */
    @SuppressWarnings("unchecked")
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        jScrollPane1 = new javax.swing.JScrollPane();
        ConsultFunc = new javax.swing.JTable();
        nomePesq = new javax.swing.JTextField();
        TitPesq = new javax.swing.JLabel();
        jButton1 = new javax.swing.JButton();
        ConsultaNome = new javax.swing.JRadioButton();
        ConsultaNomeGuerra = new javax.swing.JRadioButton();
        Tconsult = new javax.swing.JLabel();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
        setTitle("Funcionários");
        setAlwaysOnTop(true);
        setBackground(new java.awt.Color(20, 91, 155));
        setResizable(false);
        addWindowListener(new java.awt.event.WindowAdapter() {
            public void windowOpened(java.awt.event.WindowEvent evt) {
                formWindowOpened(evt);
            }
            public void windowClosing(java.awt.event.WindowEvent evt) {
                formWindowClosing(evt);
            }
        });

        ConsultFunc.setAutoCreateRowSorter(true);
        ConsultFunc.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        ConsultFunc.setModel(new javax.swing.table.DefaultTableModel(
            new Object [][] {

            },
            new String [] {
                "Matricula", "Nome Guerra", "Nome", "CodFil"
            }
        ));
        ConsultFunc.setAutoResizeMode(javax.swing.JTable.AUTO_RESIZE_OFF);
        ConsultFunc.setRowHeight(30);
        ConsultFunc.setShowVerticalLines(false);
        ConsultFunc.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultFuncKeyPressed(evt);
            }
        });
        jScrollPane1.setViewportView(ConsultFunc);

        nomePesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        nomePesq.setFocusable(false);
        nomePesq.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                nomePesqMouseClicked(evt);
            }
        });
        nomePesq.addFocusListener(new java.awt.event.FocusAdapter() {
            public void focusGained(java.awt.event.FocusEvent evt) {
                nomePesqFocusGained(evt);
            }
        });
        nomePesq.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                nomePesqKeyPressed(evt);
            }
        });

        TitPesq.setFont(new java.awt.Font("Tahoma", 0, 14)); // NOI18N
        TitPesq.setText("Nome.:");

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/Figuras/icone_pesquisar.png"))); // NOI18N
        jButton1.setToolTipText("Toque para pesquisar");
        jButton1.setBorderPainted(false);
        jButton1.setContentAreaFilled(false);
        jButton1.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                jButton1MouseClicked(evt);
            }
        });
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        buttonGroup1.add(ConsultaNome);
        ConsultaNome.setSelected(true);
        ConsultaNome.setText("Nome");
        ConsultaNome.setFocusable(false);
        ConsultaNome.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                ConsultaNomeMouseClicked(evt);
            }
        });

        buttonGroup1.add(ConsultaNomeGuerra);
        ConsultaNomeGuerra.setText("Nome Guerra");
        ConsultaNomeGuerra.setFocusable(false);
        ConsultaNomeGuerra.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseClicked(java.awt.event.MouseEvent evt) {
                ConsultaNomeGuerraMouseClicked(evt);
            }
        });
        ConsultaNomeGuerra.addKeyListener(new java.awt.event.KeyAdapter() {
            public void keyPressed(java.awt.event.KeyEvent evt) {
                ConsultaNomeGuerraKeyPressed(evt);
            }
        });

        javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addComponent(jScrollPane1)
            .addGroup(layout.createSequentialGroup()
                .addContainerGap()
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(Tconsult)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(ConsultaNome)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                        .addComponent(ConsultaNomeGuerra))
                    .addGroup(layout.createSequentialGroup()
                        .addComponent(TitPesq)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                        .addComponent(nomePesq, javax.swing.GroupLayout.PREFERRED_SIZE, 323, javax.swing.GroupLayout.PREFERRED_SIZE)
                        .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.UNRELATED)
                        .addComponent(jButton1, javax.swing.GroupLayout.PREFERRED_SIZE, 52, javax.swing.GroupLayout.PREFERRED_SIZE)))
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING)
            .addGroup(javax.swing.GroupLayout.Alignment.TRAILING, layout.createSequentialGroup()
                .addContainerGap(javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE)
                    .addComponent(ConsultaNome)
                    .addComponent(ConsultaNomeGuerra)
                    .addComponent(Tconsult))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING, false)
                    .addComponent(jButton1, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                    .addComponent(nomePesq)
                    .addComponent(TitPesq, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                .addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED)
                .addComponent(jScrollPane1, javax.swing.GroupLayout.PREFERRED_SIZE, 558, javax.swing.GroupLayout.PREFERRED_SIZE))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

    private void formWindowOpened(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowOpened
        ConsultFunc.requestFocus();
        DefaultTableModel modelo = (DefaultTableModel) ConsultFunc.getModel();
        setIconImage(Toolkit.getDefaultToolkit().getImage(getClass().getResource("/Figuras/icone_satellite.png")));
        try {
            persistfunc = new Persistencia_OLD(param, "/Dados/mapconect.txt", "DESK");
            Consulta consult = new Consulta("select top 20 matr,nome_guer,nome,codfil from funcion where Situacao=?", persistfunc);
            consult.setString("A");
            consult.select();
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4).substring(0, consult.getString(4).indexOf("."))});
            }
            consult.Close();
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 0);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 1);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 2);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 3);
            consult.Close();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowOpened

    private void jButton1MouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_jButton1MouseClicked
        Consulta();
    }//GEN-LAST:event_jButton1MouseClicked

    private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton1ActionPerformed
        // TODO add your handling code here:
    }//GEN-LAST:event_jButton1ActionPerformed

    private void nomePesqKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_nomePesqKeyPressed
        if (evt.getKeyCode() == KeyEvent.VK_ENTER) {
            Consulta();
        } else if (evt.getKeyCode() == KeyEvent.VK_F2) {
            if (ConsultaNome.isSelected()) {
                ConsultaNomeGuerra.setSelected(true);
                TitPesq.setText("NGuer:");
                nomePesq.requestFocus();
            } else {
                ConsultaNome.setSelected(true);
                TitPesq.setText("Nome.:");
                nomePesq.requestFocus();
            }
        }
    }//GEN-LAST:event_nomePesqKeyPressed

    private void ConsultFuncKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultFuncKeyPressed
        if (evt.getKeyCode() == 27) {
            String mat;
            try {
                mat = (String) ConsultFunc.getValueAt(ConsultFunc.getSelectedRow(), 0);
            } catch (Exception e) {
                mat = "0";
            }
            Main.DadosConsultas.setMatr(mat);
            this.dispose();
        } else if (evt.getKeyCode() == KeyEvent.VK_P) {
            nomePesq.setFocusable(true);
            nomePesq.setText("");
            nomePesq.requestFocus();
        }
    }//GEN-LAST:event_ConsultFuncKeyPressed

    private void ConsultaNomeMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_ConsultaNomeMouseClicked
        ConsultaNome.setSelected(true);
        TitPesq.setText("Nome:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");

    }//GEN-LAST:event_ConsultaNomeMouseClicked

    private void ConsultaNomeGuerraKeyPressed(java.awt.event.KeyEvent evt) {//GEN-FIRST:event_ConsultaNomeGuerraKeyPressed

    }//GEN-LAST:event_ConsultaNomeGuerraKeyPressed

    private void ConsultaNomeGuerraMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_ConsultaNomeGuerraMouseClicked
        ConsultaNomeGuerra.setSelected(true);
        TitPesq.setText("NGuer:");
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_ConsultaNomeGuerraMouseClicked

    private void nomePesqFocusGained(java.awt.event.FocusEvent evt) {//GEN-FIRST:event_nomePesqFocusGained
        Tconsult.setText("F2- Mudar Forma da Pesquisa");
    }//GEN-LAST:event_nomePesqFocusGained

    private void nomePesqMouseClicked(java.awt.event.MouseEvent evt) {//GEN-FIRST:event_nomePesqMouseClicked
        nomePesq.setFocusable(true);
        nomePesq.requestFocus();
    }//GEN-LAST:event_nomePesqMouseClicked

    private void formWindowClosing(java.awt.event.WindowEvent evt) {//GEN-FIRST:event_formWindowClosing
        try {
            persistfunc.FechaConexao();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
    }//GEN-LAST:event_formWindowClosing

    private void Consulta() {
        DefaultTableModel modelo = (DefaultTableModel) ConsultFunc.getModel();
        setIconImage(Toolkit.getDefaultToolkit().getImage(getClass().getResource("/Figuras/icone_satellite.png")));
        try {
            Consulta consult;
            if (ConsultaNome.isSelected()) {
                consult = new Consulta("select matr,nome_guer,nome,codfil from funcion where Situacao=? and Nome like ?", persistfunc);
                consult.setString("A");
                consult.setString("%" + nomePesq.getText() + "%");
            } else {
                consult = new Consulta("select matr,nome_guer,nome,codfil from funcion where Situacao=? and Nome_Guer like ?", persistfunc);
                consult.setString("A");
                consult.setString("%" + nomePesq.getText() + "%");
            }
            consult.select();
            modelo.setNumRows(0);
            while (consult.Proximo()) {
                modelo.addRow(new String[]{consult.getString(1).substring(0, consult.getString(1).indexOf(".")),
                    consult.getString(2),
                    consult.getString(3),
                    consult.getString(4).substring(0, consult.getString(4).indexOf("."))});
            }
            consult.Close();
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 0);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 1);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 2);
            br.com.sasw.pacotesuteis.utilidades.UtilidadesTabela.Redimensionar(ConsultFunc, 3);
            consult.Close();
        } catch (Exception e) {
            javax.swing.JOptionPane.showMessageDialog(null, e.getMessage(), "Falha", 1);
        }
        nomePesq.setFocusable(false);
        Tconsult.setText("");
        ConsultFunc.requestFocus();
    }

    public void setTipoConsult(int tipo) {
        tipoconsult = tipo;
    }

    /**
     *
     * @param Param
     */
    public void setParam(String Param) {
        param = Param;
    }

    /**
     * @param args the command line arguments
     */
    public static void main(String args[]) {
        /* Set the Nimbus look and feel */
        //<editor-fold defaultstate="collapsed" desc=" Look and feel setting code (optional) ">
        /* If Nimbus (introduced in Java SE 6) is not available, stay with the default look and feel.
         * For details see http://download.oracle.com/javase/tutorial/uiswing/lookandfeel/plaf.html 
         */
        try {
            for (javax.swing.UIManager.LookAndFeelInfo info : javax.swing.UIManager.getInstalledLookAndFeels()) {
                if ("Nimbus".equals(info.getName())) {
                    javax.swing.UIManager.setLookAndFeel(info.getClassName());
                    break;
                }
            }
        } catch (ClassNotFoundException ex) {
            java.util.logging.Logger.getLogger(ConsultFunc.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (InstantiationException ex) {
            java.util.logging.Logger.getLogger(ConsultFunc.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (IllegalAccessException ex) {
            java.util.logging.Logger.getLogger(ConsultFunc.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        } catch (javax.swing.UnsupportedLookAndFeelException ex) {
            java.util.logging.Logger.getLogger(ConsultFunc.class.getName()).log(java.util.logging.Level.SEVERE, null, ex);
        }
        //</editor-fold>

        /* Create and display the form */
        java.awt.EventQueue.invokeLater(new Runnable() {
            public void run() {
                new ConsultFunc().setVisible(true);
            }
        });
    }
    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.JTable ConsultFunc;
    private javax.swing.JRadioButton ConsultaNome;
    private javax.swing.JRadioButton ConsultaNomeGuerra;
    private javax.swing.JLabel Tconsult;
    private javax.swing.JLabel TitPesq;
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.JButton jButton1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTextField nomePesq;
    // End of variables declaration//GEN-END:variables
}
