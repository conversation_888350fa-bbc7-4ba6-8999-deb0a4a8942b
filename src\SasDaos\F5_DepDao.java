/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;
import SasBeans.F5_Dep;
import SasBeans.Municipios;
import SasBeansCompostas.CarregarRelatorio;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class F5_DepDao {

    public List<CarregarRelatorio> listaDepen(String matricula, Persistencia persistencia) throws Exception {
        try {
            List<CarregarRelatorio> retorno = new ArrayList<>();
            String sql = "Select F5_Dep.*, Municipios.UF from F5_Dep \n"
                    + "	left join Municipios on Municipios.Codigo = F5_Dep.CodNaturalid \n"
                    + "	Where Matr = ? \n"
                    + "       and Tipo = 'F'\n"
                    + "       and DepSF ='S'\n"
                    + "	order by Dt_Nasc ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();

            CarregarRelatorio cm;
            F5_Dep f5_Dep;
            Municipios municipios;

            while (consulta.Proximo()) {
                cm = new CarregarRelatorio();
                f5_Dep = new F5_Dep();
                municipios = new Municipios();

                f5_Dep.setNome(consulta.getString("Nome"));
                f5_Dep.setSexo(consulta.getString("Sexo"));
                f5_Dep.setDt_Nasc(consulta.getLocalDate("Dt_Nasc"));

                municipios.setUF(consulta.getString("UF"));

                cm.setF5_Dep(f5_Dep);
                cm.setMunicipios(municipios);

                retorno.add(cm);
            }

            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("F5_DepDao.ListaDep - " + e.getMessage()
                    + "Select F5_Dep.*, Municipios.UF from F5_Dep \n"
                    + "	left join Municipios on Municipios.Codigo = F5_Dep.CodNaturalid \n"
                    + "	Where Matr = " + matricula + " \n"
                    + "       and Tipo = 'F'\n"
                    + "       and DepSF ='S'\n"
                    + "	order by Dt_Nasc ");
        }
    }

    public List<CarregarRelatorio> listaIRDepen(String matricula, Persistencia persistencia) throws Exception {
        try {
            List<CarregarRelatorio> retorno = new ArrayList<>();
            String sql = "Select F5_Dep.*, Municipios.UF from F5_Dep \n"
                    + "left join Municipios on Municipios.Codigo = F5_Dep.CodNaturalid \n"
                    + "Where Matr = ?\n"
                    + "and DepIR ='S'\n"
                    + "order by Dt_Nasc ";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(matricula);
            consulta.select();

            CarregarRelatorio cm;
            F5_Dep f5_Dep;
            Municipios municipios;

            while (consulta.Proximo()) {
                cm = new CarregarRelatorio();
                f5_Dep = new F5_Dep();
                municipios = new Municipios();

                f5_Dep.setNome(consulta.getString("Nome"));
                f5_Dep.setSexo(consulta.getString("Sexo"));
                f5_Dep.setDt_Nasc(consulta.getLocalDate("Dt_Nasc"));

                municipios.setUF(consulta.getString("UF"));

                cm.setF5_Dep(f5_Dep);
                cm.setMunicipios(municipios);

                retorno.add(cm);
            }

            consulta.Close();
            return retorno;

        } catch (Exception e) {
            throw new Exception("F5_DepDao.ListaDep - " + e.getMessage()
                    + "Select F5_Dep.*, Municipios.UF from F5_Dep \n"
                    + "left join Municipios on Municipios.Codigo = F5_Dep.CodNaturalid \n"
                    + "Where Matr = " + matricula + "\n"
                    + "and DepIR ='S'\n"
                    + "order by Dt_Nasc ");
        }
    }
}
