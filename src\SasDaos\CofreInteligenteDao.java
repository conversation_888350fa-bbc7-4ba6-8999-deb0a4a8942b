/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package SasDaos;

import Dados.Consulta;
import Dados.Persistencia;

/**
 *
 * <AUTHOR>
 */
public class CofreInteligenteDao {

    /**
     * Grava retorno dado para uma nota com codcidade
     *
     * @param Mensagem - Mensagem recebida
     * @param Lido - Tag informando se mensagem já processada/lida
     * @param persistencia - Conexão com o banco
     * @throws Exception
     */
    public void gravaMensagemPerto(String IDEvento, String CodEquipamento, String Tamanho_Mensagem, String Prioridade, String Data, String Hora,
            String Operador, String TipoEvento, String Dados_Operacao, String ValorTotalOp, String NumRecibo, String TipoMoeda,
            String Lido, Persistencia persistencia) throws Exception {
        String sql = "Insert into Integra_CIPerto "
                + "(Sequencia,ID_Evento,Cod_Equipamento,<PERSON><PERSON><PERSON>_Mensagem,Prioridade,Data,Hora,Operador,TipoEvento, "
                + " Dados_Operacao,Valor_Total_Operacao,Num_Recibo,Tipo_Moeda,Lido,Dt_Recebimento,Hr_Recebimento) Values ( "
                + " (Select Isnull(Max(Sequencia),0)+1 from Integra_CIPErto), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, Convert(Date,Getdate()), Substring(Convert(Varchar,Getdate(),108),1,5)) ";
        try {
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(IDEvento);
            consulta.setString(CodEquipamento);
            consulta.setString(Tamanho_Mensagem);
            consulta.setString(Prioridade);
            consulta.setString(Data);
            consulta.setString(Hora);
            consulta.setString(Operador);
            consulta.setString(TipoEvento);
            consulta.setString(Dados_Operacao);
            consulta.setString(ValorTotalOp);
            consulta.setString(NumRecibo);
            consulta.setString(TipoMoeda);
            consulta.setString(Lido);
            consulta.insert();
            consulta.close();
        } catch (Exception e) {
            throw new Exception("Insert into Integra_CIPerto (Sequencia, Mensagem, Lido, Dt_Recebimento, Hr_Recebimeno) Values ( "
                    + "(Sequencia,ID_Evento,Cod_Equipamento,Tamanho_Mensagem,Prioridade,Data,Hora,Operador,TipoEvento, "
                    + " Dados_Operacao,Valor_Total_Operacao,Num_Recibo,Tipo_Moeda,Lido,Dt_Recebimento,Hr_Recebimento) Values ( "
                    + " (Select Isnull(Max(Sequencia),0)+1 from XMLCI), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, Convert(Date,Getdate()), Substring(Convert(Varchar,Getdate(),108),1,5)) ");
        }

    }
}
